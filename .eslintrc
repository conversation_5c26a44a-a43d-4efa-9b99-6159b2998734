{
  "parser": "@typescript-eslint/parser",
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended" // Enables eslint-plugin-prettier and displays prettier errors as ESLint errors. Make sure this is always the last configuration in the extends array.
    // "plugin:jest" // TODO: leaving this commented out in case we install eslint jest plugin
  ],
  "env": {
    "es6": true,
    "node": true,
    "browser": true
  },
  "parserOptions": {
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "rules": {
    "semi": ["error", "always"],
    "quotes": ["error", "double"],
    "react/prop-types": [2, { "ignore": ["children"] }],
    "@typescript-eslint/no-explicit-any": "off"
  },
  "overrides": [
    {
      "files": ["*.js"],
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/explicit-function-return-type": "off"
      }
    },
    {
      "files": ["*.ts"],
      "rules": {
        "no-use-before-define": "off",
        "@typescript-eslint/no-use-before-define": [
          "error",
          { "functions": false, "classes": false, "enums": false, "typedefs": false, "variables": true }
        ]
      }
    }
  ]
}
