# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: CI

on:
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          always-auth: true
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          registry-url: https://registry.npmjs.org
          scope: "@wealthyhood"
      - name: Install npm dependencies
        run: npm install --production=false
        env:
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Build Client & Server
        run: npm run build
