# Dashboard

## Details
- Offerings (new only)
- Market commentary (all fund managers should be able to comment about markets)
- Portfolio
- Transactions

--------------------------------------------

# Portfolio

## Details
- Portfolio value chart (should be able to see either total portfolio value or individual token performance - checklist selection)
- List of holdings: 
    - list of different tokens with percentage of total portfolio for each token
    - should be able to see number of tokens, token value, holding value
    - should be able to be redirected to the fund page by clicking the fund icon/details button
    - displays tags that indicate asset class and type of fund

# Offerings

## Details
- Contains new funds and established funds
- User will be able to invest either on the fund page or by clickin the invest button on the offerings page
- Filters (for new & established / for asset class / for country)

### Established Funds
*Basic information*
- Fund logo
- Fund name
- Asset class & type of fund
- (we don't need to show documents)
- Minimum investment
- Token price
- Minimum redemption period & frequency
- Performance: we display either year to date or performance since bought
- Invest/details button

----------------------------

- For established funds each token will be dipslaying the same information we display on the portfolio page for the holdings (should be able to see  token value, not number of tokens or holding value)
- For the performance we display either year to date or performance since bought
- For each established fund, user should be able to expand section that contains information about
    - Name of management company
    - Investment objective
    - Country
    - When the fund started
    - Performance (year to date and annualised since inception)

### New funds
*Basic information*
- Fund logo
- Fund name
- Asset class & type of fund (a tag should be whether it's open or closed)
- (we don't need to show documents)
- Minimum investment
- Token price
- Minimum redemption period & frequency
- Progress bar for investment target (with indications for min and max targets)
- Invest button
- The expand section should be the same as the established funds (apart from performance)

# Search
- Search input on the navigation bar
- On each key press should display relevant results
- User should be able to click on the results and be redirected to the fund page
- On enter press user will be redirected to a page same as offerings with different results populated according to search

# Notifications
- Fund news (will redirect user to fund page on the news section)

# Funds Submission process
- Create a page where fund managers can submit all information required for the new fund. This information will be reviewed by Wealthyhood and upon completion of offline legal processes, the offering will be available to the users

# Fund Pages

## Tab Sections
- Investment Objective
- Performance & Statistics
- Fund Information
- Fund Documents
- Commentary (can involve either comments about the market or communication with the investors - can be separate & should mark as private - investors only - or public)
- Team

## Additional Information
- User should be able to buy or redempt tokens (through investing box fixed shown on the right side of the page)
- This box should also show minimum investments & token price

## Other details
- Fund reporting: it will be updated as a document and the user will receive notification / email

## For Campaigns
- Redemption should be disabled
- Same progress bar for targets as in offerings page

# Fund admin dashboard
- Create stablecoin for the dividends
- Payment of the dividends will be done through the platform

# To be discussed
- How fund announcements will be submitted to the platform
- Registration process for the legal and compliance part for investors and fund managers
- Create different types of users (non-sophisticated / sophisticated) & probably display only the offerings on which those users can invest

# Protocol

## Campaign
During the campaign we will hold on our databases all the information about the investments. Once the campaign completes successfully, we will allocate the token balances to the corresponding addresses.

## Transfer
- Investigate harbor approach
- Either go with external API request (through oracle) or deploy whitelist to separate smart contract.
- Transactions will be allowed only to accounts that are whitelisted by our platform (or a partner)

*Links*
- https://ethereum.stackexchange.com/questions/301/why-cant-contracts-make-api-calls
- https://ethereum.stackexchange.com/questions/2/how-can-an-ethereum-contract-get-data-from-a-website?noredirect=1&lq=1
- https://tlsnotary.org
- https://medium.com/aigang-network/how-ethereum-contract-can-communicate-with-external-data-source-2e32616ea180
- https://ethereumdev.io/getting-data-internet-oraclize/

## Migration process
- When smart contract code has to be updated the new updates will have to be linked to a unique id to the first deployment
- Investigate how to migrate

## Redemption
For redemption
- The user will have to send the tokens to our address (may be also keep a dictionary with the addresses that have redeempted money)
- Burn tokens from redemption address, initiate bank transaction to the user

## Dividends
TODO