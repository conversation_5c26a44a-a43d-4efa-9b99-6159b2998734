<p align="center">
  <a href="https://app.wealthyhood.com">
    <img width=100% src="https://wealthyhood.com/img/logo-full-dark.png">
  </a>
</p>

# Wealthyhood Web App

<table>
<tr>
<td>
Wealthyhood is the first smart, personalised & commission-free wealth management app.
</td>
</tr>
</table>

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes. See deployment for notes on how to deploy the project on a live system.

### Prerequisites

For this project you only need to install [Docker](https://www.docker.com) and [Visual Studio Code](https://code.visualstudio.com)

In addition, you need to have the files with the env variables `dev.env`, `stage.env`, `prod.env` & `cypress.env.json`

### Installing

Once docker is installed, pull the required images

```
docker-compose pull images
```

then build

```
docker-compose build
```

Finally, after you've started the dev environment, you'll have to copy the project's npm dependencies to the container's host (your computer) to make them accessible to VS Code. For this step, you don't have to install anything; just run the following command:

```
docker cp wealthyhood_server:/workdir/node_modules .
```

## Development

To start the dev environment, run

`docker-compose up -d`

## Running the tests

```
docker run cypress
```

## Deployment

We're using two environments, **production** and **staging**.

- Both environments are deployed automatically when you push through git to the configured branch. The environment is configured on [Render](https://render.com).

## Technologies

TODO

## Versioning

TODO
