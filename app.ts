import { CustomRequest } from "custom";
import flash from "connect-flash";
import { auth } from "express-openid-connect";
import express, { NextFunction, Request, Response } from "express";
import MongoStore from "connect-mongo";
import session from "express-session";
import path from "path";
import logger from "morgan";
import bodyParser from "body-parser";
import helpers from "./helpers";
import * as errorController from "./controllers/errorController";
import { findOrCreateUser<PERSON><PERSON><PERSON>, isLoggedIn<PERSON>andler } from "./middlewares/authMiddleware";
import index from "./routes/index";
import adminRoutes from "./routes/adminRoutes";
import investorRoutes from "./routes/investorRoutes";
import portfolioRoutes from "./routes/portfolioRoutes";
import rewardRoutes from "./routes/rewardRoutes";
import investmentProductRoutes from "./routes/investmentProductRoutes";
import assetNewsRoutes from "./routes/assetNewsRoutes";
import telemetryRoutes from "./routes/telemetryRoutes";
import transactionRoutes from "./routes/transactionRoutes";
import orderRoutes from "./routes/orderRoutes";
import winstonLogger from "./services/loggerService";
import * as Sentry from "@sentry/node";
import { ExtraErrorData } from "@sentry/integrations";
import { hcCaptureXhr } from "./services/libhoney";
import cookieParser from "cookie-parser";
import { isSupportedMobile } from "./utils/mobileUtil";
import { envIsDev } from "./utils/environmentUtil";
import { captureException } from "@sentry/react";
import giftRoutes from "./routes/giftRoutes";
import mandateRoutes from "./routes/mandateRoutes";
import automationRoutes from "./routes/automationRoutes";
import eventRoutes from "./routes/eventRoutes";
import savingsProductRoutes from "./routes/savingsProductRoutes";
import axios from "axios";

// TODO: temporary workaround due to wrong type declaration of express-session - downgrading conflicts
// with connect-mongo
declare module "express-session" {
  interface Session {
    returnTo: string;
  }
}

const app = express();

Sentry.init({
  enabled: !envIsDev(),
  dsn: process.env.SENTRY_DNS,
  environment: process.env.NODE_ENV,
  integrations: [
    new ExtraErrorData(),
    // enable HTTP calls tracing
    new Sentry.Integrations.Http({ tracing: false })
  ],
  tracesSampler: () => false
});

// view engine setup
app.set("views", path.join(".", "views"));
app.set("view engine", "pug");

// The Sentry request handler must be the first middleware on the app
// RequestHandler creates a separate execution context using domains, so that every
// transaction/span/breadcrumb is attached to its own Hub instance
app.use(
  Sentry.Handlers.requestHandler({
    // keys to be extracted from req
    request: true,
    // server name
    serverName: true,
    // generate transaction name
    //   methodPath == request.method + request.path (eg. "GET|/foo")
    transaction: true, // default: true = 'methodPath'
    // keys to be extracted from req.user
    user: ["id", "email"],
    // node version
    version: true,
    // timeout for fatal route errors to be delivered
    flushTimeout: 3000
  })
);

/**
 * Trust proxy
 */

// Needed because we're behind a load balancer or reverse proxy
if (process.env.NODE_ENV !== "development") {
  app.enable("trust proxy"); // TODO: we should specify a subnet?

  app.use((req: Request, res: Response, next: NextFunction) => {
    if (req.secure) {
      return next();
    }
    // TODO: sanitize
    winstonLogger.warn(`req is not secure, redirecting to: https://${req.headers.host}${req.url}`);
    res.redirect("https://" + req.headers.host + req.url);
  });
}

// uncomment after placing your favicon in /public
//app.use(favicon(path.join(__dirname, 'public', 'favicon.ico')));
app.use(logger("dev"));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(cookieParser());

app.use(express.static(path.join(".", "public")));

// We redirect any users coming from mobile platforms using the AppsFlyer link.
app.use((req: CustomRequest, res: express.Response, next: express.NextFunction): void => {
  try {
    const userAgent = req.headers["user-agent"];
    if (isSupportedMobile(userAgent)) {
      res.redirect(process.env.APPSFLYER_URL);
    } else {
      next();
    }
  } catch (err) {
    captureException(err);
    next();
  }
});

app.use(
  auth({
    authRequired: false,
    auth0Logout: true,
    issuerBaseURL: `https://${process.env.AUTH0_CUSTOM_DOMAIN}`,
    baseURL: process.env.AUTH0_RETURN_TO,
    clientID: process.env.AUTH0_CLIENT_ID,
    clientSecret: process.env.AUTH0_CLIENT_SECRET,
    secret: process.env.SECRET,
    idpLogout: true,
    routes: {
      // Override the default login route to use your own login route as shown below
      login: false,
      postLogoutRedirect: process.env.AUTH0_RETURN_TO
    },
    session: {
      absoluteDuration: 1 * 24 * 3600 * 1000, // one day - specified in milliseconds
      cookie: {
        // domain: process.env.NODE_ENV === "production" ? process.env.SESSION_DOMAIN : null, // TODO:
        secure: process.env.NODE_ENV !== "development" // serve secure cookies, requires https
        // sameSite: process.env.NODE_ENV === "production" // TODO:
      },
      name: "sessionId", // use generic session name to avoid server fingerprinting & targetted attacks
      store: MongoStore.create({
        mongoUrl: process.env.DATABASE_URL,
        stringify: false,
        touchAfter: 3600, // one hour - time period in seconds,
        ttl: 1 * 24 * 60 * 60, // one day - using as workaround in case cookie max age is not used
        mongoOptions: {
          useNewUrlParser: true,
          useUnifiedTopology: true
        }
      }) as any
    }
  })
);

// Sessions allow us to store data on visitors from request to request
// This keeps users logged in and allows us to send flash messages
app.use(
  session({
    cookie: {
      // domain: process.env.NODE_ENV === "production" ? process.env.SESSION_DOMAIN : null, // TODO:
      maxAge: 1 * 24 * 3600 * 1000, // one day - specified in milliseconds
      secure: process.env.NODE_ENV !== "development" // serve secure cookies, requires https
      // sameSite: process.env.NODE_ENV === "production" // TODO:
    },
    name: "sessionId", // use generic session name to avoid server fingerprinting & targetted attacks
    resave: false,
    proxy: undefined, // uses the "trust proxy" setting from express
    saveUninitialized: true,
    secret: process.env.SECRET,
    store: MongoStore.create({
      mongoUrl: process.env.DATABASE_URL,
      stringify: false,
      touchAfter: 3600, // one hour - time period in seconds,
      ttl: 1 * 24 * 60 * 60, // one day - using as workaround in case cookie max age is not used
      mongoOptions: {
        useNewUrlParser: true,
        useUnifiedTopology: true
      }
    })
  })
);

app.use(flash());
app.use(findOrCreateUserHandler);

// pass variables to our templates + all requests
app.use((req: CustomRequest, res: express.Response, next: express.NextFunction): void => {
  res.locals.h = helpers;
  res.locals.user = req.user || null;
  res.locals.currentPath = req.path;
  res.locals.environment = process.env.NODE_ENV;
  res.locals.flashes = req.flash();
  next();
});

// HoneyComb handler
app.use(
  hcCaptureXhr({
    writeKey: process.env.HONEYCOMB_API_KEY,
    dataset: "web-client-xhr", // TODO: move to config
    disabled: process.env.NODE_ENV === "development"
  })
);

app.use("/", index);
app.use("/admin", isLoggedInHandler, adminRoutes);
app.use("/investor", investorRoutes);
app.use("/portfolios", isLoggedInHandler, portfolioRoutes);
app.use("/rewards", isLoggedInHandler, rewardRoutes);
app.use("/investment-products", isLoggedInHandler, investmentProductRoutes);
app.use("/savings-products", isLoggedInHandler, savingsProductRoutes);
app.use("/telemetry", telemetryRoutes);
app.use("/transactions", isLoggedInHandler, transactionRoutes);
app.use("/orders", isLoggedInHandler, orderRoutes);
app.use("/gifts", isLoggedInHandler, giftRoutes);
app.use("/mandates", isLoggedInHandler, mandateRoutes);
app.use("/automations", isLoggedInHandler, automationRoutes);
app.use("/events", eventRoutes);
app.use("/asset-news", isLoggedInHandler, assetNewsRoutes);
app.get("/check-file", isLoggedInHandler, async (req, res) => {
  try {
    if (!req.query.link) {
      res.status(400).json({
        message: "'link' query parameter is missing"
      });
    }
    await axios.get(req.query.link?.toString());
    res.sendStatus(204);
  } catch (err) {
    res.sendStatus(500);
  }
});

// The Sentry error handler must be before any other error middleware and after all controllers
app.use(Sentry.Handlers.errorHandler());

// If that above routes didn't work, we 404 them and forward to error handler
app.use(errorController.notFound);

if (process.env.NODE_ENV === "development") {
  app.use(errorController.developmentErrors);
} else {
  app.use(errorController.productionErrors);
}

export default app;
