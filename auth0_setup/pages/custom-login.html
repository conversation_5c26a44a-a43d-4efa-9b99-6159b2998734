<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>Log in | Investment Platform</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="icon"
      type="image/x-icon"
      href="https://wealthyhood.com/favicon-32x32.png?v=6260bf0eae5f3b01d2063ca661189191"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
  </head>
  <style>
    body,
    html {
      font-family: "Poppins", sans-serif !important;
      height: 100%;
    }

    .bg-login {
      background-repeat: no-repeat !important;
      height: 100% !important;
    }

    @media (min-width: 1080px) {
      .bg-login {
        background-size: contain;
      }
    }

    .bg-primary {
      background-color: #546be5 !important;
    }

    .text-primary {
      color: #546be5 !important;
    }

    .btn-primary {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #101327;
      padding: 16px;

      /* Button Shadow/Shadow_primary_md */
      box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
      border-radius: 25px;
      border: none;
    }

    @media (max-width: 500px) {
      .btn-primary {
        max-width: 100% !important;
      }
    }

    .btn-primary:hover,
    .btn-primary:active,
    .btn-primary:focus,
    .btn-primary:active:focus {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #282b3d !important;
      padding: 16px;

      /* Button Shadow/Shadow_primary_md */
      box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
      border-radius: 25px;
      border: none;
    }

    .btn-primary.disabled {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #282b3d;
      padding: 16px;
      opacity: 0.7;

      cursor: none;
      /* Button Shadow/Shadow_primary_md */
      box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
      border-radius: 25px;
      border: none;
    }

    .btn-secondary {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #f3f3f4;
      padding-left: 15px;
      padding-right: 15px;
      padding-top: 12px;
      padding-bottom: 10px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      color: #101327;

      border-radius: 25px;
      border: none;
    }

    .btn-secondary:hover,
    .btn-secondary:active,
    .btn-secondary:focus,
    .btn-secondary:active:focus {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #dbdbdc !important;
      color: #101327 !important;
      padding-left: 15px;
      padding-right: 15px;
      padding-top: 12px;
      padding-bottom: 10px;

      border-radius: 25px;
      border: none;
    }

    .btn-secondary.disabled,
    .btn-secondary:disabled {
      /* Button */
      min-width: 168px;
      max-width: 250px;
      height: 57px;
      /* Wealthyhood New/Wealthyhood Dark Blue */
      background: #dbdbdc;
      color: #101327;
      padding-left: 15px;
      padding-right: 15px;
      padding-top: 12px;
      padding-bottom: 10px;
      opacity: 0.7;

      cursor: none;
      /* Button Shadow/Shadow_primary_md */
      box-shadow: 0px 6px 12px rgba(159, 159, 159, 0.48);
      border-radius: 25px;
      border: none;
    }

    .verification-input {
      padding: 16px;
      gap: 12px;

      /*max-width: 516px;*/
      width: 100%;
      height: 57px;

      /* Wealthyhood New/Wealthyhood dark blue - 5% */
      background: rgba(16, 19, 39, 0.05);
      border-radius: 16px;
      border: 3px solid transparent;
    }

    .verification-input:active,
    .verification-input:focus,
    .verification-input:active:focus {
      outline: #377dff !important;
      border: 3px solid;
      border-color: #377dff !important;
    }

    .verification-input:disabled {
      outline: none !important;
      border: none !important;
    }

    #login-error-message {
      display: none;
      white-space: break-spaces;
    }

    .icon-primary {
      color: #546be5;
      cursor: pointer;
    }

    .fas:hover {
      opacity: 0.7;
    }

    #btn-resend:hover {
      opacity: 0.7;
    }

    .loading-mask {
      pointer-events: none;
      opacity: 0.8;
    }

    @-webkit-keyframes spin {
      from {
        -webkit-transform: rotate(0deg);
      }

      to {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    #login-cover-spin::after {
      content: "";
      display: block;
      position: absolute;
      left: 48%;
      top: 40%;
      width: 40px;
      height: 40px;
      border-style: solid;
      border-color: #546be5;
      border-top-color: transparent;
      border-width: 4px;
      border-radius: 50%;
      -webkit-animation: spin 0.8s linear infinite;
      animation: spin 0.8s linear infinite;
    }

    #verification-cover-spin::after {
      content: "";
      display: block;
      position: absolute;
      left: 48%;
      top: 40%;
      width: 40px;
      height: 40px;
      border-style: solid;
      border-color: #546be5;
      border-top-color: transparent;
      border-width: 4px;
      border-radius: 50%;
      -webkit-animation: spin 0.8s linear infinite;
      animation: spin 0.8s linear infinite;
    }

    .z-max-level {
      z-index: 2000;
    }

    .wh-text {
      color: rgba(16, 19, 39, 0.6);
    }

    @media (max-height: 719px) {
      .top-image {
        display: none;
      }

      .overflow-scroll-sm {
        overflow-y: scroll !important;
      }
    }

    @media (min-height: 750px) {
      .h-100-after-750 {
        height: 100% !important;
      }
    }
  </style>

  <body>
    <div
      class="container-fluid p-0 bg-primary overflow-hidden bg-login position-relative overflow-scroll-sm"
      style="background-image: url('https://wealthyhood.com/img/bg-login.png')"
    >
      <div class="row mt-md-4 ms-md-5 mb-md-0 mt-4 mb-4">
        <span class="position-relative z-max-level d-none d-sm-block">
          <img alt="logo" src="https://wealthyhood.com/svg/logo-white.svg" />
        </span>
        <span class="d-flex justify-content-center align-self-center position-relative d-block d-sm-none">
          <img alt="logo" src="https://wealthyhood.com/svg/logo-white.svg" />
        </span>
      </div>
      <div class="row m-0 flex-nowrap h-100-after-750">
        <div
          id="login-box"
          class="d-flex flex-column justify-content-center align-self-center position-relative m-0"
        >
          <div
            id="login-card"
            class="card card-body pb-md-3 p-md-5 pb-3 p-4 mb-3 align-self-center w-100"
            style="max-width: 430px; min-width: 300px; border-radius: 32px"
          >
            <div class="row mb-md-4 mb-4">
              <h3 class="fw-bolder">Let’s get started</h3>
            </div>
            <div class="row mb-4">
              <p class="wh-text">Enter your email to create an account or log in.</p>
            </div>
            <div class="row m-0 w-100 mb-md-4 mb-3">
              <div class="form-group p-0">
                <label class="fw-bolder mb-2"> Email </label>
                <input
                  id="email"
                  type="text"
                  class="verification-input position-relative z-max-level"
                  name="email"
                  placeholder="Your e-mail"
                />
              </div>
            </div>
            <div class="row m-0 mb-md-4 mb-3 w-100">
              <button
                type="button"
                id="btn-login"
                class="btn btn-primary w-100"
                style="max-width: 100% !important"
              >
                Send verification code
              </button>
            </div>
            <div class="row wh-text m-0 mb-2">
              <div class="col-5">
                <hr />
              </div>
              <div class="col-2 align-self-center">
                <div class="text-center">Or</div>
              </div>
              <div class="col-5">
                <hr />
              </div>
            </div>
            <div class="row m-0 mb-3 w-100">
              <button
                type="button"
                id="btn-google"
                class="btn btn-secondary w-100"
                style="max-width: 100% !important"
              >
                <div class="d-flex justify-content-center">
                  <img
                    class="me-2"
                    src="https://wealthyhood.com/svg/google.svg"
                    style="width: 24px; height: 24px"
                  />
                  Continue with Google
                </div>
              </button>
            </div>
            <div class="row m-0 w-100">
              <button
                type="button"
                id="btn-apple"
                class="btn btn-secondary w-100"
                style="max-width: 100% !important"
              >
                <div class="d-flex justify-content-center">
                  <img
                    class="me-2 align-self-center"
                    src="https://wealthyhood.com/svg/apple.svg"
                    style="width: 30px; height: 30px"
                  />
                  <div class="align-self-center">Continue with Apple</div>
                </div>
              </button>
            </div>
            <div class="row m-0 w-100">
              <div id="login-error-message" class="text-danger text-center pt-3"></div>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center w-100 text-start text-light">
              <p class="text-center wh-text mb-1 mt-2" style="max-width: 300px; min-width: 200px">
                By continuing, you agree to our
                <a href="https://wealthyhood.com/eu/privacy-policy/" target="_blank">Privacy Policy</a>. Capital at
                risk.
              </p>
            </div>
            <div class="d-none" id="login-cover-spin"></div>
          </div>
        </div>
        <div
          id="verification-box"
          class="
            d-flex
            flex-column
            justify-content-center
            align-self-center
            fixed-top
            position-relative
            m-0
            d-none
          "
        >
          <div
            id="verification-card"
            class="card card-body p-md-5 p-4 align-self-center w-100"
            style="max-width: 430px; min-width: 300px; border-radius: 32px"
          >
            <div class="row mb-md-4 mb-2">
              <div class="col p-0">
                <button
                  type="button"
                  id="btn-back"
                  class="btn btn-clean cursor-pointer position-relative z-max-level"
                >
                  <i class="fas fa-arrow-left icon-primary"></i>
                </button>
              </div>
            </div>
            <div class="row mb-md-4 mb-4">
              <h3 class="fw-bolder">Verify your email</h3>
            </div>
            <div class="row mb-4">
              <p class="wh-text m-0">We’ve sent a 6-digit verification code to</p>
              <p class="wh-text m-0"><span id="target-email" class="text-primary text-truncate"></span></p>
              <p class="wh-text m-0">Enter the code below.</p>
            </div>
            <div class="row m-0 w-100 mb-md-4 mb-3">
              <div class="form-group p-0">
                <input
                  id="verification-code"
                  type="password"
                  class="verification-input position-relative z-max-level"
                  name="verification-code"
                  placeholder="Your verification code"
                />
              </div>
            </div>
            <div class="row m-0 w-100 mb-md-4 mb-3">
              <button
                type="button"
                id="btn-verify"
                class="btn btn-primary w-100"
                style="max-width: 100% !important"
              >
                Verify email
              </button>
            </div>
            <div class="row m-0 w-100">
              <button
                type="button"
                id="btn-resend"
                class="btn btn-clean text-primary w-100"
                style="max-width: 100% !important"
              >
                Resend code
              </button>
            </div>
            <div class="row m-0 w-100">
              <div id="verification-error-message" class="text-danger text-center"></div>
            </div>
            <div class="d-none" id="verification-cover-spin"></div>
          </div>
          <div
            class="
              d-flex
              justify-content-center
              align-self-center
              position-absolute
              top-0
              start-50
              translate-middle
            "
            style="padding-bottom: 170px"
          >
            <img
              class="top-image"
              alt=""
              src="https://wealthyhood.com/img/wh-email.png"
              style="width: 300px; height: 300px"
            />
          </div>
        </div>
      </div>
      <div
        class="
          d-flex
          justify-content-center
          align-self-center
          position-absolute
          top-50
          start-0
          translate-middle
          d-none d-xxl-block
        "
        style="padding-left: 700px"
      >
        <img
          class="top-image"
          alt=""
          src="https://wealthyhood.com/img/astronaut.png"
          style="width: 230px; height: 230px"
        />
      </div>
    </div>

    <script src="https://cdn.auth0.com/js/auth0/9.18/auth0.min.js"></script>
    <script src="https://cdn.auth0.com/js/polyfills/1.0/object-assign.min.js"></script>
    <script>
      window.addEventListener("load", function () {
        const config = JSON.parse(decodeURIComponent(escape(window.atob("@@config@@"))));

        const leeway = config.internalOptions.leeway;
        if (leeway) {
          const convertedLeeway = parseInt(leeway);

          if (!isNaN(convertedLeeway)) {
            config.internalOptions.leeway = convertedLeeway;
          }
        }

        const params = Object.assign(
          {
            overrides: {
              __tenant: config.auth0Tenant,
              __token_issuer: config.authorizationServer.issuer
            },
            domain: config.auth0Domain,
            clientID: config.clientID,
            redirectUri: config.callbackURL,
            responseType: "token id_token"
          },
          config.internalOptions
        );

        const webAuth = new auth0.WebAuth(params);

        function showLoginLoader() {
          document.getElementById("login-cover-spin").classList.remove("d-none");
          document.getElementById("login-card").classList.add("loading-mask");
        }

        function hideLoginLoader() {
          document.getElementById("login-cover-spin").classList.add("d-none");
          document.getElementById("login-card").classList.remove("loading-mask");
        }

        function showVerificationLoader() {
          document.getElementById("verification-cover-spin").classList.remove("d-none");
          document.getElementById("verification-card").classList.add("loading-mask");
        }

        function hideVerificationLoader() {
          document.getElementById("verification-cover-spin").classList.add("d-none");
          document.getElementById("verification-card").classList.remove("loading-mask");
        }

        function login(e) {
          e.preventDefault();
          hideLoginError();
          showLoginLoader();

          const button = this;
          const email = document.getElementById("email").value;
          document.getElementById("target-email").innerHTML = email;
          button.disabled = true;

          webAuth.passwordlessStart(
            {
              connection: "email",
              send: "code",
              email: email
            },
            function (err, res) {
              if (err) {
                showLoginError(err, "Provided email is wrong!");
              }

              hideLoginLoader();
              button.disabled = false;

              if (res) {
                showVerificationBox();
              }
            }
          );
        }

        function resendCode(e) {
          e.preventDefault();
          hideVerificationError();
          showVerificationLoader();

          const button = this;
          const email = document.getElementById("email").value;
          document.getElementById("target-email").innerHTML = email;
          button.disabled = true;

          webAuth.passwordlessStart(
            {
              connection: "email",
              send: "code",
              email: email
            },
            function (err, res) {
              if (err) {
                showVerificationError(err);
              }

              hideVerificationLoader();
              button.disabled = false;
            }
          );
        }

        function showVerificationBox() {
          // hide login box
          document.getElementById("login-box").classList.add("d-none");

          // clear verification box 'state'
          document.getElementById("verification-code").value = "";
          hideVerificationError();
          // show verification box
          const verificationBox = document.getElementById("verification-box");
          verificationBox.classList.remove("d-none");
        }

        function passwordLessLogin(e) {
          e.preventDefault();
          hideVerificationError();
          showVerificationLoader();

          const button = this;
          const email = document.getElementById("email").value;
          const verificationCode = document.getElementById("verification-code").value;
          button.disabled = true;

          webAuth.passwordlessLogin(
            {
              connection: "email",
              verificationCode,
              email: email,
              onRedirecting: (done) => {
                console.log("Redirecting passwordless...");
                done();
              }
            },
            (err, res) => {
              if (err) {
                showVerificationError(err);
              }

              hideVerificationLoader();
              button.disabled = false;
            }
          );
        }

        function loginWithGoogle() {
          webAuth.authorize(
            {
              connection: "google-oauth2"
            },
            function (err) {
              if (err) {
                showLoginError(err, "Oops something went wrong!");
              }
            }
          );
        }

        function loginWithApple() {
          webAuth.authorize(
            {
              connection: "apple"
            },
            function (err) {
              if (err) {
                showLoginError(err, "Oops something went wrong!");
              }
            }
          );
        }

        function hideLoginError() {
          document.getElementById("login-error-message").style.display = "none";
        }

        function showLoginError(err, message) {
          const errorMessage = document.getElementById("login-error-message");
          errorMessage.innerHTML = message;
          errorMessage.style.display = "block";
        }

        function hideVerificationError() {
          document.getElementById("verification-error-message").style.display = "none";
        }

        function showVerificationError(err) {
          const errorMessage = document.getElementById("verification-error-message");
          errorMessage.innerHTML = "Verification code is wrong!";
          errorMessage.style.display = "block";
        }

        function goBack(e) {
          e.preventDefault();

          document.getElementById("verification-box").classList.add("d-none");
          const loginBox = document.getElementById("login-box");
          loginBox.classList.remove("d-none");

          hideLoginLoader();
        }

        document.getElementById("btn-back").addEventListener("click", goBack);
        document.getElementById("email").addEventListener("keypress", function (event) {
          if (event.key === "Enter") {
            login(event);
          }
        });
        document.getElementById("btn-login").addEventListener("click", login);
        document.getElementById("btn-google").addEventListener("click", loginWithGoogle);
        document.getElementById("btn-apple").addEventListener("click", loginWithApple);

        document.getElementById("btn-resend").addEventListener("click", resendCode);
        document.getElementById("verification-code").addEventListener("keypress", function (event) {
          if (event.key === "Enter") {
            passwordLessLogin(event);
          }
        });
        document.getElementById("btn-verify").addEventListener("click", passwordLessLogin);
      });
    </script>
  </body>
</html>
