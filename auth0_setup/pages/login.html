<!--This file is used in Auth0 as the custom Lock (passwordless) login page.
For any real changes to take place, any changes to this file must also be
applied to Auth0 -> Branding -> Universal Login -> Login -> Lock (passwordless) -->

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Log in | Investment Platform</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<body>
<style>
    body {
        background-color: #14233c !important;
    }

    .auth0-lock-social-button {
        margin-top: 5px !important;
        margin-bottom: 5px !important;
        border-radius: 5px 5px 5px 5px !important;
        display: inline-block !important;
        height: 50px !important;
    }

    .auth0-lock-social-button-text {
        line-height: 50px !important;
        padding-left: 54px !important;
    }

    .auth0-lock-social-button-icon {
        margin-left: 6px !important;
        height: 100% !important;
    }

    .auth0-lock-submit {
        margin-top: 20px !important;
        background-color: #0059d6 !important;
    }

    .auth0-lock-widget {
        width: 400px !important;
        box-shadow: none !important;
    }

    .auth0-lock-widget-container {
        position: relative !important;
        width: 100% !important;
        height: auto !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-form {
        padding-top: 14px !important;
        padding-bottom: 16px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-family: ulp-font, -apple-system, BlinkMacSystemFont, Roboto, Helvetica, sans-serif;
        font-feature-settings: normal !important;
        font-kerning: auto !important;
        font-language-override: normal !important;
        font-optical-sizing: auto !important;
        font-size: 14px !important;
        font-size-adjust: none !important;
        font-stretch: 100% !important;
        font-style: normal !important;
        font-variant-alternates: normal !important;
        font-variant-caps: normal !important;
        font-variant-east-asian: normal !important;
        font-variant-ligatures: normal !important;
        font-variant-numeric: normal !important;
        font-variant-position: normal !important;
        font-variation-settings: normal !important;
        font-weight: 400 !important;
        -moz-osx-font-smoothing: grayscale
    }

    .auth0-lock.auth0-lock .auth0-lock-form p {
        line-height: 21px !important;
        font-size: 14px !important;
        color: #2D333A !important;
    }

    .auth0-lock-overlay {
        background: #14233c !important;
    }

    .auth0-lock-back-button {
        left: 0px !important;
    }

    .auth0-lock-content-wrapper {
        padding-top: 10px !important;
        flex-grow: unset !important;
        flex-shrink: unset !important;
    }

    .auth0-lock-cred-pane-internal-wrapper {
        padding-left: 40px !important;
        padding-right: 40px !important;
        padding-bottom: 40px !important;
        height: unset !important;
    }

    .auth0-lock-name {
        font-family: ulp-font, -apple-system, BlinkMacSystemFont, Roboto, Helvetica, sans-serif !important;
        font-feature-settings: normal !important;
        font-kerning: auto !important;
        font-language-override: normal !important;
        font-optical-sizing: auto !important;
        font-size: 24px !important;
        font-size-adjust: none !important;
        font-stretch: 100% !important;
        font-style: normal !important;
        font-variant-alternates: normal !important;
        font-variant-caps: normal !important;
        font-variant-east-asian: normal !important;
        font-variant-ligatures: normal !important;
        font-variant-numeric: normal !important;
        font-variant-position: normal !important;
        font-variation-settings: normal !important;
        font-weight: 400 !important;
        -moz-osx-font-smoothing: grayscale
    }

    .auth0-lock.auth0-lock .auth0-lock-submit .auth0-label-submit {
        height: auto !important;
        line-height: normal !important;
        text-transform: none !important;
        font-family: ulp-font, -apple-system, BlinkMacSystemFont, Roboto, Helvetica, sans-serif !important;
        font-feature-settings: normal !important;
        font-kerning: auto !important;
        font-language-override: normal !important;
        font-optical-sizing: auto !important;
        font-size: 16px !important;
        font-size-adjust: none !important;
        font-stretch: 100% !important;
        font-style: normal !important;
        font-variant-alternates: normal !important;
        font-variant-caps: normal !important;
        font-variant-east-asian: normal !important;
        font-variant-ligatures: normal !important;
        font-variant-numeric: normal !important;
        font-variant-position: normal !important;
        font-variation-settings: normal !important;
        font-weight: 400 !important;
        -moz-osx-font-smoothing: grayscale !important;
        letter-spacing: 0px !important;
        display: unset !important;
        transition: unset !important;
    }

    .auth0-lock-submit:hover {
        background: #003073 !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-submit {
        height: 52px !important;
        border-radius: 5px 5px 5px 5px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-submit .auth0-loading-container {
        height: 100% !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-submit .auth0-loading-container .auth0-loading {
        top: 8px !important;
    }

    .auth0-lock-blur-support {
        display: none !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-header-logo {
        height: 52px !important;
        margin-bottom: 30px !important;
        margin-top: 9px !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-header {
        padding-top: 40px !important;
        padding-right: 10px !important;
        padding-bottom: 0px !important;
        padding-left: 10px !important;
        height: auto !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-input-wrap {
        background: white !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-input-wrap .auth0-lock-input {
        height: 52px !important;
        padding-left: 0px !important;
        font-size: 16px !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-terms {
        background: #fff !important;
    }

    .auth0-lock-icon-box {
        display: none !important;
    }

    .auth0-lock.auth0-lock .auth0-lock-input-wrap.auth0-lock-input-wrap-with-icon {
        padding-left: 16px !important;
    }

    .icon-text {
        display: none !important;
    }

    @media (max-width: 480px) {
        .auth0-lock-widget {
            display: flex !important;
            width: 100% !important;
            align-content: center !important;
            justify-content: center !important;
        }

        .auth0-lock-center {
            display: flex;
            justify-content: center;
            align-content: center;
        }

        .auth0-lock-quiet {
            justify-content: center;
        }
    }
</style>
<!--[if IE 8]>
<script src="//cdnjs.cloudflare.com/ajax/libs/ie8/0.2.5/ie8.js"></script>
<![endif]-->

<!--[if lte IE 9]>
<script src="https://cdn.auth0.com/js/base64.js"></script>
<script src="https://cdn.auth0.com/js/es5-shim.min.js"></script>
<![endif]-->

<script src="https://cdn.auth0.com/js/lock/11.32/lock.min.js"></script>
<script>
  // Decode utf8 characters properly
  const config = JSON.parse(decodeURIComponent(escape(window.atob("@@config@@"))));
  config.extraParams = config.extraParams || {};
  const connection = config.connection;
  const prompt = config.prompt;
  const loginHint = config.extraParams.login_hint;

  const lock = new Auth0LockPasswordless(config.clientID, config.auth0Domain, {
    auth: {
      redirectUrl: config.callbackURL,
      responseType: (config.internalOptions || {}).response_type ||
        (config.callbackOnLocationHash ? "token" : "code"),
      params: config.internalOptions
    },
    prefill: {
      email: loginHint
    },
    configurationBaseUrl: config.clientConfigurationBaseUrl,
    overrides: {
      __tenant: config.auth0Tenant,
      __token_issuer: config.authorizationServer.issuer
    },
    assetsUrl: config.assetsUrl,
    allowedConnections: connection ? [connection] : null,
    rememberLastLogin: !prompt,
    languageBaseUrl: config.languageBaseUrl,
    showTerms: false,
    languageDictionary: {
      // Override strings from https://github.com/auth0/lock/blob/master/src/i18n/en.js
      title: "Welcome to Wealthyhood!",
      submitLabel: "Continue",
      passwordlessEmailCodeInstructions: "A verification code has been sent to %s.",
      passwordlessEmailInstructions: "Enter your email to<br/>create an account or log in.",
      codeInputPlaceholder: "enter your code here",
      emailInputPlaceholder: "Email address",
      signUpWithLabel: "Sign in with %s"
    },
    theme: {
      logo: config.icon,
      primaryColor: config.colors.primary
    },
    avatar: null,
    closable: false
  });

  lock.show();
</script>
</body>
</html>
