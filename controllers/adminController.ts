import AdminFailedAccountsPage, {
  AdminFailedAccountsPagePropsType,
  DurationArray as AdminFailedAccountsPageDurationArray,
  DurationType as AdminFailedAccountsPageDurationType
} from "../react-views/pages/adminFailedAccountsPage";
import { captureException } from "@sentry/node";
import { CustomRequest } from "custom";
import { Request, Response } from "express";
import validator from "validator";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { AddressDTOInterface } from "../models/Address";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import { PortfolioDocument, PortfolioModeEnum } from "../models/Portfolio";
import { RewardDocument } from "../models/Reward";
import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  DepositCashTransactionDocument,
  RebalanceTransactionDocument,
  RevertRewardTransactionDocument,
  TransactionCategoryType,
  TransactionDocument,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import { UserDocument } from "../models/User";
import AdminEditPartyPage, { AdminEditPartyPropsType } from "../react-views/pages/adminEditPartyPage";
import AdminRewardDetailsPage, { AdminRewardDetailsPropsType } from "../react-views/pages/adminRewardDetailsPage";
import AdminRewardsPage, { AdminRewardsPropsType } from "../react-views/pages/adminRewardsPage";
import AdminRewardSubmissionPage, {
  AdminRewardSubmissionPropsType
} from "../react-views/pages/adminRewardSubmissionPage";
import AdminBankAccountsPage, { AdminBankAccountsPropsType } from "../react-views/pages/adminBankAccountsPage";
import AdminUserListPage, { AdminUserListPropsType } from "../react-views/pages/adminUserListPage";
import AdminAssetTransactionsPage, {
  AssetTransactionsAdminPropsType
} from "../react-views/pages/adminAssetTransactionsPage";
import AdminDepositTransactionsPage, {
  AdminDepositTransactionsAdminPropsType
} from "../react-views/pages/adminDepositTransactionsPage";
import AdminWithdrawalsPage, {
  AdminWithdrawalTransactionsPropsType
} from "../react-views/pages/adminWithdrawalTransactionsPage";
import appApiService, { API_ROUTES } from "../services/appApiService";
import {
  OrderAnalyticsResponse,
  PaginatedTransactionsResponse,
  UnsubmittedOrderAnalyticsResponse
} from "apiResponse";
import { formatCurrency } from "../react-views/utils/currencyUtil";
import AdminOrdersBreakdownPage, {
  AdminOrdersBreakdownPropsType
} from "../react-views/pages/adminOrdersBreakdownPage";
import AdminDepositTransactionBreakdownPage, {
  AdminDepositTransactionBreakdownPropsType
} from "../react-views/pages/adminDepositTransactionBreakdownPage";
import AdminWithdrawalTransactionBreakdownPage, {
  AdminWithdrawalTransactionBreakdownPropsType
} from "../react-views/pages/adminWithdrawalTransactionBreakdownPage";
import AdminOrderAnalyticsPage, {
  AdminOrderAnalyticsPropsType
} from "../react-views/pages/adminOrderAnalyticsPage";
import AdminOrderAnalyticsBreakdownPage, {
  AdminOrderAnalyticsBreakdownPropsType
} from "../react-views/pages/adminOrderAnalyticBreakdownPage";
import AdminOrderManagementPage, {
  AdminOrderManagementPropsType
} from "../react-views/pages/adminOrderManagementPage";
import AdminGiftsPage, { AdminGiftsPropsType } from "../react-views/pages/adminGiftsPage";
import { GiftDocument } from "../models/Gift";
import AdminGiftDetailsPage, { AdminGiftDetailsPropsType } from "../react-views/pages/adminGiftDetailsPage";
import { getDateOfDaysAgo } from "../react-views/utils/dateUtil";
import { AmlCheckFlagType } from "../models/AmlCheck";
import { PartialRecord } from "../react-views/types/utils";

const { ASSET_CONFIG } = investmentUniverseConfig;

/**
 * PUBLIC CONTROLLER METHODS
 */

export const createBonusDeposit = async (req: CustomRequest, res: Response): Promise<Response> => {
  const { bonusDepositAmount, targetUserId } = req.body;

  const createBonusDepositData = {
    bonusDepositAmount,
    targetUserId
  };

  await appApiService.postM2Madmin(
    API_ROUTES.portfolios.addBonusDeposit(),
    createBonusDepositData,
    {},
    req.user.id
  );

  return res.sendStatus(204);
};
export const createRewards = async (req: CustomRequest, res: Response): Promise<Response> => {
  const {
    referrerEmail,
    referralEmail,
    referrerETF,
    referralETF,
    referrerConsiderationAmount,
    referralConsiderationAmount
  } = req.body;

  const referrerRewardData = {
    referrerEmail,
    referralEmail,
    targetUserEmail: referrerEmail,
    asset: referrerETF,
    considerationAmount: referrerConsiderationAmount
  };
  const referralRewardData = {
    referrerEmail,
    referralEmail,
    targetUserEmail: referralEmail,
    asset: referralETF,
    considerationAmount: referralConsiderationAmount
  };
  await Promise.all([
    appApiService.postM2Madmin(API_ROUTES.rewards.all(), referrerRewardData),
    appApiService.postM2Madmin(API_ROUTES.rewards.all(), referralRewardData)
  ]);

  return res.sendStatus(204);
};

export const createSingleReward = async (req: CustomRequest, res: Response): Promise<Response> => {
  const { targetUserEmail, asset, considerationAmount } = req.body;

  const rewardData = {
    targetUserEmail: targetUserEmail,
    asset: asset,
    considerationAmount: considerationAmount
  };

  await Promise.all([appApiService.postM2Madmin(API_ROUTES.rewards.all(), rewardData)]);

  return res.sendStatus(204);
};

export const getRewardView = async (req: Request, res: Response): Promise<void> => {
  const rewardId = req.params.id;

  const reward: RewardDocument = await appApiService.getM2Madmin(API_ROUTES.rewards.id(rewardId));

  const props: AdminRewardDetailsPropsType = { reward };

  return res.render("renderReactLayout", {
    title: "Reward Details",
    activePage: "reward",
    reactComp: AdminRewardDetailsPage,
    props: JSON.stringify(props),
    scriptName: "adminRewardDetails"
  });
};

export const getRewardsView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 20;
  const currentPage = Number(req.query.page || 1);

  const apiRes = await appApiService.getM2Madmin(API_ROUTES.rewards.all(), {
    pageSize: PAGE_SIZE,
    page: currentPage,
    targetUser: req.query.targetUser
  });

  const props: AdminRewardsPropsType = {
    rewards: apiRes.rewards,
    rewardsSize: apiRes.pagination.total,
    pageSize: apiRes.pagination.pageSize,
    currentPage: apiRes.pagination.page
  };

  return res.render("renderReactLayout", {
    title: "Rewards",
    activePage: "rewards",
    reactComp: AdminRewardsPage,
    props: JSON.stringify(props),
    scriptName: "adminRewards"
  });
};

export const getBankAccountsView = async (req: Request, res: Response): Promise<void> => {
  const pendingBankAccounts = await appApiService.getM2Madmin(API_ROUTES.bankAccounts.pending());

  const props: AdminBankAccountsPropsType = {
    pendingBankAccounts: pendingBankAccounts
  };

  return res.render("renderReactLayout", {
    title: "Bank Accounts",
    activePage: "bankAccounts",
    reactComp: AdminBankAccountsPage,
    props: JSON.stringify(props),
    scriptName: "adminBankAccounts"
  });
};

export const getGiftsView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 20;
  const currentPage = Number(req.query.page || 1);

  const apiRes = await appApiService.getM2Madmin(API_ROUTES.gifts.all(), {
    pageSize: PAGE_SIZE,
    page: currentPage,
    targetUserEmail: req.query.targetUserEmail
  });

  const props: AdminGiftsPropsType = {
    gifts: apiRes.gifts,
    giftsSize: apiRes.pagination.total,
    pageSize: apiRes.pagination.pageSize,
    currentPage: apiRes.pagination.page
  };

  return res.render("renderReactLayout", {
    title: "Gifts",
    activePage: "gifts",
    reactComp: AdminGiftsPage,
    props: JSON.stringify(props),
    scriptName: "adminGifts"
  });
};

export const getGiftView = async (req: Request, res: Response): Promise<void> => {
  const giftId = req.params.id;

  const gift: GiftDocument = await appApiService.getM2Madmin(API_ROUTES.gifts.id(giftId));

  const props: AdminGiftDetailsPropsType = { gift };

  return res.render("renderReactLayout", {
    title: "Gift Details",
    activePage: "gift",
    reactComp: AdminGiftDetailsPage,
    props: JSON.stringify(props),
    scriptName: "adminGiftDetails"
  });
};

export const getRewardSubmissionView = async (req: Request, res: Response): Promise<void> => {
  const props: AdminRewardSubmissionPropsType = {};

  return res.render("renderReactLayout", {
    title: "Reward Submission",
    activePage: "rewardSubmission",
    reactComp: AdminRewardSubmissionPage,
    props: JSON.stringify(props),
    scriptName: "adminRewardSubmission"
  });
};

export const getUserView = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.params.userId;
  if (!userId || !validator.isHexadecimal(userId)) {
    req.flash("error", "Invalid user id");
    return res.redirect("/admin/users");
  }

  const user: UserDocument = (await appApiService.getM2Madmin(API_ROUTES.users.id(userId)))?.[0];

  if (user) {
    const [portfolios, userDataRequests, rewards] = await Promise.all([
      appApiService.getM2M(API_ROUTES.portfolios.all(), user.id, {
        mode: PortfolioModeEnum.REAL,
        sort: "createdAt"
      }),
      appApiService.getM2Madmin(API_ROUTES.userDataRequests.all(), {
        owner: user.id
      }),
      appApiService.getM2Madmin(API_ROUTES.rewards.all(), {
        targetUser: user.id
      })
    ]);

    const realPortfolio = portfolios[0];

    const transactions = realPortfolio
      ? await appApiService.getM2Madmin(API_ROUTES.transactions.all(), {
          portfolio: realPortfolio.id,
          sort: "-createdAt"
        })
      : [];

    // We may be in a state where user may have a WK portfolioId for their automatically created real portfolio
    // after passing the kyc process, but without having converted their virtual to real portfolio yet.
    // Only a cash deposit may have been made at that point.
    const props: AdminEditPartyPropsType = {
      viewedUser: user,
      transactions,
      userDataRequests,
      rewards,
      wealthkernelPortfolioId: realPortfolio.providers?.wealthkernel?.id
    };

    return res.render("renderReactLayout", {
      title: `User: ${user.email}`,
      reactComp: AdminEditPartyPage,
      props: JSON.stringify(props),
      scriptName: "adminEditParty"
    });
  } else {
    req.flash("error", "Invalid user id");
    return res.redirect("/admin/users");
  }
};

export const getUsersView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 50;
  const currentPage = Number(req.query.page || 1);

  const apiRes = await appApiService.getM2Madmin(API_ROUTES.users.all(), {
    pageSize: PAGE_SIZE,
    page: currentPage,
    populatePortfolios: true,
    email: req.query.email,
    sort: "-lastLogin"
  });

  const props: AdminUserListPropsType = {
    users: apiRes.users,
    usersSize: apiRes.pagination.total,
    pageSize: PAGE_SIZE,
    currentPage
  };

  return res.render("renderReactLayout", {
    title: `Users: ${apiRes.pagination.total}`,
    activePage: "userlist",
    reactComp: AdminUserListPage,
    props: JSON.stringify(props),
    scriptName: "adminUserList"
  });
};

export const getAssetTransactionsView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 50;
  const currentPage = Number(req.query.page ?? 1);

  const apiRes: PaginatedTransactionsResponse<AssetTransactionDocument> = await appApiService.getM2Madmin(
    API_ROUTES.transactions.assets.all(),
    {
      page: currentPage,
      pageSize: PAGE_SIZE,
      sort: "-createdAt",
      populateOwner: true
    }
  );

  const props: AssetTransactionsAdminPropsType = {
    transactions: apiRes.transactions,
    transactionsSize: apiRes.pagination.total,
    pageSize: apiRes.pagination.pageSize,
    currentPage: apiRes.pagination.page
  };

  res.render("renderReactLayout", {
    title: "Asset Transactions",
    activePage: "assetTransactionsList",
    reactComp: AdminAssetTransactionsPage,
    props: JSON.stringify(props),
    scriptName: "assetTransactionsAdmin"
  });
};

export const getDepositTransactionsView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 50;
  const currentPage = Number(req.query.page || 1);

  const apiRes: PaginatedTransactionsResponse<DepositCashTransactionDocument> = await appApiService.getM2Madmin(
    API_ROUTES.transactions.deposits.all(),
    {
      page: currentPage,
      pageSize: PAGE_SIZE,
      sort: "-createdAt",
      populateOwner: true
    }
  );

  const props: AdminDepositTransactionsAdminPropsType = {
    transactions: apiRes.transactions,
    transactionsSize: apiRes.pagination.total,
    pageSize: apiRes.pagination.pageSize,
    currentPage: apiRes.pagination.page
  };

  res.render("renderReactLayout", {
    title: "Payments",
    activePage: "depositTransactionsList",
    reactComp: AdminDepositTransactionsPage,
    props: JSON.stringify(props),
    scriptName: "depositTransactionsAdmin"
  });
};

export const getWithdrawalTransactionsView = async (req: Request, res: Response): Promise<void> => {
  const PAGE_SIZE = 50;
  const currentPage = Number(req.query.page || 1);

  const apiRes: PaginatedTransactionsResponse<WithdrawalCashTransactionDocument> = await appApiService.getM2Madmin(
    API_ROUTES.transactions.withdrawals.all(),
    {
      page: currentPage,
      pageSize: PAGE_SIZE,
      sort: "-createdAt",
      populateOwner: true
    }
  );

  const props: AdminWithdrawalTransactionsPropsType = {
    transactions: apiRes.transactions,
    transactionsSize: apiRes.pagination.total,
    pageSize: apiRes.pagination.pageSize,
    currentPage: apiRes.pagination.page
  };

  res.render("renderReactLayout", {
    title: "Withdrawals",
    activePage: "withdrawalTransactionsList",
    reactComp: AdminWithdrawalsPage,
    props: JSON.stringify(props),
    scriptName: "adminWithdrawals"
  });
};

/**
 * @description Handles the admin request to sync transaction info in db with latest state from WK.
 */
export const syncTransaction = async (req: Request, res: Response): Promise<void> => {
  const transactionId = req.params.transactionId;
  if (!transactionId || !validator.isMongoId(transactionId)) {
    res.status(500).send("Transaction id is not a mongo id");
    return;
  }

  try {
    await appApiService.postM2Madmin(API_ROUTES.transactions.sync(transactionId));
    res.sendStatus(201);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const updateUser = async (req: Request, res: Response): Promise<void> => {
  const userId = req.params.userId;
  if (!userId || !validator.isMongoId(userId)) {
    req.flash("error", "Invalid user id");
    return res.redirect("/admin/users");
  }

  const {
    firstName,
    lastName,
    dateOfBirth,
    nationality,
    taxResidencyCountryCode,
    taxResidencyProofType,
    taxResidencyProofValue,
    addressLine1,
    addressLine2,
    addressLine3,
    addressCity,
    addressCountryCode,
    addressPostCode,
    referredByEmail,
    isPotentiallyDuplicateAccount,
    amlScreening
  } = req.body;

  const addressData: AddressDTOInterface = {
    owner: userId,
    line1: addressLine1,
    line2: addressLine2,
    line3: addressLine3,
    city: addressCity,
    countryCode: addressCountryCode,
    postalCode: addressPostCode
  };

  // We only update address if it contains all the required fields
  if (addressData.line1 && addressData.city && addressData.countryCode && addressData.postalCode) {
    const address = await appApiService.postM2Madmin(API_ROUTES.addresses.all(), addressData, { owner: userId });
    if (address) {
      req.flash("success", "Address was updated successfully");
    } else {
      req.flash("error", "An error occurred when updating the address");
    }
  }

  // We only include the tax residency in the update body if it contains all the required fields
  let taxResidency;
  if (taxResidencyCountryCode && taxResidencyProofType && taxResidencyProofValue) {
    taxResidency = {
      countryCode: taxResidencyCountryCode,
      proofType: taxResidencyProofType,
      value: taxResidencyProofValue
    };
  }
  const userData = {
    firstName: firstName,
    lastName: lastName,
    dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
    nationalities: nationality ? [nationality] : undefined,
    taxResidency: taxResidency,
    referredByEmail: referredByEmail,
    isPotentiallyDuplicateAccount: isPotentiallyDuplicateAccount,
    amlScreening
  };

  // If any of the fields of the user are defined, we make the update request.
  if (Object.values(userData).some((value) => value)) {
    const user = await appApiService.postM2Madmin(API_ROUTES.users.id(userId), userData);
    if (user) {
      req.flash("success", "User was updated successfully");
    } else {
      req.flash("error", "An error occurred when updating the user");
    }
  }

  res.redirect("back");
};

export const deleteUser = async (req: Request, res: Response): Promise<void> => {
  const userId = req.params.userId;
  if (!userId || !validator.isMongoId(userId)) {
    req.flash("error", "Invalid user id");
    return res.redirect("/admin/users");
  }

  const { requestType } = req.body;
  await appApiService.postM2Madmin(API_ROUTES.userDataRequests.all(), { requestType, owner: userId });

  res.redirect("back");
};

const _getAssetTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: AssetTransactionDocument
): Promise<void> => {
  const assetTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.assets.id(transaction._id), {
    populateOrders: true
  });

  const orders = assetTransaction.orders;
  const amount = assetTransaction.consideration.amount || 0;

  // Locale is hardcoded to 'en' as this is an admin view.
  let title = formatCurrency(amount / 100, assetTransaction.consideration.currency, "en");
  if (assetTransaction.portfolioTransactionCategory === "buy") {
    title += " Purchase";
  } else if (assetTransaction.portfolioTransactionCategory === "sell") {
    title += " Sale";
  } else if (assetTransaction.portfolioTransactionCategory === "update" && assetTransaction.orders.length == 1) {
    const { isin, side } = assetTransaction.orders[0];
    title = Object.values(ASSET_CONFIG).find((config) => config.isin == isin)?.simpleName + " - " + side;
  } else if (assetTransaction.portfolioTransactionCategory === "update") {
    title = "Portfolio Update";
  }

  const props: AdminOrdersBreakdownPropsType = {
    enableSync: true,
    orders: orders,
    transactionId: transaction._id.toString(),
    ownerId: transaction.owner.toString()
  };

  res.render("renderReactLayout", {
    title,
    subtitle: "Portfolio Transaction",
    reactComp: AdminOrdersBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminAssetTransactionBreakdown"
  });
};

const _getRebalanceTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: RebalanceTransactionDocument
): Promise<void> => {
  const rebalanceTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.id(transaction._id), {
    populateOrders: true
  });

  const orders = rebalanceTransaction.orders ?? [];

  const props: AdminOrdersBreakdownPropsType = {
    enableSync: true,
    orders: orders,
    transactionId: transaction._id.toString(),
    ownerId: transaction.owner.toString()
  };

  res.render("renderReactLayout", {
    title: "Portfolio Rebalance",
    subtitle: "Portfolio Transaction",
    reactComp: AdminOrdersBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminAssetTransactionBreakdown"
  });
};

const _getChargeTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: ChargeTransactionDocument
): Promise<void> => {
  if (!["combined", "holdings"].includes(transaction.chargeMethod)) {
    return res.redirect("/");
  }

  const chargeTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.id(transaction._id), {
    populateOrders: true
  });

  const orders = chargeTransaction.orders ?? [];

  const props: AdminOrdersBreakdownPropsType = {
    enableSync: true,
    orders: orders,
    transactionId: transaction._id.toString(),
    ownerId: transaction.owner.toString()
  };

  res.render("renderReactLayout", {
    title: `Portfolio Charge ${transaction.chargeMethod}`,
    subtitle: "Portfolio Transaction",
    reactComp: AdminOrdersBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminAssetTransactionBreakdown"
  });
};

const _getRevertRewardTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: RevertRewardTransactionDocument
): Promise<void> => {
  const revertRewardTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.id(transaction._id), {
    populateOrders: true
  });

  const orders = revertRewardTransaction.orders ?? [];

  const props: AdminOrdersBreakdownPropsType = {
    enableSync: true,
    orders: orders,
    transactionId: transaction._id.toString(),
    ownerId: transaction.owner.toString()
  };

  res.render("renderReactLayout", {
    title: "Portfolio Revert Reward",
    subtitle: "Portfolio Transaction",
    reactComp: AdminOrdersBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminAssetTransactionBreakdown"
  });
};

const _getDepositTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: DepositCashTransactionDocument
): Promise<void> => {
  const depositTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.deposits.id(transaction._id));
  const props: AdminDepositTransactionBreakdownPropsType = {
    depositAmount: transaction.consideration.amount / 100,
    currency: transaction.consideration.currency,
    transactionId: transaction.id,
    ownerId: transaction.owner.toString(),
    providers: depositTransaction.providers,
    transferWithIntermediary: transaction.transferWithIntermediary
  };

  res.render("renderReactLayout", {
    title: "Payment Details",
    reactComp: AdminDepositTransactionBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminDepositTransactionBreakdown"
  });
};

const _getWithdrawalTransactionView = async (
  req: CustomRequest,
  res: Response,
  transaction: WithdrawalCashTransactionDocument
): Promise<void> => {
  const withdrawalTransaction = await appApiService.getM2Madmin(API_ROUTES.transactions.id(transaction._id));

  const props: AdminWithdrawalTransactionBreakdownPropsType = {
    withdrawalAmount: transaction.consideration.amount / 100,
    currency: transaction.consideration.currency,
    transactionId: transaction.id,
    ownerId: transaction.owner.toString(),
    providers: withdrawalTransaction.providers,
    transferWithIntermediary: transaction.transferWithIntermediary
  };

  res.render("renderReactLayout", {
    title: "Withdrawal Details",
    reactComp: AdminWithdrawalTransactionBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminWithdrawalTransactionBreakdown"
  });
};

export const getTransactionView = async (req: CustomRequest, res: Response): Promise<void> => {
  const TRANSACTION_HANDLERS: Record<
    TransactionCategoryType,
    (req: CustomRequest, res: Response, transaction: TransactionDocument) => void
  > = {
    AssetTransaction: _getAssetTransactionView,
    WithdrawalCashTransaction: _getWithdrawalTransactionView,
    DepositCashTransaction: _getDepositTransactionView,
    RebalanceTransaction: _getRebalanceTransactionView,
    ChargeTransaction: _getChargeTransactionView,
    RevertRewardTransaction: _getRevertRewardTransactionView,
    DividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    CashbackTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    WealthyhoodDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsTopupTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsWithdrawalTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    AssetDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    StockSplitTransaction: (req: CustomRequest, res: Response): void => res.redirect("/")
  };

  const transactionId = req.params.id;
  if (!validator.isMongoId(transactionId)) {
    return res.redirect("back");
  }

  const transaction: TransactionDocument = await appApiService.getM2Madmin(
    API_ROUTES.transactions.id(transactionId)
  );

  if (!transaction) {
    req.flash("error", "Transaction does not exist");
    return res.redirect("back");
  }

  TRANSACTION_HANDLERS[transaction.category](req, res, transaction);
};

export const updateReward = async (req: CustomRequest, res: Response): Promise<Response> => {
  const rewardId = req.params.id;
  const { depositId } = req.body;

  const rewardData = {
    depositId
  };

  await appApiService.postM2Madmin(API_ROUTES.rewards.id(rewardId), rewardData);

  return res.sendStatus(204);
};

export const pauseLine = async (req: CustomRequest, res: Response): Promise<Response> => {
  const id = req.params.id;
  const side = req.query.side;

  try {
    await appApiService.postM2Madmin(API_ROUTES.investmentProducts.pause(id), {}, { side });
    return res.sendStatus(204);
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const resumeLine = async (req: CustomRequest, res: Response): Promise<Response> => {
  const id = req.params.id;
  const side = req.query.side;

  try {
    await appApiService.postM2Madmin(API_ROUTES.investmentProducts.resume(id), {}, { side });
    return res.sendStatus(204);
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const getOrderManagementView = async (req: CustomRequest, res: Response): Promise<void> => {
  const investmentProducts = await appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id);
  const investmentProductsDict = Object.fromEntries(
    investmentProducts.map((investmentProduct: InvestmentProductDocument) => [
      investmentProduct.commonId,
      investmentProduct
    ])
  );

  const apiRes: UnsubmittedOrderAnalyticsResponse = await appApiService.getM2Madmin(
    API_ROUTES.orders.unsubmittedOrderAnalytics()
  );

  const props: AdminOrderManagementPropsType = {
    lines: apiRes.lines,
    investmentProductsDict
  };

  res.render("renderReactLayout", {
    title: "Order Management",
    activePage: "order-management",
    reactComp: AdminOrderManagementPage,
    props: JSON.stringify(props),
    scriptName: "adminOrderManagement"
  });
};

export const getOrderAnalyticsBreakdownViewForAsset = async (req: CustomRequest, res: Response): Promise<void> => {
  const submissionDay = req.query.submissionDay;
  const assetId = req.query.assetId;

  const orders = await appApiService.getM2Madmin(API_ROUTES.orders.all(), {
    submissionDay,
    assetId
  });

  const isin = ASSET_CONFIG[assetId as investmentUniverseConfig.AssetType].isin;

  const rewards: RewardDocument[] = await appApiService.getM2Madmin(API_ROUTES.rewards.all(), {
    orderSubmissionDay: submissionDay,
    assetId
  });

  const rewardsOrders = rewards.map(({ consideration, quantity, updatedAt, _id, order, status }) => ({
    _id,
    quantity,
    consideration,
    createdAt: updatedAt,
    providers: order?.providers,
    isin,
    side: "reward",
    status,
    rejectionReason: null,
    transaction: null
  }));

  // aggregated orders and sort them by createdAt descending
  const allOrders = [...orders, ...rewardsOrders].sort(
    (order1, order2) => new Date(order2.createdAt).getTime() - new Date(order1.createdAt).getTime()
  );
  const props: AdminOrdersBreakdownPropsType = {
    enableSync: false,
    orders: allOrders,
    columns: ["WK Order ID", "Status", "Reason", "Action", "Money", "Quantity", "Created At", "Submitted At"],
    transactionId: null
  };

  res.render("renderReactLayout", {
    title: `Orders - ${submissionDay}`,
    subtitle: `ISIN: ${isin}`,
    reactComp: AdminOrdersBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminOrdersBreakdown"
  });
};

export const getOrderAnalyticsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const currentPage = Number(req.query.page || 0);
  const apiRes: OrderAnalyticsResponse = await appApiService.getM2Madmin(API_ROUTES.orders.analytics(), {
    page: currentPage
  });

  const props: AdminOrderAnalyticsPropsType = {
    analytics: apiRes.analytics,
    currentPage
  };

  res.render("renderReactLayout", {
    title: "Order Analytics History",
    activePage: "analytics",
    reactComp: AdminOrderAnalyticsPage,
    props: JSON.stringify(props),
    scriptName: "adminOrderAnalytics"
  });
};

export const getOrderAnalyticsBreakdownView = async (req: CustomRequest, res: Response): Promise<void> => {
  const apiRes: OrderAnalyticsResponse = await appApiService.getM2Madmin(API_ROUTES.orders.analytics(), {
    ...req.query
  });

  const props: AdminOrderAnalyticsBreakdownPropsType = {
    date: apiRes.analytics[0].date,
    lines: apiRes.analytics[0].lines
  };

  res.render("renderReactLayout", {
    title: `Order Analytics Breakdown - ${req.query.date}`,
    activePage: "order-analytics-breakdown",
    reactComp: AdminOrderAnalyticsBreakdownPage,
    props: JSON.stringify(props),
    scriptName: "adminOrderAnalyticsBreakdown"
  });
};

export const getSuspendedBankAccounts = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2Madmin(API_ROUTES.bankAccounts.suspended());

    res.status(200).json(data);
  } catch (err) {
    res.status(500).json({
      message: "Could not fetch suspended bank accounts"
    });
  }
};

export const getPendingBankAccounts = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2Madmin(API_ROUTES.bankAccounts.pending());

    res.status(200).json(data);
  } catch (err) {
    res.status(500).json({
      message: "Could not fetch pending bank accounts"
    });
  }
};

export const activateBankAccount = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.postM2Madmin(
      API_ROUTES.bankAccounts.setWealthyhoodStatus(req.params.bankAccountId),
      { status: "Active" }
    );

    res.status(200).json(data);
  } catch (err) {
    res.status(500).json({
      message: "Could not activate bank account!"
    });
  }
};

export const getTaxStatement = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const start = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const end = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

    const { fileUri } = await appApiService.postM2Madmin(
      API_ROUTES.users.specifiedUserStatement(req.params.userId),
      {},
      {
        start,
        end
      }
    );

    res.status(200).json({ fileUri });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch account statement." });
  }
};

export const removeDuplicateFlag = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2Madmin(API_ROUTES.users.removeDuplicateFlag(req.params.id));

    res.sendStatus(204);
  } catch (err) {
    res.status(500).json({
      message: "Could not remove duplicate flag"
    });
  }
};

export const removeKycPassportFlag = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2Madmin(API_ROUTES.users.removeKycPassportFlag(req.params.id));

    res.sendStatus(204);
  } catch (err) {
    res.status(500).json({
      message: "Could not remove KYC passport flag"
    });
  }
};

export const overrideKycDecision = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2Madmin(API_ROUTES.users.overrideKycDecision(req.params.id));

    res.sendStatus(204);
  } catch (err) {
    res.status(500).json({
      message: "Could not override KYC decision"
    });
  }
};

export const getFailedUsersView = async (req: Request, res: Response): Promise<void> => {
  // Configs & default settings
  const DURATION_DAYS_CONFIG: Record<AdminFailedAccountsPageDurationType, number> = {
    max: Number.MAX_VALUE,
    "1y": 365,
    "6m": 180,
    "3m": 90,
    "1m": 30,
    "1w": 7,
    "1d": 1
  };
  const PAGE_SIZE = 50;
  const DEFAULT_DURATION: AdminFailedAccountsPageDurationType = "1w";

  // Query Params
  const currentPage = Number(req.query.page || 1);
  const duration: AdminFailedAccountsPageDurationType = AdminFailedAccountsPageDurationArray.includes(
    req.query.duration as AdminFailedAccountsPageDurationType
  )
    ? (req.query.duration as AdminFailedAccountsPageDurationType)
    : DEFAULT_DURATION;
  const kycFailedAfter = getDateOfDaysAgo(new Date(), DURATION_DAYS_CONFIG[duration]);

  // Fetch failed users
  const apiRes = await appApiService.getM2Madmin(API_ROUTES.users.all(), {
    pageSize: PAGE_SIZE,
    page: currentPage,
    populateKycOperation: true,
    populateAccounts: true,
    hasRequestedDeletion: false,
    email: req.query.email,
    kycStatus: "failed",
    kycFailedAfter,
    hasAcceptedTerms: true,
    hasSubmittedRequiredInfo: true,
    sort: "-kycFailedAfter"
  });

  const props: AdminFailedAccountsPagePropsType = {
    users: apiRes.users,
    usersSize: apiRes.pagination.total,
    pageSize: PAGE_SIZE,
    duration,
    currentPage
  };

  return res.render("renderReactLayout", {
    title: `Users: ${apiRes.pagination.total}`,
    activePage: "failedusers",
    reactComp: AdminFailedAccountsPage,
    props: JSON.stringify(props),
    scriptName: "adminFailedAccounts"
  });
};

enum AdminAmlCheckAction {
  WHITELIST = "WHITELIST"
}

export const updateAmlChecks = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data: PartialRecord<AmlCheckFlagType, AdminAmlCheckAction> = req.body;

    await appApiService.postM2Madmin(API_ROUTES.users.updateAmlChecks(req.params.id), data);

    res.sendStatus(204);
  } catch (err) {
    res.status(500).json({
      message: "Could not update aml checks"
    });
  }
};
