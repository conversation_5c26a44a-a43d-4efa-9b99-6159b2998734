import { Response } from "express";
import { captureException } from "@sentry/node";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { CustomRequest } from "custom";

const { ASSET_CONFIG } = investmentUniverseConfig;

export const getAllAssetNews = async (req: CustomRequest, res: Response): Promise<Response> => {
  const assetCommonId: investmentUniverseConfig.AssetType = req.query
    .assetCommonId as investmentUniverseConfig.AssetType;

  try {
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const getAllAssetNews = await appApiService.getM2M(API_ROUTES.assetNews.all(), req.user.id, {
        assetId: assetCommonId
      });
      return res.status(200).json(getAllAssetNews);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};
