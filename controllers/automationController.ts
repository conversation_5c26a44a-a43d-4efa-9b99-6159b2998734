import { CustomRequest } from "custom";
import { Response } from "express";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { captureException } from "@sentry/node";

export const getAutomations = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const automations = await appApiService.getM2M(API_ROUTES.automations.all(), req.user.id);
    res.status(200).json(automations);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch automations." });
  }
};

export const cancelAutomation = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const automationId = req.params.id;

    const automations = await appApiService.postM2M(API_ROUTES.automations.cancel(automationId), req.user.id);
    res.status(200).json(automations);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to cancel automation." });
  }
};
