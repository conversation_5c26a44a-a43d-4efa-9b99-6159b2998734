import { CustomError, CustomRequest } from "custom";
import { NextFunction, Request, Response } from "express";

/**
 * @description With async/await, you need some way to catch errors
 * Instead of using try{} catch(e) {} in each controller, we wrap the function in catchErrors(), catch and errors
 * they throw, and pass it along to our express middleware with next()
 */
export const catchErrors = (fn: (req: Request, res: Response, next: NextFunction) => any) => {
  return (req: Request, res: Response, next: NextFunction): any => {
    return fn(req, res, next).catch(next);
  };
};

/**
 * In development we show good error messages so if we hit a syntax error or any other previously un-handled error,
 * we can show good info on what happened
 */
export const developmentErrors = (err: CustomError, req: Request, res: Response, next: NextFunction): void => {
  err.stack = err.stack || "";
  const errorDetails = {
    title: "Error",
    message: err.message,
    status: err.status,
    stackHighlighted: err.stack.replace(/[a-z_-\d]+.js:\d+:\d+/gi, "<mark>$&</mark>")
  };
  res.status(err.status || 500);
  res.format({
    // Based on the `Accept` http header
    "text/html": () => {
      res.render("devError", errorDetails);
    }, // Form Submit, Reload the page
    "application/json": () => res.json(errorDetails) // Ajax call, send JSON back
  });
};

/**
 * @description If we hit a route that is not found, we mark it as 404 and pass it along to the next error
 * handler to display
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const err: CustomError = new Error("Not Found");
  err.status = 404;
  next(err);
};

export const productionErrors = (
  err: CustomError,
  req: CustomRequest,
  res: Response,
  next: NextFunction
): void => {
  res.status(err.status || 500);
  res.render("error", {
    title: "Error",
    message: err.message,
    user: req.user,
    INTERCOM_APP_ID: process.env.INTERCOM_APP_ID,
    INTERCOM_WEB_VERIFICATION_SECRET: process.env.INTERCOM_WEB_VERIFICATION_SECRET,
    error: {}
  });
};
