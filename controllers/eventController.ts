import appApiService, { API_ROUTES } from "../services/appApiService";
import { CustomRequest } from "custom";
import { Response } from "express";
import logger from "../services/loggerService";
import { captureException } from "@sentry/node";

export enum EventEnum {
  APP_OPENED = "appOpened"
}

export const sendEvent = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.user.id;
  const eventId = req.body.eventId;

  try {
    if (eventId === EventEnum.APP_OPENED) {
      await appApiService.postM2M(
        API_ROUTES.events.all(),
        userId,
        {
          eventId: EventEnum.APP_OPENED
        },
        {},
        {
          platform: "web"
        },
        { useApiPrefix: false }
      );
    }
  } catch (err) {
    captureException(err);
    logger.error(`Event '${eventId}' failed to emit for user ${userId}`, {
      module: "EventController",
      method: "sendEvent"
    });
  }

  res.sendStatus(204);
};
