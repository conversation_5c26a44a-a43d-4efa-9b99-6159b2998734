import { CustomRequest } from "custom";
import { Response } from "express";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { captureException } from "@sentry/node";

export const getGifts = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { hasViewedAppModal, used } = req.query;
    const gifts = await appApiService.getM2M(API_ROUTES.gifts.all(), req.user.id, { hasViewedAppModal, used });
    res.status(200).json(gifts);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch gifts." });
  }
};

export const createGift = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const { targetUserEmail, message } = req.body;

    await appApiService.postM2M(API_ROUTES.gifts.all(), req.user.id, {
      targetUserEmail,
      message
    });

    return res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to create gift." });
  }
};

export const updateGift = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const giftId = req.params.id;
    const { hasViewedAppModal } = req.body;

    await appApiService.postM2M(API_ROUTES.gifts.id(giftId), req.user.id, { hasViewedAppModal });

    return res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch gifts." });
  }
};
