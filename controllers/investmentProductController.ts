import { Request, Response } from "express";
import { captureException } from "@sentry/node";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { CustomRequest } from "custom";

const { ASSET_CONFIG } = investmentUniverseConfig;

export const getInvestmentProducts = async (req: CustomRequest, res: Response) => {
  try {
    const investmentProducts = await appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id, {
      populateTicker: true
    });
    res.status(200).json(investmentProducts);
  } catch (err) {
    res.sendStatus(500);
  }
};
export const getInvestmentProduct = async (req: CustomRequest, res: Response): Promise<Response> => {
  const assetCommonId: investmentUniverseConfig.AssetType = req.params
    .assetCommonId as investmentUniverseConfig.AssetType;
  try {
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const assetData = await appApiService.getM2M(API_ROUTES.investmentProducts.getEtfData(), req.user.id, {
        assetId: assetCommonId
      });

      const { currentPrice, tradedCurrency, tags, marketInfo, ...fundamentals } = assetData;
      return res.status(200).json({ currentPrice, tradedCurrency, fundamentals, tags, marketInfo });
    } else {
      return res.sendStatus(500);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

/**
 *
 * @returns
 * - [{date: string, close: number},...] - "date" is formatted "YYYY-MM-DD".
 * - 400 - if the assetCommonId is invalid.
 * - 500 - if an error occurred.
 */
export const getInvestmentProductHistory = async (req: CustomRequest, res: Response): Promise<Response> => {
  const assetCommonId: investmentUniverseConfig.AssetType = req.params
    .assetCommonId as investmentUniverseConfig.AssetType;
  try {
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const historicalPrice = await appApiService.getM2M(
        API_ROUTES.investmentProducts.getHistoricalPrices(),
        req.user.id,
        {
          assetId: assetCommonId
        }
      );
      return res.status(200).json(historicalPrice);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const getInvestmentProductPricesByTenor = async (req: CustomRequest, res: Response): Promise<Response> => {
  const assetCommonId: investmentUniverseConfig.AssetType = req.params
    .assetCommonId as investmentUniverseConfig.AssetType;
  try {
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const historicalPrice = await appApiService.getM2M(
        API_ROUTES.investmentProducts.pricesByTenor(),
        req.user.id,
        {
          assetId: assetCommonId
        }
      );
      return res.status(200).json(historicalPrice);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const getCreateProductView = (req: Request, res: Response): void => {
  res.render("investmentProducts/editInvestmentProduct", {
    title: "Create Investment Product",
    investmentProduct: {}
  });
};

export const getInvestmentProductInvestmentDetails = async (
  req: CustomRequest,
  res: Response
): Promise<Response> => {
  const assetCommonId: investmentUniverseConfig.AssetType = req.params
    .assetCommonId as investmentUniverseConfig.AssetType;
  try {
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const investmentDetails = await appApiService.getM2M(
        API_ROUTES.investmentProducts.getInvestmentDetails(),
        req.user.id,
        {
          assetId: assetCommonId
        }
      );
      return res.status(200).json(investmentDetails);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const getInvestmentProductRecentActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const assetCommonId: investmentUniverseConfig.AssetType = req.params
      .assetCommonId as investmentUniverseConfig.AssetType;
    if (ASSET_CONFIG[assetCommonId] && !ASSET_CONFIG[assetCommonId].deprecatedBy) {
      const limit = req.query?.limit;
      const orders = await appApiService.getM2M(API_ROUTES.investmentProducts.recentActivity(), req.user.id, {
        limit,
        assetId: assetCommonId
      });
      res.status(200).json(orders);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};
