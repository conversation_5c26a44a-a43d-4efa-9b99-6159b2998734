import { ArticleDataType, LearningGuideWithChaptersDataType } from "../react-views/configs/learningHubConfig";
import SavingsWithdrawalSuccessPage, {
  SavingsWithdrawalSuccessPagePropsType
} from "../react-views/pages/savingsWithdrawalSuccessPage";
import SavingsTopupSuccessPage, {
  SavingsTopupSuccessPagePropsType
} from "../react-views/pages/savingsTopupSuccessPage";
import {
  SavingsProductActivityItemType,
  SavingsProductFeeDetailsType,
  UserSavingsItemType
} from "../react-views/types/savings";
import IdVerificationPollingPage from "../react-views/pages/idVerificationPollingPage";
import OpenAccountPage, { OpenAccountPagePropsType } from "../react-views/pages/openAccountPage";
import { ReferralCodeDocument } from "../models/ReferralCode";
import { captureException } from "@sentry/node";
import { CustomRequest } from "custom";
import { Request, Response } from "express";
import validator from "validator";
import { banksConfig, countriesConfig, investmentUniverseConfig, plansConfig } from "@wealthyhood/shared-configs";
import { AddressDTOInterface } from "../models/Address";
import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  TransactionCategoryType,
  TransactionDocument,
  TransactionStatusType
} from "../models/Transaction";
import CollectPersonalDetailsPage, {
  CollectPersonalDetailsPagePropsType
} from "../react-views/pages/collectPersonalDetailsPage";
import InvestmentSuccessPage, { InvestmentSuccessPropsType } from "../react-views/pages/investmentSuccessPage";
import InvestorCashPage, {
  InvestorAccountType,
  InvestorCashPagePropsType
} from "../react-views/pages/investorCashPage";
import PaymentSuccessPage, { PaymentSuccessPropsType } from "../react-views/pages/paymentSuccessPage";
import CreateMyPortfolioPage from "../react-views/pages/createMyPortfolioPage";
import WithdrawalSuccessPage, { WithdrawalSuccessPropsType } from "../react-views/pages/withdrawalSuccessPage";
import logger from "../services/loggerService";
import {
  ProviderType,
  TRUELAYER_DATA_PROVIDERS,
  TRUELAYER_ENABLED_PROVIDERS,
  TruelayerPaymentsClient
} from "../services/truelayerService";
import appApiService, { API_ROUTES } from "../services/appApiService";
import VerificationSuccessPage, {
  VerificationSuccessPropsType
} from "../react-views/pages/verificationSuccessPage";
import { ServiceError } from "../utils/errorUtil";
import VerificationInitiatedPage, {
  VerificationInitiatedPropsType
} from "../react-views/pages/verificationInitiatedPage";
import VerificationPendingPage, {
  VerificationPendingPropsType
} from "../react-views/pages/verificationPendingPage";
import RebalancingSuccessPage, { RebalancingSuccessPropsType } from "../react-views/pages/rebalancingSuccessPage";
import SelectPlanOnboardingPage, {
  SelectPlanOnboardingPropsType
} from "../react-views/pages/selectPlanOnboardingPage";
import { PagePropsType } from "../react-views/types/page";
import TransactionCancellationSuccessPage from "../react-views/pages/transactionCancellationSuccessPage";
import { GiftDocument } from "../models/Gift";
import InvestorAccountDetailsPage from "../react-views/pages/investorAccountDetailsPage";
import ClosingAccountPage from "../react-views/pages/closingAccountPage";
import ResidencyCountryPage, { ResidencyCountryPropsType } from "../react-views/pages/residencyCountryPage";
import EmailVerifiedPage from "../react-views/pages/emailVerifiedPage";
import GiftSuccessPage, { GiftSuccessPropsType } from "../react-views/pages/giftSuccessPage";
import SendGiftPage, { SendGiftPropsType } from "../react-views/pages/sendGiftPage";
import BillingPage, { BillingPropsType } from "../react-views/pages/billingPage";
import PlanUpdateSuccessPage, { PlanUpdateSuccessPropsType } from "../react-views/pages/planUpdateSuccessPage";
import LearningHubPage, {
  LearningHubPropsType,
  TABS_QUERY_MAP,
  TabType
} from "../react-views/pages/learningHubPage";
import GuidePage from "../react-views/pages/guidePage";
import ArticlePage, { ArticlePropsType } from "../react-views/pages/articlePage";
import PendingTransactionsPage, {
  PendingTransactionsPropsType
} from "../react-views/pages/pendingTransactionsPage";
import MandateSuccessPage, { MandateSuccessPropsType } from "../react-views/pages/mandateSuccessPage";
import ChangePlanPage, { ChangePlanPropsType } from "../react-views/pages/changePlanPage";
import NewRecurringTopUpSuccessPage, {
  NewRecurringTopUpSuccessPropsType
} from "../react-views/pages/newRecurringTopUpSuccessPage";
import { AutomationDocument, SavingsTopUpAutomationDocument, TopUpAutomationDocument } from "../models/Automation";
import AutopilotPage, { AutopilotPropsType } from "../react-views/pages/autopilotPage";
import { envIsDev, envIsProd } from "../utils/environmentUtil";
import EarnFreeSharesPage, { EarnFreeSharesPropsType } from "../react-views/pages/earnFreeSharesPage";
import InviteFriendPage, { InviteFriendPropsType } from "../react-views/pages/inviteFriendPage";
import SetReferralCodePage, { SetReferralCodePropsType } from "../react-views/pages/setReferralCodePage";
import JoinWealthybitesPage, { JoinWealthybitesPropsType } from "../react-views/pages/joinWealthybitesPage";
import { OtherModalType } from "../react-views/components/modalsWrapper";
import {
  TransactionActivityItemType,
  TransactionActivityTransactionItemType
} from "../react-views/types/transaction";
import { PaymentMethodDocument } from "../models/PaymentMethod";
import { UserDocument } from "../models/User";
import { KycOperationDocument } from "../models/KycOperation";
import { InvestmentProductDocument } from "../models/InvestmentProduct";
import { IResult } from "truelayer-client";
import { BankProviderType } from "../react-views/types/bank";
import ConfigUtil from "../utils/configUtil";
import PaymentPendingPage, { PaymentPendingPropsType } from "../react-views/pages/paymentPendingPage";
import IdVerificationPage from "../react-views/pages/idVerificationPage";
import IdVerificationResumePage from "../react-views/pages/idVerificationResumePage";
import StatementsPage, { StatementsPropsType } from "../react-views/pages/statementsPage";
import NotificationSettingsPage, {
  NotificationSettingsPropsType
} from "../react-views/pages/notificationSettingsPage";
import { isFeatureEnabled } from "../react-views/utils/featureFlagUtil";
import { ConfigCatFeatureFlags } from "../config/featuresConfig";
import { PaginatedApiResponse } from "apiResponse";
import DailySummaryPage, { DailySummaryPagePropsType } from "../react-views/pages/dailySummaryPage";
import { PortfolioDocument, PortfolioModeEnum } from "../models/Portfolio";
import { BannerDataType, BannerEnum } from "../react-views/configs/bannerConfig";
import InvestmentsPage, { DashboardPagePropsType } from "../react-views/pages/investmentsPage";
import InvestmentActivityPage, { InvestmentActivityPropsType } from "../react-views/pages/investmentActivityPage";
import { ModalPromptType } from "../react-views/types/modalPrompt";

const ALLOWED_TRUELAYER_CALLBACK_URL_ACTIONS = [
  "just-pay",
  "pay-and-invest",
  "pay-lifetime-subscription",
  "pay-and-save"
];

const ALLOWED_SALTEDGE_CALLBACK_URL_ACTIONS = ["just-pay"];

const truelayerPaymentsClient = new TruelayerPaymentsClient();

/**
 * ROUTER METHODS
 */

export const verifyUser = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const verifiedStatus = await appApiService.postM2M(API_ROUTES.users.verify(), req.user.id);
    res.status(200).json(verifiedStatus);
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: "An error occurred, please try to verify later."
    });
  }
};

export const setResidencyCountry = async (req: CustomRequest, res: Response): Promise<void> => {
  const { residencyCountry } = req.body;
  const body = { residencyCountry: residencyCountry };
  try {
    const verifiedStatus = await appApiService.postM2M(API_ROUTES.users.residencyCountry(), req.user.id, body);
    res.status(200).json(verifiedStatus);
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: "An error occurred, please try to verify later."
    });
  }
};

export const setAddress = async (req: CustomRequest, res: Response): Promise<void> => {
  const { addressLine1, addressLine2, postCode, city, country } = req.body;
  const addressData: AddressDTOInterface = {
    owner: req.user.id,
    line1: addressLine1,
    line2: addressLine2,
    postalCode: postCode,
    city,
    countryCode: country
  };

  try {
    await appApiService.postM2M(API_ROUTES.addresses.all(), req.user.id, addressData);
    res.sendStatus(204);
  } catch (err) {
    captureException(err);
    res.sendStatus(err.response?.data?.status ?? 500);
  }
};

export const setPersonalDetails = async (req: CustomRequest, res: Response): Promise<void> => {
  const { firstName, lastName, dateOfBirth, nationality } = req.body;

  await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
    firstName,
    lastName,
    dateOfBirth: new Date(dateOfBirth),
    nationalities: nationality ? [nationality] : null
  });

  res.sendStatus(204);
};

export const setTaxResidency = async (req: CustomRequest, res: Response): Promise<void> => {
  const { value } = req.body;
  const { proofType } = ConfigUtil.getTaxResidencyConfig(req.user.residencyCountry);

  await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
    taxResidency: { countryCode: req.user.residencyCountry, proofType, value },
    submittedRequiredInfoAt: new Date()
  });
  res.sendStatus(204);
};

export const createPayment = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { paymentAmount, bankAccountId, bankAccount, bankLinkedFrom, bankId } = req.body;
    if (!bankAccountId && !bankAccount && !bankId) {
      throw new ServiceError("Either a bank account ID, bank ID or bank account data should be passed");
    }

    const { paymentUri } = await appApiService.postM2M(API_ROUTES.transactions.deposits.all(), req.user.id, {
      paymentAmount,
      bankAccount,
      bankId,
      bankLinkedFrom,
      bankAccountId,
      depositAndInvest: false
    });

    req.flash("success", "Your deposit has been created.");
    res.status(200).json({ message: "Your deposit has been created!", redirectUri: paymentUri });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: "An error occurred, please try to make a payment later."
    });
  }
};

export const createLifetimeChargePayment = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { price, bankAccountId } = req.body;
    if (!bankAccountId) {
      throw new ServiceError("A bank account ID should be passed");
    }

    const { paymentUri } = await appApiService.postM2M(
      API_ROUTES.transactions.charges.lifetime.all(),
      req.user.id,
      {
        price,
        bankAccountId
      }
    );

    req.flash("success", "Your payment has been created.");
    res.status(200).json({ message: "Your payment has been created!", redirectUri: paymentUri });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message:
        err instanceof ServiceError
          ? err.message
          : err.response?.data?.error?.message ?? "An error occurred, please try to make a payment later."
    });
  }
};

/**
 * @description This is used in the 1-step deposit & invest flow.
 */
export const depositAndInvest = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const { orderAmount, bankAccountId, bankAccount, bankLinkedFrom } = req.body;
    if (!bankAccountId && !bankAccount) {
      throw new ServiceError("Either a bank account ID or bank account data should be passed");
    }

    const { paymentUri, depositId } = await appApiService.postM2M(
      API_ROUTES.transactions.deposits.all(),
      req.user.id,
      {
        paymentAmount: orderAmount,
        bankAccount,
        bankLinkedFrom,
        bankAccountId,
        depositAndInvest: true
      }
    );

    if (user.portfolioConversionStatus == "inProgress") {
      throw new ServiceError("An error occurred when executing your investment. Please try later.");
    }

    const transaction = await appApiService.postM2M(
      API_ROUTES.portfolios.investPendingDeposit(user.portfolios[0]._id),
      user.id,
      {
        orderAmount,
        pendingDeposit: depositId
      },
      { ...req.query }
    );

    req.flash("success", "Your deposit has been created.");
    res.status(200).json({ message: "Your deposit has been created!", transaction, redirectUri: paymentUri });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message:
        err instanceof ServiceError
          ? err.message
          : err.response?.data?.error?.message ?? "An error occurred, please try to make a payment later."
    });
  }
};

export const createSubscription = async (req: CustomRequest, res: Response): Promise<void> => {
  const { category, price } = req.body;

  try {
    await appApiService.postM2M(API_ROUTES.subscriptions.all(), req.user.id, {
      category,
      price
    });

    res.sendStatus(200);
  } catch (err) {
    res.status(500).json({
      message: "Could not create subscription"
    });
  }
};

export const updateSubscription = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const subscriptionId = req.params.id;
    const { category, mandate, price } = req.body;

    await appApiService.postM2M(API_ROUTES.subscriptions.id(subscriptionId), req.user.id, {
      category,
      mandate,
      price
    });
    res.status(200).json({ message: "Your subscription has been updated!" });
  } catch (err) {
    captureException(err);
    res.sendStatus(400);
  }
};

export const renewSubscription = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const subscriptionId = req.params.id;

    const subscription = await appApiService.postM2M(API_ROUTES.subscriptions.renew(subscriptionId), req.user.id);
    res.status(200).json(subscription);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to renew subscription." });
  }
};

/**
 * @description This is used in the 1-step deposit & buy individual assets flow.
 */
export const depositAndBuyAsset = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const assetId: investmentUniverseConfig.AssetType = req.body.asset;

    const { orderAmount, bankAccountId } = req.body;
    const { paymentUri, depositId } = await appApiService.postM2M(
      API_ROUTES.transactions.deposits.all(),
      req.user.id,
      {
        paymentAmount: orderAmount,
        bankAccountId,
        depositAndInvest: true
      }
    );

    // Create asset transaction and order, both pending the above deposit
    await appApiService.postM2M(API_ROUTES.portfolios.buyAssetPendingDeposit(user.portfolios[0].id), user.id, {
      pendingOrders: { [assetId]: { side: "buy", money: orderAmount } },
      pendingDeposit: depositId
    });

    req.flash("success", "Your deposit has been created.");
    res.status(200).json({ message: "Your deposit has been created!", redirectUri: paymentUri });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: err instanceof ServiceError ? err.message : "An error occurred, please try to make a payment later."
    });
  }
};

export const getLinkedBankAccounts = async (req: CustomRequest, res: Response): Promise<void> => {
  // Fetch linked bank accounts
  // Find if the investors linked bank provider is available for payments. If not, no provider will be
  // specified in the created payment and selection will be done in the truelayer dialog.
  // TODO: in dashboard we don't care whether bank provider is available for payments, we only want to display them
  // => update bank account to also keep logo and displayable name and ignore the truelayer pay provider availability.
  try {
    const linkedBankAccounts = await appApiService.getM2M(API_ROUTES.users.linkedBankAccounts(), req.user.id);
    res.status(200).json(linkedBankAccounts);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch linked bank account." });
  }
};

export const getTruelayerProviders = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const providersResponse = await truelayerPaymentsClient.getProviders();
    const enabledPayProvidersSet = new Set(TRUELAYER_ENABLED_PROVIDERS);
    const allowedProviders = providersResponse.results.filter((provider) =>
      enabledPayProvidersSet.has(provider.provider_id)
    );
    res.status(200).json(allowedProviders);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch Truelayer providers." });
  }
};

export const generateAccountStatement = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const start = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const end = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

    const { fileUri } = await appApiService.postM2M(
      API_ROUTES.users.statement(),
      req.user.id,
      {},
      {
        start,
        end
      }
    );

    res.status(200).json({ fileUri });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch account statement." });
  }
};

export const getCashView = async (req: CustomRequest, res: Response): Promise<void> => {
  const realPortfolio = req.user?.portfolios?.[0];

  const userSavings: UserSavingsItemType[] = await appApiService.getM2M(
    API_ROUTES.savingsProducts.me(),
    req.user.id
  );

  // For now, we assume the user has access to only one savings product (e.g. European users only have
  // access to the EUR MMF).
  const userSavingsProductId = userSavings?.[0]?.savingsProductId;

  const [
    cashActivityItems,
    automations,
    investmentProducts,
    pendingIncomingCashFlowsTransactions,
    providersResponse,
    savingsActivityItems
  ]: [
    TransactionActivityTransactionItemType[],
    AutomationDocument[],
    InvestmentProductDocument[],
    TransactionDocument[],
    IResult<ProviderType>,
    SavingsProductActivityItemType[]
  ] = await Promise.all([
    appApiService.getM2M(API_ROUTES.transactions.cashActivity(), req.user.id),
    appApiService.getM2M(API_ROUTES.automations.all(), req.user.id),
    appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id, {
      populateTicker: true
    }),
    appApiService.getM2M(API_ROUTES.portfolios.pendingCashFlows(realPortfolio.id), req.user.id),
    truelayerPaymentsClient.getProviders(),
    appApiService.getM2M(API_ROUTES.savingsProducts.activity(), req.user.id, {
      savingsProductId: userSavingsProductId
    })
  ]);

  const topUpAutomation = automations.find(
    (automation) => automation.category === "TopUpAutomation"
  ) as TopUpAutomationDocument;
  const savingsTopUpAutomation = automations.find(
    (automation) => automation.category === "SavingsTopUpAutomation"
  ) as SavingsTopUpAutomationDocument;

  // Total portfolio value & cash
  const portfolioValue = realPortfolio?.currentTicker?.price || 0;
  const availableCash = realPortfolio.cash?.[req.user.currency]?.available || 0;

  const truelayerProviders = providersResponse.results;

  const initialSelectedAccount = req.query.selectedAccount as InvestorAccountType;

  const props: InvestorCashPagePropsType = {
    pendingIncomingCashFlowsTransactions,
    availableCash,
    truelayerProviders,
    portfolioValue,
    investmentProducts,
    cashActivityItems,
    topUpAutomation,
    savingsTopUpAutomation,
    userSavings,
    savingsActivityItems,
    initialSelectedAccount
  };

  return res.render("renderReactLayoutNew", {
    title: "Cash",
    activePage: "cash",
    reactComp: InvestorCashPage,
    props: JSON.stringify(props),
    scriptName: "cash"
  });
};

export const getAccountDetailsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: PagePropsType = {
    user: req.user
  };

  return res.render("renderReactLayoutNew", {
    title: "My Account",
    reactComp: InvestorAccountDetailsPage,
    props: JSON.stringify(props),
    scriptName: "investorAccountDetails"
  });
};

export const getLearningHubView = async (req: CustomRequest, res: Response): Promise<void> => {
  const queryTab: string = req.query.tab as string; // Extract tab from query parameters
  const [analystInsights, news, glossary, learningGuides] = await Promise.all([
    appApiService.getM2M(
      API_ROUTES.learningHub.analystInsights(),
      req.user.id,
      { page: 1 },
      { rawResponse: true }
    ),
    appApiService.getM2M(API_ROUTES.learningHub.news(), req.user.id),
    appApiService.getM2M(API_ROUTES.learningHub.glossary(), req.user.id),
    appApiService.getM2M(API_ROUTES.learningHub.learningGuides(), req.user.id)
  ]);

  // Find the key in TABS_QUERY_MAP that matches queryTab
  const initialActiveTab = Object.keys(TABS_QUERY_MAP).find(
    (key) => TABS_QUERY_MAP[key as TabType] === queryTab
  ) as TabType;

  const props: LearningHubPropsType = {
    initialActiveTab: initialActiveTab || "Analyst Insights",
    analystInsights: analystInsights,
    news: news || [],
    glossaryItems: glossary || [],
    learningGuides,
    featureFlags: req.featureFlags
  };

  return res.render("renderReactLayoutNew", {
    title: "Learning Hub",
    reactComp: LearningHubPage,
    activePage: "learning-hub",
    props: JSON.stringify(props),
    scriptName: "learningHub"
  });
};

export const getGuideView = async (req: CustomRequest, res: Response): Promise<void> => {
  const learningGuide = (await appApiService.getM2M(
    API_ROUTES.learningHub.learningGuideBySlug(req.params.slug),
    req.user.id
  )) as LearningGuideWithChaptersDataType;

  const props = {
    learningGuide
  };

  return res.render("renderReactLayoutNew", {
    title: "Guide",
    reactComp: GuidePage,
    activePage: "learn",
    props: JSON.stringify(props),
    scriptName: "guide"
  });
};

export const getPendingTransactionsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const [pendingTransactions, providersResponse, investmentProducts, automations] = await Promise.all([
    appApiService.getM2M(API_ROUTES.transactions.pending.all(), req.user.id),
    truelayerPaymentsClient.getProviders(),
    appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id, {
      populateTicker: true
    }),
    appApiService.getM2M(API_ROUTES.automations.all(), req.user.id)
  ]);

  const truelayerProviders = providersResponse.results;
  const topUpAutomation = automations.find(
    (automation: TopUpAutomationDocument) => automation.category === "TopUpAutomation"
  ) as TopUpAutomationDocument;

  const props: PendingTransactionsPropsType = {
    pendingTransactions,
    truelayerProviders,
    investmentProducts,
    topUpAutomation
  };
  return res.render("renderReactLayoutNew", {
    title: "Pending Transactions",
    reactComp: PendingTransactionsPage,
    activePage: "pendingTransactions",
    props: JSON.stringify(props),
    scriptName: "pendingTransactions"
  });
};

export const getAnalystInsightsArticleView = async (req: CustomRequest, res: Response): Promise<void> => {
  const id = req.params.id;
  const returnTo = req.query.returnTo as string;

  let article: ArticleDataType;
  if (isFeatureEnabled(ConfigCatFeatureFlags.ANALYST_INSIGHT_MIGRATION, req.featureFlags)) {
    article = await appApiService.getM2M(API_ROUTES.learningHub.analystInsightById(id), req.user.id);
  } else {
    const articles: ArticleDataType[] = await appApiService.getM2M(
      API_ROUTES.learningHub.analystInsights(),
      req.user.id
    );
    article = articles.find((article) => article.key === req.params.id);
  }

  const props: ArticlePropsType = {
    article: article,
    dateToDisplay: article.publishedAt,
    returnTo
  };

  return res.render("renderReactLayoutNew", {
    title: "Analyst Insights",
    reactComp: ArticlePage,
    activePage: "analyst-insights",
    props: JSON.stringify(props),
    scriptName: "analystInsights"
  });
};

export const getNewsArticleView = async (req: CustomRequest, res: Response): Promise<void> => {
  const articles: ArticleDataType[] = await appApiService.getM2M(API_ROUTES.learningHub.news(), req.user.id);
  const article = articles.find((article) => article.key === req.params.id);
  const props: ArticlePropsType = {
    article: article,
    dateToDisplay: article.createdAt
  };

  return res.render("renderReactLayoutNew", {
    title: "News",
    reactComp: ArticlePage,
    activePage: "news",
    props: JSON.stringify(props),
    scriptName: "news"
  });
};

export const getBillingView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const chargeTransactions: ChargeTransactionDocument[] = await appApiService.getM2M(
    API_ROUTES.transactions.billingActivity(),
    req.user.id,
    { limit: 5 }
  );

  const subscription = await appApiService.getM2M(API_ROUTES.subscriptions.all(), req.user.id);

  const providersResponse = await truelayerPaymentsClient.getProviders();
  const truelayerProviders = providersResponse.results;

  const paymentMethods: PaymentMethodDocument[] = await appApiService.getM2M(
    API_ROUTES.paymentMethods.all(),
    req.user.id
  );

  const initialShowCardPaymentModal = req.query?.initialShowCardPaymentModal === "true";

  const props: BillingPropsType = {
    user,
    chargeTransactions,
    truelayerProviders,
    subscription,
    initialShowCardPaymentModal,
    paymentMethods,
    activePage: "billing",
    featureFlags: req.featureFlags
  };

  return res.render("renderReactLayoutNew", {
    title: "Billing",
    reactComp: BillingPage,
    props: JSON.stringify(props),
    scriptName: "billing"
  });
};

export const getStatementsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const props: StatementsPropsType = {
    user
  };

  return res.render("renderReactLayoutNew", {
    title: "Account statement",
    reactComp: StatementsPage,
    props: JSON.stringify(props),
    scriptName: "statements"
  });
};

export const getNotificationSettingsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const notificationSettings = await appApiService.getM2M(API_ROUTES.notificationSettings.me(), req.user.id);

  const props: NotificationSettingsPropsType = {
    user,
    email: notificationSettings.email,
    app: notificationSettings.app
  };

  return res.render("renderReactLayoutNew", {
    title: "Email settings",
    reactComp: NotificationSettingsPage,
    props: JSON.stringify(props),
    scriptName: "notificationSettings"
  });
};

export const updateNotificationSettings = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const body = { id: req.body.notificationId, active: req.body.active };
    await appApiService.postM2M(API_ROUTES.notificationSettings.me(), req.user.id, body);

    res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getCollectPersonalDetailsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const { taxResidency, firstName, lastName, dateOfBirth, nationalities } = req.user;

  const employmentConfig = await appApiService.getM2M(API_ROUTES.users.employmentConfig(), req.user.id);

  const address = req.user.addresses[0];
  const props: CollectPersonalDetailsPagePropsType = {
    address,
    taxResidencyProofValue: taxResidency ? taxResidency.value : "",
    firstName,
    lastName,
    dateOfBirth: dateOfBirth
      ? new Date(dateOfBirth).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "short",
          year: "numeric"
        })
      : "",
    nationality: nationalities?.length > 0 ? (nationalities[0] as countriesConfig.CountryCodesType) : "",
    employmentConfig,
    employmentInfo: req.user?.employmentInfo
  };

  res.render("renderReactLayoutNew", {
    title: "Verify Account",
    reactComp: CollectPersonalDetailsPage,
    props: JSON.stringify(props),
    scriptName: "collectPersonalDetails"
  });
};

export const getPaymentPendingView = (req: CustomRequest, res: Response): void => {
  const bankId = req.query.bankId as banksConfig.BankType;
  const saltedgeCustomPaymentId = req.query.saltedgeCustomPaymentId as string;

  const props: PaymentPendingPropsType = {
    saltedgeCustomPaymentId,
    bankId
  };

  res.render("renderReactLayoutNew", {
    title: "Payment Pending",
    reactComp: PaymentPendingPage,
    props: JSON.stringify(props),
    scriptName: "paymentPending"
  });
};

export const getPaymentSuccessView = (req: CustomRequest, res: Response): void => {
  const bankReference = req.query.bankReference as string;
  const paymentAmount = req.query.paymentAmount as string;

  if (
    !paymentAmount ||
    !bankReference ||
    !validator.isNumeric(paymentAmount) ||
    !validator.isAlphanumeric("bankReference".replace("-", ""))
  ) {
    return res.redirect("/");
  }

  const props: PaymentSuccessPropsType = {
    bankReference,
    paymentAmount: parseFloat(paymentAmount)
  };

  res.render("renderReactLayoutNew", {
    title: "Payment Success",
    reactComp: PaymentSuccessPage,
    props: JSON.stringify(props),
    scriptName: "paymentSuccess"
  });
};

export const getGiftSuccessView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: GiftSuccessPropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Gift on the way",
    reactComp: GiftSuccessPage,
    props: JSON.stringify(props),
    scriptName: "giftSuccess"
  });
};

export const getSendGiftView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: SendGiftPropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Send gift",
    reactComp: SendGiftPage,
    props: JSON.stringify(props),
    scriptName: "sendGift"
  });
};

export const getEarnFreeSharesView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: EarnFreeSharesPropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Earn free shares",
    reactComp: EarnFreeSharesPage,
    props: JSON.stringify(props),
    scriptName: "earnFreeShares"
  });
};

export const getInviteFriendView = async (req: CustomRequest, res: Response): Promise<void> => {
  const referral = req.user.oneTimeReferralCode as ReferralCodeDocument;

  const props: InviteFriendPropsType = {
    initialReferralCode: referral?.code ?? req.user.participant?.wlthdId
  };

  res.render("renderReactLayoutNew", {
    title: "Invite a friend to Wealthyhood",
    reactComp: InviteFriendPage,
    props: JSON.stringify(props),
    scriptName: "inviteFriend"
  });
};

export const getWithdrawalSuccessView = (req: CustomRequest, res: Response): void => {
  const props: WithdrawalSuccessPropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Withdrawal Success",
    reactComp: WithdrawalSuccessPage,
    props: JSON.stringify(props),
    scriptName: "withdrawalSuccess"
  });
};

export const getCreateMyPortfolioView = async (req: CustomRequest, res: Response): Promise<void> => {
  res.render("renderReactLayoutNew", {
    title: "Create portfolio!",
    reactComp: CreateMyPortfolioPage,
    props: JSON.stringify({}),
    scriptName: "createMyPortfolio"
  });
};

export const acceptTermsAndConditions = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
      hasAcceptedTerms: true
    });

    res.sendStatus(204);
  } catch (error) {
    res.status(500).json({
      message: "Oops something went wrong!"
    });
  }
};

export const setupRecurringTopUp = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { bankAccount, orderAmount, allocationMethod, dayOfMonth, postponeActivation = false } = req.body;

    let mandate: string = req.body.mandate;
    if (!mandate) {
      mandate = (
        await appApiService.postM2M(API_ROUTES.mandates.all(), req.user.id, {
          category: "Top-Up",
          bankAccount
        })
      ).id;
    }

    await appApiService.postM2M(API_ROUTES.automations.all(), req.user.id, {
      category: "TopUpAutomation",
      mandate: mandate,
      frequency: "monthly",
      postponeActivation,
      allocationMethod,
      dayOfMonth,
      orderAmount
    });

    res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "Oops something went wrong!"
    });
  }
};

export const setupRecurringSavingsTopUp = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { bankAccount, orderAmount, savingsProduct, dayOfMonth } = req.body;

    let mandate: string = req.body.mandate;
    if (!mandate) {
      mandate = (
        await appApiService.postM2M(API_ROUTES.mandates.all(), req.user.id, {
          category: "Top-Up",
          bankAccount
        })
      ).id;
    }

    await appApiService.postM2M(API_ROUTES.automations.all(), req.user.id, {
      category: "SavingsTopUpAutomation",
      mandate: mandate,
      frequency: "monthly",
      dayOfMonth,
      orderAmount,
      savingsProduct
    });

    res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "Oops something went wrong!"
    });
  }
};

export const setupAutomatedRebalancing = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2M(API_ROUTES.automations.all(), req.user.id, {
      category: "RebalanceAutomation",
      frequency: "monthly"
    });

    res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "Oops something went wrong!"
    });
  }
};

export const getSelectPlanView = async (req: CustomRequest, res: Response): Promise<void> => {
  const promotionalSavingsProductId = ConfigUtil.getSavingsProductUniverse(
    req.user?.companyEntity
  ).promotionalSavingsProduct;

  const [paymentMethods, promotionalSavingsProductData]: [PaymentMethodDocument[], SavingsProductFeeDetailsType] =
    await Promise.all([
      appApiService.getM2M(API_ROUTES.paymentMethods.all(), req.user.id),
      appApiService.getM2M(API_ROUTES.savingsProducts.feeDetails(), req.user.id, {
        savingsProductId: promotionalSavingsProductId
      })
    ]);

  const initialShowCardPaymentModal = req.query?.initialShowCardPaymentModal === "true";

  let initialSelectedPrice = req.query?.select as plansConfig.PriceType;
  if (req.user.isSweatcoinReferred) {
    initialSelectedPrice = "paid_mid_lifetime_sweatcoin_1";
  }

  const props: SelectPlanOnboardingPropsType = {
    user: req.user,
    paymentMethods: paymentMethods.filter((paymentMethod) => !paymentMethod.wallet),
    initialShowCardPaymentModal,
    initialSelectedPrice,
    promotionalSavingsProductData: {
      ...promotionalSavingsProductData,
      savingsProductId: promotionalSavingsProductId
    }
  };

  res.render("renderReactLayoutNew", {
    activePage: "select-plan",
    title: "Choose your plan",
    reactComp: SelectPlanOnboardingPage,
    props: JSON.stringify(props),
    scriptName: "selectPlan"
  });
};

export const getChangePlanView = async (req: CustomRequest, res: Response): Promise<void> => {
  const promotionalSavingsProductId = ConfigUtil.getSavingsProductUniverse(
    req.user?.companyEntity
  ).promotionalSavingsProduct;

  const [paymentMethods, promotionalSavingsProductData]: [PaymentMethodDocument[], SavingsProductFeeDetailsType] =
    await Promise.all([
      appApiService.getM2M(API_ROUTES.paymentMethods.all(), req.user.id),
      appApiService.getM2M(API_ROUTES.savingsProducts.feeDetails(), req.user.id, {
        savingsProductId: promotionalSavingsProductId
      })
    ]);

  const initialSelectedPrice = req.query?.select as plansConfig.PriceType;
  const initialShowCardPaymentModal = req.query?.initialShowCardPaymentModal === "true";

  const props: ChangePlanPropsType = {
    user: req.user,
    paymentMethods: paymentMethods.filter((paymentMethod) => !paymentMethod.wallet),
    featureFlags: req.featureFlags,
    initialShowCardPaymentModal,
    initialSelectedPrice,
    promotionalSavingsProductData: {
      ...promotionalSavingsProductData,
      savingsProductId: promotionalSavingsProductId
    }
  };

  res.render("renderReactLayoutNew", {
    title: "Choose your plan",
    activePage: "change-plan",
    reactComp: ChangePlanPage,
    props: JSON.stringify(props),
    scriptName: "changePlan"
  });
};

export const getSetReferralCodeView = async (req: CustomRequest, res: Response): Promise<void> => {
  const notSeenGifts = (await appApiService.getM2M(API_ROUTES.gifts.all(), req.user.id, {
    hasViewedAppModal: false
  })) as GiftDocument[];

  if (notSeenGifts.length > 0) {
    return res.redirect("/investor/open-account");
  }

  const props: SetReferralCodePropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Have a referral code?",
    reactComp: SetReferralCodePage,
    props: JSON.stringify(props),
    scriptName: "setReferralCode"
  });
};

export const getJoinWealthybitesView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: JoinWealthybitesPropsType = {};

  if (req.user.viewedWealthybitesScreen) {
    return res.redirect("/");
  }
  res.render("renderReactLayoutNew", {
    title: "Join Wealthybites Community",
    reactComp: JoinWealthybitesPage,
    props: JSON.stringify(props),
    scriptName: "joinWealthybites"
  });
};

export const getInvestmentSuccessView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: InvestmentSuccessPropsType = {
    activePage: "account"
  };

  res.render("renderReactLayoutNew", {
    title: "Investment Success",
    reactComp: InvestmentSuccessPage,
    props: JSON.stringify(props),
    scriptName: "investmentSuccess"
  });
};

export const getPlanUpdateSuccessView = (req: CustomRequest, res: Response): void => {
  const from = req.query.from as plansConfig.PriceType;
  const to = req.query.to as plansConfig.PriceType;

  const props: PlanUpdateSuccessPropsType = { user: req.user, from, to };

  res.render("renderReactLayoutNew", {
    title: "Plan Update Success",
    reactComp: PlanUpdateSuccessPage,
    props: JSON.stringify(props),
    scriptName: "planUpdateSuccess"
  });
};

export const getMandateSuccessView = (req: CustomRequest, res: Response): void => {
  const target = req.query.target as "autopilot" | "myaccount";
  const dayOfMonth = parseInt(req.query.dayOfMonth as string);

  const props: MandateSuccessPropsType = {
    target,
    dayOfMonth
  };

  res.render("renderReactLayoutNew", {
    title: "Direct Debit Set Up Successfully",
    reactComp: MandateSuccessPage,
    props: JSON.stringify(props),
    scriptName: "mandateSuccess"
  });
};

export const getNewRecurringTopUpSuccessView = (req: CustomRequest, res: Response): void => {
  const target = req.query.target as "autopilot" | "myaccount";

  const props: NewRecurringTopUpSuccessPropsType = {
    target
  };

  res.render("renderReactLayoutNew", {
    title: "Direct Debit Set Up Successfully",
    reactComp: NewRecurringTopUpSuccessPage,
    props: JSON.stringify(props),
    scriptName: "newRecurringTopUpSuccess"
  });
};

export const getRebalancingSuccessView = (req: CustomRequest, res: Response): void => {
  const props: RebalancingSuccessPropsType = {};

  res.render("renderReactLayoutNew", {
    title: "Rebalancing Success",
    reactComp: RebalancingSuccessPage,
    props: JSON.stringify(props),
    scriptName: "rebalancingSuccess"
  });
};

export const getTransactionCancellationSuccessView = (req: CustomRequest, res: Response): void => {
  const props: PagePropsType = {
    activePage: "account"
  };
  res.render("renderReactLayoutNew", {
    title: "Cancel Success",
    reactComp: TransactionCancellationSuccessPage,
    props: JSON.stringify(props),
    scriptName: "cancellationSuccess"
  });
};

export const getInvestorProfileView = async (req: Request, res: Response): Promise<void> => {
  res.render("investorProfile", {
    title: "Profile"
  });
};

export const getRequestProfileUpdateView = async (req: CustomRequest, res: Response): Promise<void> => {
  res.render("requestProfileUpdate", {
    title: "Profile",
    email: req.user.email
  });
};

/**
 * @description Handles the truelayer pay callback once the user authenticates & authorises a payment with their
 * bank. Once callback is called, a wealthkernel deposit is created and the db deposit is updated with
 * the wealthkernel deposit id & status.
 */
export const truelayerPayCallback = async (req: CustomRequest, res: Response): Promise<void> => {
  logger.info("Handling truelayer pay callback.", {
    module: "InvestorController",
    method: "truelayerPayCallback",
    userEmail: req.user.email
  });
  try {
    const action = req.params.action as string;
    if (!ALLOWED_TRUELAYER_CALLBACK_URL_ACTIONS.includes(action)) {
      logger.error(`User ${req.user.email} is redirected to truelayer pay callback but with an invalid action.`, {
        module: "InvestorController",
        method: "truelayerPayCallback",
        userEmail: req.user.email
      });
      return res.redirect("/");
    }
    if (req.user.portfolios.length === 0) {
      logger.error(`User ${req.user.email} is redirected to truelayer pay callback but has no real portfolios.`, {
        module: "InvestorController",
        method: "truelayerPayCallback",
        userEmail: req.user.email
      });
      return res.redirect("/");
    }

    const paymentId = req.query.payment_id as string;

    if (action === "pay-lifetime-subscription") {
      const syncedChargeTransaction = await appApiService.postM2M(
        API_ROUTES.transactions.charges.lifetime.syncTruelayer(),
        req.user.id,
        null,
        {
          truelayerId: paymentId
        }
      );
      if (!syncedChargeTransaction) {
        // No results exist => payment was not retrieved from truelayer API
        logger.error(`Truelayer payment with id ${paymentId} not found`, {
          module: "InvestorController",
          method: "truelayerPayCallback",
          userEmail: req.user.email
        });
        req.flash("error", "An error occurred when creating the payment. Please check later.");
        return res.redirect("/");
      }

      const paymentStatus = syncedChargeTransaction.truelayer.status;
      if (paymentStatus === "failed") {
        req.flash("error", "Payment failed.");
        res.redirect("/");
      } else if (paymentStatus === "authorized" || paymentStatus === "executed") {
        if (
          req.user.subscription.price === "free_monthly" &&
          req.user.portfolioConversionStatus === "notStarted"
        ) {
          req.flash("success", "Payment has been created!");
          res.redirect("/");
        } else {
          req.flash("success", "Payment has been created!");
          res.redirect("/investor/plan-update-success");
        }
      }
    } else {
      const syncedDepositTransaction = await appApiService.postM2M(
        API_ROUTES.transactions.deposits.syncTruelayer(),
        req.user.id,
        null,
        {
          truelayerId: paymentId
        }
      );
      if (!syncedDepositTransaction) {
        // No results exist => payment was not retrieved from truelayer API
        logger.error(`Truelayer payment with id ${paymentId} not found`, {
          module: "InvestorController",
          method: "truelayerPayCallback",
          userEmail: req.user.email
        });
        req.flash("error", "An error occurred when creating the payment. Please check later.");
        return res.redirect("/");
      }

      const paymentStatus = syncedDepositTransaction.truelayer.status;
      if (paymentStatus === "failed") {
        req.flash("error", "Payment failed.");
        res.redirect("/");
      } else if (paymentStatus === "authorized" || paymentStatus === "executed") {
        req.flash("success", "Payment has been created!");
        const { bankReference, consideration } = syncedDepositTransaction;
        if (action == "pay-and-invest") {
          res.redirect(`/investor/investments?showSuccessOrder=true&depositId=${syncedDepositTransaction._id}`);
        } else if (action === "pay-and-save") {
          res.redirect("/investor/savings-topup-success?redirect=/");
        } else {
          res.redirect(
            `/investor/payment-success?bankReference=${bankReference}&paymentAmount=${consideration.amount / 100}`
          );
        }
      }
    }
  } catch (err) {
    captureException(err);
    logger.error("We couldn't process truelayer pay callback.", {
      module: "InvestorController",
      method: "truelayerPayCallback",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });
    req.flash("error", "An error occurred, please check the status of your payment later.");
    res.redirect("/");
  }
};

/**
 * @description Handles the Saltedge pay callback once the user authenticates & authorises a payment with their
 * bank.
 */
export const saltedgePayCallback = async (req: CustomRequest, res: Response): Promise<void> => {
  logger.info("Handling Saltedge pay callback.", {
    module: "InvestorController",
    method: "saltedgePayCallback",
    userEmail: req.user.email
  });

  try {
    const action = req.params.action as string;
    if (!ALLOWED_SALTEDGE_CALLBACK_URL_ACTIONS.includes(action)) {
      logger.error(`User ${req.user.email} is redirected to Saltedge pay callback but with an invalid action.`, {
        module: "InvestorController",
        method: "saltedgePayCallback",
        userEmail: req.user.email
      });
      return res.redirect("/");
    }

    if (req.user.portfolios.length === 0) {
      logger.error(`User ${req.user.email} is redirected to Saltedge pay callback but has no real portfolios.`, {
        module: "InvestorController",
        method: "saltedgePayCallback",
        userEmail: req.user.email
      });
      return res.redirect("/");
    }

    const bankId = req.query.bankId as string;
    const customId = req.query.customId as string;

    res.redirect(`/investor/payment-pending?saltedgeCustomPaymentId=${customId}&bankId=${bankId}`);
  } catch (err) {
    captureException(err);
    logger.error("We couldn't process Saltedge pay callback.", {
      module: "InvestorController",
      method: "saltedgePayCallback",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });
    req.flash("error", "An error occurred, please check the status of your payment later.");
    res.redirect("/");
  }
};

/**
 * @description Handles the Stripe pay callback once the user adds a payment method.
 */
export const stripeAddPaymentMethodCallback = async (req: CustomRequest, res: Response): Promise<void> => {
  logger.info("Handling Stripe pay callback.", {
    module: "InvestorController",
    method: "stripeAddPaymentMethodCallback",
    userEmail: req.user.email
  });
  try {
    const setupIntentId = req.query.setup_intent as string;
    const selectedPrice = req.query.selectedPrice as string;
    const source = req.query.source as string;
    const initialShowCardPaymentModal = req.query.initialShowCardPaymentModal as string;

    await appApiService.postM2M(API_ROUTES.paymentMethods.completeStripe(), req.user.id, { setupIntentId });

    req.flash("success", "Payment method has been added!");

    res.redirect(
      `/investor/${source}?select=${selectedPrice}&initialShowCardPaymentModal=${initialShowCardPaymentModal}`
    );
  } catch (err) {
    captureException(err);
    logger.error("We couldn't process Stripe pay callback.", {
      module: "InvestorController",
      method: "stripeAddPaymentMethodCallback",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });
    req.flash("error", "An error occurred!");
    res.redirect("/");
  }
};

export const getTruelayerDataAuthUrl = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const originPage = req.query.originPage;
    const originModal = req.query.originModal as OtherModalType;
    const displayedAsset = req.query.displayedAsset;
    const providerId = req.query.providerId;
    const selectedPrice = req.query.selectedPrice;
    const environment = envIsProd() ? "production" : "development";

    let uri = `${process.env.TRUELAYER_AUTH_URL}?response_type=code&client_id=${
      process.env.TRUELAYER_CLIENT_ID
    }&scope=accounts+offline_access&redirect_uri=${
      process.env.TRUELAYER_DATA_REDIRECT_URI
    }&providers=${TRUELAYER_DATA_PROVIDERS}&user_email=${encodeURIComponent(req.user.email)}&state=userId_${
      req.user.id
    }__platform_web__env_${environment}__redirectPage_${originPage}__providerId_${providerId}`;
    if (originModal) {
      uri += `__modal_${originModal}`;
    }
    if (originModal === "assetBuy" && displayedAsset) {
      uri += `__asset_${displayedAsset}`;
    }
    if (selectedPrice) {
      uri += `__selectedPrice_${selectedPrice}`;
    }

    if (envIsProd()) {
      uri += `&provider_id=${providerId}`;
    }

    res.status(200).json({ redirectUri: uri });
  } catch (err) {
    captureException(err);
    logger.error("We couldn't build the link bank account Truelayer URL", {
      module: "InvestorController",
      method: "linkBankAccount",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });
    res.status(500).json({
      message: "Could not connect bank account"
    });
  }
};

export const getVerificationInitiatedView = (req: CustomRequest, res: Response) => {
  const props: VerificationInitiatedPropsType = {};
  res.render("renderReactLayoutNew", {
    title: "Verification Initiated",
    reactComp: VerificationInitiatedPage,
    props: JSON.stringify(props),
    scriptName: "verificationInitiated"
  });
};

export const getVerificationPendingView = (req: CustomRequest, res: Response) => {
  const props: VerificationPendingPropsType = {};
  res.render("renderReactLayoutNew", {
    title: "Verification Pending",
    reactComp: VerificationPendingPage,
    props: JSON.stringify(props),
    scriptName: "verificationPending"
  });
};

export const getClosingAccountView = (req: CustomRequest, res: Response) => {
  if (!req.user.hasDisassociationRequest) {
    return res.redirect("/");
  }

  const props: VerificationInitiatedPropsType = {};
  res.render("renderReactLayoutNew", {
    title: "Closing account",
    reactComp: ClosingAccountPage,
    props: JSON.stringify(props),
    scriptName: "closingAccount"
  });
};

export const getEmailVerifiedView = (req: CustomRequest, res: Response) => {
  if (req.user.hasAcceptedTerms) {
    return res.redirect("/");
  }
  if (!req.user.submittedRequiredInfo) {
    return res.redirect("/investor/collect-personal-details");
  }

  const props: VerificationInitiatedPropsType = {};
  res.render("renderReactLayoutNew", {
    title: "Email verified",
    reactComp: EmailVerifiedPage,
    props: JSON.stringify(props),
    scriptName: "emailVerified"
  });
};

export const getResidencyCountryView = (req: CustomRequest, res: Response) => {
  const props: ResidencyCountryPropsType = {};
  res.render("renderReactLayoutNew", {
    title: "Residency Country",
    reactComp: ResidencyCountryPage,
    props: JSON.stringify(props),
    scriptName: "residencyCountry"
  });
};

export const getVerificationSuccessView = async (req: CustomRequest, res: Response) => {
  const props: VerificationSuccessPropsType = {};

  await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
    viewedKYCSuccessPage: true
  });

  res.render("renderReactLayoutNew", {
    title: "Verification Success",
    reactComp: VerificationSuccessPage,
    props: JSON.stringify(props),
    scriptName: "verificationSuccess"
  });
};

export const getInvestmentsView = async (req: CustomRequest, res: Response): Promise<void> => {
  const portfolios: PortfolioDocument[] = await appApiService.getM2M(API_ROUTES.portfolios.all(), req.user.id, {
    mode: PortfolioModeEnum.REAL,
    populateTicker: true
  });

  // Specify which is going to be the displayed portfolio
  const realPortfolio = portfolios[0];
  const { cash, currency } = realPortfolio;

  const portfolioValue = realPortfolio.currentTicker?.price || 0;
  const availableCash = cash?.[req.user.currency]?.available || 0;

  const [portfolioPricesByTenor, portfolioWithReturns, pendingRebalanceTransactions] = await Promise.all([
    appApiService.getM2M(API_ROUTES.portfolios.pricesByTenor(realPortfolio.id), req.user.id),
    appApiService.getM2M(API_ROUTES.portfolios.idWithReturnsByTenor(realPortfolio.id), req.user.id),
    appApiService.getM2M(API_ROUTES.transactions.all(), req.user.id, {
      category: "RebalanceTransaction",
      status: ["Pending"] as TransactionStatusType[]
    })
  ]);

  const portfolioReturnsValues = portfolioWithReturns.returnsValues;
  const holdingsWithReturns = portfolioWithReturns.holdings;
  const upByValues = portfolioWithReturns.upByValues;

  const showSuccessOrderModal = req.query.showSuccessOrder === "true";
  const successOrderDocument = showSuccessOrderModal ? await _getSuccessfullyOrderDocument(req) : null;
  const props: DashboardPagePropsType = {
    availableCash,
    currency,
    portfolioPricesByTenor,
    holdingsWithReturns,
    portfolioId: realPortfolio.id,
    portfolioReturnsValues,
    portfolioValue,
    upByValues,
    hasPendingRebalanceTransactions: pendingRebalanceTransactions.length > 0,
    featureFlags: req.featureFlags,
    showSuccessOrderModal,
    successOrderDocument
  };

  return res.render("renderReactLayoutNew", {
    title: "Investments",
    subtitle: "Your portfolio summary",
    activePage: "investments",
    reactComp: InvestmentsPage,
    props: JSON.stringify(props),
    scriptName: "investments"
  });
};

export const getInvestmentActivityView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const [activity, automations, investmentProducts, providersResponse]: [
    TransactionActivityItemType[],
    AutomationDocument[],
    InvestmentProductDocument[],
    IResult<ProviderType>
  ] = await Promise.all([
    appApiService.getM2M(API_ROUTES.users.investmentActivity(), req.user.id),
    appApiService.getM2M(API_ROUTES.automations.all(), req.user.id),
    appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id, {
      populateTicker: true
    }),
    truelayerPaymentsClient.getProviders()
  ]);

  const topUpAutomation = automations.find(
    (automation) => automation.category === "TopUpAutomation"
  ) as TopUpAutomationDocument;
  const savingsTopUpAutomation = automations.find(
    (automation) => automation.category === "SavingsTopUpAutomation"
  ) as SavingsTopUpAutomationDocument;
  const truelayerProviders = providersResponse.results;

  const props: InvestmentActivityPropsType = {
    user,
    activity,
    investmentProducts,
    truelayerProviders,
    topUpAutomation,
    savingsTopUpAutomation
  };

  return res.render("renderReactLayoutNew", {
    title: "Investment Activity",
    reactComp: InvestmentActivityPage,
    props: JSON.stringify(props),
    scriptName: "investmentActivity"
  });
};

export const getAutopilotView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const [activeOrPendingMandates, automations, linkedBankAccounts, userSavings] = await Promise.all([
    appApiService.getM2M(API_ROUTES.mandates.all(), req.user.id, {
      category: "Top-Up",
      includeInactive: false
    }),
    appApiService.getM2M(API_ROUTES.automations.all(), req.user.id),
    appApiService.getM2M(API_ROUTES.users.linkedBankAccounts(), req.user.id),
    appApiService.getM2M(API_ROUTES.savingsProducts.me(), req.user.id)
  ]);

  const activeTopUpAutomation = (automations as AutomationDocument[]).find(
    (automation) => automation.status !== "Inactive" && automation.category === "TopUpAutomation"
  ) as TopUpAutomationDocument;
  const activeSavingsTopUpAutomations = (automations as AutomationDocument[]).filter(
    (automation) => automation.status !== "Inactive" && automation.category === "SavingsTopUpAutomation"
  ) as SavingsTopUpAutomationDocument[];
  const rebalanceAutomation = (automations as AutomationDocument[]).find(
    (automation) => automation.category === "RebalanceAutomation"
  );

  const props: AutopilotPropsType = {
    user,
    activeTopUpAutomation,
    activeSavingsTopUpAutomations,
    rebalanceAutomation,
    activeOrPendingMandates,
    linkedBankAccounts,
    userSavings,
    activePage: "autopilot"
  };

  return res.render("renderReactLayoutNew", {
    title: "Autopilot",
    reactComp: AutopilotPage,
    props: JSON.stringify(props),
    scriptName: "autopilot"
  });
};

export const getHomeView = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;

  const promotionalSavingsProductId = ConfigUtil.getSavingsProductUniverse(
    user?.companyEntity
  ).promotionalSavingsProduct;

  const [dailySummaries, savingsProductFeeDetails, pendingTransactions] = await Promise.all([
    appApiService.getM2M(API_ROUTES.users.summaries(), req.user.id),
    appApiService.getM2M(API_ROUTES.savingsProducts.feeDetails(), req.user.id, {
      savingsProductId: promotionalSavingsProductId
    }),
    appApiService.getM2M(API_ROUTES.transactions.pending.all(), req.user.id)
  ]);

  let banners = await _getBannerIdsSortedByOrder(req);
  if (pendingTransactions.length > 0) {
    banners = [{ bannerId: BannerEnum.PendingTransaction, data: { title: "Pending transactions" } }, ...banners];
  }

  const props: DailySummaryPagePropsType = {
    user,
    dailySummaries,
    savingsProductFeeDetails,
    banners,
    activePage: "home"
  };

  return res.render("renderReactLayoutNew", {
    title: "Home",
    reactComp: DailySummaryPage,
    props: JSON.stringify(props),
    scriptName: "home"
  });
};

export const getTransactionView = async (req: CustomRequest, res: Response): Promise<void> => {
  const TRANSACTION_HANDLERS: Record<
    TransactionCategoryType,
    (req: CustomRequest, res: Response, transaction: TransactionDocument) => void
  > = {
    AssetTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    DepositCashTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    DividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    WithdrawalCashTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    RebalanceTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    ChargeTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    RevertRewardTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    CashbackTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    WealthyhoodDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsTopupTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsWithdrawalTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    SavingsDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    AssetDividendTransaction: (req: CustomRequest, res: Response): void => res.redirect("/"),
    StockSplitTransaction: (req: CustomRequest, res: Response): void => res.redirect("/")
  };

  const transactionId = req.params.id;
  if (!validator.isMongoId(transactionId)) {
    return res.redirect("back");
  }

  const transaction: TransactionDocument = await appApiService.getM2M(
    API_ROUTES.transactions.id(transactionId),
    req.user.id
  );

  if (!transaction) {
    req.flash("error", "Transaction does not exist");
    return res.redirect("back");
  }

  TRANSACTION_HANDLERS[transaction.category](req, res, transaction);
};

export const deactivateBankAccount = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const bankAccountId = req.params.id;
    await appApiService.postM2M(API_ROUTES.bankAccounts.deactivate(bankAccountId), req.user.id);
    res.status(200).json({ message: "Your bank account has been deactivated" });
  } catch (err) {
    res.status(500).json({
      message: "Could not deactivate bank account"
    });
  }
};

export const closeAccount = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.user.id;
  await appApiService.postM2M(API_ROUTES.userDataRequests.all(), userId, {
    requestType: "disassociation"
  });

  res.sendStatus(204);
};

export const setReferrer = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.user.id;
  const { referralCode } = req.body;

  await appApiService.postM2M(API_ROUTES.users.setReferrer(), userId, {
    referralCode
  });

  res.sendStatus(204);
};

export const subscribeWealthybites = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.user.id;
  const { didSubscribe } = req.body;

  await appApiService.postM2M(
    API_ROUTES.users.subscribeWealthybites(),
    userId,
    {},
    {
      didSubscribe: didSubscribe
    }
  );

  res.sendStatus(204);
};

export const inviteFriend = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { targetUserEmail } = req.body;
    await appApiService.postM2M(API_ROUTES.rewardInvitations.all(), req.user.id, {
      targetUserEmail
    });
    res.sendStatus(200);
  } catch (err) {
    res.status(500).json({
      message: "Could not invite friend"
    });
  }
};

export const resetReferralCode = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const referralCode = await appApiService.postM2M(API_ROUTES.referralCodes.all(), req.user.id);

    res.status(200).json({ referralCode: referralCode.code });
  } catch (err) {
    res.status(500).json({
      message: "Could not invite friend"
    });
  }
};

export const sendDeletionFeedback = async (req: CustomRequest, res: Response): Promise<void> => {
  const userId = req.user.id;
  const { deletionFeedback } = req.body;

  await appApiService.postM2M(API_ROUTES.users.deletionFeedback(), userId, {
    feedback: deletionFeedback
  });

  res.sendStatus(204);
};

export const getOpenAccountView = async (req: CustomRequest, res: Response): Promise<void> => {
  const notSeenGifts = (await appApiService.getM2M(API_ROUTES.gifts.all(), req.user.id, {
    hasViewedAppModal: false
  })) as GiftDocument[];

  await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
    viewedWelcomePage: true
  });

  const props: OpenAccountPagePropsType = {
    requestLocale: req.locale
  };

  if (notSeenGifts?.length > 0) {
    props.notSeenGift = notSeenGifts[0];

    await appApiService.postM2M(API_ROUTES.gifts.id(notSeenGifts[0].id), req.user.id, {
      hasViewedAppModal: true
    });
  }

  res.render("renderReactLayoutNew", {
    title: "Open Account!",
    reactComp: OpenAccountPage,
    props: JSON.stringify(props),
    scriptName: "openAccount"
  });
};

export const setEmploymentInfo = async (req: CustomRequest, res: Response): Promise<void> => {
  const { incomeRangeId, employmentStatus, industry, sourcesOfWealth } = req.body;
  const body = { incomeRangeId, employmentStatus, industry, sourcesOfWealth };
  try {
    await appApiService.postM2M(API_ROUTES.users.employmentInfo(), req.user.id, body);
    res.sendStatus(204);
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: "An error occurred, please try to verify later."
    });
  }
};

export const getBilling = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const chargeTransactions = await appApiService.getM2M(API_ROUTES.transactions.billingActivity(), req.user.id);
    return res.status(200).json(chargeTransactions);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const initiateStripePaymentMethod = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;

    const { clientSecret } = await appApiService.postM2M(API_ROUTES.paymentMethods.initiateStripe(), userId);

    res.status(200).json({ clientSecret });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const initiateStripeSubscription = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;

    const data = await appApiService.postM2M(API_ROUTES.subscriptions.initiateStripe(), userId, req.body);

    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const completeStripeSubscription = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;

    const { clientSecret } = await appApiService.postM2M(
      API_ROUTES.subscriptions.completeStripe(),
      userId,
      req.body
    );

    res.status(200).json({ clientSecret });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const updateSubscriptionPaymentMethod = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;

    await appApiService.postM2M(API_ROUTES.subscriptions.updatePaymentMethod(), userId, req.body);

    res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getInvestorInfo = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;
    const populate = req.query.populate;

    const investorDetails: UserDocument = await appApiService.getM2M(API_ROUTES.users.me(), userId, { populate });

    res.status(200).json(investorDetails);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getIdVerificationToken = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const kycInitiationResponse: {
      sdkToken: string;
    } = await appApiService.postM2M(API_ROUTES.kycOperations.initiate(), req.user.id);

    res.status(200).json(kycInitiationResponse);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getIdVerificationView = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const kycInitiationResponse: {
      sdkToken: string;
    } = await appApiService.postM2M(API_ROUTES.kycOperations.initiate(), req.user.id);

    res.render("renderReactLayoutNew", {
      title: "ID verification",
      reactComp: IdVerificationPage,
      props: JSON.stringify({
        token: kycInitiationResponse.sdkToken
      }),
      scriptName: "idVerification"
    });
  } catch (err) {
    /**
     * If there is a conflict move user to success screen to continue polling.
     */
    if (err.response?.status === 409) {
      res.redirect("/investor/id-verification/pending");
      return;
    }

    captureException(err);
    logger.error("Failed to initiate Sumsub.", {
      module: "InvestorController",
      method: "getIdVerificationView",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });

    res.redirect("/");
  }
};

export const getIdVerificationPollingView = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    logger.info(`User ${req.user.id} passed ID verification`, {
      module: "InvestorController",
      method: "getIdVerificationPollingView",
      data: {
        userId: req.user.id,
        queryParams: req.query
      }
    });

    res.render("renderReactLayoutNew", {
      title: "ID Verification",
      reactComp: IdVerificationPollingPage,
      props: JSON.stringify({}),
      scriptName: "idVerificationPolling"
    });
  } catch (err) {
    captureException(err);
    logger.error("Syncing KYC operation failed.", {
      module: "InvestorController",
      method: "getIdVerificationPollingView",
      userEmail: req.user.email,
      data: {
        error: err
      }
    });
    res.redirect("/");
  }
};

export const getIdVerificationResumeView = async (req: CustomRequest, res: Response): Promise<void> => {
  res.render("renderReactLayoutNew", {
    title: "ID Verification",
    reactComp: IdVerificationResumePage,
    props: JSON.stringify({}),
    scriptName: "idVerificationResume"
  });
};

export const syncIdVerification = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const kycOperation: KycOperationDocument = await appApiService.getM2M(
      API_ROUTES.kycOperations.me(),
      req.user.id
    );

    res.status(200).json({
      id: kycOperation.id,
      owner: kycOperation.owner,
      status: kycOperation.status,
      isProcessed: kycOperation.isProcessed
    });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getSavingsTopupSuccessView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: SavingsTopupSuccessPagePropsType = {
    redirectUri: req.query.redirect as string
  };

  res.render("renderReactLayoutNew", {
    title: "Savings Topup Success!",
    reactComp: SavingsTopupSuccessPage,
    props: JSON.stringify(props),
    scriptName: "savingsTopupSuccess"
  });
};

export const getSavingsWithdrawalSuccessView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: SavingsWithdrawalSuccessPagePropsType = {
    redirectUri: req.query.redirect as string
  };

  res.render("renderReactLayoutNew", {
    title: "Savings Withdrawal Success!",
    reactComp: SavingsWithdrawalSuccessPage,
    props: JSON.stringify(props),
    scriptName: "savingsWithdrawalSuccess"
  });
};

export const getBankProviders = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const bankProviders: BankProviderType[] = await appApiService.getM2M(
      API_ROUTES.bankAccounts.bankProviders(),
      req.user.id,
      { scope: req.query.scope }
    );

    res.status(200).json(bankProviders);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getGoCardlessAuthUrl = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const bankId = req.query.bankId;
    const redirectPage = req.query.originPage;
    const modal = req.query.originModal as OtherModalType;
    const asset = req.query.displayedAsset;
    const providerId = req.query.providerId;
    const selectedPrice = req.query.selectedPrice;
    const environment = envIsProd() ? "production" : envIsDev() ? "development" : "staging";

    const redirectUriState = Object.entries({
      redirectPage,
      modal,
      asset,
      providerId,
      selectedPrice,
      env: environment
    })
      .filter(([_, value]) => !!value)
      .map(([key, value]) => `${key}_${value}`)
      .join("__");

    const data: { redirectUri: string } = await appApiService.postM2M(
      API_ROUTES.bankAccounts.initiateBankLinking(),
      req.user.id,
      { bankId, redirectUriState }
    );

    res.status(200).json({ redirectUri: data.redirectUri });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const skipPortfolioCreation = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    await appApiService.postM2M(API_ROUTES.users.me(), req.user.id, {
      skippedPortfolioCreation: true
    });
    res.sendStatus(204);
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message: "An error occurred, please try later."
    });
  }
};

export const getAnalystInsights = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const page = req.query.page;

    const response: PaginatedApiResponse<ArticleDataType> = await appApiService.getM2M(
      API_ROUTES.learningHub.analystInsights(),
      req.user.id,
      { page },
      { rawResponse: true }
    );

    res.status(200).json({ pagination: response.pagination, data: response.data });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occured" });
  }
};

export const getModalPrompts = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const modalPrompts: ModalPromptType[] = await appApiService.getM2M(API_ROUTES.users.prompts(), req.user.id, {
      type: "modal"
    });

    res.status(200).json(modalPrompts);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

const _getSuccessfullyOrderDocument = async (req: CustomRequest): Promise<AssetTransactionDocument> => {
  let successOrderDocument: AssetTransactionDocument;
  try {
    if (req.query.assetTransactionId) {
      successOrderDocument = await appApiService.getM2M(
        API_ROUTES.transactions.assets.id(String(req.query.assetTransactionId)),
        req.user.id,
        {
          populateOrders: true,
          populateForeignCurrencyRates: true
        }
      );
    } else if (req.query.depositId) {
      successOrderDocument = await appApiService.getM2M(
        API_ROUTES.transactions.getAssetTransactionLinkedToDeposit(String(req.query.depositId)),
        req.user.id
      );
    }
  } catch (err) {
    logger.error("No transaction ID is provided", {
      module: "portfolioController",
      method: "_getSuccessfullyOrderDocument"
    });
  }

  return successOrderDocument;
};

const _getBannerIdsSortedByOrder = async (req: CustomRequest): Promise<BannerDataType[]> => {
  let response: {
    bannerPrompts: (BannerDataType & {
      order: number;
    })[];
  };

  try {
    response = await appApiService.getM2M(API_ROUTES.users.prompts(), req.user.id, {
      type: "banner"
    });
  } catch (error) {
    response = {
      bannerPrompts: [
        {
          order: 0,
          bannerId: BannerEnum.EarnFreeShares
        },
        {
          order: 1,
          bannerId: BannerEnum.NewToInvesting
        },
        {
          order: 2,
          bannerId: BannerEnum.InvestingInETFs
        }
      ]
    };

    captureException(error);
    logger.error("Request go get banners failed. Using fallback", {
      module: "portfolioController",
      method: "_getBannerIdsSortedByOrder",
      data: error
    });
  }

  return response.bannerPrompts
    .filter((banner) => !!BannerEnum[banner.bannerId as BannerEnum])
    .sort((a, b) => a.order - b.order)
    .map((banner) => {
      const { bannerId, data } = banner;
      return { bannerId: bannerId as BannerEnum, data };
    });
};
