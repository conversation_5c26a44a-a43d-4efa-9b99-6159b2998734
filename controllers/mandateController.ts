import { CustomRequest } from "custom";
import { Response } from "express";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { captureException } from "@sentry/node";

export const getMandates = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const includeInactive = req.query.includeInactive ?? true;
    const category = req.query.category;

    const mandates = await appApiService.getM2M(API_ROUTES.mandates.all(), req.user.id, {
      includeInactive,
      category
    });

    res.status(200).json(mandates);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch mandates." });
  }
};
