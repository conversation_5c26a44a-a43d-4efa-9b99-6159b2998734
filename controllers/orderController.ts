import { CustomRequest } from "custom";
import { Response } from "express";
import { captureException } from "@sentry/node";
import appApiService, { API_ROUTES } from "../services/appApiService";
import logger from "../services/loggerService";

export const cancelOrder = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const orderId = req.params.id;

    await appApiService.postM2M(API_ROUTES.orders.cancel(orderId), req.user._id);

    res.status(200).json({
      redirectUri: "/investor/cancellation-success"
    });
  } catch (err) {
    logger.error("Order cancel failed", {
      module: "OrderController",
      method: "cancelOrder",
      userEmail: req.user.email,
      data: err
    });
    captureException(err);
    res.status(500).json({
      message: "An error occurred when cancelling your order. Please try later."
    });
  }
};

export const generateTradeConfirmation = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const orderId = req.params.id;

    const { fileUri } = await appApiService.postM2M(API_ROUTES.orders.tradeConfirmations(orderId), req.user.id);

    res.status(200).json({ fileUri });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch account statement." });
  }
};
