import { PortfolioPricesByTenorType } from "../react-views/types/portfolio";
import { captureException } from "@sentry/node";
import axios from "axios";
import { CustomRequest } from "custom";
import { Request, Response } from "express";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { HoldingsType, PortfolioWithReturnsByTenorDocument } from "../models/Portfolio";
import AssetDiscoveryPage, { AssetDiscoveryPropsType } from "../react-views/pages/assetDiscoveryPage";
import PortfolioCreationSuccessPage, {
  PortfolioCreationSuccessPagePropsType
} from "../react-views/pages/portfolioCreationSuccessPage";
import PortfolioTargetPage, { PortfolioTargetPagePropsType } from "../react-views/pages/portfolioTargetPage";
import PortfolioPersonalisationPage, {
  PortfolioPersonalisationPropsType
} from "../react-views/pages/portfolioPersonalisationPage";
import PortfolioCreationPage, { PortfolioCreationPropsType } from "../react-views/pages/portfolioCreationPage";
import PortfolioCreationRoboAdvisorPage, {
  PortfolioCreationRoboAdvisorPropsType
} from "../react-views/pages/portfolioCreationRoboAdvisorPage";
import PortfolioCreationTemplateInfoPage, {
  PoPortfolioCreationTemplateInfoPropsType
} from "../react-views/pages/portfolioCreationTemplateInfoPage";
import PortfolioSetupPage, { PortfolioSetupPropsType } from "../react-views/pages/portfolioSetupPage";
import PortfolioSetupAsStepPage, {
  PortfolioSetupAsStepPagePropsType
} from "../react-views/pages/portfolioSetupAsStepPage";
import { formatCurrency } from "../react-views/utils/currencyUtil";
import logger from "../services/loggerService";
import PortfolioService from "../services/portfolioService";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { GiftDocument } from "../models/Gift";
import { AssetDiscoveryDataType } from "../react-views/types/assetDiscovery";
import EdgeServerApiService, { EdgeServerApiRoutes } from "../services/edgeServerApiService";
import { ServiceError } from "../utils/errorUtil";
import ConfigUtil from "../utils/configUtil";
import { AllocationType } from "../react-views/types/allocation";
import { ROBO_ADVISOR_CONFIG, RoboAdvisorRiskLevelEnum } from "../react-views/configs/roboAdvisorConfig";

const { RISK_CONFIG } = investmentUniverseConfig;

const PROJECTION_FOR_ALLOCATION_URL_V2 = `${process.env.STATISTICS_SERVICE_URL}/v2/portfolios/projection`;
const PORTFOLIO_PAST_PERFORMANCE_URL_V3 = `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/past-performance-all`;

type PortfolioProjectionType = {
  labelData: {
    risk: number;
    returnPercentage: number;
    returnValue: {
      normalScenario: number;
      worstScenario: number;
      bestScenario: number;
    };
  };
  chartData: {
    bestScenario: number[];
    normalScenario: number[];
    worstScenario: number[];
  };
};

/**
 * PRIVATE UTILS
 */
async function getAllocationProjectionV2(params: any): Promise<PortfolioProjectionType> {
  const res = await axios.get(PROJECTION_FOR_ALLOCATION_URL_V2, { params });
  return res.data.projection;
}

async function _getPortfolioPastPerformanceV3(
  initial: string,
  allocation: Record<string, string>
): Promise<Record<string, { date: Date; value: number }[]>> {
  const res = await axios({
    method: "GET",
    url: PORTFOLIO_PAST_PERFORMANCE_URL_V3,
    headers: { "Content-Type": "application/json" },
    params: {
      tenor: "0",
      initial,
      ...allocation
    }
  });
  const { total_past_performance_all } = res.data;
  return Object.fromEntries(
    Object.entries(total_past_performance_all).map(
      ([tenor, total_past_performance]: [string, Record<string, number>]) => {
        return [
          tenor,
          Object.entries(total_past_performance).map(([timestampStr, value]: [string, number]) => ({
            date: new Date(parseInt(timestampStr)),
            value: Math.round(value)
          })) as { date: Date; value: number }[]
        ];
      }
    )
  );
}

export const getPortfolioWithReturnsByTenor = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const portfolioWithReturnsByTenor: PortfolioWithReturnsByTenorDocument = await appApiService.getM2M(
      API_ROUTES.portfolios.idWithReturnsByTenor(req.params.id),
      req.user.id
    );
    res.status(200).json(portfolioWithReturnsByTenor);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "An error occurred. Please try later."
    });
  }
};

export const getAvailableHoldings = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const availableHoldings: HoldingsType[] = await appApiService.getM2M(
      API_ROUTES.portfolios.availableHoldings(req.params.id),
      req.user.id
    );
    res.status(200).json(availableHoldings);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "An error occurred. Please try later."
    });
  }
};

/**
 * PUBLIC CONTROLLER METHODS
 */

/**
 * @description Handles cash-to-portfolio conversion during portfolio top-up.
 * It also handles the transition from simulated to real portfolio mode and performs actual execution
 * of the investments.
 *
 * Steps:
 * - Safety checks to allow action to take place
 * - Asset transaction creation
 * - Creation of corresponding buy orders (wealthkernel & db storage)
 * - Linking of orders to parent asset transaction
 * - Deduct available investor cash
 */
export const buyPortfolio = async (req: CustomRequest, res: Response): Promise<void> => {
  const user = req.user;
  const portfolioId = req.params.id;
  const { orderAmount } = req.body;
  const paymentMethod = (req.query.paymentMethod as "cash" | "gift") ?? "cash";

  try {
    if (user.portfolioConversionStatus == "inProgress") {
      res.status(403).json({ message: "An error occurred when executing your investment. Please try later." });
      return;
    }

    const transaction = await appApiService.postM2M(
      API_ROUTES.portfolios.buy(portfolioId),
      req.user._id,
      {
        ...req.body
      },
      { paymentMethod, ...req.query }
    );

    const message = `Your order to buy ${formatCurrency(
      orderAmount,
      req.user.currency,
      ConfigUtil.getDefaultUserLocale(req.user.residencyCountry)
    )} of assets, was placed successfully!`;
    req.flash("success", message);
    res.status(200).json({
      message,
      redirectUri: `/investor/investments?showSuccessOrder=true&assetTransactionId=${transaction._id}`
    });

    return;
  } catch (err) {
    logger.error("Portfolio buy failed", {
      module: "PortfolioController",
      method: "buyPortfolio",
      userEmail: req.user.email,
      data: err
    });
    captureException(err);
    res.status(500).json({
      message: "An error occurred when executing your investment. Please try later."
    });
  }
};

export const createOrUpdatePortfolioAllocation = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const portfolioId = req.params.id;
    const { allocation, flow } = req.body;

    const updatedPortfolio = await appApiService.postM2M(
      API_ROUTES.portfolios.allocation(portfolioId),
      req.user.id,
      {
        allocation,
        flow
      }
    );
    return res.status(200).json(updatedPortfolio);
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

export const getAllocationProjectionAPI = async (req: Request, res: Response): Promise<void> => {
  const projection = await getAllocationProjectionV2(req.query);
  res.json({ projection });
};

export const getOptimalAllocationAPI = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { allocation } = await appApiService.getM2M(API_ROUTES.statistics.optimalAllocation(), req.user.id, {
      ...req.query
    });

    res.json({ allocation });
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getPastPerformance = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2M(API_ROUTES.statistics.pastPerformance(), req.user.id, {
      ...req.query
    });

    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getFuturePerformance = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2M(API_ROUTES.statistics.futurePerformance(), req.user.id, {
      ...req.query
    });

    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getRestrictedHoldings = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2M(API_ROUTES.portfolios.restrictedHoldings(req.params.id), req.user.id);
    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getRestrictedAssets = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2M(API_ROUTES.portfolios.restrictedAssets(req.params.id), req.user.id, {
      assetId: req.query.assetId
    });
    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getFuturePerformanceMonteCarlo = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.getM2M(API_ROUTES.statistics.futurePerformanceMonteCarlo(), req.user.id, {
      ...req.query
    });

    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getPortfolioBackTesting = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { initial, ...allocation } = req.query as Record<string, string>;
    const pastPerformance = await _getPortfolioPastPerformanceV3(initial, allocation);

    res.status(200).json({ pastPerformance });
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const getAssetDiscoveryView = async (req: CustomRequest, res: Response): Promise<void> => {
  const [assetDiscoveryDataResponse, investmentProducts] = await Promise.all([
    EdgeServerApiService.get<{ discovery: AssetDiscoveryDataType }>(EdgeServerApiRoutes.appConfig),
    appApiService.getM2M(API_ROUTES.investmentProducts.all(), req.user.id, {
      populateTicker: true
    })
  ]);

  const props: AssetDiscoveryPropsType = {
    assetDiscoveryData: assetDiscoveryDataResponse.discovery,
    featureFlags: req.featureFlags,
    investmentProducts,
    fromPage: (req.query.fromPage as "home" | "investments") ?? "investments"
  };

  return res.render("renderReactLayoutNew", {
    title: "Asset Discovery",
    activePage: "asset-discovery",
    reactComp: AssetDiscoveryPage,
    props: JSON.stringify(props),
    scriptName: "assetDiscovery"
  });
};

export const getCreationSuccessView = async (req: CustomRequest, res: Response): Promise<void> => {
  const unusedSeenGifts = (await appApiService.getM2M(API_ROUTES.gifts.all(), req.user.id, {
    used: false
  })) as GiftDocument[];

  const props: PortfolioCreationSuccessPagePropsType = {
    user: req.user,
    isGifted: unusedSeenGifts.length > 0,
    gift: unusedSeenGifts?.[0]
  };

  return res.render("renderReactLayoutNew", {
    title: "Success!",
    activePage: "portfolio-creation-success",
    reactComp: PortfolioCreationSuccessPage,
    props: JSON.stringify(props),
    scriptName: "portfolioCreationSuccess"
  });
};

export const getTargetPortfolioView = async (req: CustomRequest, res: Response): Promise<void> => {
  const realPortfolio = req.user?.portfolios?.[0];
  if (!realPortfolio.isTargetAllocationSetup) {
    return res.redirect("/portfolios/creation?shouldSetupTargetAllocation=true");
  } else if (req.user.isConvertingPortfolio) {
    req.flash("error", "Target view is not available while your portfolio is getting invested.");
    return res.redirect("/");
  }

  const [rebalanceAutomations, pendingRebalanceTransactions] = await Promise.all([
    appApiService.getM2M(API_ROUTES.automations.all(), req.user.id, { category: "RebalanceAutomation" }),
    appApiService.getM2M(API_ROUTES.transactions.pendingRebalances(), req.user.id)
  ]);

  const hasPendingRebalance = pendingRebalanceTransactions.length > 0;
  const hasActiveRebalanceAutomation = rebalanceAutomations?.[0]?.active;

  const props: PortfolioTargetPagePropsType = {
    portfolio: realPortfolio,
    user: req.user,
    hasActiveRebalanceAutomation,
    hasPendingRebalance,
    featureFlags: req.featureFlags,
    triggerTargetAllocationSuccessModal: req.query.triggerTargetAllocationSuccessModal === "true"
  };

  return res.render("renderReactLayoutNew", {
    title: "Target Portfolio",
    subtitle: "Update your portfolio",
    activePage: "portfolio-target",
    reactComp: PortfolioTargetPage,
    props: JSON.stringify(props),
    scriptName: "portfolioTarget"
  });
};

export const getPersonalisationView = async (req: CustomRequest, res: Response): Promise<void> => {
  const realPortfolio = req.user?.portfolios?.[0];
  // Defaults to true
  const useSavedPersonalisationPreferencesFlag = req.query?.useSavedPersonalisationPreferences !== "false";

  let savedPersonalisationPreferences: {
    assetClasses: investmentUniverseConfig.AssetClassType[];
    geography: investmentUniverseConfig.InvestmentGeographyType;
    risk: number;
    sectors: investmentUniverseConfig.InvestmentSectorType[];
  } = {
    assetClasses: [],
    geography: "global",
    risk: RISK_CONFIG.default,
    sectors: []
  };
  if (PortfolioService.isPortfolioPersonalised(realPortfolio) && useSavedPersonalisationPreferencesFlag) {
    savedPersonalisationPreferences = realPortfolio.personalisationPreferences;
  }

  const props: PortfolioPersonalisationPropsType = {
    portfolioId: realPortfolio.id,
    savedPersonalisationPreferences
  };

  res.render("renderReactLayoutNew", {
    title: "Personalise your Portfolio",
    reactComp: PortfolioPersonalisationPage,
    props: JSON.stringify(props),
    scriptName: "portfolioPersonalisation"
  });
};

export const getPortfolioCreationView = async (req: CustomRequest, res: Response): Promise<void> => {
  const isRoboAdvisorEnabled = req.user.isRoboAdvisorEnabled;

  const props: PortfolioCreationPropsType = {
    isRoboAdvisorEnabled,
    shouldSetupTargetAllocation: req.query?.shouldSetupTargetAllocation === "true"
  };

  res.render("renderReactLayoutNew", {
    title: "Portfolio Creation",
    reactComp: PortfolioCreationPage,
    props: JSON.stringify(props),
    scriptName: "portfolioCreation"
  });
};

export const getPortfolioCreationRoboAdvisorView = async (req: CustomRequest, res: Response): Promise<void> => {
  const isRoboAdvisorEnabled = req.user.isRoboAdvisorEnabled;
  const realPortfolio = req.user?.portfolios?.[0];

  if (!isRoboAdvisorEnabled) {
    res.redirect("/portfolios/creation");
    return;
  }

  const props: PortfolioCreationRoboAdvisorPropsType = {
    realPortfolio
  };

  res.render("renderReactLayoutNew", {
    title: "Portfolio Creation Robo Advisor",
    reactComp: PortfolioCreationRoboAdvisorPage,
    props: JSON.stringify(props),
    scriptName: "portfolioCreationRoboAdvisor"
  });
};

export const getPortfolioCreationTemplateInfoView = async (req: CustomRequest, res: Response): Promise<void> => {
  const props: PoPortfolioCreationTemplateInfoPropsType = {
    useSavedPersonalisationPreferences: req.query.useSavedPersonalisationPreferences as string
  };

  res.render("renderReactLayoutNew", {
    title: "Portfolio Creation Template",
    reactComp: PortfolioCreationTemplateInfoPage,
    props: JSON.stringify(props),
    scriptName: "portfolioCreationTemplateInfo"
  });
};

export const getPortfolioSetupView = async (req: CustomRequest, res: Response): Promise<void> => {
  const realPortfolio = req.user?.portfolios?.[0];
  const roboAdvisorRiskLevel = req.query.roboAdvisorRiskLevel as RoboAdvisorRiskLevelEnum;

  let initialAllocation: AllocationType;
  if (roboAdvisorRiskLevel) {
    initialAllocation = { [ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].assetId]: 100 };
  }

  const props: PortfolioSetupPropsType = {
    realPortfolio,
    initialAllocation,
    roboAdvisorRiskLevel
  };

  res.render("renderReactLayoutNew", {
    title: "Create your Portfolio",
    reactComp: PortfolioSetupPage,
    props: JSON.stringify(props),
    scriptName: "portfolioSetup"
  });
};

export const getPortfolioSetupAsStepView = async (req: CustomRequest, res: Response): Promise<void> => {
  const realPortfolio = req.user?.portfolios?.[0];

  const props: PortfolioSetupAsStepPagePropsType = { realPortfolio };

  res.render("renderReactLayoutNew", {
    title: "Modify your Portfolio",
    reactComp: PortfolioSetupAsStepPage,
    props: JSON.stringify(props),
    scriptName: "portfolioSetupAsStep"
  });
};

/**
 * @description Saves the investor's personalisation preferences to his/hers virtual portfolio,
 * and creates a real portfolio with these preferences. If any preference is missing,
 * then default values are set. The default preferences are:
 * {
 *   assetClasses: AssetClassArray,
 *   geography: "global",
 *   sectors: [],
 *   risk: RISK_CONFIG.default / RISK_CONFIG.maxLevel
 * }
 *
 * @param req The request, where
 *    req.params.id is the ID of the portfolio to be updated, and
 *    req.body.personalisationPreferences are the preferences
 * @param res The response
 * @returns
 *   500 if a server error occurs
 *   201 on successfully saving the personalisation preferences
 */
export const submitPersonalisationPreferences = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const portfolioId = req.params.id;
    const { personalisationPreferences } = req.body;

    await appApiService.postM2M(API_ROUTES.portfolios.preferences(portfolioId), req.user.id, {
      personalisationPreferences
    });
    res.sendStatus(201);
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
  }
};

export const updatePortfolio = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const portfolioId = req.params.id;
    const { pendingOrders, gift } = req.body;
    const paymentMethod: "cash" | "gift" = (req.query.paymentMethod as "cash" | "gift") ?? "cash";

    // api request spec body property orderType changed to side ,so we fix  body before request
    const newPendingOrders = Object.fromEntries(
      Object.entries(pendingOrders).map(
        ([assetId, pendingOrder]: [
          investmentUniverseConfig.AssetType,
          { orderType: "buy" | "sell"; quantity?: number; money?: number }
        ]) => {
          const newPendingOrder: { side: "buy" | "sell"; quantity?: number; money?: number } = {
            side: pendingOrder.orderType
          };
          if (pendingOrder.orderType == "sell") {
            newPendingOrder.quantity = pendingOrder.quantity;
          }
          if (pendingOrder.orderType == "buy") {
            newPendingOrder.money = pendingOrder.money;
          }
          return [assetId, newPendingOrder];
        }
      )
    );

    const body: any = {
      pendingOrders: newPendingOrders
    };

    if (gift) {
      body.gift = gift;
    }

    await appApiService.postM2M(API_ROUTES.portfolios.submitOrders(portfolioId), req.user.id, body, {
      paymentMethod,
      ...req.query
    });
    res.status(200).json({
      message: "Portfolio updated successfully!",
      redirectUri: "/investor/investment-success"
    });
    return;
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Portfolio couldn't be updated" });
    return;
  }
};

/**
 * Withdraws an amount from the user's portfolio's available cash.
 *
 * @param req The request needs to have the designated portfolio ID as a parameter, and the amount (in POUNDS) in the body.
 * @param res Only the status is sent as a response.
 * @returns
 *    400 if the portfolio ID is invalid
 *    400 if the amount is not a number
 *    403 if the user doesn't own the portfolio
 *    403 if the portfolio is virtual
 *    403 if the withdrawal amount is greater than the portfolio's available cash
 *    500 if an unknown error occurred
 *    204 if the withdrawal request is completed successfully
 */
export const withdraw = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const portfolioId = req.params.id;
    const { amount, bankAccountId } = req.body;

    await appApiService.postM2M(API_ROUTES.portfolios.withdraw(portfolioId), req.user.id, {
      amount,
      bankAccountId
    });
    res.sendStatus(204);
    return;
  } catch (err) {
    captureException(err);
    res.sendStatus(500);
    return;
  }
};

/**
 * Initiates a rebalance transaction for the user's portfolio based on their current target allocation.
 *
 * @param req The request needs to have the designated portfolio ID as a parameter.
 * @param res Only the status is sent as a response.
 * @returns
 *    204 if the rebalance request is completed successfully
 *    500 if an error occurred
 */
export const rebalancePortfolio = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const portfolioId = req.params.id;

    await appApiService.postM2M(API_ROUTES.portfolios.rebalance(portfolioId), req.user.id);

    return res.sendStatus(204);
  } catch (err) {
    captureException(err);
    return res.sendStatus(500);
  }
};

/**
 * @description This is used in the 1-step deposit & save flow.
 */
export const topupSavingsPendingDeposit = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const { orderAmount, bankAccountId, bankAccount, bankLinkedFrom } = req.body;
    if (!bankAccountId && !bankAccount) {
      throw new ServiceError("Either a bank account ID or bank account data should be passed");
    }

    const { paymentUri } = await appApiService.postM2M(API_ROUTES.transactions.savings.deposit(), req.user.id, {
      paymentAmount: orderAmount,
      bankAccount,
      bankLinkedFrom,
      bankAccountId,
      depositAndInvest: true
    });

    req.flash("success", "Your savings topup has been created.");
    res.status(200).json({ message: "Your savings topup has been created!", redirectUri: paymentUri });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message:
        err instanceof ServiceError
          ? err.message
          : err.response?.data?.error?.message ?? "An error occurred, please try to make a payment later."
    });
  }
};

export const topupSavings = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const { orderAmount, savingsProductId } = req.body;

    const transaction = await appApiService.postM2M(
      API_ROUTES.portfolios.topupSavings(user.portfolios[0]._id),
      user.id,
      {
        orderAmount,
        savingsProductId
      }
    );

    req.flash("success", "Your deposit has been created.");
    res.status(200).json({
      message: "Your topup has been created!",
      transaction,
      redirectUri: "/investor/savings-topup-success?redirect=/"
    });
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message:
        err instanceof ServiceError
          ? err.message
          : err.response?.data?.error?.message ?? "An error occurred, please try to make a payment later."
    });
  }
};

export const withdrawSavings = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const { orderAmount, savingsProductId } = req.body;

    await appApiService.postM2M(API_ROUTES.portfolios.withdrawSavings(user.portfolios[0]._id), user.id, {
      orderAmount,
      savingsProductId
    });

    req.flash("success", "Your savings withdrawal has been created.");
    res.sendStatus(204);
  } catch (err) {
    captureException(err);
    res.status(400).json({
      message:
        err instanceof ServiceError
          ? err.message
          : err.response?.data?.error?.message ??
            "An error occurred, please try to make a savings withdrawal later."
    });
  }
};

export const getPricesByTenor = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const portfolioPricesByTenor: PortfolioPricesByTenorType = await appApiService.getM2M(
      API_ROUTES.portfolios.pricesByTenor(req.params.id),
      req.user.id
    );
    res.status(200).json(portfolioPricesByTenor);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "An error occurred. Please try later."
    });
  }
};
