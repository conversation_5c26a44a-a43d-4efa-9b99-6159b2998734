import { CustomRequest } from "custom";
import { Response } from "express";
import { RewardStatusType } from "../models/Reward";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { captureException } from "@sentry/node";

export const getRewards = async (req: CustomRequest, res: Response): Promise<Response> => {
  const status = req.query.status as RewardStatusType;
  const restrictedOnly = req.query.restrictedOnly;
  const rewards = await appApiService.getM2M(API_ROUTES.rewards.all(), req.user.id, { status, restrictedOnly });

  return res.status(200).json(rewards);
};

export const updateReward = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const rewardId = req.params.id;
    const { accepted, hasViewedAppModal } = req.body;
    await appApiService.postM2M(API_ROUTES.rewards.id(rewardId), req.user.id, {
      accepted,
      hasViewedAppModal
    });
    res.sendStatus(200);
  } catch (err) {
    res.status(500).json({
      message: "Could not update reward"
    });
  }
};

export const generateTradeConfirmation = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const rewardId = req.params.id;

    const { fileUri } = await appApiService.postM2M(API_ROUTES.rewards.tradeConfirmations(rewardId), req.user.id);

    res.status(200).json({ fileUri });
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to fetch account statement." });
  }
};
