import { Response } from "express";
import { captureException } from "@sentry/node";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { CustomRequest } from "custom";
import logger from "../services/loggerService";

const { SavingsProductArray } = savingsUniverseConfig;

export const getSavingsProductData = async (req: CustomRequest, res: Response): Promise<Response> => {
  const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
  try {
    if (savingsProductId && SavingsProductArray.includes(savingsProductId)) {
      const savingsProductData = await appApiService.getM2M(API_ROUTES.savingsProducts.data(), req.user.id, {
        savingsProductId: savingsProductId
      });
      return res.status(200).json(savingsProductData);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    logger.error(`Could not fetch savings product data ${savingsProductId}`, {
      module: "SavingsProductController",
      method: "getSavingsProductData"
    });
    return res.sendStatus(500);
  }
};

export const getSavingsProductFeeDetails = async (req: CustomRequest, res: Response): Promise<Response> => {
  const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
  try {
    if (savingsProductId && SavingsProductArray.includes(savingsProductId)) {
      const savingsProductFeeDetails = await appApiService.getM2M(
        API_ROUTES.savingsProducts.feeDetails(),
        req.user.id,
        {
          savingsProductId: savingsProductId
        }
      );
      return res.status(200).json(savingsProductFeeDetails);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    logger.error(`Could not fetch savings product fee details ${savingsProductId}`, {
      module: "SavingsProductController",
      method: "getSavingsProductFeeDetails"
    });
    return res.sendStatus(500);
  }
};

export const getSavingsProductActivity = async (req: CustomRequest, res: Response): Promise<Response> => {
  const savingsProductId = req.query.savingsProductId as savingsUniverseConfig.SavingsProductType;
  try {
    if (savingsProductId && SavingsProductArray.includes(savingsProductId)) {
      const limit = req.query?.limit;
      const savingsProductActivity = await appApiService.getM2M(
        API_ROUTES.savingsProducts.activity(),
        req.user.id,
        {
          limit,
          savingsProductId: savingsProductId
        }
      );
      return res.status(200).json(savingsProductActivity);
    } else {
      return res.sendStatus(400);
    }
  } catch (err) {
    captureException(err);
    logger.error(`Could not savings product activity ${savingsProductId}`, {
      module: "SavingsProductController",
      method: "getSavingsProductActivity"
    });
    return res.sendStatus(500);
  }
};
