import { CustomRequest } from "custom";
import { Response } from "express";
import { DepositCashTransactionDocument } from "../models/Transaction";
import { captureException } from "@sentry/node";
import appApiService, { API_ROUTES } from "../services/appApiService";
import logger from "../services/loggerService";
import { PortfolioModeEnum } from "../models/Portfolio";

export const getDeposits = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const deposits: DepositCashTransactionDocument[] = await appApiService.getM2M(
      API_ROUTES.transactions.deposits.all(),
      req.user.id,
      {
        status: [req.query.status],
        saltedgeCustomPaymentId: req.query.saltedgeCustomPaymentId
      }
    );
    res
      .status(200)
      // If the query status is not 'Cancelled' filter out cancelled transactions
      .json(
        deposits.filter(({ displayStatus }) => displayStatus !== "Cancelled" || req.query.status === "Cancelled")
      );
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getSingleAssetTransactionPreview = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.postM2M(API_ROUTES.transactions.preview(), req.user.id, req.body, {
      category: "AssetTransaction",
      portfolioId: req.user.portfolios[0].id,
      ...req.query
    });
    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getPortfolioTransactionPreview = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.postM2M(API_ROUTES.transactions.preview(), req.user.id, null, {
      category: "AssetTransaction",
      portfolioId: req.user.portfolios[0].id,
      ...req.query
    });
    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const getRebalanceTransactionPreview = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const data = await appApiService.postM2M(API_ROUTES.transactions.preview(), req.user.id, null, {
      category: "RebalanceTransaction",
      portfolioId: req.user.portfolios[0].id
    });
    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "An error occurred" });
  }
};

export const cancelTransaction = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const transactionId = req.params.id;

    await appApiService.postM2M(API_ROUTES.transactions.cancel(transactionId), req.user._id);

    res.status(200).json({
      redirectUri: "/investor/cancellation-success"
    });
  } catch (err) {
    logger.error("Transaction cancel failed", {
      module: "TransactionController",
      method: "cancelTransaction",
      userEmail: req.user.email,
      data: err
    });
    captureException(err);
    res.status(500).json({
      message: "An error occurred when cancelling your order. Please try later."
    });
  }
};

export const getWhDividends = async (req: CustomRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const realPortfolio = user.portfolios.find(({ mode }) => mode == PortfolioModeEnum.REAL);

    const params = {
      category: "WealthyhoodDividendTransaction",
      portfolio: realPortfolio.id,
      status: "Settled",
      hasViewedAppModal: false
    };

    const data = await appApiService.getM2M(API_ROUTES.transactions.all(), user._id, params);

    res.status(200).json(data);
  } catch (err) {
    captureException(err);
    res.status(500).json({
      message: "Failed to fetch wh dividends."
    });
  }
};

export const updateWhDividend = async (req: CustomRequest, res: Response): Promise<Response> => {
  try {
    const whDividendId = req.params.id;
    const { hasViewedAppModal } = req.body;

    await appApiService.postM2M(API_ROUTES.transactions.wealthyhoodDividends.id(whDividendId), req.user.id, {
      hasViewedAppModal
    });

    return res.sendStatus(200);
  } catch (err) {
    captureException(err);
    res.status(500).json({ message: "Failed to update wh dividend." });
  }
};
