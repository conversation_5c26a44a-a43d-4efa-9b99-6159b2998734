import { CustomRequest } from "custom";
import { Response } from "express";
import EmailDisposablePage, { EmailDisposablePropsType } from "../react-views/pages/emailDisposablePage";

export const getEmailDisposableView = (req: CustomRequest, res: Response): void => {
  if (req.oidc.isAuthenticated()) {
    if (req.user.emailDisposable) {
      const props: EmailDisposablePropsType = {};

      return res.render("renderReactLayout", {
        title: "Disposable Email Found",
        activePage: "emailDisposable",
        reactComp: EmailDisposablePage,
        props: JSON.stringify(props),
        scriptName: "emailDisposable"
      });
    } else {
      return res.redirect("/");
    }
  } else {
    req.session.returnTo = req.originalUrl;
    return res.redirect("/auth");
  }
};
