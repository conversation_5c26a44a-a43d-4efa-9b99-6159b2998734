FROM node:20.11.1-slim

ARG NPM_TOKEN
ENV NPM_TOKEN $NPM_TOKEN

# Create a directory where our app will be placed
RUN mkdir -p /workdir

# Change directory so that our commands run inside this new dir
WORKDIR /workdir

# Copy dependency definitions
COPY .npmrc .npmrc
COPY package.json package.json
COPY package-lock.json package-lock.json

# Install dependecies
RUN npm install

# Expose the port the app runs in
EXPOSE 3000

# Serve the app
CMD ["npm", "start"]
