export type GlossaryKeyType =
  | "aum"
  | "nav"
  | "discretionaryInvesting"
  | "systematicInvesting"
  | "longShortInvesting"
  | "directionalInvesting"
  | "return"
  | "annualizedReturn"
  | "volatility"
  | "maximumDrawdown"
  | "sharpeRatio"
  | "sortinoRatio"
  | "alpha"
  | "beta"
  | "managementFee"
  | "performanceFee"
  | "entryFee"
  | "hurdleRate";

type GlossaryValueType = {
  name: string;
  description: string;
  urlPath: string;
};

type GlossaryType = {
  [key in GlossaryKeyType]: GlossaryValueType;
};

const glossary: GlossaryType = {
  aum: {
    name: "AUM",
    description:
      "The total market value of the investments that a fund (or an entity in general) manages on behalf of clients.",
    urlPath: "aum"
  },
  nav: {
    name: "NAV",
    description:
      "The net value of a fund. It is calculated as the total value of the fund’s assets minus the total value of its liabilities. Usually, it represents the per-share price of the fund on a specific date, usually by the end of a month.",
    urlPath: "nav"
  },
  discretionaryInvesting: {
    name: "Discretionary Investing",
    description:
      "Buy and sell decisions are made at the portfolio manager's discretion, based on a pre-determined mandate.",
    urlPath: "discretionary-investing"
  },
  systematicInvesting: {
    name: "Systematic Investing",
    description:
      "Buy and sell decisions, as well as risk management, are made according to pre-defined quantitative rules in a methodical way.",
    urlPath: "systematic-investing"
  },
  longShortInvesting: {
    name: "Long-short Investing",
    description:
      "The fund manager takes both long and short positions in investments typically from a specific market segment.",
    urlPath: "long-short-investing"
  },
  directionalInvesting: {
    name: "Directional investing",
    description:
      "The fund manager takes specific views of the future direction of the market or underlying asset. It is the opposite of long-short investing and it can be either long or short.",
    urlPath: "directional-investing"
  },
  return: {
    name: "Return",
    description:
      "The yield that an investment generates over a period of time. It is the percentage increase or decrease in the value of the investment in that period.",
    urlPath: "return"
  },
  annualizedReturn: {
    name: "Annualized return",
    description:
      "The amount of money an investment has earned per annum. It represents the annual return of an investment for a period of time, assuming that gains were re-invested every year.",
    urlPath: "annualized-return"
  },
  volatility: {
    name: "Volatility",
    description:
      "How much an asset's price swings around its mean price. In other words, it shows how large up or down movements an investor should expect from an asset.",
    urlPath: "volatility"
  },
  maximumDrawdown: {
    name: "Maximum drawdown",
    description: "The maximum observed loss from a peak to a trough of the value of an investment",
    urlPath: "max-drawdown"
  },
  sharpeRatio: {
    name: "Sharpe Ratio",
    description:
      "A measure that expresses the return of an investment, compared to its risk and is a snapshot of the quality of the investment.",
    urlPath: "sharpe-ratio"
  },
  sortinoRatio: {
    name: "Sortino Ratio",
    description:
      "It is a variation of the Sharpe Ratio that distinguishes between upside and downside moves. It is also a measure of an asset’s risk-adjusted returns, but takes only the downside risk and movements into account, ignoring upside movements.",
    urlPath: "sortino-ratio"
  },
  alpha: {
    name: "Alpha",
    description:
      "Shows how much profit the fund can generate over the market’s returns and expresses whether a fund manager can systematically earn returns that exceed the broad market as a whole.",
    urlPath: "alpha"
  },
  beta: {
    name: "Beta",
    description: "Is an indication of how correlated an investment is with a benchmark’s ups and downs.",
    urlPath: "beta"
  },
  managementFee: {
    name: "Management Fee",
    description:
      "A fee charged by the Fund Manager for managing a fund. It is intended to cover the manager’s compensation, but also other administrative costs and is charged regardless of the performance of the fund.",
    urlPath: "management-fee"
  },
  performanceFee: {
    name: "Performance Fee",
    description:
      "A fee charged by the Fund Manager for generating positive returns. It is charged only if the fund has positive returns for the year and in some cases above a minimum threshold.",
    urlPath: "performance-fee"
  },
  entryFee: {
    name: "Entry Fee",
    description:
      "A fee charged at the point an investment is made. It is a one-off payment and is usually quoted as a % of the investment value.",
    urlPath: "entry-fee"
  },
  hurdleRate: {
    name: "Hurdle rate",
    description:
      "It is used to specify the minimum return threshold that a fund manager needs to achieve, before triggering performance fees.",
    urlPath: "hurdle-rate"
  }
};

export default glossary;
