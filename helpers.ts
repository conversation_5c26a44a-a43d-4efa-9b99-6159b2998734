/*
  This is a file of data and helper functions that we can expose and use in our templating function
*/

// FS is a built in module to node that let's us read files from the system we're running on
import fs from "fs";
import React from "react";
import { renderToString } from "react-dom/server";
import { getTermDescription, getTermUrl } from "./utils/glossaryUtil";
import { inUserRoles, isInvestor, isAdmin } from "./utils/userRoleUtil";

// // moment.js is a handy library for displaying dates. We need this in our templates to display things like "Posted 5 minutes ago"
// exports.moment = require('moment');

// exports.menu = [
//   { slug: '/stores', title: 'Stores', icon: 'store', },
//   { slug: '/tags', title: 'Tags', icon: 'tag', },
//   { slug: '/top', title: 'Top', icon: 'top', },
//   { slug: '/add', title: 'Add', icon: 'add', },
//   { slug: '/map', title: 'Map', icon: 'map', },
// ];

const helpers = {
  allowInProd: (): boolean => process.env.NODE_ENV === "production",
  // Dump is a handy debugging function we can use to sort of "console.log" our data
  dump: (obj: any): string => JSON.stringify(obj, null, 2),
  // inserting an SVG
  icon: (name: string): Buffer => fs.readFileSync(`./public/images/icons/${name}.svg`),
  renderReact: (reactComp: React.ComponentClass<any, any>, props: any): string =>
    renderToString(React.createElement(reactComp, props)),
  // Some details about the site
  siteName: "Wealthyhood",
  glossaryUtil: {
    getTermDescription,
    getTermUrl
  },
  userUtil: {
    inUserRoles,
    isInvestor,
    isAdmin
  }
};

export default helpers;
