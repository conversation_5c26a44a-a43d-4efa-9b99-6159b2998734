import { NextFunction, Request, Response } from "express";
import { captureException } from "@sentry/node";
import { CustomRequest } from "custom";
import { UserTypeEnum } from "../models/User";
import appApiService, { API_ROUTES } from "../services/appApiService";
import logger from "../services/loggerService";
import { isEmailDisposable } from "../utils/emailUtil";
import { inUserRoles } from "../utils/userRoleUtil";

export const findOrCreateUserHandler = async (
  req: CustomRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // We'll store or retrieve the user from db only if they're
  // authenticated. If not than proceed to routes. For routes
  // that require auth, we'll send the user to login view.
  if (req.oidc.isAuthenticated()) {
    try {
      // profile has all the auth0 information for the user
      const profile = req.oidc.user;
      const userProfile: any = {
        createdAt: profile[`${process.env.DOMAIN_URL}/createdAt`],
        email: profile.email,
        emailDisposable: isEmailDisposable(profile.email),
        auth0: {
          id: profile.sub
        },
        lastLogin: req.oidc.user.updated_at,
        grsf: req.cookies?.["grsf"],
        wlthd: req.cookies?.["wlthd"],
        financeAdsSessionId: req.cookies?.["sID"]
      };
      userProfile.emailVerified = profile.email_verified;
      const auth0Roles = profile[`${process.env.DOMAIN_URL}/roles`] || [];
      let userRoles = auth0Roles.filter((auth0Role: UserTypeEnum) =>
        Object.values(UserTypeEnum).includes(auth0Role)
      );
      const isTestAccount = userRoles.includes(UserTypeEnum.TEST_ACCOUNT);

      if (userRoles.length === 0) {
        // If no roles are passed from auth0 we set the user as an investor
        userRoles = [UserTypeEnum.INVESTOR];
      } else if (userRoles.length === 1 && isTestAccount) {
        // If the only role passed from auth0 is TEST_ACCOUNT, we set the user as an investor
        userRoles.push(UserTypeEnum.INVESTOR);
      }
      userProfile.role = userRoles;

      req.user = (await appApiService.postM2Madmin(API_ROUTES.users.email(profile.email), userProfile))?.[0];
    } catch (err) {
      captureException(err);
      logger.error("Error in auth middleware", {
        module: "AuthMiddleware",
        method: "findOrCreateUserHandler",
        userEmail: req.oidc.user.email,
        data: {
          error: err,
          headers: err.response ? JSON.stringify(err.response.headers) : ""
        }
      });
    }
  }
  next();
};

export const isLoggedInHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  // first check if the user is authenticated
  if (req.oidc.isAuthenticated()) {
    if (req.user) {
      if (inUserRoles(req.user, [UserTypeEnum.INVESTOR])) {
        if (req.user.emailDisposable) {
          return res.redirect("/email-disposable");
        } else if (!req.user.emailVerified) {
          return res.redirect("/auth");
        } else {
          return next(); //carry on they are logged in
        }
      } else {
        return next();
      }
    } else {
      // We're support to have set the req.user by now. If not something
      // has gone wrong, so logout the user.
      res.oidc.logout();
      return;
    }
  }

  req.session.returnTo = req.originalUrl;
  return res.redirect("/auth");
};

export const isNotLoggedInHandler = (req: Request, res: Response, next: NextFunction): void => {
  if (req.oidc.isAuthenticated()) {
    return res.redirect("/");
  } else {
    return next();
  }
};
