import { ConfigCatFeatureFlags } from "../config/featuresConfig";
import { captureException } from "@sentry/node";
import configCatService from "../services/configCatService";
import { CustomRequest } from "custom";
import { NextFunction, Response } from "express";
import logger from "../services/loggerService";

export const addFeatureFlagsToRequest = (featureFlags: ConfigCatFeatureFlags[]) => {
  return async (req: CustomRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const results = await configCatService.getFeatureFlagStatuses(featureFlags, req.user.email);

      if (!req?.featureFlags) {
        req.featureFlags = results;
      } else {
        req.featureFlags.push(...results);
      }
    } catch (err) {
      captureException(err);
      logger.error(`Could not retrieve flag status for user id ${req?.user?.id}`, {
        module: "FeatureMiddleware",
        method: "addFeatureFlagsToRequest"
      });
    } finally {
      next();
    }
  };
};
