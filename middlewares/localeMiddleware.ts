import { NextFunction, Response } from "express";
import { captureException } from "@sentry/node";
import { CustomRequest } from "custom";
import logger from "../services/loggerService";
import { localeConfig } from "@wealthyhood/shared-configs";

export const addLocaleToRequest = async (req: CustomRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    req.locale = req.headers["accept-language"]?.split(",")[0] as localeConfig.LocaleType;
  } catch (err) {
    captureException(err);
    logger.error(`Could not retrieve locale for request!`, {
      module: "LocaleMiddleware",
      method: "addLocaleToRequest",
      data: {
        headers: req.headers
      }
    });
  } finally {
    next();
  }
};
