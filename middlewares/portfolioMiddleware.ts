import { NextFunction, Response } from "express";
import { captureException } from "@sentry/node";
import { CustomRequest } from "../types/custom";

export const realPortfolioAllocationIsNotCreatedHandler = async (
  req: CustomRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const realPortfolio = req.user?.portfolios?.[0];
    if (!realPortfolio.isTargetAllocationSetup) {
      return next();
    }
    res.redirect("/portfolios/creation-success");
  } catch (err) {
    captureException(err);
    res.redirect("/portfolios/creation-success");
  }
};
