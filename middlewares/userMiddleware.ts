import { CustomRequest } from "custom";
import { NextFunction, Response } from "express";
import { UserTypeEnum } from "../models/User";
import { inUserRoles, isAdmin, isInvestor } from "../utils/userRoleUtil";

export const hasConvertedPortfolioHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (req.user.hasConvertedPortfolio) {
    return next();
  }
  res.redirect("/");
};

export const inUserRolesHandler = (
  allowedRoles: UserTypeEnum[]
): ((req: CustomRequest, res: Response, next: NextFunction) => void) => {
  return (req: CustomRequest, res: Response, next: NextFunction): void => {
    if (inUserRoles(req.user, allowedRoles)) {
      return next();
    }

    return res.redirect("/");
  };
};

export const isAdminHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (isAdmin(req.user)) {
    return next();
  }
  res.redirect("/");
};

export const isInvestorHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (req.user.hasDisassociationRequest) {
    return res.redirect("/investor/closing-account");
  }

  if (req.user.hasJoinedWaitingList && !req.user.isEuWhitelisted) {
    return res.redirect("/investor/residency-country");
  }

  if (req.user.submittedRequiredInfo && !req.user.hasAcceptedTerms) {
    return res.redirect("/investor/email-verified");
  }

  if (isInvestor(req.user)) {
    return next();
  }

  return res.redirect("/");
};
