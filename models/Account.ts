import { PortfolioDocument } from "./Portfolio";
import { UserDocument } from "./User";
import { AccountStatusType, PortfolioWrapperTypeEnum } from "../services/wealthkernelService";
import { DocumentWithId } from "model";

export interface AccountDTOInterface {
  wrapperType: PortfolioWrapperTypeEnum;
  name: string;
  productId: string;
  owner: string;
  providers?: {
    wealthkernel?: {
      id: string;
      status: AccountStatusType;
    };
  };
}

interface AccountInterface extends Omit<AccountDTOInterface, "owner"> {
  owner: string | UserDocument;

  // virtuals
  readonly portfolios?: PortfolioDocument[];
}

export interface AccountDocument extends AccountInterface, DocumentWithId {}
