import { countriesConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import { DocumentWithId } from "model";

export interface AddressDTOInterface {
  owner: string | UserDocument;
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  region?: string;
  countryCode: countriesConfig.CountryCodesType;
  postalCode: string;
  providers?: {
    wealthkernel?: {
      id: string;
    };
  };
}

export interface AddressInterface extends Omit<AddressDTOInterface, "owner"> {
  owner: string | UserDocument;
}

export interface AddressDocument extends AddressInterface, DocumentWithId {}
