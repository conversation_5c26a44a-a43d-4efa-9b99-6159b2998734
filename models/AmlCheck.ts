import { UserDocument } from "./User";
import { DocumentWithId } from "model";

export const AmlCheckFlagArray = ["sanction", "pep", "adverseMedia", "warning", "fitnessAndProbity"] as const;
export type AmlCheckFlagType = typeof AmlCheckFlagArray[number];

export type AmlCheckFlagsType = Record<AmlCheckFlagType, boolean>;

/**
 * INTERFACES
 */
export interface AmlCheckInterface extends AmlCheckFlagsType {
  owner: string | UserDocument;
  lastCheckedAt: Date;
}

export interface AmlCheckDocument extends AmlCheckInterface, DocumentWithId {}
