import { DocumentWithId } from "model";
import { InvestmentProductDocument } from "./InvestmentProduct";

export enum AssetNewsSentimentEnum {
  Positive = "Positive",
  Neutral = "Neutral",
  Negative = "Negative"
}

export interface AssetNewsDTOInterface {
  investmentProducts: string[];
  newsUrl: string;
  imageUrl: string;
  title: string;
  text: string;
  source: string;
  topics?: string[];
  tickers?: string[];
  date: Date;
  sentiment: AssetNewsSentimentEnum;
  type: string;
  providers: {
    stockNews: {
      id: string;
    };
  };
  readonly displayDate: string;
}

export interface AssetNewsInterface extends Omit<AssetNewsDTOInterface, "investmentProducts"> {
  investmentProducts: string[] | InvestmentProductDocument[];
}

export interface AssetNewsDocument extends AssetNewsInterface, DocumentWithId {}
