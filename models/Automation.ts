import { DocumentWithId } from "model";
import { UserDocument } from "./User";
import { MandateDocument } from "./Mandate";
import { PortfolioDocument } from "./Portfolio";
import { currenciesConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";

/**
 * TYPES
 */
export const AutomationCategoryArray = [
  "RebalanceAutomation",
  "TopUpAutomation",
  "SavingsTopUpAutomation"
] as const;
export type AutomationCategoryType = typeof AutomationCategoryArray[number];

export const AutomationStatusArray = ["Pending", "Active", "Inactive"] as const;
export type AutomationStatusType = typeof AutomationStatusArray[number];

export const FrequencySettingArray = ["monthly"] as const;
export type FrequencySettingType = typeof FrequencySettingArray[number];

/**
 * INTERFACES
 */
export interface AutomationDTOInterface {
  category: AutomationCategoryType;
  owner: string;
  portfolio: string;
  frequency: FrequencySettingType;
}

export interface TopUpAutomationDTOInterface extends AutomationDTOInterface {
  dayOfMonth: number; // 1-28 or -1 (if the last day of the month)
  mandate: string;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // value of the recurring top-up - stored in cents
    amount: number;
  };
}

export interface SavingsTopUpAutomationDTOInterface extends AutomationDTOInterface {
  dayOfMonth: number; // 1-28 or -1 (if the last day of the month)
  mandate: string;
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // value of the recurring top-up - stored in cents
    amount: number;
  };
}

export interface AutomationInterface extends Omit<AutomationDTOInterface, "owner" | "portfolio"> {
  active: boolean;
  owner: string | UserDocument;
  portfolio: string | PortfolioDocument;

  // VIRTUALS
  status: AutomationStatusType;
}

interface TopUpAutomationInterface extends Omit<TopUpAutomationDTOInterface, "owner" | "mandate" | "portfolio"> {
  owner: string | UserDocument;
  portfolio: string | PortfolioDocument;
  mandate: string | MandateDocument;
}

interface SavingsTopUpAutomationInterface
  extends Omit<SavingsTopUpAutomationDTOInterface, "owner" | "mandate" | "portfolio"> {
  owner: string | UserDocument;
  portfolio: string | PortfolioDocument;
  mandate: string | MandateDocument;
}

export interface AutomationDocument extends AutomationInterface, DocumentWithId {}

export interface TopUpAutomationDocument extends TopUpAutomationInterface, AutomationDocument {}

export interface SavingsTopUpAutomationDocument extends SavingsTopUpAutomationInterface, AutomationDocument {}

export interface RebalanceAutomationDocument extends AutomationInterface, AutomationDocument {}
