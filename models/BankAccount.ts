import { UserDocument } from "./User";
import { BankAccountStatusType } from "../services/wealthkernelService";
import { DocumentWithId } from "model";
import { MandateDocument } from "./Mandate";
import { banksConfig } from "@wealthyhood/shared-configs";

export const WealthyhoodBankAccountStatusArray = ["Active", "Pending", "Suspended"] as const;
export type WealthyhoodBankAccountStatusType = typeof WealthyhoodBankAccountStatusArray[number];

export interface BankAccountDTOInterface {
  owner: string;
  name: string;
  number: string;
  sortCode: string;
  iban?: string;
  bic?: string;
  truelayerProviderId: string;
  createdAt?: Date;
  providers?: {
    wealthkernel?: {
      id: string;
      status?: BankAccountStatusType;
    };
    wealthyhood?: {
      status?: WealthyhoodBankAccountStatusType;
    };
  };
  nameFlagged: boolean;
  bankName?: string;
  bankId?: banksConfig.BankType;
  active: boolean;

  readonly mandate?: MandateDocument;
  readonly displayBankName?: string;
  readonly bankIconURL?: string;
  readonly supportsEasyTransfer?: boolean;
  readonly displayAccountIdentifier?: string;
  readonly isAvailableForDirectDebit?: boolean;
}

export interface BankAccountInterface extends Omit<BankAccountDTOInterface, "owner"> {
  owner: string | UserDocument;
}

export interface BankAccountDocument extends BankAccountInterface, DocumentWithId {}
