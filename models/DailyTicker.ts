import { InvestmentProductDocument } from "./InvestmentProduct";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { DocumentWithId } from "model";

/**
 * TYPES
 */
export type MultiCurrencyPriceType = Record<currenciesConfig.MainCurrencyType, number>;

/**
 * DOCUMENTS
 */
interface DailyTickerDocument extends DocumentWithId {
  date: Date;
  price: number;
  openingPrice?: number;
  closingPrice?: number;
  returnPercentage: number;
  dailyReturnPercentage: number;
  monthlyReturnPercentage: number;

  // virtuals
  dateLabel: string;
}

export interface DailyAssetTickerDocument extends DailyTickerDocument {
  investmentProduct: string | InvestmentProductDocument;
  pricePerCurrency: MultiCurrencyPriceType;
}
export interface DailyPortfolioTickerDocument extends DailyTickerDocument {
  portfolio: string;
  wealthkernelValue: number;
}
