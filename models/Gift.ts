import { UserDocument } from "./User";
import { DepositStatusType } from "../services/wealthkernelService";
import { AssetTransactionDocument } from "./Transaction";
import { DocumentWithId } from "model";
import { currenciesConfig } from "@wealthyhood/shared-configs";

/**
 * TYPES
 */
export const GiftStatusArray = ["Pending", "Settled"] as const;
export type GiftStatusType = typeof GiftStatusArray[number];

/**
 * INTERFACES
 */
interface GiftDTOInterface {
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // the total value of the gift - stored in cents
    amount: number;
  };
  message: string;
  gifter: string;
  targetUserEmail: string;
  // the associated deposit transaction that we make from the Wealthyhood wallet to the user's e-money wallet ONLY
  // after the reward is used by the user for a transaction
  deposit?: {
    providers?: {
      wealthkernel: {
        id?: string;
        status?: DepositStatusType;
        submittedAt?: Date;
      };
    };
  };
}

interface GiftInterface extends Omit<GiftDTOInterface, "gifter"> {
  createdAt: Date;
  message: string;
  unrestrictedAt: Date;
  gifter: string | UserDocument;
  hasViewedAppModal: boolean;
  status: GiftStatusType;

  // VIRTUALS
  readonly linkedAssetTransaction: string | AssetTransactionDocument;
  readonly used: boolean;
}

export interface GiftDocument extends GiftInterface, DocumentWithId {}
