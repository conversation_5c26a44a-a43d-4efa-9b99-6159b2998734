import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { DailyAssetTickerDocument } from "./DailyTicker";
import { DocumentWithId } from "model";

/**
 * TYPES
 */
export type LineType = {
  active: boolean;

  // generated by MongoDB
  readonly updatedAt: Date;
};

/**
 * DOCUMENTS
 */
export interface InvestmentProductDocument extends DocumentWithId {
  currentTicker: DailyAssetTickerDocument;
  commonId: investmentUniverseConfig.AssetType;
  listed?: boolean;
  buyLine: LineType;
  sellLine: LineType;

  // VIRTUALS
  readonly tradedCurrency: currenciesConfig.MainCurrencyType;
  readonly tradedPrice: number;
}
