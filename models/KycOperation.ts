import { UserDocument } from "./User";
import { ReviewAnswerType, ReviewStatusType } from "../services/sumsubService";
import { ProviderEnum } from "../services/providerService";
import { DocumentWithId } from "model";

/**
 * TYPES
 */
const KycOperationStatusArray = ["Pending", "Passed", "Failed", "ManuallyPassed"] as const;
export type KycOperationStatusType = typeof KycOperationStatusArray[number];

/**
 * INTERFACES
 */
export interface KycOperationDTOInterface {
  updatedAt?: Date;
  owner: string;
  activeProviders: ProviderEnum[];
  status?: KycOperationStatusType;
  /**
   * This flag is used to determine if the user was manually submitted for an AML workflow.
   */
  isManualAmlWorkflowSubmitted?: boolean;
  providers: {
    sumsub?: {
      id?: string;
      submittedAt?: Date;
      status?: ReviewStatusType;
      decision?: ReviewAnswerType;
    };
  };
}

export interface KycOperationInterface extends Omit<KycOperationDTOInterface, "owner" | "status"> {
  createdAt: Date;
  owner: string | UserDocument;
  status?: KycOperationStatusType;

  // virtual
  isJourneyCompleted: boolean;
  isProcessed: boolean;
}

export interface KycOperationDocument extends KycOperationInterface, DocumentWithId {}
