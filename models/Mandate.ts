import { UserDocument } from "./User";
import { BankAccountDocument } from "./BankAccount";
import { GoCardlessMandateStatusType } from "../services/goCardlessService";
import { DocumentWithId } from "model";

/**
 * TYPES
 */
export const MandateStatusArray = ["Pending", "Active", "Inactive"] as const;
export type MandateStatusType = typeof MandateStatusArray[number];

export const MandateCategoryArray = ["Subscription", "Top-Up"] as const;
export type MandateCategoryType = typeof MandateCategoryArray[number];

/**
 * INTERFACES
 */
export interface MandateDTOInterface {
  owner: string;
  bankAccount: string;
  category: MandateCategoryType;
  providers: {
    gocardless?: {
      status: GoCardlessMandateStatusType;
      id: string;
    };
  };
}

export interface MandateInterface extends Omit<MandateDTOInterface, "owner" | "bankAccount"> {
  createdAt: Date;
  updatedAt: Date;
  owner: string | UserDocument;
  bankAccount: string | BankAccountDocument;

  // VIRTUALS
  readonly status: MandateStatusType;
  readonly isActive: boolean;
}

export interface MandateDocument extends MandateInterface, DocumentWithId {}
