import { AssetTransactionDocument, RebalanceTransactionDocument } from "./Transaction";
import { OrderSideType, OrderStatusType as WealthkernelOrderStatusType } from "../services/wealthkernelService";
import { DocumentWithId } from "model";
import { currenciesConfig, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";

export const OrderStatusArray = ["Pending", "Matched", "Rejected", "Cancelled", "Settled"] as const;
export type OrderStatusType = typeof OrderStatusArray[number];

export type UnitPriceType = { amount: number; currency: currenciesConfig.MainCurrencyType };

export type DisplayExchangeRateType = {
  rate: number;
  currency: currenciesConfig.MainCurrencyType;
};

export type FeeType = {
  currency: currenciesConfig.MainCurrencyType;
  amount: number;
};

export type FeesType = {
  fx: FeeType;
  commission?: FeeType; // Older transactions/rewards do not have commission
  executionSpread?: FeeType; // Older transactions/rewards do not have execution spread
  realtimeExecution?: FeeType;
};

type CommonIdType = investmentUniverseConfig.AssetType | savingsUniverseConfig.SavingsProductType;

export interface OrderInterface {
  consideration?: {
    originalAmount?: number; // amount before fees applied, stored in cents
    amountSubmitted?: number; // amount submitted to WK, after fees applied, stored in cents
    amount: number; // final settlement amount (initially equal to amountSubmitted)
    currency: currenciesConfig.MainCurrencyType;
  };
  quantity?: number;
  createdAt?: Date;
  updatedAt?: Date;
  isin: string;
  fees?: FeesType;
  rejectionReason?: string;
  settlementCurrency: currenciesConfig.MainCurrencyType;
  side: OrderSideType;
  isMatched: boolean;
  hasExecutionStarted: boolean;
  // Is being used for debugging to associate a pending order with a transaction if it fails during creation
  transaction: string | AssetTransactionDocument | RebalanceTransactionDocument;
  providers?: {
    wealthkernel?: {
      status: WealthkernelOrderStatusType;
      id: string;
      submittedAt: Date;
    };
  };
  displayAmount?: number;
  displayQuantity?: number;
  displayUnitPrice?: UnitPriceType;
  isCancellable?: boolean;
  assetCategory?: investmentUniverseConfig.AssetCategoryType;
  status: OrderStatusType;
  displayExchangeRate?: DisplayExchangeRateType;
  commonId?: CommonIdType;
  displayDate: Date;
  estimatedRealTimeCommission?: number;
  displayUserFriendlyId?: string;
}
export interface OrderDocument extends OrderInterface, DocumentWithId {}
