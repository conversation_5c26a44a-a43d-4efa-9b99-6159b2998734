import { DocumentWithId } from "model";

/**
 * TYPES
 */
export type GoogleAdsMetadataType = {
  adSetId?: string;
  campaign?: string;
  campaignId?: string;
  gclid?: string;
};
export type ParticipantMetadataType = {
  financeAds?: { influencerId: string };
  googleAds?: GoogleAdsMetadataType;
};
const PlatformArray = ["android", "ios"] as const;
export type PlatformType = typeof PlatformArray[number];
export const ParticipantRoleArray = ["BASIC", "AMBASSADOR"] as const;
export type ParticipantRoleType = typeof ParticipantRoleArray[number];
const TrackingSourceArray = ["apple", "google", "meta", "organic"] as const;
export type TrackingSourceType = typeof TrackingSourceArray[number];

/**
 * INTERFACES
 */
export interface ParticipantDTOInterface {
  appInstallInfo?: {
    createdAt: Date;
    platform: PlatformType;
  };
  // This field is currently used for E2E tracking from Landing Page to Server
  // only on the GA Service. For events sent through segment to third party services
  // we use appsflyerId as anonymousId when we don't have user's id yet.
  anonymousId?: string;
  appsflyerId?: string;
  attributionErrorMsg?: string;
  email?: string;
  gaClientId?: string;
  grsfId?: string;
  metadata?: ParticipantMetadataType;
  participantRole?: ParticipantRoleType;
  referrer?: string;
  trackingSource?: TrackingSourceType;
}

export interface ParticipantInterface extends Omit<ParticipantDTOInterface, "referrer"> {
  wlthdId: string;
  participantRole: ParticipantRoleType;
  referrer?: string | ParticipantDocument;

  // virtuals
  readonly isAmbassador: boolean;
}

export interface ParticipantDocument extends ParticipantInterface, DocumentWithId {}
