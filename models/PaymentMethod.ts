import { UserDocument } from "./User";
import { BonusStatusType } from "../services/wealthkernelService";
import { DocumentWithId } from "model";

/**
 * ENUMS
 */
export enum PaymentMethodTypeEnum {
  CARD = "card"
}

enum WalletEnum {
  APPLE_PAY = "apple_pay",
  GOOGLE_PAY = "google_pay"
}

export enum PaymentMethodBrandEnum {
  VISA = "visa",
  MASTERCARD = "mastercard",
  AMEX = "amex",
  DISCOVER = "discover",
  DINERS = "diners"
}

/**
 * INTERFACES
 */
export interface PaymentMethodDTOInterface {
  owner: string;
  type: PaymentMethodTypeEnum;
  brand: PaymentMethodBrandEnum;
  lastFourDigits: string;
  fingerprint: string;
  providers: {
    stripe: {
      id?: string;
      status?: BonusStatusType;
      submittedAt?: Date;
    };
  };

  // OPTIONALS
  wallet?: WalletEnum;
}

export interface PaymentMethodInterface extends Omit<PaymentMethodDTOInterface, "owner"> {
  owner: string | UserDocument;
}

export interface PaymentMethodDocument extends PaymentMethodInterface, DocumentWithId {}
