/**
 * Mode REAL:
 * Consider the scenario of going from tracking portfolio to investment (simulated → virtual). How will we handle that?
 * That also relates with what to do with cash when making a cash deposit while having a simulated portfolio.
 * This is where the REAL mode is used. Mode refers only to the asset holdings. If cash is held it's always real.
 * Only asset transactions can be simulated → so when an investment is created, create an real asset transaction
 * and change portfolio mode from VIRTUAL to REAL, to reflect that.
 */
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { AccountDocument } from "./Account";
import { DailyPortfolioTickerDocument } from "./DailyTicker";
import { InvestmentProductDocument } from "./InvestmentProduct";
import { UserDocument } from "./User";
import { PortfolioStatusType } from "../services/wealthkernelService";
import { DocumentWithId } from "model";

export enum PortfolioModeEnum {
  REAL = "REAL"
}

/**
 * TYPES
 */
export type CashType = {
  // in pounds, not cents
  available: number;
  settled: number;
  reserved: number;
};
export type HoldingsType = {
  asset?: InvestmentProductDocument;
  assetCommonId: investmentUniverseConfig.AssetType;
  quantity: number;
};
export type GiftedHoldingType = {
  unrestrictedAt: Date;
  createdAt: Date;
  quantity: number;
};
export type InitialHoldingsAllocationType = {
  asset?: InvestmentProductDocument;
  assetCommonId: investmentUniverseConfig.AssetType;
  percentage: number;
};
export type PersonalisationType = {
  assetClasses: investmentUniverseConfig.AssetClassType[];
  geography: investmentUniverseConfig.InvestmentGeographyType;
  risk: number;
  sectors: investmentUniverseConfig.InvestmentSectorType[];
};

export interface PortfolioDTOInterface {
  account?: string;
  currency?: currenciesConfig.MainCurrencyType;
  holdings: HoldingsType[];
  giftedHoldings?: Map<investmentUniverseConfig.AssetType, GiftedHoldingType[]>;
  initialHoldingsAllocation: InitialHoldingsAllocationType[];
  mode: PortfolioModeEnum;
  name?: string;
  owner: string;
  personalisationPreferences?: PersonalisationType;
}

interface PortfolioInterface extends Omit<PortfolioDTOInterface, "account" | "owner"> {
  account?: string | AccountDocument;
  // Cash values are in pounds, not cents
  cash: {
    EUR?: CashType;
    GBP: CashType;
    USD?: CashType;
  };
  createdAt: Date;
  currency: currenciesConfig.MainCurrencyType;
  lastUpdated: Date;
  owner: string | UserDocument;
  providers?: {
    wealthkernel?: {
      status: PortfolioStatusType;
      id: string;
    };
  };

  // virtuals
  readonly amountAvailableToWithdraw: number;
  readonly unsettledCash: number;
  readonly currentTicker: DailyPortfolioTickerDocument;
  readonly isReal: boolean;
  readonly isTargetAllocationSetup: boolean;
}

/**
 * DOCUMENTS
 */
export interface PortfolioDocument extends PortfolioInterface, DocumentWithId {}

export interface PortfolioReturnRates {
  max: number | null;
  "5y": number;
  "1y": number;
  "6m": number;
  "3m": number;
  "1m": number;
  "1w": number;
}
export interface PortfolioWithReturnsByTenorDocument extends PortfolioDocument {
  returnsValues: PortfolioReturnRates;
  upByValues: PortfolioReturnRates;
  holdings: (HoldingsType & { sinceBuyReturns?: number })[];
}
