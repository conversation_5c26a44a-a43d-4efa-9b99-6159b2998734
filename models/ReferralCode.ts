import { UserDocument } from "./User";
import { DocumentWithId } from "model";

/**
 * ENUMS
 */
export enum LifetimeEnum {
  EXPIRING = "expiring",
  NON_EXPIRING = "non-expiring"
}

/**
 * TYPES
 */
const LifetimeArray = ["expiring", "non-expiring"] as const;
type LifetimeType = typeof LifetimeArray[number];

/**
 * INTERFACES
 */

export interface ReferralCodeDTOInterface {
  // boolean that indicates whether the link is valid to be used
  // always true for non-expiring links
  active?: boolean;
  // indicates whether the link can be used specific amount of times or indefinitely
  lifetime?: LifetimeType;
  // the user id for the user that owns the link
  owner: string;
}

interface ReferralCodeInterface extends Omit<ReferralCodeDTOInterface, "active" | "owner"> {
  active: boolean;
  // the referral code - similar to the wlthdId of the participant
  code: string;
  owner: string | UserDocument;
}

export interface ReferralCodeDocument extends ReferralCodeInterface, DocumentWithId {}
