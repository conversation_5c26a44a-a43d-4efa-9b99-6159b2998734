import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import { DepositStatusType, OrderStatusType } from "../services/wealthkernelService";
import { DocumentWithId } from "model";
import { UnitPriceType } from "./Order";
import { FeesType } from "./Transaction";

/**
 * TYPES
 */
const RewardDepositStatusArray = ["Empty", "Pending", "Settled"] as const;
export type RewardDepositStatusType = typeof RewardDepositStatusArray[number];
const RewardOrderStatusArray = ["Empty", "Pending", "Settled"] as const;
export type RewardOrderStatusType = typeof RewardOrderStatusArray[number];
export const RewardStatusArray = ["Pending", "Settled"] as const;
export type RewardStatusType = typeof RewardStatusArray[number];

export enum RewardActivityFilterEnum {
  Rewards = "Rewards"
}

type DisplayExchangeRateType = {
  rate: number;
  currency: currenciesConfig.MainCurrencyType;
};

/**
 * INTERFACES
 */
export interface RewardDTOInterface {
  // the ETF that we'll give as a reward
  asset: investmentUniverseConfig.AssetType;
  isin: string;
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    // the total value of the ETF that we'll reward - stored in cents
    amount: number;
  };
  // asset quantity that user buys when the ETF order settles
  quantity?: number;
  // the user that made the referral (in case of double reward)
  referrer?: string;
  // the campaign the reward came from (in case of single reward)
  referralCampaign?: string;
  // the user that got referred
  referral: string;
  // the user that the reward is for (is referrer or referral)
  targetUser: string;
  // the date when updates takes place on the document
  // currently used to store the date when the reward is Settled
  updatedAt?: Date;
  // the associated deposit transaction that we make from the
  // Wealthyhood wallet to the user's e-money wallet
  deposit?: {
    providers?: {
      wealthkernel: {
        id?: string;
        status?: DepositStatusType;
        submittedAt?: Date;
      };
    };
  };
  // the order request to move the money from the user's e-wallet
  // to buy the corresponding ETF
  order?: {
    providers?: {
      wealthkernel: {
        id?: string;
        status?: OrderStatusType;
        submittedAt?: Date;
      };
    };
  };
}

interface RewardInterface extends Omit<RewardDTOInterface, "referrer" | "referral" | "targetUser"> {
  createdAt: Date;
  unrestrictedAt: Date;
  referrer: string | UserDocument;
  referral: string | UserDocument;
  targetUser: string | UserDocument;
  hasViewedAppModal: boolean;
  accepted: boolean;
  fees: FeesType;

  // virtuals
  depositStatus: RewardDepositStatusType;
  orderStatus: RewardOrderStatusType;
  status: RewardStatusType;
  displayDate: Date;
  displayAmount: number;
  displayUnitPrice?: UnitPriceType;
  activityFilter: RewardActivityFilterEnum;
  displayExchangeRate?: DisplayExchangeRateType;
  displayUserFriendlyId?: string;
}

export interface RewardDocument extends RewardInterface, DocumentWithId {}
