import { countriesConfig } from "@wealthyhood/shared-configs";
import { DocumentWithId } from "model";
import {
  AmlScreeningResultEnum,
  EmploymentStatusType,
  SourceOfFundsEnum,
  SourceOfWealthType,
  UserDocument
} from "../models/User";

export enum RiskScoreClassificationEnum {
  LowRisk = "Low risk",
  MediumRisk = "Medium risk",
  HighRisk = "High risk",
  Prohibited = "Prohibited"
}
export type RiskAssessmentNationality = {
  value: countriesConfig.CountryCodesType;
  score: number;
};

export type RiskAssessmentSourcesOfFunds = {
  value: SourceOfFundsEnum[];
  score: number;
};

export type RiskAssessmentEmploymentStatus = {
  value: EmploymentStatusType;
  score: number;
};

export type RiskAssessmentVolumeOfTransactions = {
  value: number; // stored in pounds
  score: number;
};

export type RiskAssessmentAmlScreening = {
  value: AmlScreeningResultEnum;
  score: number;
};

export type RiskAssessmentSourcesOfWealth = {
  value: SourceOfWealthType[];
  score: number;
};

export interface RiskAssessmentDTOInterface {
  owner: string | UserDocument;
  createdAt: Date;
  nationality: RiskAssessmentNationality;
  sourcesOfFunds: RiskAssessmentSourcesOfFunds;
  employmentStatus: RiskAssessmentEmploymentStatus;
  volumeOfTransactions: RiskAssessmentVolumeOfTransactions;
  amlScreening: RiskAssessmentAmlScreening;
  sourcesOfWealth: RiskAssessmentSourcesOfWealth;
  totalScore: number;
  readonly classification: RiskScoreClassificationEnum;
}

export interface RiskAssessmentInterface extends Omit<RiskAssessmentDTOInterface, "owner"> {
  owner: string | UserDocument;
}

export interface RiskAssessmentDocument extends RiskAssessmentInterface, DocumentWithId {}
