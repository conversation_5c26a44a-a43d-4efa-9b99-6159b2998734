import { plansConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";
import { MandateDocument } from "./Mandate";
import { PaymentMethodDocument } from "./PaymentMethod";
import { SubscriptionStatusType } from "../services/stripeService";
import { DocumentWithId } from "model";

/**
 * DOCUMENTS
 */
export interface SubscriptionDTOInterface {
  active: boolean;
  owner: string;
  price: plansConfig.PriceType;
  category: plansConfig.SubscriptionCategoryType;
  mandate?: string;
  paymentMethod?: string;
  nextChargeAt?: Date;
  expiration?: {
    date: Date;
    downgradesTo: plansConfig.PriceType;
  };
  hasUsedFreeTrial?: boolean;
  providers?: {
    stripe: {
      id?: string;
      status?: SubscriptionStatusType;
    };
  };
}

export interface SubscriptionInterface
  extends Omit<SubscriptionDTOInterface, "owner" | "mandate" | "paymentMethod"> {
  owner: string | UserDocument;
  mandate?: string | MandateDocument;
  paymentMethod?: string | PaymentMethodDocument;
  createdAt: Date;
  readonly plan: plansConfig.PlanType;
  readonly isPaidPlan: boolean;
}

export interface SubscriptionDocument extends SubscriptionInterface, DocumentWithId {}
