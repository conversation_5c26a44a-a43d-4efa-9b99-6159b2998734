import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

/**
 * DTO INTERFACES
 */
interface SundownDigestSectionInterface {
  companyName?: string;
  companyTicker?: string;
  assetId?: publicInvestmentUniverseConfig.PublicAssetType;
  assetReturnPercentage?: number;
  title?: string;
  content?: string;
  tag?: string;
}

export interface SundownDigestDTOInterface {
  date: Date;
  content: string;
  formattedContent?: {
    overview?: string;
    sections?: Array<SundownDigestSectionInterface>;
  };
  providers?: {
    stockNews: {
      id: string;
    };
  };
}

type SundownDigestInterface = SundownDigestDTOInterface;

/**
 * DOCUMENTS
 */
export interface SundownDigestDocument extends SundownDigestInterface, Document {}
