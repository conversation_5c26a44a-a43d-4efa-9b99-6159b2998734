// Transaction corresponds not to the Wealthkernel Transactions, but to the individual action that corresponds
// to the user's transaction. That can be a cash transaction (deposit, withdrawal) or an asset transaction
// (buy, sell orders). So the wealthkernel status & id will be the corresponding status & id for deposits & withdrawals.
// For asset transactions we don't keep any wealthkernel info, because it depends on the status of the underlying
// orders. There is only a field indicating whether the asset transaction is settled or not.

import {
  currenciesConfig,
  investmentUniverseConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { OrderDocument } from "./Order";
import { InitialHoldingsAllocationType, PortfolioDocument } from "./Portfolio";
import { UserDocument } from "./User";
import { TruelayerPaymentStatusType } from "../services/truelayerService";
import {
  BonusStatusType,
  DepositStatusType,
  DirectDebitPaymentStatusType,
  TransactionStatusType as WealthkernelTransactionStatusType,
  WithdrawalRequestType,
  WithdrawalStatusType
} from "../services/wealthkernelService";
import { BankAccountDocument } from "./BankAccount";
import { SubscriptionDocument } from "./Subscription";
import { UserDataRequestDocument } from "./UserDataRequest";
import { RewardDocument } from "./Reward";
import * as gocardless from "../services/goCardlessService";
import { AutomationDocument } from "./Automation";
import { PaymentMethodDocument } from "./PaymentMethod";
import { DocumentWithId } from "model";
import { ForeignCurrencyRatesType } from "../react-views/types/transactionPreview";
import { SaltedgePaymentStatusType } from "../services/saltedgeService";

/**
 * TYPES
 */
export type FeesType = {
  fx: FeeType;
  realtimeExecution?: FeeType; // Older transactions/rewards do not have real time execution
  commission?: FeeType; // Older transactions/rewards do not have commission
  executionSpread?: FeeType; // Older transactions/rewards do not have execution spread
};

export type FeeType = {
  currency: currenciesConfig.MainCurrencyType;
  amount: number;
};

type ExecutionProgressType = {
  total: number;
  matched: number;
  label: string;
};

export type BilateralPaymentType = {
  incomingPayment?: {
    providers: {
      devengo: {
        id?: string;
        status?: IncomingPaymentStatusType;
        accountId: string;
      };
    };
  };
  outgoingPayment?: {
    providers: {
      devengo: {
        id: string;
        status: OutgoingPaymentStatusType;
      };
    };
  };
};

export const IncomingPaymentStatusArray = ["created", "rejected", "confirmed"] as const;
export type IncomingPaymentStatusType = typeof IncomingPaymentStatusArray[number];

export const OutgoingPaymentStatusArray = [
  "created",
  "validating",
  "blocked",
  "denied",
  "pending",
  "delayed",
  "processing",
  "retrying",
  "confirmed",
  "rejected",
  "canceled",
  "reversed"
] as const;
export type OutgoingPaymentStatusType = typeof IncomingPaymentStatusArray[number];

export const TransactionStatusArray = [
  "PendingReinvestment",
  "PendingTopUp",
  "PendingDeposit",
  "PendingGift",
  "PendingWealthkernelCharge",
  "Pending",
  "Cancelled",
  "Rejected",
  "DepositFailed",
  "Settled"
] as const;
export type TransactionStatusType = typeof TransactionStatusArray[number];
export const RebalanceTransactionStatusArray = [
  "NotStarted",
  "PendingSell",
  "PendingBuy",
  "Settled",
  "Rejected",
  "Cancelled"
] as const;
export type RebalanceTransactionStatusType = typeof RebalanceTransactionStatusArray[number];
export const ChargePaymentStatusArray = ["Pending", "Confirmed", "PaidOut", "Failed"] as const;
export type ChargePaymentStatusType = typeof ChargePaymentStatusArray[number];

const TransactionCategoryArray = [
  "DepositCashTransaction",
  "DividendTransaction",
  "AssetDividendTransaction",
  "WithdrawalCashTransaction",
  "AssetTransaction",
  "RebalanceTransaction",
  "ChargeTransaction",
  "RevertRewardTransaction",
  "CashbackTransaction",
  "WealthyhoodDividendTransaction",
  "SavingsTopupTransaction",
  "SavingsWithdrawalTransaction",
  "SavingsDividendTransaction",
  "StockSplitTransaction"
] as const;
export type TransactionCategoryType = typeof TransactionCategoryArray[number];

const PortfolioTransactionCategoryArray = ["buy", "sell", "update"] as const;
export type PortfolioTransactionCategoryType = typeof PortfolioTransactionCategoryArray[number];

const ChargeMethodArray = ["cash", "holdings", "combined", "direct-debit", "card"] as const;
export type ChargeMethodType = typeof ChargeMethodArray[number];

export enum ExecutionTypeEnum {
  MARKET_HOURS = "MARKET_HOURS",
  REALTIME = "REALTIME"
}

export type ExecutionWindowsType = {
  stocks?: ExecutionWindowType;
  etfs?: ExecutionWindowType;
};

export type ExecutionWindowType = MarketHoursExecutionWindowType | RealTimeExecutionWindowType;

export type MarketHoursExecutionWindowType = {
  executionType: ExecutionTypeEnum.MARKET_HOURS;
  start: Date;
  end: Date;
};

export type RealTimeExecutionWindowType = {
  executionType: ExecutionTypeEnum.REALTIME;
};

const ChargeTypeArray = ["fx", "commission", "subscription", "custody"] as const;
export type ChargeTypeType = typeof ChargeTypeArray[number];

export enum TransactionInvestmentActivityFilterEnum {
  Asset = "Asset",
  Buy = "Buy",
  Sell = "Sell",
  Rebalance = "Rebalance",
  Dividends = "Dividends" // stock dividends
}

export enum TransactionCashActivityFilterEnum {
  Deposit = "Deposit", // payments & savings withdrawals
  Withdraw = "Withdraw", // withdrawals & savings top-ups
  Investments = "Investments", // all asset transactions (excluding the ones linked to gifts)
  Dividends = "Dividends", // stock dividends
  Bonus = "Bonus" // combines Wealthyhood plan dividends and cashback
}

export enum DisplayTagEnum {
  INSTANT_INVEST = "INSTANT_INVEST",
  AUTOPILOT = "AUTOPILOT"
}

/**
 * DOCUMENTS
 */
interface TransactionInterfaceDTO {
  consideration: {
    currency: currenciesConfig.MainCurrencyType;
    amount?: number; // stored in cents
    cashAmount?: number; // for some transaction types (e.g. charge), the cash part of the consideration.amount, stored in cents
    holdingsAmount?: number; // for some transaction types (e.g. charge), the holdings part of the consideration.amount, stored in cents
  };
  owner: string;
  portfolio: string | PortfolioDocument;
  wealthkernelPortfolioId?: string; // each portfolio transaction is associated with a wealthkernel portfolio
  createdAt: Date;
}

interface TransactionInterface extends Omit<TransactionInterfaceDTO, "owner" | "portfolio"> {
  category: TransactionCategoryType;
  settledAt: Date;
  owner: UserDocument | string;
  portfolio: PortfolioDocument | string;
  displayAmount?: number;
  isCancellable?: boolean;
  hasExecutionStarted?: boolean;

  // virtual for deposits
  status: TransactionStatusType;
  displayDate: Date;
  investmentActivityFilter?: TransactionInvestmentActivityFilterEnum;
  cashActivityFilter?: TransactionCashActivityFilterEnum;
  displayTag?: DisplayTagEnum;
  displayStatus: TransactionStatusType;
}

interface DepositCashTransactionInterface extends TransactionInterface {
  // A deposit does not have a Truelayer entity & bank reference if it is initiated from a direct debit payment.
  bankReference?: string;
  providers?: {
    truelayer?: {
      id: string;
      status: TruelayerPaymentStatusType;
    };
    saltedge?: {
      id: string;
      customId: string;
      status: SaltedgePaymentStatusType;
    };
    wealthkernel?: {
      id: string;
      // This is a workaround for scenarios where wealthkernel settles a deposit received from the
      // user, without having received a deposit expectation. In that case they transfer the cash
      // to the user's portfolio and they create a transaction (but no deposit).
      // We have to manually submit that transaction id, so that the deposit can settle.
      transactionId?: string;
      status: DepositStatusType;
    };
  };
  transferWithIntermediary?: {
    [TransferWithIntermediaryStageEnum.ACQUISITION]?: BilateralPaymentType;
    [TransferWithIntermediaryStageEnum.COLLECTION]?: BilateralPaymentType;
  };
  directDebit?: {
    providers?: {
      wealthkernel?: {
        id: string;
        status: DirectDebitPaymentStatusType;
      };
    };
  };
  bankAccount: string | BankAccountDocument;
  linkedAutomation?: string | AutomationDocument;
  linkedAssetTransaction?: AssetTransactionDocument;
  linkedSavingsTopup?: SavingsTopupTransactionDocument;
  shouldIncludeMandateStep?: boolean;
  isDirectDebitPaymentCollected?: boolean;
  directDebitProgressPercentage?: number;
  isMoneyReceived?: boolean;
}

export enum TransferWithIntermediaryStageEnum {
  ACQUISITION = "acquisition",
  COLLECTION = "collection"
}

interface WithdrawalCashTransactionInterface extends TransactionInterface {
  providers?: {
    wealthkernel?: {
      id: string;
      status: WithdrawalStatusType;
    };
  };
  transferWithIntermediary?: {
    [TransferWithIntermediaryStageEnum.COLLECTION]?: {
      incomingPayment?: {
        providers: {
          devengo: {
            id: string;
            status: IncomingPaymentStatusType;
            accountId: string;
          };
        };
      };
      outgoingPayment?: {
        providers: {
          devengo: {
            id: string;
            status: OutgoingPaymentStatusType;
          };
        };
      };
    };
  };
  bankAccount: string | BankAccountDocument;
  bankReference: string;
  linkedUserDataRequest: string | UserDataRequestDocument;
  withdrawalRequestType: WithdrawalRequestType;
}

interface RevertRewardTransactionInterface extends TransactionInterface {
  linkedUserDataRequest: string | UserDataRequestDocument;
  reward: string | RewardDocument;

  readonly orders: OrderDocument[];
}

interface WealthyhoodDividendTransactionInterface
  extends TransactionInterface,
    Omit<WealthyhoodDividendTransactionDTOInterface, "owner" | "portfolio"> {}

export interface WealthyhoodDividendTransactionDTOInterface extends TransactionInterfaceDTO {
  price: plansConfig.PriceType;
  dividendMonth?: string; // YYYY-MM
  hasViewedAppModal?: boolean;
  deposit?: {
    providers?: {
      wealthkernel: {
        id?: string;
        status?: BonusStatusType;
        submittedAt?: Date;
      };
    };
  };
}

export interface AssetTransactionInterfaceDTO extends TransactionInterfaceDTO {
  portfolioTransactionCategory: PortfolioTransactionCategoryType;
  pendingDeposit?: string;
  pendingGift?: string;
  linkedUserDataRequest?: string;
  linkedAutomation?: string;
  executionWindow: ExecutionWindowsType;
  originalInvestmentAmount: number;
  status: TransactionStatusType;
}

export interface CashbackTransactionDTOInterface extends TransactionInterfaceDTO {
  price: plansConfig.PriceType;
  cashbackMonth: string; // YYYY-MM
  linkedAssetTransaction?: string;
  deposit?: {
    providers?: {
      wealthkernel: {
        id?: string;
        status?: BonusStatusType;
        submittedAt?: Date;
      };
    };
  };
}

interface AssetTransactionInterface
  extends TransactionInterface,
    Omit<AssetTransactionInterfaceDTO, "owner" | "portfolio" | "pendingDeposit" | "linkedAutomation"> {
  orders: OrderDocument[];
  pendingDeposit?: DepositCashTransactionDocument | string;
  linkedAutomation?: AutomationDocument | string;
  fees: FeesType;
  displayQuantity?: number;
  executionProgress?: ExecutionProgressType;
  foreignCurrencyRates?: ForeignCurrencyRatesType;
}

interface RebalanceTransactionInterface extends TransactionInterface {
  targetAllocation: InitialHoldingsAllocationType[];
  rebalanceStatus: RebalanceTransactionStatusType;
  fees: FeesType;
  buyExecutionWindow: MarketHoursExecutionWindowType;
  sellExecutionWindow: MarketHoursExecutionWindowType;
  linkedAutomation?: string | AutomationDocument;

  readonly orders: OrderDocument[];
  readonly hasSellExecutionStarted: boolean;
  readonly hasBuyExecutionStarted: boolean;
}

interface DividendTransactionInterface extends TransactionInterface {
  readonly asset: investmentUniverseConfig.AssetType;
  readonly isin: string;
  readonly wealthkernel?: {
    id: string;
    status: WealthkernelTransactionStatusType;
  };
}

interface CashbackTransactionInterface
  extends TransactionInterface,
    Omit<CashbackTransactionDTOInterface, "owner" | "portfolio" | "linkedAssetTransaction"> {
  linkedAssetTransaction: string;
}

interface ChargeTransactionInterface extends TransactionInterface {
  readonly chargeMethod: ChargeMethodType;
  readonly chargeMonth: string; // YYYY-MM
  readonly chargeType: ChargeTypeType;
  readonly subscription: string | SubscriptionDocument;
  readonly paymentMethod?: string | PaymentMethodDocument;
  readonly bankAccount?: string | BankAccountDocument;
  readonly bankReference?: string;
  readonly price?: plansConfig.PriceType;
  // The WK ID of the charge taking out the amount from the user's portfolio and moving it to ours.
  readonly providers?: {
    wealthkernel?: {
      id: string;
      status: WealthkernelTransactionStatusType;
    };
    // If chargeMethod is direct debit, the payment is done via GoCardless.
    gocardless?: {
      id: string;
      status: gocardless.PaymentStatusType;
    };
  };
  readonly originalChargeAmount: number;

  // VIRTUALS
  readonly orders: OrderDocument[];
  readonly paymentStatus?: ChargePaymentStatusType; // only present when charge method is direct-debit
}

export interface SavingsTopupTransactionDTOInterface extends TransactionInterfaceDTO {
  pendingDeposit: string;
  status: TransactionStatusType;
}

export interface SavingsTopupTransactionInterface
  extends TransactionInterface,
    Omit<SavingsTopupTransactionDTOInterface, "owner" | "portfolio" | "pendingDeposit"> {
  pendingDeposit?: DepositCashTransactionDocument;
  linkedAutomation?: string | AutomationDocument;

  // VIRTUALS
  readonly order: OrderDocument;
  readonly displayTitle: string;
  readonly linkedSavingsDividend?: SavingsDividendTransactionDocument;
}

export interface SavingsWithdrawalTransactionDTOInterface extends TransactionInterfaceDTO {
  status: TransactionStatusType;
}

export interface SavingsWithdrawalTransactionInterface
  extends TransactionInterface,
    Omit<SavingsWithdrawalTransactionDTOInterface, "owner" | "portfolio"> {
  // VIRTUALS
  readonly order: OrderDocument;
  readonly displayTitle: string;
}

export interface SavingsDividendTransactionDTOInterface extends TransactionInterfaceDTO {
  fees: FeesType;
  status: TransactionStatusType;
  savingsProduct: savingsUniverseConfig.SavingsProductType;
  originalDividendAmount: number;
  dividendMonth?: string; // YYYY-MM
}

export interface SavingsDividendTransactionInterface
  extends TransactionInterface,
    Omit<SavingsDividendTransactionDTOInterface, "owner" | "portfolio" | "linkedSavingsTopup"> {
  linkedSavingsTopup?: SavingsTopupTransactionDocument;

  // VIRTUALS
  readonly displayTitle: string;
}

export interface TransactionDocument extends TransactionInterface, DocumentWithId {}

export interface DepositCashTransactionDocument extends DepositCashTransactionInterface, TransactionDocument {}

export interface WithdrawalCashTransactionDocument
  extends WithdrawalCashTransactionInterface,
    TransactionDocument {}

export interface AssetTransactionDocument extends AssetTransactionInterface, TransactionDocument {}

export interface RebalanceTransactionDocument extends RebalanceTransactionInterface, TransactionDocument {}

export interface DividendTransactionDocument extends DividendTransactionInterface, TransactionDocument {}

export interface ChargeTransactionDocument extends ChargeTransactionInterface, TransactionDocument {}

export interface CashbackTransactionDocument extends CashbackTransactionInterface, TransactionDocument {}

export interface RevertRewardTransactionDocument extends RevertRewardTransactionInterface, TransactionDocument {}

export interface WealthyhoodDividendTransactionDocument
  extends WealthyhoodDividendTransactionInterface,
    TransactionDocument {}

export interface SavingsTopupTransactionDocument extends SavingsTopupTransactionInterface, TransactionDocument {}

export interface SavingsWithdrawalTransactionDocument
  extends SavingsWithdrawalTransactionInterface,
    TransactionDocument {}

export interface SavingsDividendTransactionDocument
  extends SavingsDividendTransactionInterface,
    TransactionDocument {}
