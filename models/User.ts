import { AmlCheckDocument, AmlCheckFlagsType } from "./AmlCheck";
import { KycOperationDocument } from "./KycOperation";
import { ReferralCodeDocument } from "./ReferralCode";
import {
  countriesConfig,
  currenciesConfig,
  entitiesConfig,
  taxResidencyConfig
} from "@wealthyhood/shared-configs";
import { AccountDocument } from "./Account";
import { AddressDocument } from "./Address";
import { BankAccountDocument } from "./BankAccount";
import { ParticipantDocument } from "./Participant";
import { PortfolioDocument } from "./Portfolio";
import { SubscriptionDocument } from "./Subscription";
import { UserDataRequestDocument } from "./UserDataRequest";
import { EmploymentInfoType } from "../react-views/types/employmentConfig";
import { DocumentWithId } from "model";
import { RiskAssessmentDocument } from "./RiskAssessment";
import { WalletDocument } from "./Wallet";

type Auth0ObjectType = {
  id: string; // The primary Auth0 ID of the user (the one used when the user was created)
} & Partial<Record<AuthProviderType, string>>;

const AuthProviderArray = ["email", "apple", "google", "username-password"] as const;
export type AuthProviderType = typeof AuthProviderArray[number];

const PlatformArray = ["android", "ios"] as const;
export type PlatformType = typeof PlatformArray[number];
export enum KycStatusEnum {
  PENDING = "pending",
  FAILED = "failed",
  PASSED = "passed"
}
export const KycStatusArray = ["pending", "failed", "passed"] as const;
export type KycStatusType = typeof KycStatusArray[number];

const PortfolioConversionStatusArrayConst = ["notStarted", "inProgress", "completed", "fullyWithdrawn"] as const;
export type PortfolioConversionStatusType = typeof PortfolioConversionStatusArrayConst[number];
export type TaxResidencyType = {
  countryCode: countriesConfig.CountryCodesType;
  proofType: taxResidencyConfig.IdentifierType;
  value: string;
};
export const UKResidencyStatusArrayConst = ["uk", "non-uk", ""] as const;
export type UKResidencyStatusType = typeof UKResidencyStatusArrayConst[number];

type WealthkernelType = {
  partyId?: string;
};
export enum UserTypeEnum {
  ADMIN = "ADMIN",
  INVESTOR = "INVESTOR",
  TEST_ACCOUNT = "TEST_ACCOUNT"
}

export enum AmlScreeningResultEnum {
  NoHit = "NoHit",
  MaterialAdverseMediaPEP = "MaterialAdverseMediaPEP"
}

export const EmploymentStatusArray = [
  "FullTime",
  "PartTime",
  "SelfEmployed",
  "Unemployed",
  "Retired",
  "Student",
  "NotWorkingDueToIllnessOrDisability",
  "CarerOrParent"
] as const;
export type EmploymentStatusType = typeof EmploymentStatusArray[number];

export enum SourceOfFundsEnum {
  LinkedUKBankAccount = "LinkedUKBankAccount"
}

export const SourceOfWealthArray = [
  "Salary",
  "Inheritance",
  "Gift",
  "BusinessOwnership",
  "SaleOfProperty",
  "GamblingOrLottery",
  "PersonalSavings",
  "LegalSettlement",
  "SaleOfInvestments",
  "Dividend"
] as const;
export type SourceOfWealthType = typeof SourceOfWealthArray[number];

export type UserAmlChecksType = AmlCheckFlagsType & {
  updatedAt: string;
};

export interface UserInterface {
  auth0: Auth0ObjectType;
  createdAt: Date;
  submittedRequiredInfoAt?: Date;
  currency?: currenciesConfig.MainCurrencyType;
  companyEntity?: entitiesConfig.CompanyEntityEnum;
  dateOfBirth?: Date;
  email: string;
  emailDisposable: boolean;
  emailVerified: boolean;
  hasAcceptedTerms: boolean;
  hasSeenBilling: boolean;
  firstName?: string;
  img: string;
  initialInvestment: number;
  kycStatus: KycStatusType;
  lastName?: string;
  lastLogin: Date;
  nationalities: countriesConfig.CountryCodesType[];
  role: UserTypeEnum[];
  passports: string[];
  monthlyInvestment: number;
  portfolioConversionStatus: PortfolioConversionStatusType;
  referredByEmail: string;
  providers?: {
    wealthkernel?: {
      id: string;
    };
    gocardless?: {
      id: string;
    };
    complyAdvantage?: {
      id: string;
    };
    sumsub?: {
      id: string;
    };
  };
  residencyCountry?: countriesConfig.CountryCodesType;
  taxResidency: TaxResidencyType;
  UKResidentStatus: UKResidencyStatusType;
  viewedWelcomePage: boolean;
  wealthkernel: WealthkernelType;
  lastLoginPlatform: PlatformType;
  canSendGiftUntil: Date;
  gocardless?: {
    id: string;
  };
  employmentInfo: EmploymentInfoType;
  isPotentiallyDuplicateAccount: boolean;
  viewedKYCSuccessPage: boolean;
  viewedWealthybitesScreen: boolean;
  amlScreening: AmlScreeningResultEnum;
  amlChecks: UserAmlChecksType;
  skippedPortfolioCreation?: boolean;
  isPassportVerified: boolean;

  // virtual getters
  readonly isPriceMomentumSentimentEnabled: boolean;
  readonly shouldShowStatements: boolean;
  readonly accounts: AccountDocument[];
  readonly addresses: AddressDocument[];
  readonly addressSubmitted: boolean;
  readonly passportSubmitted: boolean;
  readonly taxResidencySubmitted: boolean;
  readonly bankLinked: boolean;
  readonly bankAccounts: BankAccountDocument[];
  readonly portfolios: PortfolioDocument[];
  readonly generalInvestmentAccount: AccountDocument;
  readonly subscription: SubscriptionDocument;
  readonly userDataRequests: UserDataRequestDocument[];
  readonly submittedRequiredInfo: boolean;
  readonly isConvertingPortfolio: boolean;
  readonly hasConvertedPortfolio: boolean;
  readonly participant: ParticipantDocument;
  readonly hasSubscription: boolean;
  readonly hasDisassociationRequest: boolean;
  readonly hasSuspendedAccount: boolean;
  readonly intercomUserIdHashAndroid: string;
  readonly intercomUserIdHashIos: string;
  readonly intercomUserIdHashWeb: string;
  readonly canUnlockFreeShare: boolean;
  readonly canReceiveCashback: boolean;
  readonly hasFailedKyc: boolean;
  readonly hasPassedKyc: boolean;
  readonly oneTimeReferralCode: ReferralCodeDocument | string;
  readonly wallet: WalletDocument | string;
  // TODO: This virtual is temporary until we migrate to the new fields on the mobile clients
  readonly kycPassed: boolean;
  readonly employmentInfoSubmitted: boolean;
  readonly isCashbackFeatureEnabled: boolean;
  readonly isRoboAdvisorEnabled: boolean;
  readonly isSweatcoinReferred: boolean;
  readonly shouldShowKYCSuccessPage: boolean;
  readonly isVerified: boolean;
  readonly kycOperation?: KycOperationDocument;
  readonly hasCompletedKycJourney: boolean;
  readonly fullName?: string;
  readonly latestRiskAssessment?: RiskAssessmentDocument;
  readonly latestAmlCheck?: AmlCheckDocument;
  readonly isRealtimeETFExecutionEnabled: boolean;
  readonly hasJoinedWaitingList: boolean;
  readonly isEuWhitelisted: boolean;
}

export interface UserDocument extends UserInterface, DocumentWithId {}
