import { UserDocument } from "./User";
import { DocumentWithId } from "model";

/**
 * TYPES
 */
export const UserDataRequestArray = ["gdpr-delete", "disassociation"] as const;
export type UserDataRequestType = typeof UserDataRequestArray[number];

export const UserDataRequestStatusArray = ["Created", "Completed"] as const;
export type UserDataRequestStatusType = typeof UserDataRequestStatusArray[number];

/**
 * DOCUMENTS
 */
export interface UserDataRequestDTOInterface {
  owner: string;
  requestType: UserDataRequestType;
}

export interface UserDataRequestInterface extends Omit<UserDataRequestDTOInterface, "owner"> {
  owner: string | UserDocument;
  status: UserDataRequestStatusType;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserDataRequestDocument extends UserDataRequestInterface, DocumentWithId {}
