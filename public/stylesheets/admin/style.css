/*
  Desktop & mobile fund list display
*/

@media only screen and (min-width: 868px) {
  .table-dekstop-list {
    display: block;
  }

  .mobile-fund-list {
    display: none;
  }
}

@media only screen and (max-width: 868px) {
  .table-desktop-list {
    display: none;
  }

  .mobile-fund-list {
    display: block;
  }
}

/*
  Notification Item fix
*/
.kt-notification .kt-notification__item:after {
  border: 0;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smooth: antialiased;
  content: "" !important;
  font-size: 0.8rem;
  line-height: 0;
  position: relative;
}

/*
  Modal Close Icon
*/

.modal .modal-content .modal-header .close:before {
  content: "";
}

.modal .modal-content .modal-header .close span {
  display: block;
}

.modal .modal-header .close {
  padding: 1.5rem 1.75rem !important;
  margin: -1.5rem -1.75rem -1.5rem auto !important
  ;
}

/*
  Blur Effect
*/
.blur-effect {
  -webkit-filter: blur(3px);
  -moz-filter: blur(3px);
  -o-filter: blur(3px);
  -ms-filter: blur(3px);
  filter: blur(3px);
}

/*
  Grayscale Effect
*/
.shadow-effect {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.shadow-effect-sm {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 6px 20px 0 rgba(0, 0, 0, 0.09);
}

/*
  NoUI Slider
*/

.noUi-horizontal {
  height: 8px !important;
  background-color: #dce9f0 !important;
  box-shadow: 0 !important;
  border: 0px !important;
}

.noUi-handle {
  top: -10px !important;
}

/*
  Other
*/

.hidden {
  display: none;
}

.border-radius-sm {
  border-radius: 5px;
}

.border-radius-lg {
  border-radius: 10px;
}

.border-radius-xl {
  border-radius: 20px;
}

.font-size-xxl {
  font-size: 1.375rem !important;
}

.font-size-xl {
  font-size: 1.25rem !important;
}

.font-size-sm {
  font-size: 0.9rem !important;
}

/*
  Shepher Custom Theme
*/
#overlay {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  opacity: 0.9;
  z-index: 2;
  cursor: pointer;
}

.shepherd-text {
  padding: 0 !important;
}

.shepherd-element {
  border-radius: 10px !important;
  opacity: 1 !important;
}

.shepherd-content {
  background-color: #ffffff;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  border-radius: 10px !important;
}

.shepherd-header {
  display: none !important;
}

/*
  Sticky
*/
.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 90px;
}

.sticky-150 {
  position: -webkit-sticky;
  position: sticky;
  top: 150px;
}

/*
 XS buttons
*/
.btn.btn-xs {
  padding: 0.25rem !important;
  font-size: 0.65rem;
  line-height: 1;
  border-radius: 0.2rem;
}

.btn.btn-square {
  border-radius: 0;
}

/*
  Percent Input
*/
/* prepare wrapper element */
.percent {
  display: inline-block;
  position: relative;
}

/* position the unit to the right of the wrapper */
.percent::after {
  position: absolute;
  top: 0.8rem;
  right: 3rem;
  content: "%";
}

/* handle Firefox (arrows always shown) */
@supports (-moz-appearance: none) {
  .percent::after {
    right: 3rem;
  }
}

@media (max-width: 768px) {
  .percent::after {
    right: 1rem;
  }
}
/*
  Keen2 Custom style
*/

.spinner.spinner-sm::before {
  width: 0.7rem;
  height: 0.7rem;
}

.spinner.spinner-xl::before {
  width: 6rem;
  height: 6rem;
  margin-top: -2.5rem;
}
.spinner.spinner-xl.spinner-center:before {
  left: 50%;
  margin-left: -3.5rem;
}

.bg-navy {
  background-color: #242939 !important;
}
body {
  background-color: #fff !important;
}
.aside {
  border-radius: 1rem;
  background-color: #242939;
  width: 220px !important;
}
.aside-fixed.aside-minimize:not(.aside-minimize-hover) .aside .aside-sm-logo {
  display: block !important;
}
.aside-fixed.aside-minimize:not(.aside-minimize-hover) .aside .aside-toggle {
  display: none;
}
.aside-fixed .wrapper {
  padding-left: 300px;
}
h1 {
  font-size: 2.6rem !important;
  font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
}
h2,
h3,
h4,
h5,
h6 {
  font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
}
.h1 {
  font-size: 2.6rem !important;
  font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
}
@media (min-width: 768px) {
  .h1-md {
    font-size: 2.6rem !important;
    font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
  }
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: "Roboto", "-apple-system", Arial, sans-serif !important;
}

.svg-icon.svg-icon-xs svg {
  height: 1rem !important;
  width: 1rem !important;
}

.cursor-default {
  cursor: default !important;
}

.outline-none {
  outline: none !important;
}

.modal-fullscreen {
  width: 90vw;
  max-width: none;
  margin-top: 0px;
  margin-bottom: 0px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 991.98px) {
  .aside-fixed .wrapper {
    padding-left: 0px;
  }
}

/* A class for elements that need to be displayed always above everything else 
   on the z-axis, such as toasts and notifications. The z-index defined should 
   be the highest by convention, since there's no limit. All other elements 
   must have a lower z-index.
   Optionally, you can define further classes to serve as layers, such as 
   z-first-level, z-second-level, z-third-level, etc. or even
   z-first-floor, z-second-floor, z-third-floor, etc. */
.z-max-level {
  z-index: 2000;
}

/* Hidden arrows for type number on small screens */
@media (max-width: 768px) {
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}

.line-through {
  text-decoration: line-through;
}

.fade-in {
  animation: fadein 0.5s;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.outline-hover {
  cursor: pointer;
}

.outline-hover:hover {
  border: 1px solid #007bff !important;
}

.btn-light-white:hover {
  background-color: #e4f1ff !important;
}
