.kt-error404-v1 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.kt-error404-v2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
@media (max-width: 768px) {
  .kt-error404-v1 {
    padding: 2rem;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
  }
}
.kt-error404-v1 .kt-error404-v1__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
@media (max-width: 768px) {
  .kt-error404-v1 .kt-error404-v1__content {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
}
.kt-error404-v1 .kt-error404-v1__content .kt-error404-v1__title {
  font-size: 12rem;
  /* color: #fb2f73; */
  color: #646c9a;
  font-weight: 700;
}
.kt-error404-v1 .kt-error404-v1__content .kt-error404-v1__desc {
  font-size: 1.5rem;
}
.kt-error404-v1 .kt-error404-v1__image {
  width: 50%;
  position: relative;
}
@media (max-width: 768px) {
  .kt-error404-v1 .kt-error404-v1__image {
    width: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }
}
.kt-error404-v1 .kt-error404-v1__image .kt-error404-v1__image-content {
  width: 100%;
}

.error-page-button-v1 {
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  background: #101327;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 16px;
  border: none;
  font-size: 18px;
  line-height: 25.2px;
  font-weight: 400;
  color: #fff !important;
}

.error-page-subtitle-v1 {
  font-weight: 600;
  font-size: 40px;
  line-height: 48px;
  color: #11152e;
  margin-bottom: 24px;
}

.error-page-desc-v1 {
  text-align: center;
  margin: 0 auto;
  font-weight: 300;
  line-height: 25.2px;
  font-size: 18px;
  max-width: 600px;
  color: #757575;
  margin-bottom: 48px;
}

.error-page-button-v1:hover {
  cursor: pointer;
  background: #434659;
}
