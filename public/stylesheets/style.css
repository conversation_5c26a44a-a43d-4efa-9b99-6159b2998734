html,
body {
  height: 100%;
  width: 100%;
  font-family: "Poppins", sans-serif !important;
}

.container-fluid {
  /* padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto; */
  overflow-x: hidden;
}

.direct-debit-schedule {
  background-color: #f1f3fd;
  border-radius: 32px;
  padding: 40px;
}

.timeline {
  width: 95%;
  margin-left: auto;
  padding-top: 16px;
}

.timeline-item {
  display: flex;
  gap: 24px;
  position: relative; /* Relative positioning for the pseudo-element */
  padding-bottom: 12px;
}

.timeline-item-inactive .timeline-item-icon i {
  opacity: 0.5;
}

.timeline-item-inactive::before {
  border-left: 2px dotted rgba(83, 106, 227, 0.5) !important;
}

.timeline-item-active::before {
  border-left: 2px solid #536ae3 !important;
}

.timeline-item-half-active::after {
  bottom: 35% !important;
  border-left: 2px solid #536ae3 !important;
  content: "";
  position: absolute;
  left: -20px !important;
  top: 0;
  margin-left: -12px;
  z-index: 2;
}

.timeline-item::before {
  margin-left: -12px;
  content: "";
  position: absolute;
  left: -20px; /* Adjust this value to align the dashed line */
  top: 0;
  bottom: 0;
  border-left: 2px dotted #536ae3; /* Dotted line */
  z-index: 1; /* Ensure it stays behind the icons and text */
}

.timeline-item:last-child::before {
  display: none; /* Hide the dashed line after the last item */
}

.timeline-item-description {
  padding-top: 6px;
}

.timeline-item-description p {
  font-size: 0.875em;
}

.timeline-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-left: -50px;
  flex-shrink: 0;
  overflow: hidden;
  cursor: default;
  z-index: 100;
}

.timeline-item-icon.faded-icon {
  background-color: #f1f3fd;
}

.timeline-item-icon.faded-white-icon {
  background-color: #ffffff;
}

.text-primary-blue {
  color: #536ae3 !important;
}

.asset-class {
  max-height: 246px;
  max-width: 246px;
}

.m-12-px {
  margin-top: 12px;
  margin-bottom: 12px;
}

.vh-100 {
  height: 100vh;
  overflow-y: hidden;
}

.wh-card {
  /* Item */

  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;

  background: rgba(84, 107, 229, 0.08);
  border-radius: 16px;
  border: none !important;
}

.wh-card-body {
  align-items: center;

  background: rgba(84, 107, 229, 0.08);
  border-radius: 32px;
  border: none;
}

.daily-summary-gradient {
  background: linear-gradient(180deg, #F2F4FD 45%, rgba(255, 255, 255, 0.157458) 95%, #FFFFFF 100%);
}

.wh-lab-bottom-buttons {
  align-items: center;
  background: rgba(84, 107, 229, 0.08);
  border-radius: 24px;
  border: none;
  box-shadow: 0 4px 25px rgba(17, 21, 46, 0.12);
}

.wh-news-card {
  align-items: center;
  background-color: #e7e7e7;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  border-radius: 24px;
  border: none;
  min-height: 480px;
  max-width: 270px;
  min-width: 270px;
  color: #ffffff;
}

.wh-learning-card {
  align-items: center;
  border: none;
  border-radius: 24px;
  min-height: 100px;
  min-width: 300px;
  color: #ffffff;
}

.wh-learning-card-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.wh-invite-card {
  width: 100%;
  height: 260px;
  max-width: 380px;
  max-height: 260px;
  min-width: 380px;
  min-height: 260px;
}

.wh-account-card {
  width: 100%;
  height: 220px;
  max-width: 380px;
  max-height: 288px;
  min-width: 350px;
  min-height: 220px;
  border-radius: 20px;
}

.payment-method-select {
  height: 36px;
  max-height: 36px;
  min-height: 36px;
  width: fit-content;
  border-radius: 59px;
  background-color: #F4F4F4;
}

.payment-method-select img {
  width: 24px;
  height: 24px;
  border-radius: 227px;
}

.wh-coming-soon-card {
  background: #f1f3fd;
  color: #4957d5;
  border-radius: 4px;
  padding: 3px 6px;
}

.max-w-600px {
  max-width: 600px;
}

.wh-success-card {
  border-radius: 24px 24px 0 0;
  width: 100%;
}

@media (min-width: 600px) {
  .wh-success-card {
    height: 720px;
    max-width: 960px;
    border-radius: 24px;
  }
}

.rounded-pill {
  font-weight: 600;
  cursor: pointer;

  background: rgba(84, 107, 229, 0.08);
  border-radius: 60px;
  border: none !important;
}

.strikethrough {
  text-decoration: line-through;
}

.wh-card-amount {
  /* Item */

  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 14px;
  font-weight: 600;
  cursor: pointer;

  background: rgba(84, 107, 229, 0.08);
  border-radius: 60px;
  border: none !important;
}

.wh-card-amount:hover {
  /* Item */

  /* Auto layout */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 14px;
  font-weight: 600;
  cursor: pointer;

  opacity: 0.7;
  background: rgba(84, 107, 229, 0.05);
  border-radius: 60px;
  border: none !important;
}

.wh-account-card-option {
  align-items: center;
  height: 80px;
  width: 100%;
  /* Wealthyhood Brand/Wealthyhood Burple 1 - 8% */
  border: 1px solid rgba(84, 107, 229, 0.08);
  border-radius: 16px;
  cursor: pointer;
}

.wh-select-account-card-option {
  align-items: center;
  height: 80px;
  min-width: 400px;

  /* Wealthyhood Brand/Wealthyhood Burple 1 - 8% */
  outline: 1px solid rgba(84, 107, 229, 0.08);
  border-radius: 16px;
  cursor: pointer;
}

.wh-select-account-card-option:hover {
  /* Wealthyhood Brand/Wealthyhood Burple 1 - 8% */
  outline: 2px solid #536ae3;
}

.wh-automation-option {
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #f1f3fd;
  border-radius: 16px;
}

.wh-account-card-option-selected {
  border: 1px solid #377dff;
  background-color: rgba(84, 107, 229, 0.08);
}

.world-card-radius {
  border-radius: 16px !important;
}

.wh-simple-card {
  border-radius: 16px !important;
  background: rgba(84, 107, 229, 0.08);
}

.wh-holding {
  border-radius: 32px !important;
  border: 1px solid rgba(84, 107, 229, 0.08) !important;
  box-shadow: 2px 0 3px rgba(0, 0, 0, 0.04), -1px 0 1px rgba(0, 0, 0, 0.06);
}

.holding-component {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

.info-box-text {
  background-color: white;
  min-width: 360px;
  min-height: 290px;
  color: #757575;
  text-align: left;
  box-shadow: 0 1px 2px 0 #********;
  border-radius: 24px;
  padding: 0 1em;
}

.info-box-title {
  margin-bottom: 34px;
  margin-top: 40px;
  color: black;
  font-weight: 600;
  font-size: 18px;
  text-align: left;
}

.info-text-bold {
  color: black;
  font-weight: 600;
}

.asset-card-lg {
  border-radius: 16px !important;
  max-height: 68px;
  max-width: 68px;
  min-height: 68px;
  min-width: 68px;
  height: 68px;
  width: 68px;
  background: transparent;
}

.asset-card-md {
  border-radius: 16px !important;
  max-height: 52px;
  max-width: 52px;
  min-height: 52px;
  min-width: 52px;
  height: 52px;
  width: 52px;
  background: transparent;
}

.bg-selected {
  background-color: #d2dcfa !important;
}

.cursor-pointer {
  cursor: pointer;
}

#progressbar {
  display: flex;
  justify-content: space-between;
  margin: 0;
  padding: 0;
  height: 46px;
  width: 416px;
}

#progressbar li:first-child {
  padding-left: 0;
}

#progressbar li:last-child {
  padding-right: 0;
}

#progressbar li {
  list-style-type: none;
  border: 4px;
  border-color: #4051ac;
  font-size: 18px;
  width: 46px;
  height: 46px;
}

.li-connector {
  border-top: solid !important;
  border: 4px;
  border-color: #4051ac !important;
  margin-top: 22px;
  width: 100% !important;
}

.li-connector-done {
  border-top: solid !important;
  border: 4px;
  border-color: #ffffff !important;
  margin-top: 22px;
  width: 100% !important;
}

/*Icons in the ProgressBar*/
#progressbar #step-1:before {
  content: "1";
}

#progressbar #step-2:before {
  content: "2";
}

#progressbar #step-3:before {
  content: "3";
}

#progressbar #step-4:before {
  content: "4";
}

#progressbar #step-5:before {
  /* content: "\f00c"; */
  content: "5";
}

/*ProgressBar before any progress*/
#progressbar li:before {
  text-align: center !important;
  width: 46px;
  height: 46px;
  line-height: 40px;
  display: block;
  font-size: 18px;
  border-radius: 50%;
  border: 4px solid;
  border-color: #4051ac;
  margin: 0 auto 10px auto;
}

li.done:before {
  border-color: #ffffff !important;
}

#progressbar li.active:before {
  background: #ffffff;
  color: #4051ac;
  border-color: #ffffff;
}

.bg-primary {
  background-color: #546be5 !important;
}

.bg-loading-primary {
  background-color: #546be5;
  opacity: 0.9;
  z-index: 3000;
}

.bg-loading-danger {
  background-color: #d63e3e;
  opacity: 0.9;
  z-index: 3000;
}

.bg-warning {
  background-color: #f0ad4e !important;
}

.bg-white {
  background-color: #ffffff !important;
}

.bg-main {
  background-color: #ffffff !important;
}

@media screen and (min-width: 799px) and (min-height: 600px) {
  .bg-main {
    background-color: #f8f9fa !important;
  }
}

.btn-circle {
  padding: 24px;

  width: 60px;
  height: 60px;

  /* Wealthyhood New/Wealthyhood dark blue - 20% */
  background: rgba(16, 19, 39, 0.2);
  border-radius: 156px;
  color: #ffffff;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

.btn-circle:hover,
.btn-circle:active,
.btn-circle:focus,
.btn-circle:active:focus {
  padding: 24px;

  width: 60px;
  height: 60px;

  /* Wealthyhood New/Wealthyhood dark blue - 20% */
  background: rgba(16, 19, 39, 0.2);
  border-radius: 156px;
  color: #ffffff;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* Button Primary */
.btn-primary {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #101327;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 25px;
  border: none;
}

.btn-primary:hover,
.btn-primary:active,
.btn-primary:focus,
.btn-primary:active:focus {
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #434659 !important;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border: none;
}

.btn-primary.disabled,
.btn-primary:disabled {
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #282b3d;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;
  opacity: 0.7;

  cursor: none;
  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border: none;
}

/* Button Secondary */
.btn-secondary {
  /* Button */
  min-width: 100px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #f3f3f4;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  color: #101327;

  border-radius: 25px;
  border: none;
}

.btn-secondary:hover,
.btn-secondary:active,
.btn-secondary:focus,
.btn-secondary:active:focus {
  /* Button */
  min-width: 100px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #dbdbdc !important;
  color: #101327 !important;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: none;
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  /* Button */
  min-width: 100px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #dbdbdc;
  color: #101327;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;
  opacity: 0.7;

  cursor: none;
  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 25px;
  border: none;
}

/* Button Light */
.btn-light {
  /* Button */
  min-width: 100px;
  height: 50px;
  background: #DCE2FD;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;
  color: #11152E;

  border-radius: 25px;
  border: none;
}

.btn-light:hover,
.btn-light:active,
.btn-light:focus,
.btn-light:active:focus {
  background: #DCE2FD;
}

.btn-wh-clean,
.btn-wh-clean:hover,
.btn-wh-clean:disabled,
.btn-wh-clean.disabled {
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  padding-top: 10px;
  border-radius: 25px;
}

.btn-wh-clean:hover,
.btn-wh-clean:active,
.btn-wh-clean:focus,
.btn-wh-clean:active:focus {
  border: solid;
}

@media (max-width: 500px) {
  .btn-nmw {
    min-width: 0 !important;
    width: 100% !important;
  }

  .btn-nmw:hover,
  .btn-nmw:active,
  .btn-nmw:focus,
  .btn-nmw:active:focus,
  .btn-nmw:disabled {
    min-width: 0 !important;
    width: 100% !important;
  }

  .btn-primary {
    max-width: 100% !important;
  }

  .btn-ghost {
    max-width: 100% !important;
  }

  .btn-outline-primary {
    max-width: 100% !important;
  }

  .btn-danger {
    max-width: 100% !important;
  }

  .btn-clean {
    max-width: 100% !important;
  }
}

.btn-outline-primary {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #ffffff;
  color: #101327;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: solid;
  border-color: #101327;
}

.btn-outline-primary:hover,
.btn-outline-primary:active,
.btn-outline-primary:focus,
.btn-outline-primary:active:focus {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #101327 !important;
  color: #ffffff !important;

  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: solid;
  border-color: #282b3d !important;
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #ffffff;
  color: #101327;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: solid;
  border-color: #101327;
}

.btn-danger {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #d63e3e;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 25px;
  border: none;
}

.btn-danger:hover {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #d96060;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 25px;
  border: none;
}

.btn-ghost {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: rgba(84, 107, 229, 0.08);
  color: #546be5;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: none;
  text-align: center;
}

.btn-ghost:hover,
.btn-ghost:active,
.btn-ghost:focus,
.btn-ghost:active:focus {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #ffffff !important;
  color: #546be5 !important;

  padding-left: 15px;
  padding-right: 15px;
  padding-top: 10px;
  padding-bottom: 10px;

  border-radius: 25px;
  border: solid;
  border-color: #546be5 !important;
  text-align: center;
}

.btn-nw,
.btn-nw:hover,
.btn-nw:active,
.btn-nw:focus,
.btn-nw:active:focus,
.btn-nw.disabled,
.btn-nw:disabled {
  min-width: 50px !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  text-align: center;
}

.btn-learning {
  /* Button */
  height: 40px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: rgba(4, 4, 4, 0.55);
  border-radius: 59px;
  padding: 8px 12px;
  color: #ffffff;
  width: fit-content;
}

.btn-learning:hover {
  background: rgba(4, 4, 4, 0.3);
}

.btn-small {
  min-width: unset;
  height: 50px;
  width: 50px;
  border-radius: 32px;
}

.btn-small:hover {
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
}

.btn-plan-reverse {
  background: #101327;
  color: #ffffff;
  padding: 8px 12px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.btn-plan-reverse:hover {
  background: #434659 !important;
  color: #ffffff;
}

.btn-plan {
  background: #ffffff;
  color: #11152e;
  padding: 8px 12px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.btn-plan:hover {
  background: #11152e !important;
  color: #ffffff;
}

.btn-plan-recurrence {
  padding: 6px 12px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.btn-plan-recurrence-selected {
  background: #101327;
  color: #ffffff;
  padding: 6px 12px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.btn-plan-recurrence:hover {
  background: #434659 !important;
  color: #ffffff;
}

.btn-plan-recurrence-selected:hover {
  background: #434659 !important;
  color: #ffffff;
}

.plan-secondary-label {
  color: #11152e;
  padding: 4px 10px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.plan-save-percentage-label {
  background: #ffffff;
  padding: 4px 10px !important;
  text-align: center;

  border: none;
  border-radius: 32px;
}

.pb-10p {
  padding-bottom: 10%;
}

.h-90 {
  height: 90%;
}

.h-10 {
  height: 10%;
}

.world-card {
  /* Auto layout */
  justify-content: center;
  align-items: center;
  padding: 12px;

  background: rgba(84, 107, 229, 0.08);
  border-radius: 16px;
}

.active-sector {
  text-align: center !important;
  padding-right: 16px !important;
  padding-left: 16px !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  /* Asset Classes/Stocks */
  color: #ffffff !important;
  background: #0c0e18;
  border-radius: 40px;
}

.active-transaction-filter {
  text-align: center !important;
  padding-right: 16px !important;
  padding-left: 16px !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  color: #ffffff !important;
  background: #0c0e18;
  border-radius: 40px;
}

.active-method {
  height: 45px;
  text-align: center !important;
  padding-top: 10px !important;
  /* Asset Classes/Stocks */
  background: #282b3d;
  color: #ffffff;
  border-radius: 100px;
}

.search-bar-sticky {
  position: sticky;
  align-self: flex-start;
}

.search-bar {
  display: flex;
  padding: 15px;
  background-color: #f4f4f4;
  border-radius: 15px;
  position: fixed;
  width: 30em;
  z-index: 10;
}

.search-results {
  display: flex;
  flex-direction: column;
  height: 90vh;
  overflow-y: scroll;
  padding-top: 4em;
}
.search-results::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.search-results {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.selected-country {
  background-color: #f1f3fd;
  border-radius: 12px;
}

.search-bar-input {
  border: 0;
  background-color: #f4f4f4;
  font-weight: lighter;
  outline: none;
  width: 100%;
}

.search-bar-icon {
  color: #536ae3;
}

.border-bottom-countries-list {
  border-bottom: 1px solid #f1f3fd;
}

.search-results-none {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 40vh;
}

.search-text-not-found {
  font-weight: 500;
}
.search-text-shorten-search {
  font-weight: lighter;
  margin-top: 1em;
}

.flag-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 0.6px solid #1013270d;
  border-radius: 10px;
  height: 100%;
  width: 60px;
}

.country-name {
  display: flex;
  align-items: center;
  font-weight: lighter;
}

.country-code {
  margin-right: 1em;
  font-weight: lighter;
  color: #a2a0a8;
}

.search-elements-list-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5em;
  padding: 0.7em;
  height: 4.5em;
}

.round-border {
  border-radius: 100px;
}

.sizing-box {
  height: 100px !important;
}

.faq .text-secondary strong {
  color: black;
  font-weight: 500;
}

.faq .text-secondary a {
  text-decoration: none;
  color: #546be5;
  font-weight: 500;
}

.signal-bars .bar {
  width: 2%;
  margin-right: 2%;
  display: inline-block;

  /* Wealthyhood Brand/Wealthyhood Blue 1 */
  background: #dbdbdc;
  border-radius: 8.98933px;
}

.signal-bars .bar:hover {
  opacity: 0.7;
}

.signal-bars .bar.bar-1 {
  height: 4.8%;
}

.signal-bars .bar.bar-2 {
  height: 9.6%;
}

.signal-bars .bar.bar-3 {
  height: 14.4%;
}

.signal-bars .bar.bar-4 {
  height: 19.2%;
}

.signal-bars .bar.bar-5 {
  height: 24%;
}

.signal-bars .bar.bar-6 {
  height: 28.8%;
}

.signal-bars .bar.bar-7 {
  height: 33.6%;
}

.signal-bars .bar.bar-8 {
  height: 38.4%;
}

.signal-bars .bar.bar-9 {
  height: 43.2%;
}

.signal-bars .bar.bar-10 {
  height: 48%;
}

.signal-bars .bar.bar-11 {
  height: 52.8%;
}

.signal-bars .bar.bar-12 {
  height: 57.6%;
}

.signal-bars .bar.bar-13 {
  height: 62.4%;
}

.signal-bars .bar.bar-14 {
  height: 67.2%;
}

.signal-bars .bar.bar-15 {
  height: 72%;
}

.signal-bars .bar.bar-16 {
  height: 76.8%;
}

.signal-bars .bar.bar-17 {
  height: 81.6%;
}

.signal-bars .bar.bar-18 {
  height: 86.4%;
}

.signal-bars .bar.bar-19 {
  height: 91.2%;
}

.signal-bars .bar.bar-20 {
  height: 96%;
}

.signal-bars .bar.bar-21 {
  height: 100%;
}

.filled-bar {
  background-color: #377dff !important;
}

.t-875 {
  font-size: 0.875em;
}

.t-825 {
  font-size: 0.875em;
}

.t-75 {
  font-size: 0.75em;
}

.t-50 {
  font-size: 0.5em;
}

.mobile-font {
  font-size: 0.875em;
}

.chart-outbox {
  text-align: center;
}

.chart-wrapper {
  width: auto;
  display: inline-block;
}

.loading-mask {
  pointer-events: none;
  background-color: rgba(255, 255, 255, 0.7);
  opacity: 0.5;
}

.etf-side-mask {
  background: rgba(0, 0, 0, 0.5);
}

.close {
  background-color: transparent;
  border: none;
  font-size: 1.5em;
}

.z-max-level {
  z-index: 2000;
}

.symbol-25 {
  max-width: 32px !important;
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

#cover-spin::after {
  content: "";
  display: block;
  position: absolute;
  left: 48%;
  top: 40%;
  width: 40px;
  height: 40px;
  border-style: solid;
  border-color: #377dff;
  border-top-color: transparent;
  border-width: 4px;
  border-radius: 50%;
  -webkit-animation: spin 0.8s linear infinite;
  animation: spin 0.8s linear infinite;
}

#cover-spin-relative {
  content: "";
  display: block;
  position: relative;
  width: 40px;
  height: 40px;
  border-style: solid;
  border-color: #377dff;
  border-top-color: transparent;
  border-width: 4px;
  border-radius: 50%;
  -webkit-animation: spin 0.8s linear infinite;
  animation: spin 0.8s linear infinite;
}

.bg-light-danger {
  background-color: #ffe2e5 !important;
}

.bg-light-warning {
  background-color: rgba(255, 150, 74, 0.15);
}

@media (min-width: 600px) {
  .onboarding-container {
    max-width: 600px;
    min-width: 300px;
  }
}

.steps-container {
  /* Item */

  /* Auto layout */
  display: flex;
  width: 60px;
  flex-direction: column;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 6px;
  padding-bottom: 6px;

  border-radius: 48px;
  border: none !important;
  background: rgba(16, 19, 39, 0.3) !important;
  color: #ffffff !important;
}

.rounded-top {
  border-radius: 0 !important;
  border: none !important;
}

@media (max-width: 600px) {
  .rounded-top {
    border-radius: 32px 32px 0 0 !important;
    border: solid;
  }
}

.rounded-asset-classes {
  border-radius: 18px 18px 0 0;
}

.no-scroll-bar {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.no-scroll-bar::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.fas:hover {
  opacity: 0.7;
}

.icon-primary {
  color: #546be5;
}

.referral-email-input {
  padding: 16px;
  gap: 12px;

  width: 100%;
  height: 40px;

  /* Wealthyhood New/Wealthyhood dark blue - 5% */
  background: #f4f4f4;
  border-radius: 16px;

  outline: none !important;
  border: none !important;
}

.free-share-unlocked {
  background: rgba(243, 114, 157, 0.1);
  border: 2px solid rgba(243, 114, 157, 0);
  border-radius: 16px;

  padding: 4px 8px;
}

.unlock-free-share {
  background: rgba(243, 114, 157, 0.1);
  border: 2px solid rgba(243, 114, 157, 0.4);
  border-radius: 16px;

  padding: 4px 8px;

  animation: unlock-free-share-loop 2.5s infinite linear;
}

@keyframes unlock-free-share-loop {
  50% {
    background-color: white;
    border: 2px solid rgba(243, 114, 157, 0);
  }
  100% {
    background: rgba(243, 114, 157, 0.1);
    border: 2px solid rgba(243, 114, 157, 0.4);
  }
}

.verification-input {
  padding: 16px;
  gap: 12px;

  /*max-width: 516px;*/
  width: 100%;
  height: 57px;

  /* Wealthyhood New/Wealthyhood dark blue - 5% */
  background: rgba(16, 19, 39, 0.05);
  border-radius: 16px;
  border: none;
}

.verification-input:active,
.verification-input:focus,
.verification-input:active:focus {
  outline: #377dff !important;
  border: 3px solid;
  border-color: #377dff !important;
}

.verification-input:disabled {
  outline: none !important;
  border: none !important;
}

.verification-error-input,
.verification-error-input:active,
.verification-error-input:focus,
.verification-error-input:active:focus {
  padding: 16px;
  gap: 12px;

  /*max-width: 516px;*/
  width: 100%;
  height: 57px;

  /* Wealthyhood New/Wealthyhood dark blue - 5% */
  background: rgba(16, 19, 39, 0.05);
  border-radius: 16px !important;
  border: solid #d63e3e !important;
  outline: none !important;
}

select {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}

.bar-loader {
  width: 100%;
  height: 12px;
  display: inline-block;
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  overflow: hidden;
  border-radius: 100px;
}

.bar-loader::after {
  content: "";
  width: 192px;
  height: 12px;
  background: #fff;
  border-radius: 100px;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  animation: animloader 2s linear infinite;
}

@keyframes animloader {
  0% {
    left: 0;
    transform: translateX(-100%);
  }
  100% {
    left: 100%;
    transform: translateX(0%);
  }
}

.slide-in {
  -webkit-animation: slidein 0.5s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: slidein 0.5s; /* Firefox < 16 */
  -ms-animation: slidein 0.5s; /* Internet Explorer */
  -o-animation: slidein 0.5s; /* Opera < 12.1 */
  animation: slidein 0.5s;
}

@keyframes slidein {
  from {
    right: -500px;
  }
  to {
    right: 0;
  }
}

/* Firefox < 16 */
@-moz-keyframes slidein {
  from {
    right: -500px;
  }
  to {
    right: 0;
  }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes slidein {
  from {
    right: -500px;
  }
  to {
    right: 0;
  }
}

/* Opera < 12.1 */
@-o-keyframes slidein {
  from {
    right: -500px;
  }
  to {
    right: 0;
  }
}

.slide-out {
  -webkit-animation: slideout 1s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: slideout 1s; /* Firefox < 16 */
  -ms-animation: slideout 1s; /* Internet Explorer */
  -o-animation: slideout 1s; /* Opera < 12.1 */
  animation: slideout 1s;
}

@keyframes slideout {
  from {
    left: 0;
  }
  to {
    left: 800px;
  }
}

/* Firefox < 16 */
@-moz-keyframes slideout {
  from {
    left: 0;
  }
  to {
    left: 800px;
  }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes slideout {
  from {
    left: 0;
  }
  to {
    left: 800px;
  }
}

/* Opera < 12.1 */
@-o-keyframes slideout {
  from {
    left: 0;
  }
  to {
    left: 800px;
  }
}

.fade-in {
  -webkit-animation: fadein 1s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 1s; /* Firefox < 16 */
  -ms-animation: fadein 1s; /* Internet Explorer */
  -o-animation: fadein 1s; /* Opera < 12.1 */
  animation: fadein 1s;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Firefox < 16 */
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Opera < 12.1 */
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  -webkit-animation: fadein 1s; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: fadein 1s; /* Firefox < 16 */
  -ms-animation: fadein 1s; /* Internet Explorer */
  -o-animation: fadein 1s; /* Opera < 12.1 */
  animation: fadein 1s;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Firefox < 16 */
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Safari, Chrome and Opera > 12.1 */
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Opera < 12.1 */
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.disabled {
  pointer-events: none;
  opacity: 0.7;
}

.text-primary {
  color: #546be5 !important;
}

.text-warning {
  color: #a06c23 !important;
}

.order-input {
  font-style: normal;
  font-weight: 400;
  font-size: 60px;
  width: 100%;

  /* identical to box height, or 60px */
  text-align: center;

  /* Wealthyhood New/Wealthyhood Burple 1 */
  color: #546be5;
}

.order-input:active,
.order-input:focus,
.order-input:active:focus {
  outline: none !important;
  border: none !important;
  /* Wealthyhood New/Wealthyhood Burple 1 */
  color: #546be5;
}

.fw-100 {
  max-width: 100% !important;
  width: 100% !important;
}

.mw-100 {
  max-width: 100% !important;
}

.br-circle {
  border-radius: 1000px;
  max-width: 600px;
  min-height: 600px;
}

@media (max-width: 600px) {
  .br-circle {
    border-radius: 24px 24px 0 0;
    height: 100vh;
    margin-top: 75px;
  }
}

.switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 1rem;
}

.switch label {
  margin: 0;
}

.switch input:empty {
  margin-left: -999px;
  height: 0;
  width: 0;
  overflow: hidden;
  position: absolute;
  opacity: 0;
}

.switch input:empty ~ span {
  display: inline-block;
  position: relative;
  float: left;
  width: 1px;
  text-indent: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.switch input:empty ~ span:before,
.switch input:empty ~ span:after {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  top: 0;
  bottom: 0;
  left: 0;
  content: " ";
  -webkit-transition: all 100ms ease-in;
  transition: all 100ms ease-in;
}

.switch input[disabled] {
  cursor: not-allowed;
}

.switch input[disabled] ~ span:after,
.switch input[disabled] ~ span:before {
  cursor: not-allowed;
  opacity: 0.5;
}

.switch.switch-icon input:checked ~ span:after {
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
}

.switch input:empty ~ span {
  margin: 2px 0;
  height: 40px;
  width: 70px;
  border: 1px solid rgba(84, 107, 229, 0.08);
  border-radius: 88px;
  background-color: transparent;
}

.switch input:empty ~ span:before,
.switch input:empty ~ span:after {
  width: 70px;
  border: 1px solid rgba(84, 107, 229, 0.08);
  border-radius: 88px;
  background-color: transparent;
}

.switch input:empty ~ span:after {
  height: 30px;
  width: 30px;
  top: 4px;
  margin-left: 5px;
  font-size: 0.65em;
  text-align: center;
  vertical-align: middle;
}

.switch input:checked ~ span:after {
  margin-left: 35px;
}

.switch input:empty ~ span:before {
  background-color: #ebedf3;
  color: #546be5;
}

.switch input:empty ~ span:after {
  background-color: rgba(84, 107, 229, 0.08);
  color: #546be5;
}

.switch input:checked ~ span:before {
}

.switch input:checked ~ span:after {
  color: #546be5;
  background-color: rgba(84, 107, 229, 0.08);
}

@keyframes flickerAnimation {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
@-o-keyframes flickerAnimation {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}
@-moz-keyframes flickerAnimation {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes flickerAnimation {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}

.animate-flicker {
  -webkit-animation: flickerAnimation 1s infinite;
  -moz-animation: flickerAnimation 1s infinite;
  -o-animation: flickerAnimation 1s infinite;
  animation: flickerAnimation 1s infinite;
}

.responsive-img {
  max-height: 300px;
}

@media (max-width: 600px) {
  .responsive-img {
    max-height: 200px;
  }
}

/* these classes are use for qr code banner backward compatibility*/
.max-w-300px {
  max-width: 300px !important;
}

.font-size-xxl {
  font-size: 1.1rem !important;
}

.border-radius-xl {
  border: none;
  border-radius: 20px;
}

.border-radius-xxl {
  border: none;
  border-radius: 32px;
}

/* ---------------------------------------------- */

.wh-main-header {
  box-shadow: 0 2px 12px rgba(159, 159, 159, 0.16);
}

@media (min-width: 600px) {
  .wh-main-header {
    height: 90px;
  }
}
.wh-primary-label {
  border-radius: 6px !important;
  background: #f2f3fd;
  padding: 4px 8px;
  color: #536ae3;
  text-align: center;
}
.wh-header-logo {
  width: 215.21px;
  height: 34.89px;
}

.wh-avatar-img {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  padding-top: 9px;
  width: 54px;
  height: 54px;
  background: rgba(84, 107, 229, 0.08);
  border-radius: 1000px;
  border: none !important;
}

.wh-avatar-img-lg {
  font-size: 3.5rem;
  font-weight: 500;
  text-align: center;
  width: 104px;
  height: 104px;
  background-color: #536ae3;
  border-radius: 1000px;
  border: none !important;
  line-height: 104px;
}

.wh-avatar-img-sm {
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
  width: 30px;
  height: 30px;
  background-color: #536ae3;
  border-radius: 1000px;
  border: none !important;
  line-height: 30px;
}

.wh-side-option {
  min-width: 180px;
  align-self: flex-end !important;
}

.wh-side-option:hover {
  min-width: 180px;
  color: #546be5 !important;
  align-self: flex-end !important;
}

.wh-side-bar {
  position: fixed;
  padding-top: 130px;
}

.wh-side-option-selected {
  background: #ffffff;
  color: #546be5 !important;
  box-shadow: 0 2px 12px rgba(159, 159, 159, 0.16);
  border-radius: 48px;
  /*padding: 8px 16px !important;*/
}

.text-primary {
  color: #546be5 !important;
}

.text-justify {
  /*text-align: justify !important;*/
}

.justify-content-auto {
  justify-content: center !important;
}

@media screen and (min-width: 1199px) and (min-height: 650px) {
  .justify-content-auto {
    justify-content: start !important;
  }
}

.flip-material-icon {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.btn-link {
  text-decoration: none;
}

.btn-link:hover {
  text-decoration: underline;
}

.wh-receipt-card {
  padding: 12px;
  background: rgba(16, 19, 39, 0.02);
  border: 1px solid rgba(16, 19, 39, 0.05);
  backdrop-filter: blur(2px);

  /* Note: backdrop-filter has minimal browser support */
  border-radius: 8px;
}

.wh-receipt-bar {
  width: 46.15px;
  height: 4.75px;
  border-radius: 12px;
}

.modal-content.bg-dividend-receipt{
  background: linear-gradient(180deg,rgba(255, 255, 255, 1) 0%, #F9FAFE 100%);
}

.modal-content.bg-reward-receipt{
  background: linear-gradient(180deg,rgba(255, 255, 255, 1) 0%, #F9FAFE 100%);
}

.modal-content.bg-order-buy-receipt{
  background: linear-gradient(180deg,rgba(255, 255, 255, 1) 0%, #EDFCF8 100%);
}

.modal-content.bg-order-sell-receipt{
  background: linear-gradient(180deg,rgba(255, 255, 255, 1) 0%, #FFF5F5 100%);
}

.bg-receipt-success {
  background: url("/images/backgrounds/receipt-success-background.jpg");
}

.bg-receipt-danger {
  background: url("/images/backgrounds/receipt-danger-background.jpg");
}

.receipt-separator-primary {
  height: 10px;
  background: url("/images/backgrounds/receipt-primary-separator.png");
}

.receipt-separator-success {
  height: 10px;
  background: url("/images/backgrounds/receipt-success-separator.png");
}

.receipt-separator-danger {
  height: 10px;
  background: url("/images/backgrounds/receipt-danger-separator.png");
}

.modal-content {
  border-radius: 24px !important;
  background: linear-gradient(0deg, white, rgba(242, 244, 253, 1));
}
.white-background-modal .modal-content{
   background: #fff;
}

.clickable-transaction:hover {
  color: #546be5;
  cursor: pointer;
}

.fw-bold {
  font-weight: 500 !important;
}

.fw-bolder {
  font-weight: 500 !important;
}

/*------------------------------------
  Step
------------------------------------*/
.step {
  position: relative;
  list-style: none;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-right: -0.9375rem;
  margin-left: -0.9375rem;
}

.step .step-item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-preferred-size: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
  padding-right: 0.9375rem;
  padding-left: 0.9375rem;
}

.step .step-content-wrapper {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.step .step-content {
  -ms-flex: 1;
  flex: 1;
}

/*------------------------------------
  Step Icon
------------------------------------*/
.step .step-icon {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 1rem;
  font-weight: bolder;
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  margin-right: 1rem;
}

.step .step-icon::after {
  position: absolute;
  top: 3.59375rem;
  left: 1.5625rem;
  height: calc(100% - 2.65625rem);
  border-left: 0.125rem solid #e7eaf3;
  content: "";
}

.step .step-icon-pseudo::before {
  display: block;
  width: 0.25rem;
  height: 0.25rem;
  background-color: #97a4af;
  border-radius: 50%;
  content: "";
}

.gift-step-icon {
  background-color: white !important;
  border: 2px solid #dce2fd !important;
  height: 32px !important;
  width: 32px !important;
}

.gift-step-icon::after {
  border-left: 2px dashed #dce2fd !important;
  top: 32px !important;
  height: calc(100% + 0.25rem) !important;
  /*height: 2rem !important;*/
  left: 15px !important;
}

.gift-last-step-icon {
  background-color: white !important;
  border: 2px solid #dce2fd !important;
  height: 32px !important;
  width: 32px !important;
}

.gift-last-step-icon::after {
  border-left: none !important;
}

.max-w-600px {
  max-width: 600px !important;
}

.border-radius-24px {
  border-radius: 24px !important;
}

.deposit-options-item {
  border-radius: 8px;
  padding: 8px;
  border: 1px solid #546be514;
}

.deposit-options-item .asset-icon{
  background-color: #F4F4F4;
}

#popover-explanation {
  font-family: "Poppins", sans-serif !important;
}
/*! nouislider - 14.6.2 - 9/16/2020 */
/* Functional styling;
 * These styles are required for noUiSlider to function.
 * You don't need to change these rules to apply your design.
 */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
}

.noUi-base,
.noUi-connects {
  width: 100%;
  height: 4px;
  position: relative;
  z-index: 1;
}

/* Wrapper for all connect elements.
 */
.noUi-connects {
  overflow: hidden;
  z-index: 0;
}

.noUi-connect,
.noUi-origin {
  will-change: transform;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  -ms-transform-origin: 0 0;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform-style: flat;
  transform-style: flat;
}

.noUi-connect {
  height: 100%;
  width: 100%;
}

.noUi-origin {
  height: 10%;
  width: 10%;
}

/* Offset direction
 */
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  left: 0;
  right: auto;
}

/* Give origins 0 height/width so they don't interfere with clicking the
 * connect elements.
 */
.noUi-vertical .noUi-origin {
  width: 0;
}

.noUi-horizontal .noUi-origin {
  height: 0;
}

.noUi-handle {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
  background: #fff;
  border: 0.5rem solid #546be5;
  height: 1.45rem;
  -webkit-transform: matrix(-1, 0, 0, 1, 0, 0);
  transform: matrix(-1, 0, 0, 1, 0, 0);
  width: 1.45rem;
  border-radius: 50%;
  top: -10px !important;
}

.noUi-touch-area {
  height: 100%;
  width: 100%;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  -webkit-transition: transform 0.3s;
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

/* Slider size and handle placement;
 */
.noUi-horizontal {
  height: 4px !important;
  background-color: #dce9f0 !important;
  box-shadow: none !important;
  border: 0 !important;
}

.noUi-horizontal .noUi-handle {
  width: 24px;
  height: 24px;
  right: -17px;
  background-color: #546be5;
}

.noUi-vertical {
  width: 18px;
}

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  right: -6px;
  top: -17px;
}

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -17px;
  right: auto;
}

/* Styling;
 * Giving the connect element a border radius causes issues with using transform: scale
 */
.noUi-target {
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #d3d3d3;
  -webkit-box-shadow: inset 0 1px 1px #f0f0f0, 0 3px 6px -5px #bbb;
  box-shadow: none !important;
}

.noUi-connects {
  border-radius: 3px;
}

.noUi-connect {
  background: #546be5;
  height: 4px;
}

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle {
  top: -10px !important;
  border-radius: 50%;
  cursor: default;
  box-shadow: none !important;
}

.noUi-active {
  -webkit-box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ddd, 0 3px 6px -3px #bbb;
  box-shadow: inset 0 0 1px #fff, inset 0 1px 7px #ddd, 0 3px 6px -3px #bbb;
}

/* Handle stripes;
 */
.noUi-handle:before,
.noUi-handle:after {
  content: "";
  display: block;
  position: absolute;
  height: 14px;
  width: 1px;
  background: #e8e7e6;
  left: 14px;
  top: 6px;
}

.noUi-handle:after {
  left: 17px;
}

.noUi-vertical .noUi-handle:before,
.noUi-vertical .noUi-handle:after {
  width: 14px;
  height: 1px;
  left: 6px;
  top: 14px;
}

.noUi-vertical .noUi-handle:after {
  top: 17px;
}

/* Disabled state;
 */
[disabled] .noUi-connect {
  background: #b8b8b8;
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

/* Base;
 *
 */
.noUi-pips,
.noUi-pips * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-pips {
  position: absolute;
  color: #999;
}

/* Values;
 *
 */
.noUi-value {
  position: absolute;
  white-space: nowrap;
  text-align: center;
}

.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}

/* Markings;
 *
 */
.noUi-marker {
  position: absolute;
  background: #ccc;
}

.noUi-marker-sub {
  background: #aaa;
}

.noUi-marker-large {
  background: #aaa;
}

/* Horizontal layout;
 *
 */
.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
}

.noUi-rtl .noUi-value-horizontal {
  -webkit-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
}

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}

/* Vertical layout;
 *
 */
.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  padding-left: 25px;
}

.noUi-rtl .noUi-value-vertical {
  -webkit-transform: translate(0, 50%);
  transform: translate(0, 50%);
}

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}

.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
  white-space: nowrap;
}

.noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}

.noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}

.noUi-horizontal .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(50%, 0);
  transform: translate(50%, 0);
  left: auto;
  bottom: 10px;
}

.noUi-vertical .noUi-origin > .noUi-tooltip {
  -webkit-transform: translate(0, -18px);
  transform: translate(0, -18px);
  top: auto;
  right: 28px;
}

.noUi-handle::before,
.noUi-handle::after {
  display: none !important;
}

.wh-referral-card {
  background: url("/images/backgrounds/stars.svg"),
    linear-gradient(85.28deg, #000000 0.79%, rgba(104, 121, 210, 0) 125.67%),
    url("/images/backgrounds/contour-lines-background.svg"), #6879d2;
  background-size: 30%, 100%, 100%;
  background-repeat: no-repeat;
  border-radius: 16px;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-gift-card {
  background-size: 100%, 100%;
  background: #546be5 linear-gradient(84.8deg, #ffffff -40%, rgba(90, 111, 221, 0) 112.01%) url("/images/backgrounds/contour-lines-background.svg") no-repeat;
  border-radius: 16px;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-rewards-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  background: #546be5 linear-gradient(85.75deg, #f9b0c8 -24.09%, rgba(244, 113, 156, 0) 99.42%) url("/images/backgrounds/contour-lines-background.svg") no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-plans-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  background: #f3729d linear-gradient(86.81deg, #f4719c 23.01%, rgba(244, 113, 156, 0) 98.48%) url("/images/backgrounds/contour-lines-background.svg") no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-cashback-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  background: #546be5 linear-gradient(84.32deg, #11152e -7.32%, rgba(73, 87, 213, 0.13) 99.13%) url("/images/backgrounds/contour-lines-background.svg") no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-carouselItem-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.22);
  background: #fff no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-dividend-paid_low-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  background: linear-gradient(262.63deg, #e44d7f 20.87%, #c73364 100%) no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

.wh-dividend-paid_mid-card {
  background-size: 100%, 100%;
  border-radius: 16px;
  background: linear-gradient(262.63deg, #ab71d9 20.87%, #8055a4 100%) no-repeat;
  padding: 16px;
  min-width: 450px;
  width: 100%;
}

@media (max-width: 600px) {
  .wh-referral-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-gift-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-rewards-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-plans-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-cashback-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-carouselItem-card {
    min-width: 320px;
  }

  .wh-dividend-paid_low-card {
    min-width: 320px;
    min-height: 152px;
  }

  .wh-dividend-paid_mid-card {
    min-width: 320px;
    min-height: 152px;
  }
}

.bg-starry {
  background: url("/images/backgrounds/stary-background.svg") no-repeat;
  background-size: 100%;
}

.intercom-lightweight-app {
  z-index: 1000 !important;
}

/*!* Toggle *!*/

.toggle-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 42px;
  height: 24px;
  padding: 1px;
  cursor: pointer;
  user-select: none;
}

.toggle-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #E0E0E0;
  border-radius: 75px;
  transition: background-color 0.2s ease;
}

.toggle-handle {
  position: relative;
  width: 18px;
  height: 18px;
  background-color: white;
  border-radius: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateX(2px);
}

.toggle-icon {
  font-size: 13.5px;
  color: #536AE3;
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Checked state */
.toggle-switch.checked .toggle-track {
  background-color: #536AE3;
}

.toggle-switch.checked .toggle-handle {
  transform: translateX(20px);
}

.toggle-switch.checked .toggle-icon {
  opacity: 1;
}

/* Disabled state */
.toggle-switch.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Focus state for accessibility */
.toggle-switch:focus-visible {
  outline: 2px solid #536AE3;
  outline-offset: 2px;
}

select:invalid {
  color: #727a82;
}

#compound-calc-annual-return-slider {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-annual-return-slider .noUi-connects {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-annual-return-slider .noUi-connect {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-annual-return-slider .noUi-handle {
  width: 24px !important;
  height: 24px !important;
  top: -5px !important;
  right: -12px !important;
  background-color: #536ae3 !important;
  border: none;
}
#compound-calc-annual-return-slider .noUi-handle .noUi-touch-area {
  visibility: hidden;
}
.isPortfolioAnnualReturnSelected #compound-calc-annual-return-slider .noUi-handle .noUi-touch-area {
  height: 12px;
  width: 12px;
  background-color: white;
  position: absolute;
  top: 5.5px;
  left: 5.5px;
  border-radius: 10px;
  visibility: visible !important;
}
#compound-calc-annual-return-slider-dummy .noUi-handle {
  width: 12px !important;
  height: 12px !important;
  top: -14px !important;
  right: -8px !important;
  background-color: #536ae3 !important;
  border: none;
  cursor: inherit !important;
}
.isDummyHandleColoredWhite #compound-calc-annual-return-slider-dummy .noUi-handle {
  background-color: white !important;
}
.isPortfolioAnnualReturnSelected #compound-calc-annual-return-slider-dummy .noUi-handle {
  visibility: hidden !important;
}
#compound-calc-annual-return-slider-dummy {
  background-color: inherit !important;
}
#compound-calc-annual-return-slider-dummy .noUi-tooltip {
  background: none;
  border: none;
  top: 15px;
  cursor: pointer;
}
#compound-calc-annual-return-slider-dummy .noUi-tooltip * {
  visibility: visible;
}
#compound-calc-annual-return-slider-dummy .noUi-connects {
  display: none;
}
#compound-calc-annual-return-slider-dummy {
  cursor: inherit !important;
}
#compound-calc-monthly-investment-slider {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-monthly-investment-slider .noUi-connects {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-monthly-investment-slider .noUi-connect {
  height: 15px !important;
  border-radius: 1.5em !important;
}
#compound-calc-monthly-investment-slider .noUi-handle {
  width: 24px !important;
  height: 24px !important;
  top: -5px !important;
  right: -12px !important;
  background-color: #536ae3 !important;
  border: none;
}

.cashback-amount-banner {
  background: rgba(243, 114, 157, 0.1);
  border: 2px solid rgba(243, 114, 157, 0);
  border-radius: 16px;

  padding: 4px 8px;
}

.earn-cashback-banner {
  background: rgba(243, 114, 157, 0.1);
  border: 2px solid rgba(243, 114, 157, 0.4);
  border-radius: 16px;

  padding: 4px 8px;

  animation: earn-cashback-banner-loop 2.5s infinite linear;
}

@keyframes earn-cashback-banner-loop {
  50% {
    background-color: white;
    border: 2px solid rgba(243, 114, 157, 0);
  }
  100% {
    background: rgba(243, 114, 157, 0.1);
    border: 2px solid rgba(243, 114, 157, 0.4);
  }
}

.max-w-400px {
  max-width: 400px;
}

.max-w-500px {
  max-width: 500px;
}

.bg-received-wh-dividend {
  background: url("/images/backgrounds/received-wh-dividend.png");
}

.w-fit-content {
  width: fit-content;
}

.max-w-520px {
  max-width: 520px;
}

.not-selected-carousel-dot {
    color: #dce2fd;
}

/* A class for elements that need to be displayed always above everything else 
   on the z-axis, such as toasts and notifications. The z-index defined should 
   be the highest by convention, since there's no limit. All other elements 
   must have a lower z-index.
   Optionally, you can define further classes to serve as layers, such as 
   z-first-level, z-second-level, z-third-level, etc. or even
   z-first-floor, z-second-floor, z-third-floor, etc. */
.z-max-level {
  z-index: 2000;
}

.learning-hub-article-preview-tag {
  color: #757575;
  background: #f4f4f4;
  padding: 8px 16px;
  border-radius: 30px;
}

.learning-hub-article-tag {
  color: #757575;
  background: #fff;
  padding: 8px 16px;
  border-radius: 30px;
}

.learning-hub-article-preview-icon,
.learning-hub-article-icon {
  background: #f4f4f4;
  padding: 3px;
  border-radius: 50%;
}

.learning-hub-article-preview-image {
  border-radius: 16px;
  width: 160px;
  height: 160px;
  object-fit: cover;
  object-position: top;
}

.learning-hub-article-image {
  width: 100%;
  aspect-ratio: 127 / 112;
  object-fit: cover;
  border-radius: 16px;
  object-position: top;
}

.learning-hub-article-preview-image-container {
  height: 160px;
}

.learning-hub-article-preview-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 120%;
}

.learning-hub-article-title {
  line-height: 120%;
  font-size: 2rem !important;
}

.paywalled {
  position:relative;
}
.paywalled:after{
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 126px;
  background: hsla(0, 0%, 100%, 0.2);
  background: linear-gradient(180deg, hsla(0, 0%, 100%, 0) 0, #fff 100%);
}

#learning-hub-item-paywall{
  max-width: 500px;
}
.paywall-card{
  background-color: #F1F3FD;
  border-radius: 16px;
  padding: 24px;
  margin: auto;
}
.paywall-card ul{
  font-size: 14px;
}

.assets-search-input,
.glossary-search-input {
  background-color: #f4f4f4;
  outline: "none";
  flex-grow: 1;
}

.assets-search-input:focus,
.glossary-search-input:focus {
  outline: none;
  border: none !important;
}

.assets-search-container,
.glossary-search-container {
  background-color: #f4f4f4;
  border-radius: 16px;
}

.accordion-body-container {
  overflow: hidden;
  transition: opacity 0.5s ease-out, max-height 0.5s ease-out;
  max-height: 0;
  opacity: 0;
}

.accordion-body-container.expand {
  max-height: 1000px;
  opacity: 1;
}

.accordion-header {
  border-radius: 16px;
  border: 1px solid #f1f3fd;
}

.accordion-header-active {
  background: #f4f4f4;
}

.accordion-html-container img {
  max-width: 100%;
  height: auto;
  max-height: 100%;
  width: 100%;
}

.learning-hub-html-container img {
  max-width: 100%;
  height: auto;
}

.delete-asset-icon {
  color: #acacac;
}
.delete-asset-icon:hover {
  color: #d63e3e;
}

.asset-modal-card {
  background-color: #f1f3fd;
  border-radius: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  direction: rtl;
}

.progress {
  height: 100%;
  border-radius: 0 10px 10px 0;
  position: absolute;
  right: 0;
}

.progress-bar-left {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.progress-left {
  height: 100%;
  border-radius: 0 10px 10px 0;
  position: absolute;
}

.sector-icon-container {
  border-width: 2px;
  border-color: rgb(241, 243, 253);
  border-style: solid;
  border-radius: 8px;
}

.kid-document-icon {
  font-size: 36px !important;
  color: black;
  background-color: #1013270d;
  border-radius: 8px;
}

.daily-market-summary-clamped p {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* number of lines to show */
  line-clamp: 3;
  -webkit-box-orient: vertical;
  color: #757575;
}

@media (max-width: 600px) {
  .daily-market-summary-clamped p {
    -webkit-line-clamp: 5;
    /* number of lines to show */
    line-clamp: 5;
  }
}

.underlined-date {
  padding-bottom: 1px;
  border-bottom: 4px solid #536AE3;
}

.markets-carousel {
  margin: 0 auto;
  padding: 5px 0;
  max-width: 700px;
  overflow: hidden;
  display: flex;
  > * {
    flex: 0 0 100%;
  }
}

.markets-carousel-group {
  display: flex;
  gap: 3rem;
  /* Add padding to the right to create a gap between the last and first card. */
  padding-right: 3rem;
  will-change: transform;
  animation: scrolling 20s linear infinite;
}

.markets-carousel-card {
  white-space: nowrap;
}
.fade-line {
  border: 0;
  height: 1px;
  background-image: linear-gradient(
          to right,
          rgba(0, 0, 0, 0),
          rgba(0, 0, 0, 0.65),
          rgba(0, 0, 0, 0)
  );
}

a.nostyle:link {
  text-decoration: inherit;
  color: inherit;
  cursor: auto;
}

a.nostyle:visited {
  text-decoration: inherit;
  color: inherit;
  cursor: auto;
}

.horizontal-border {
  width: 100%;
  height: 2px;
  background-color: #dce2fd;
  border-radius: 10px;
}

.fw-bolder-black {
  font-weight: 500 !important;
  color: black;
}

.asset-icon {
  border-width: 2px !important;
  border-style: solid !important;
  border-radius: 20% !important;
}

.dropdown-button {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

.dropdown-menu {
  max-height: 300px;
  overflow: scroll;
}

.wh-card-amount.dropdown {
  opacity: 1;
}

.tooltip-overflows-right {
  padding-right: 3rem;
}

.tooltip-overflows-left {
  padding-left: 3rem;
}

.custom-option-selected {
  padding: 5px;
  background-color: #f1f3fd;
}

.custom-option {
  padding: 5px;
  background-color: #f1f3fd;
}

.asset-collection-pill {
  background-color: #f1f3fd;
  padding: 4px 8px;
  text-align: center;
  border-radius: 50rem;
  white-space: nowrap;
}

.btn-asset-discovery-see-all {
  background-color: #f1f3fd;
  color: #11152e;
  border-radius: 16px;
  padding: 15px 0;
}

.btn-asset-discovery-see-all:hover {
  background-color: #f1f3fd;
}

.wh-rebalance-card-color {
  background-color: rgba(84, 107, 229, 0.08);
  padding: 33px !important;
}

.review-asset {
  padding: 0.6rem;
  width: fit-content;
  border-radius: 1000px;
}

.add-review-asset {
  background-color: #f1f3fd;
  color: #4957d5;
}

.added-review-asset {
  background-color: #536ae3;
  color: white;
}

.remove-review-asset {
  color: #c20f0f;
  background-color: #ffe6e6;
}

.removed-review-asset {
  color: white;
  background-color: #d63c3c;
}

.edit-review-asset-icon {
  margin: 0;
  margin-right: 0.3rem;
  height: 24px;
  width: 24px;
}

.review-card-body {
  flex: 1 1 auto;
  background: rgba(84, 107, 229, 0.08);
  padding: 26px;
}

.update-and-review-btn {
  display: flex;
  align-items: center;
  max-width: fit-content !important;
  padding: 1rem 3rem !important;
}

.group-by-radio-btn {
  max-width: fit-content;
  background: #f1f3fd;
  color: #4957d5;
  border-radius: 1000px;
}

.group-by-radio-btn:hover {
  background: #f1f3fd;
  color: #4957d5;
}

.autopilot-prompt-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dash-vertical {
  width: 4px;
  height: 25px;
  border-right: solid;
  border-color: transparent;
  border-radius: 2px;
  margin-bottom: 8px;
}

#success-spinner {
  content: "";
  display: block;
  position: relative;
  width: 60px;
  height: 60px;
  border-style: solid;
  border-color: #536ae3;
  border-top-color: transparent;
  border-width: 4px;
  border-radius: 50%;
  -webkit-animation: spin 0.8s linear infinite;
  animation: spin 0.8s linear infinite;
}

.asset-news-image {
  border-radius: 16px;
  height: 100px;
  width: 100px;
  object-fit: cover;
  object-position: top;
}

.asset-news-image-container {
  height: 100px;
  width: 100px;
}

.asset-news-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 120%;
  font-size: 18px;
}

.asset-news-icon {
  font-size: 14px !important;
  padding: 6px;
  border-radius: 50%;
}

.asset-news-side-title {
  font-size: 2rem;
  margin: 0;
  padding: 0;
  margin-bottom: 48px;
}

.asset-news-side-section-title {
  font-size: 1.25rem;
  margin: 0;
  padding: 0;
  margin-bottom: 20px;
}

.no-decoration {
  text-decoration: none;
}

.banner-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-bottom: 2px;
}

.banner-tag-icon {
  padding: 6px;
  border-radius: 50%;
  font-size: 14px !important;
}

.banner-title-with-tag {
  font-size: 16px;
  line-height: 140%;
  padding-right: 2.5rem;
}

@media (max-width: 600px) {
  .banner-title-with-tag {
    padding-right: 1rem;
  }
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.cash-balance-incoming {
  background: #ffedbf;
  color: #c78830;
  border-radius: 22px;
}

.cash-balance-incoming:hover {
  background: #ffedbf;
  color: #c78830;
  border-radius: 22px;
}

.savings-interest-incoming {
  background: #536ae3;
  color: white;
  border-radius: 22px;
}

.savings-interest-incoming:hover {
  background: #536ae3;
  color: white;
  border-radius: 22px;
}

.account-tab-container {
  border: 3px solid black;
  border-radius: 24px;
  padding: 5px 3px;
}

.account-tab {
  padding: 4px 12px;
  cursor: pointer;
}

.account-tab-active {
  background: black;
  color: #ffffff;
  border-radius: 24px;
}

.delete-btn {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #d63e3e1a;
  color: #d63e3e;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 16px;
  border: none;
}

.delete-btn:hover {
  /* Button */
  min-width: 168px;
  max-width: 250px;
  height: 50px;
  /* Wealthyhood New/Wealthyhood Dark Blue */
  background: #d63e3e1a;
  color: #d63e3e;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 12px;
  padding-bottom: 10px;

  /* Button Shadow/Shadow_primary_md */
  box-shadow: 0 6px 12px rgba(159, 159, 159, 0.48);
  border-radius: 16px;
  border: none;
}
.country-not-available-text {
  margin-left: auto;
  padding: 0 6px;
  border-radius: 6px;
  background: #f2f3fd;
  color: #536ae3;
  text-wrap: nowrap;
}

.country-not-available-flag {
  opacity: 40%;
}

.earn-interest-cta {
  text-decoration: none;
  color: #000;
  background-color: #fff;
  border-radius: 16px;
  padding: 4px 8px;
  cursor: pointer;
}

.earn-interest-cta:hover {
  text-decoration: none;
  color: #000;
  background-color: #fff;
  border-radius: 16px;
  padding: 4px 8px;
  cursor: pointer;
}

.asset-tag-pill {
  display: flex;
  align-items: center;
  background: #f1f3fd;
  width: fit-content;
  border-radius: 17px;
  padding: 3px 11px;
}

.wh-account-card-small {
  width: 100%;
  max-width: 500px;
  max-height: 200px;
  min-width: 450px;
  min-height: 150px;
  border-radius: 20px;
}

.daily-summary-card {
  border-radius: 16px;
  border: 1px solid #DCE2FD;
  box-shadow: 0 4px 24px 0 #********;
}

.fw-600 {
  font-weight: 600 !important;
}

.account-card-btn {
  background-color: white;
  color: black;
}

.account-card-btn:hover {
  background-color: #f3f3f4;
  color: black;
}

.automation-savings-entry.active .MuiSwitch-switchBase.Mui-checked {
  color: #905fb8;
}

.automation-savings-entry.active .MuiSwitch-switchBase.Mui-checked:hover {
  background-color: #905fb83a;
}

.automation-savings-entry.active .MuiSwitch-root .MuiSwitch-track {
  background-color: #dbafff !important;
}

.automation-savings-entry.active {
  background-color: #f9f1ff;
}

.automation-savings-entry.active .automation-entry-subtitle {
  color: #905fb8 !important;
}

.automation-savings-entry.active .material-symbols-outlined {
  color: #905fb8 !important;
}

.automation-entry.active {
  background-color: #f1f3fd;
}

.transaction-row-subtitle span {
  font-size: 14px !important;
}
