import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";

type PropsType = {
  name: string;
  weight: string;
  logoUrl: string;
};

class EtfHoldingCard extends React.Component<PropsType> {
  private static _createAlternateLogoUrl(logoUrl: string): string {
    if (!logoUrl) return null;

    const split = logoUrl.split("/");
    split[split.length - 1] = split[split.length - 1].toUpperCase().replace("PNG", "png");
    return split.join("/");
  }

  private static _getSimpleName(name: string): string {
    const commonEndings: string[] = [
      "Inc\\.",
      "Inc",
      "Corporation",
      "Corp",
      "Company",
      "Limited",
      "Group",
      "\\.com",
      "com",
      "Motor",
      "\\& Co",
      "Co\\.Ltd",
      "Co",
      "Ltd",
      "IndustriesLtd",
      "plc",
      "p\\.l\\.c",
      "Incorporated",
      "SA\\.",
      "S\\.A",
      "Se\\.",
      "SA",
      "AG",
      "SE",
      "A/S",
      "SpA",
      "Aktiengessellschaft",
      "& Company"
    ];
    const patterns = commonEndings.join("|");

    const regex = new RegExp(`\\b(?:${patterns})\\b`, "gi");
    // remove common patterns and ending dot
    return name.replace(regex, "").replace(new RegExp("\\.$"), "");
  }

  render(): JSX.Element {
    const { name, weight, logoUrl } = this.props;
    const altLogoUrl = EtfHoldingCard._createAlternateLogoUrl(logoUrl);
    const simpleName = EtfHoldingCard._getSimpleName(name);

    return (
      <div className="row h-100 m-0">
        <div className="m-0 p-0" style={{ minHeight: "68px", maxHeight: "68px" }}>
          <div className="w-100 h-100 p-0 m-0">
            <div className="row m-0 h-100">
              {logoUrl && (
                <div className="col-4 p-0 align-self-center">
                  {/*Pattern for loading image with fallback options */}
                  <object
                    className="w-100"
                    data={logoUrl}
                    type="image/png"
                    style={{ maxHeight: "42px", maxWidth: "42px" }}
                  >
                    <object
                      className="w-100"
                      data={altLogoUrl}
                      type="image/png"
                      style={{ maxHeight: "42px", maxWidth: "42px" }}
                    >
                      <div className="w-100" style={{ minHeight: "42px" }} />
                    </object>
                  </object>
                </div>
              )}
              <div className="col-8 p-1 text-start align-self-center">
                <OverlayTrigger
                  placement="bottom"
                  overlay={
                    <Popover id="popover-explanation">
                      <Popover.Content>{name}</Popover.Content>
                    </Popover>
                  }
                >
                  <div className="w-100 font-weight-bolder text-truncate">{simpleName}</div>
                </OverlayTrigger>
                <span className="text-primary">{weight}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default EtfHoldingCard;
