import React from "react";
import PauseIcon from "./icons/pauseIcon";
import PlayIcon from "./icons/playIcon";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";

type PropsType = {
  isPaused: boolean;
  onStatusChangeClick: () => Promise<void>;
};

class OrderLineStatusButton extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isPaused, onStatusChangeClick } = this.props;

    return (
      <LoadingOnSubmitButton type="button" className="btn btn-icon btn-white" customonclick={onStatusChangeClick}>
        {isPaused ? (
          <span className="svg-icon svg-icon-xxl svg-icon-danger">
            <PauseIcon />
          </span>
        ) : (
          <span className="svg-icon svg-icon-xxl svg-icon-success">
            <PlayIcon />
          </span>
        )}
      </LoadingOnSubmitButton>
    );
  }
}

export default OrderLineStatusButton;
