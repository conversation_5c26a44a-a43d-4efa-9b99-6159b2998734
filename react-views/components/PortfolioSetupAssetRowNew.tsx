import React from "react";
import AssetIcon from "./assetIcon";

type PropsType = {
  category: "etf" | "stock";
  simpleName: string;
  description: string;
  logoUrl: string;
  onAssetClick?: () => void;
  onPercentageChange: (percentage: number) => void;
  percentage: number;
  onDelete?: () => void;
};

class PortfolioSetupAssetRowNew extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-75 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 4px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  render(): JSX.Element {
    const { simpleName, description, logoUrl, onAssetClick, percentage, onPercentageChange, onDelete, category } =
      this.props;

    return (
      <div className={"row m-0 my-2 p-0 " + (onAssetClick ? "cursor-pointer" : "")}>
        <div className="col-2 col-sm-2 p-0 m-0 align-self-center text-center" onClick={onAssetClick}>
          <div className="position-relative" style={{ width: "fit-content" }}>
            <AssetIcon category={category} iconUrl={logoUrl} size="lg" />
            {PortfolioSetupAssetRowNew._getLabel(category)}
          </div>
        </div>
        <div className="col-5 col-sm-7 pt-md-3 ps-md-2 pt-3 ps-4" onClick={onAssetClick}>
          <div className="d-flex align-items-center mb-1">
            <h6 className="fw-bold m-0">{simpleName}</h6>
          </div>
          <p className="d-block t-875 fw-bold text-truncate text-muted">{description}</p>
        </div>
        <div
          className={
            "col-3 col-sm-2 p-1 bg-light text-center align-self-center round-border " +
            (percentage == 0 ? "text-primary" : "")
          }
        >
          <div className="row flex-nowrap">
            <div
              className="col align-self-center pe-2 cursor-pointer"
              onClick={(event) => {
                event.stopPropagation();
                if (percentage > 0) {
                  onPercentageChange(percentage - 1);
                }
              }}
            >
              <i className="fas fa-sm fa-minus" />
            </div>
            {percentage}%
            <div
              className="col align-self-center ps-1 cursor-pointer"
              onClick={(event) => {
                event.stopPropagation();
                onPercentageChange(percentage + 1);
              }}
            >
              <i className="fas fa-sm fa-plus" />
            </div>
          </div>
        </div>
        {onDelete && (
          <div className="col-2 col-sm-1 p-1 text-center align-self-center round-border ps-2 ps-md-4">
            <div className="col align-self-center cursor-pointer delete-asset-icon" onClick={onDelete}>
              <i
                className="d-flex material-symbols-outlined justify-content-center align-self-center"
                style={{ fontSize: "16px" }}
              >
                delete
              </i>
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default PortfolioSetupAssetRowNew;
