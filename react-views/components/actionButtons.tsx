import { eventEmitter, EVENTS } from "../utils/eventService";
import React from "react";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  disableBuyButton?: boolean;
  disableActivityButton?: boolean;
};

class ActionButtons extends React.Component<PropsType> {
  render(): JSX.Element {
    const { user } = this.context as GlobalContextType;
    const { disableBuyButton, disableActivityButton } = this.props;

    return (
      <div className={"d-none d-md-block"}>
        <div className="d-flex justify-content-center mb-4">
          <div className={"d-flex flex-column align-items-center"}>
            <button
              type="button"
              className={
                "d-flex align-items-center justify-content-center btn btn-primary btn-small mx-4 border-radius-xxl" +
                (disableBuyButton ? " disabled" : "")
              }
              onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
            >
              <span
                className="mx-auto material-symbols-outlined align-self-center px-3"
                style={{ fontSize: "20px", color: "#fff" }}
              >
                add
              </span>
            </button>
            <p className={"fw-bolder t-875 mt-2"}>Buy</p>
          </div>

          <div className={"d-flex flex-column align-items-center"}>
            <a
              href="/investor/investment-activity"
              className={`d-flex align-items-center justify-content-center text-muted text-decoration-none text-start btn btn-light btn-small mx-4 account-card-btn ${
                disableActivityButton ? " disabled" : ""
              }`}
            >
              <span
                className="mx-auto material-symbols-outlined align-self-center px-3"
                style={{ fontSize: "20px", color: "#000" }}
              >
                history_2
              </span>
            </a>
            <p className={"fw-bolder t-875 mt-2"}>Activity</p>
          </div>
          <div className={"d-flex flex-column align-items-center"}>
            <a
              href="/portfolios/target"
              className="d-flex align-items-center justify-content-center text-muted text-decoration-none text-start btn btn-light btn-small mx-4 account-card-btn"
            >
              <span
                className="mx-auto material-symbols-outlined align-self-center px-3"
                style={{ fontSize: "20px", color: "#000" }}
              >
                page_info
              </span>
            </a>
            <p className={"fw-bolder t-875 mt-2"}>Target</p>
          </div>
        </div>
      </div>
    );
  }
}

ActionButtons.contextType = GlobalContext;

export default ActionButtons;
