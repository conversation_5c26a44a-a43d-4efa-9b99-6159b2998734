import React, { Component } from "react";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { OtherModalType } from "./modalsWrapper";

interface AddBankAccountButtonProps {
  originModal: OtherModalType;
}

class AddBankAccountButton extends Component<AddBankAccountButtonProps> {
  render() {
    const { originModal } = this.props;

    return (
      <button type="button" className="btn btn-ghost fw-100">
        <div
          className="d-flex justify-content-center align-self-center flex-row"
          onClick={() =>
            eventEmitter.emit(EVENTS.linkBankAccountModal, {
              originModal
            })
          }
        >
          <span className="material-symbols-outlined align-self-center me-2" style={{ fontSize: "16px" }}>
            add
          </span>
          Add bank account
        </div>
      </button>
    );
  }
}

export default AddBankAccountButton;
