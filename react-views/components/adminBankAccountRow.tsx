import React from "react";
import { BankAccountDocument } from "../../models/BankAccount";
import { UserDocument } from "../../models/User";
import { BankAccountStatusType } from "../../services/wealthkernelService";
import BankIcon from "./icons/bankIcon";

const STATUS_CONFIG: Record<BankAccountStatusType, { color: string }> = {
  Inactive: { color: "dark" },
  Pending: { color: "warning" },
  Active: { color: "success" },
  Suspended: { color: "danger" }
};

type PropsType = {
  onActivateBankAccount: (bankAccountId: string) => Promise<void>;
  bankAccount: BankAccountDocument;
};

class AdminBankAccountRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { bankAccount, onActivateBankAccount } = this.props;
    const { createdAt, name } = bankAccount;
    const providerStatus =
      bankAccount.providers?.wealthkernel?.status || bankAccount.providers?.wealthyhood?.status;
    const wealthkernelId = bankAccount.providers?.wealthkernel?.id;
    const owner = bankAccount.owner as UserDocument;

    return (
      <tr>
        {/* Icon Cell */}
        <td className="pl-2">
          <div className="symbol symbol-45 symbol-light mr-2 shadow-sm">
            <span className="symbol-label bg-white">
              <span className="svg-icon svg-icon-info svg-icon-2x">
                <BankIcon />
              </span>
            </span>
          </div>
        </td>
        {/* End Icon Cell */}

        {/* Created Date Cell */}
        <td className="pl-0">
          <span className="text-dark-75 font-weight-500 font-size-h6">
            {createdAt &&
              new Date(createdAt).toLocaleDateString("en-GB", {
                day: "numeric",
                month: "short",
                year: "numeric"
              })}
          </span>
        </td>
        {/* End Created Date Cell */}

        {/* Bank Account Status Cell */}
        <td className="pl-0 text-nowrap">
          {providerStatus ? (
            <>
              <span className={`label label-dot label-${STATUS_CONFIG[providerStatus].color}`} />
              <span className={`font-weight-bold font-size-h5 text-${STATUS_CONFIG[providerStatus].color} ml-2`}>
                {providerStatus}
              </span>
            </>
          ) : (
            "not submitted"
          )}
        </td>
        {/* End Account Status Cell */}

        {/* Bank Account Name Cell */}
        <td className="pl-0 text-nowrap">
          <span className="text-dark-75 font-size-h5">{name || "-"}</span>
        </td>
        {/* End Bank  Name  Cell */}

        <td className="pl-0 text-nowrap">
          {/* Owner Full Name Cell */}
          <a className="font-weight-bold font-size-h5" href={`/admin/users/${owner.id}`}>
            {owner.fullName}
          </a>
        </td>
        {/* End Owner Full Name Cell */}

        {/* Target User Wealthkernel  Id Cell */}
        <td className="pl-0 text-nowrap">
          <span className="text-dark-75 font-size-h5">{wealthkernelId || "-"}</span>
        </td>
        {/* End Target User Wealthkernel  Id Cell */}

        {/* Activate button Cell */}
        <td className="pl-0 text-nowrap">
          <button
            onClick={() => onActivateBankAccount(bankAccount.id)}
            disabled={bankAccount.providers?.wealthyhood?.status !== "Pending"}
            className="btn btn-primary font-size-h3"
          >
            Activate
          </button>
        </td>
        {/* End Activate button Cell */}
      </tr>
    );
  }
}

export default AdminBankAccountRow;
