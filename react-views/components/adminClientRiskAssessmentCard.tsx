import React, { Component } from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { RiskAssessmentDocument, RiskScoreClassificationEnum } from "../../models/RiskAssessment";

type PropsType = {
  assessment: RiskAssessmentDocument;
  className?: string;
};

type AssessmentField = {
  value: any;
  score: number;
};

export default class RiskAssessmentCard extends Component<PropsType> {
  private _getProgressColor = (score: number): string => {
    if (score <= 3) return "text-success";
    if (score <= 6) return "text-warning";
    return "text-danger";
  };

  private _formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString("en-GB", {
      day: "numeric",
      month: "short",
      year: "numeric"
    });
  };

  private _getTotalScoreClassificationColor = (classification: RiskScoreClassificationEnum): string => {
    switch (classification) {
      case RiskScoreClassificationEnum.LowRisk:
        return "text-success";
      case RiskScoreClassificationEnum.MediumRisk:
        return "text-warning";
      case RiskScoreClassificationEnum.HighRisk:
      case RiskScoreClassificationEnum.Prohibited:
        return "text-danger";
      default:
        return "text-secondary";
    }
  };

  private _formatValue = (field: AssessmentField): string => {
    if (!field) return "Not Available";

    if (Array.isArray(field.value)) {
      return field.value.join(", ");
    }

    if (field.value && typeof field.value === "string") {
      return field.value;
    }

    return field.value?.toString() || "Not Available";
  };

  private _renderAssessmentField = (
    label: string,
    field: AssessmentField,
    customFormat?: (field: AssessmentField) => string
  ): JSX.Element | null => {
    if (!field) return null;

    const value = customFormat ? customFormat(field) : this._formatValue(field);

    return (
      <div className="mb-6" key={label}>
        <div className="text-muted fs-7 mb-1">{label}</div>
        <div className="d-flex align-items-center justify-content-between">
          <div className="fs-6 fw-bold text-gray-800">
            <h5>{value}</h5>
          </div>
          <div className="d-flex align-items-center">
            <span className="fs-7 fw-semibold text-gray-600">
              <h5 className={this._getProgressColor(field.score)}>{field.score}</h5>
            </span>
          </div>
        </div>
      </div>
    );
  };

  render(): JSX.Element {
    const { assessment, className = "" } = this.props;

    if (!assessment) {
      return (
        <div className="card border-radius-xl shadow-sm">
          <div className="card-body p-6">
            <div className="text-gray-500">No risk assessment data available</div>
          </div>
        </div>
      );
    }

    return (
      <>
        <h2 className="font-weight-bolder mb-2 pt-3">Client Risk Assessment</h2>
        <h5 className="fs-4 text-muted mb-10">Generated on {this._formatDate(assessment.createdAt)}</h5>
        <div className={`card border-radius-xl shadow-sm ${className}`}>
          <div className="card-body p-6">
            {this._renderAssessmentField("Nationality", assessment.nationality, (field) =>
              field.value
                ? countriesConfig.countries.find((country) => country.code === field.value)?.name
                : "Not Available"
            )}

            {this._renderAssessmentField("AML Screening", assessment.amlScreening)}
            {this._renderAssessmentField("Employment Status", assessment.employmentStatus)}
            {this._renderAssessmentField("Sources of Funds", assessment.sourcesOfFunds)}
            {this._renderAssessmentField("Sources of Wealth", assessment.sourcesOfWealth)}
            {this._renderAssessmentField("Volume of Transactions", assessment.volumeOfTransactions, (field) =>
              field.value
                ? new Intl.NumberFormat("en-GB", {
                    style: "currency",
                    currency: "GBP"
                  }).format(field.value)
                : "Not Available"
            )}

            <div className="mt-8 pt-6 border-top">
              <div className="d-flex align-items-center justify-content-between">
                <div className="text-muted fs-7">Overall Risk</div>
                <div className="d-flex align-items-center">
                  <h2 className={this._getTotalScoreClassificationColor(assessment.classification)}>
                    {assessment.classification} ({assessment.totalScore})
                  </h2>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}
