import React from "react";
import GiftIcon from "../components/icons/giftIcon";
import { GiftDocument } from "../../models/Gift";
import { UserDocument } from "../../models/User";

type PropsType = {
  gift: GiftDocument;
};

class AdminGiftRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { gift } = this.props;
    const { consideration, createdAt, id, targetUserEmail, gifter } = gift;

    const gifterEmail = (gifter as UserDocument)?.email;

    return (
      <tr>
        {/* Icon Cell */}
        <td className="pl-2">
          <div className="symbol symbol-45 symbol-light mr-2 shadow-sm">
            <span className="symbol-label bg-white">
              <span className="svg-icon svg-icon-info svg-icon-2x">
                <GiftIcon />
              </span>
            </span>
          </div>
        </td>
        {/* End Icon Cell */}

        {/* Type Cell */}
        <td className="pl-0">
          <a className="text-dark-50 font-weight-bolder font-size-h5" href={`/admin/gifts/${id}`}>
            Gift
          </a>
        </td>
        {/* End Type Cell */}

        {/* Consideration Amount Cell */}
        <td className="pl-0">
          <span className="text-primary font-weight-bolder d-block font-size-h5">
            {consideration?.amount
              ? new Intl.NumberFormat("en-GB", {
                  style: "currency",
                  currency: consideration.currency,
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(consideration.amount / 100)
              : ""}
          </span>
        </td>
        {/* End Consideration Amount Cell */}

        {/* Created Date Cell */}
        <td className="pl-0">
          <span className="text-dark-75 font-weight-500 font-size-h6">
            {new Date(createdAt).toLocaleDateString("en-GB", {
              day: "numeric",
              month: "short",
              year: "numeric"
            })}
          </span>
        </td>
        {/* End Created Date Cell */}

        {/* Target User Email Cell */}
        <td className="pl-0 text-nowrap">
          <span className="text-dark-75 font-size-h5">{targetUserEmail}</span>
        </td>
        {/* End Target User Email Cell */}

        {/* Gifter User Email Cell */}
        <td className="pl-0 text-nowrap">
          <span className="text-dark-75 font-size-h5">{gifterEmail}</span>
        </td>
        {/* End Gifter User Email Cell */}
      </tr>
    );
  }
}

export default AdminGiftRow;
