import React from "react";
import GiftIcon from "../components/icons/giftIcon";
import { RewardDepositStatusType, RewardDocument, RewardOrderStatusType } from "../../models/Reward";
import { UserDocument } from "../../models/User";

const STATUS_CONFIG: Record<RewardDepositStatusType | RewardOrderStatusType, { color: string }> = {
  Empty: { color: "dark" },
  Pending: { color: "warning" },
  Settled: { color: "success" }
};

type PropsType = {
  reward: RewardDocument;
  userPage?: boolean;
};

class AdminRewardRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { reward, userPage } = this.props;
    const { consideration, createdAt, id, depositStatus, orderStatus, targetUser } = reward;
    const targetUserEmail = (targetUser as UserDocument).email;
    const wkPortfolioId = (targetUser as UserDocument).portfolios?.[0]?.providers?.wealthkernel?.id;

    return (
      <tr>
        {/* Icon Cell */}
        <td className="pl-2">
          <div className="symbol symbol-45 symbol-light mr-2 shadow-sm">
            <span className="symbol-label bg-white">
              <span className="svg-icon svg-icon-info svg-icon-2x">
                <GiftIcon />
              </span>
            </span>
          </div>
        </td>
        {/* End Icon Cell */}

        {/* Type Cell */}
        <td className="pl-0">
          <a className="text-dark-50 font-weight-bolder font-size-h5" href={`/admin/rewards/${id}`}>
            Reward
          </a>
        </td>
        {/* End Type Cell */}

        {/* Consideration Amount Cell */}
        <td className="pl-0">
          <span className="text-primary font-weight-bolder d-block font-size-h5">
            {consideration?.amount
              ? new Intl.NumberFormat("en-GB", {
                  style: "currency",
                  currency: consideration.currency,
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(consideration.amount / 100)
              : ""}
          </span>
        </td>
        {/* End Consideration Amount Cell */}

        {/* Created Date Cell */}
        <td className="pl-0">
          <span className="text-dark-75 font-weight-500 font-size-h6">
            {new Date(createdAt).toLocaleDateString("en-GB", {
              day: "numeric",
              month: "short",
              year: "numeric"
            })}
          </span>
        </td>
        {/* End Created Date Cell */}

        {/* Deposit Status Cell */}
        <td className="pl-0 text-nowrap">
          <span className={`label label-dot label-${STATUS_CONFIG[depositStatus].color}`}></span>
          <span className={`font-weight-bold text-${STATUS_CONFIG[depositStatus].color} ml-2`}>
            {depositStatus}
          </span>
        </td>
        {/* End Deposit Status Cell */}

        {/* Order Status Cell */}
        <td className="pl-0 text-nowrap">
          <span className={`label label-dot label-${STATUS_CONFIG[orderStatus].color}`}></span>
          <span className={`font-weight-bold text-${STATUS_CONFIG[orderStatus].color} ml-2`}>{orderStatus}</span>
        </td>
        {/* End Order Status Cell */}

        {/* Target User Email Cell */}
        {!userPage && (
          <td className="pl-0 text-nowrap">
            <span className="text-dark-75 font-size-h5">{targetUserEmail}</span>
          </td>
        )}
        {/* End Target User Email Cell */}

        {/* Target User Wealthkernel Portfolio Id Cell */}
        {!userPage && (
          <td className="pl-0 text-nowrap">
            <span className="text-dark-75 font-size-h5">{wkPortfolioId || "-"}</span>
          </td>
        )}
        {/* End Target User Wealthkernel Portfolio Id Cell */}
      </tr>
    );
  }
}

export default AdminRewardRow;
