import React from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  defaultValue: string;
  name: string;
} & React.SelectHTMLAttributes<HTMLSelectElement>;

class AdminSelectCountry extends React.Component<PropsType> {
  render(): JSX.Element {
    const { defaultValue, name, ...htmlProps } = this.props;

    return (
      <div className="position-relative">
        <select defaultValue={defaultValue} name={name} className="form-control" {...htmlProps}>
          <option value="">Select</option>
          {countriesConfig.countries.map(({ code, name }) => (
            <option value={code} key={`country-code-${code}`}>
              {name}
            </option>
          ))}
        </select>
      </div>
    );
  }
}

export default AdminSelectCountry;
