import React from "react";
import TransactionSyncButton from "./transactionSyncButton";
import { TransactionStatusType } from "../../models/Transaction";

type PropsType = {
  amount: number;
  currency: string;
  date: Date;
  icon: JSX.Element;
  iconColorClass: string;
  name: string;
  status: TransactionStatusType;
  transactionId: string;
  transactionUrl: string;
  repeating?: boolean;
};

const STATUS_CONFIG: Record<TransactionStatusType, { color: string; label: string }> = {
  Cancelled: { color: "danger", label: "Cancelled" },
  PendingTopUp: { color: "warning", label: "Pending" },
  PendingReinvestment: { color: "warning", label: "Pending" },
  Pending: { color: "warning", label: "Pending" },
  PendingDeposit: { color: "warning", label: "PendingDeposit" },
  PendingGift: { color: "warning", label: "PendingGift" },
  Rejected: { color: "danger", label: "Rejected" },
  DepositFailed: { color: "danger", label: "DepositFailed" },
  Settled: { color: "success", label: "Settled" },
  PendingWealthkernelCharge: { color: "warning", label: "Pending WK Charge" }
};

class AdminTransactionRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const {
      amount,
      currency,
      date,
      icon,
      iconColorClass,
      name,
      status,
      transactionId,
      transactionUrl,
      children,
      repeating
    } = this.props;

    return (
      <tr>
        <td className="pl-2">
          <div className="symbol symbol-45 symbol-light mr-2 shadow-sm">
            <span className="symbol-label bg-white">
              <span className={`svg-icon svg-icon-${iconColorClass} svg-icon-2x`}>{icon}</span>
            </span>
          </div>
        </td>
        <td className="pl-0">
          {transactionUrl ? (
            <a className="text-dark-50 font-weight-bolder font-size-h5" href={transactionUrl}>
              {name}
            </a>
          ) : (
            <span className="text-dark-50 font-weight-bolder font-size-h5">{name}</span>
          )}
          {repeating && (
            <span className={"wh-primary-label"} style={{ padding: "2px 6px", marginLeft: "10px" }}>
              Repeating
            </span>
          )}
        </td>
        <td className="pl-0">
          <span className="text-primary font-weight-bolder d-block font-size-h5">
            {amount
              ? new Intl.NumberFormat("en-GB", {
                  style: "currency",
                  currency,
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                }).format(amount / 100)
              : ""}
          </span>
        </td>
        <td className="pl-0">
          <span className="text-dark-75 font-weight-500 font-size-h6">
            {date.toLocaleDateString("en-GB", { day: "numeric", month: "short", year: "numeric" })}
          </span>
        </td>
        <td className="pl-0 text-nowrap">
          <span className={`label label-dot label-${STATUS_CONFIG[status].color}`} />
          <span className={`font-weight-bold text-${STATUS_CONFIG[status].color} ml-2`}>
            {STATUS_CONFIG[status].label}
          </span>
        </td>
        {children}
        <td>
          <TransactionSyncButton transactionId={transactionId} />
        </td>
      </tr>
    );
  }
}

export default AdminTransactionRow;
