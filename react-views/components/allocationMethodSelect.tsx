import React from "react";
import { GlobalContext } from "../contexts/globalContext";
import { AllocationMethodEnum } from "./transactionModalTargetAllocationSelector";
import { ALLOCATION_METHOD_CONFIG } from "../configs/allocationMethodConfig";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

type PropsType = {
  allocationMethodSelected: AllocationMethodEnum;
  onAllocationMethodChange: (allocationMethod: AllocationMethodEnum) => void;
  hasHoldings: boolean;
  isPortfolioAllocationSetup: boolean;
};

class AllocationMethodSelect extends React.Component<PropsType> {
  private _setAllocationMethod = (allocationMethod: AllocationMethodEnum): void => {
    const { onAllocationMethodChange } = this.props;

    onAllocationMethodChange(allocationMethod);
  };

  render(): JSX.Element {
    const { allocationMethodSelected, hasHoldings, isPortfolioAllocationSetup } = this.props;

    return (
      <>
        <div
          className={
            `row m-0 wh-account-card-option mb-3 ${!isPortfolioAllocationSetup ? "opacity-50" : ""}` +
            (allocationMethodSelected === AllocationMethodEnum.TARGET_ALLOCATION
              ? " wh-account-card-option-selected"
              : "")
          }
          onClick={() =>
            isPortfolioAllocationSetup
              ? this._setAllocationMethod(AllocationMethodEnum.TARGET_ALLOCATION)
              : emitToast({
                  content:
                    "Looks like you haven't set up your target allocation yet. To buy your target portfolio, first define your allocation in the Investments tab under 'Target'.",
                  toastType: ToastTypeEnum.error
                })
          }
        >
          <div className="col-2 p-0 align-self-center">
            <i
              className={`d-flex material-symbols-outlined justify-content-center align-self-center ${
                allocationMethodSelected === AllocationMethodEnum.TARGET_ALLOCATION ? "text-primary" : ""
              }`}
            >
              {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.TARGET_ALLOCATION].iconKey}
            </i>
          </div>
          <div className="col-9 align-self-center">
            <div className="d-flex flex-column">
              <div className={"d-flex"}>
                <span className={"me-1"}>
                  {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.TARGET_ALLOCATION].nameDisplay}
                </span>
                <HoverableInfoIcon
                  colorHex={"#536AE3"}
                  hoverText={
                    "When you select to buy your ‘Target portfolio’, your investment will be made in your target portfolio allocation. This is the portfolio allocation (weighting) you have defined in your Target tab.\n" +
                    "\n" +
                    "Differences between your target portfolio and your actual holdings can occur either from market movements (when one asset moves more than others) or if you buy/sell individual assets without updating your target portfolio allocation in your Target tab accordingly.\n" +
                    "\n" +
                    "Selecting your ‘Target portfolio’ helps maintain the allocation you have defined for your portfolio."
                  }
                />
              </div>{" "}
              <span className={"fw-light t-875"}>
                {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.TARGET_ALLOCATION].subtitle}
              </span>
            </div>
          </div>
        </div>
        <div
          className={
            `row m-0 wh-account-card-option mb-3 ${!hasHoldings ? "opacity-50" : ""}` +
            (allocationMethodSelected === AllocationMethodEnum.HOLDINGS ? " wh-account-card-option-selected" : "")
          }
          onClick={() =>
            hasHoldings
              ? this._setAllocationMethod(AllocationMethodEnum.HOLDINGS)
              : emitToast({
                  content:
                    "Looks like you don’t have any investments at the moment. To use the ‘My investments’ option, you'll need to have some active holdings first.",
                  toastType: ToastTypeEnum.error
                })
          }
        >
          <div className="col-2 p-0 align-self-center">
            <i
              className={`d-flex material-symbols-outlined justify-content-center align-self-center ${
                allocationMethodSelected === AllocationMethodEnum.HOLDINGS ? "text-primary" : ""
              }`}
            >
              {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.HOLDINGS].iconKey}
            </i>
          </div>
          <div className="col-9 align-self-center">
            <div className="d-flex flex-column">
              <div className={"d-flex"}>
                <span className={"me-1"}>
                  {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.HOLDINGS].nameDisplay}
                </span>
                <HoverableInfoIcon
                  colorHex={"#536AE3"}
                  hoverText={
                    "When you select to buy your ‘Investments’, your investment will be made in your current holdings. This is the weighting of your actual current holdings that you can also see in your Investments tab."
                  }
                />
              </div>
              <span className={"fw-light t-875"}>
                {ALLOCATION_METHOD_CONFIG[AllocationMethodEnum.HOLDINGS].subtitle}
              </span>
            </div>
          </div>
        </div>
      </>
    );
  }
}

AllocationMethodSelect.contextType = GlobalContext;

export default AllocationMethodSelect;
