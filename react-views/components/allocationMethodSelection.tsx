import React from "react";
import RiskBarSelection from "./riskBarSelection";
import InfoModal from "./modals/infoModal";

export const ALLOCATION_METHOD_CONFIG: Record<AllocationSelectionKeyType, { title: string; description: string }> =
  {
    custom: {
      title: "Custom",
      description:
        "Use custom weighting for your portfolio template and filter it out by its investment style. You can customise it in the next step."
    },
    equal: {
      title: "Equal",
      description:
        "Use equal weighting for your portfolio template. You’ll begin with equal weights across asset classes and assets. You can customise it in the next step."
    }
  };

export type AllocationSelectionKeyType = "equal" | "custom";

type PropsType = {
  activeAllocationSelectionKey: AllocationSelectionKeyType;
  selectedRisk: number;
  onRiskChangeCb: (risk: number) => void;
  onSelectionCb: (selectionKey: AllocationSelectionKeyType) => void;
};

type StateType = {
  showInfoDialog: boolean;
};

class AllocationMethodSelection extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showInfoDialog: false
    };
  }

  private _isAllocationMethodSelected(selectionKey: AllocationSelectionKeyType) {
    const { activeAllocationSelectionKey } = this.props;
    return activeAllocationSelectionKey == selectionKey;
  }

  private _getActiveAllocationSelectionKeyDescription(): string {
    const { activeAllocationSelectionKey } = this.props;
    return ALLOCATION_METHOD_CONFIG[activeAllocationSelectionKey].description;
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  private _setSelectedRisk(selectedRisk: number) {
    this.props.onRiskChangeCb(selectedRisk);
  }

  private _setActiveAllocationSelectionKey(activeAllocationSelectionKey: AllocationSelectionKeyType) {
    this.props.onSelectionCb(activeAllocationSelectionKey);
  }

  render(): JSX.Element {
    const { selectedRisk, activeAllocationSelectionKey } = this.props;
    const { showInfoDialog } = this.state;

    return (
      <>
        {/* Method Selection */}
        <div className="row px-5 mb-2 pt-2">
          {Object.keys(ALLOCATION_METHOD_CONFIG).map((selectionKey: AllocationSelectionKeyType) => {
            const { title } = ALLOCATION_METHOD_CONFIG[selectionKey];
            const selectedCssClass = this._isAllocationMethodSelected(selectionKey) ? "active-method" : "";

            return (
              <div
                key={selectionKey}
                className={"col p-1 text-center align-self-center cursor-pointer " + selectedCssClass}
                onClick={(): void => this._setActiveAllocationSelectionKey(selectionKey)}
              >
                {title}
              </div>
            );
          })}
        </div>

        {/* End Method Selection */}

        <div className="row justify-content-center m-0 mt-md-4 mt-3">
          <div className="col p-0">
            <p className="text-muted">{this._getActiveAllocationSelectionKeyDescription()}</p>
          </div>
        </div>
        {activeAllocationSelectionKey === "custom" && <h5 className="my-4">Investment style</h5>}
        {/* Risk Selection */}
        {activeAllocationSelectionKey === "custom" && (
          <>
            <div className="row m-0 p-0">
              <div className="col p-0 text-center">
                <RiskBarSelection risk={selectedRisk} onRiskChanged={(risk) => this._setSelectedRisk(risk)} />
              </div>
            </div>
            <div className="row mt-1 w-100 m-0 p-0 pt-4">
              <div className="col p-0 text-start">
                <h6 className="fw-bold ps-2 t-75">Conservative</h6>
              </div>
              <div className="col p-0 text-center">
                <h6 className="fw-bold ps-2 t-75">Moderate</h6>
              </div>
              <div className="col p-0 text-end">
                <h6 className="fw-bold pe-3 t-75">Adventurous</h6>
              </div>
            </div>
          </>
        )}
        {/* End Risk Selection */}
        <div className="row justify-content-center m-0 mt-md-3 mt-2">
          {activeAllocationSelectionKey === "custom" && (
            <p
              className="text-primary cursor-pointer d-flex justify-content-start cursor-pointer align-items-center mb-4"
              onClick={(event) => {
                event.stopPropagation();
                this._setShowInfoDialog(true);
              }}
            >
              <i className="material-symbols-outlined align-self-center text-primary" style={{ fontSize: "16px" }}>
                info
              </i>
              <span style={{ paddingLeft: "4px", fontWeight: "500" }}>About custom weighting</span>
            </p>
          )}
        </div>

        <InfoModal
          title={null}
          show={showInfoDialog && activeAllocationSelectionKey == "custom"}
          handleClose={() => this._setShowInfoDialog(false)}
        >
          <h5 className="fw-bold mb-5">About ‘Custom’ portfolio weighting </h5>
          <p className="text-muted">
            If you select ‘Custom’ weighting, we’ll take into account the assets that match your selection in the
            previous steps and use the combination that produces the maximum (expected) return with the minimum
            risk.
          </p>
          <p className="text-muted">
            We’ll then fetch the portfolio template that corresponds to the investment style you defined.
          </p>
          <p className="text-muted pb-4">
            However, keep in mind that during the process, we use data from the past 10 years. There is no
            guarantee that what happened in the past will also happen in the future.
          </p>

          <p className="fw-bold">How do we calculate risk?</p>
          <p className="text-muted">
            The risk is calculated by fund managers and provided on our platform as the total expected volatility
            of your portfolio. Volatility is a statistical measure that represents how much the value of your
            portfolio swings around its mean price!
          </p>
          <p className="text-muted">
            More volatile portfolios are considered to be riskier than less volatile ones, because the value
            movements are expected to be steeper and therefore, less predictable.
          </p>
          <p className="text-muted">
            The expected volatility of your portfolio is calculated, based on how the individual assets performed
            over the past years.
          </p>
          <p className="text-muted">
            Note: The risk measure used to construct the Wealthyhood templates is the same as the one provided by
            the fund manager of the relevant ETF and set out in the ETF’s disclosure material (KID).
          </p>
          <p className="fw-bold">
            How this translates into Conservative, Moderate, Adventurous investment styles?
          </p>
          <ul className="ps-md-5 ps-4">
            <li className="text-muted">
              <p>
                <span className="fw-bolder-black">Conservative</span> portfolios have usually very low risk
                (volatility {"<"} 5%), but also lower growth potential and typically consist of just government and
                corporate bonds. The closer you get to the conservative portfolio, your risk approaches 5% or less.
              </p>
            </li>
            <li className="text-muted">
              <p>
                <span className="fw-bolder-black">Moderate</span> portfolios stand between conservative and
                adventurous portfolios (volatility between 5% & 15%). They are usually well-diversified portfolios
                with balanced risk-reward potential.
              </p>
            </li>
            <li className="text-muted">
              <p>
                <span className="fw-bolder-black">Adventurous</span> portfolios have usually higher risk
                (volatility {">"} 15%), but also the potential for higher returns and growth of your investment
                over the long run. They consist mainly of stocks. The closer you get to the adventurous portfolio,
                your risk approaches 15% or more.
              </p>
            </li>
          </ul>
          <p className="text-muted">
            <span className="fw-bolder-black">Important:</span> These portfolio templates have been constructed
            based on data from the past 10 years. Please keep in mind that past performance is not indicative of
            future returns.
          </p>
        </InfoModal>
      </>
    );
  }
}

export default AllocationMethodSelection;
