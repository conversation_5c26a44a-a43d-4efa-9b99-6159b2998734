import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faLock } from "@fortawesome/free-solid-svg-icons";
import Nouislider from "nouislider-react";

type PropsType = {
  labelName: string;
  colorClass: string;
  componentName: string;
  isAssetClass: boolean;
  isEnabled: boolean;
  range: object;
  start: number;
  step: number;
  onSliderChangeCb: (sliderCommonId: string, value: number) => void;
};

type StateType = {
  sliderValue: number;
};

class AllocationSlider extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    const { start } = props;
    this.state = {
      sliderValue: start || 0
    };
  }

  onSliderChange = (values: number[]): void => {
    const { componentName, onSliderChangeCb } = this.props;
    const value = Number(values[0]);
    onSliderChangeCb(componentName, value);
  };

  onSliderUpdate = (values: number[]): void => {
    const sliderValue = Number(values[0]);
    this.setState({ sliderValue });
  };

  render(): JSX.Element {
    const { labelName, colorClass, componentName, isAssetClass, isEnabled, range, step } = this.props;
    const { sliderValue } = this.state;

    return (
      <>
        <div className="pb-1">
          <label className={isAssetClass ? "font-weight-bolder h5 pl-2" : ""}>{labelName}</label>
          {isEnabled ? (
            <>
              <span className="float-right slider-value" id={`nouislider_${componentName}_span`}>
                {sliderValue}%
              </span>
            </>
          ) : (
            <span className="float-right">
              <FontAwesomeIcon icon={faLock} className="fas fa-lock" />
            </span>
          )}
        </div>
        <div className="row">
          <div className="col-12">
            <Nouislider
              id={`nouislider_${componentName}_bar`}
              animate={false}
              className={`ml-1 nouislider ${colorClass}`}
              disabled={!isEnabled}
              range={range}
              start={sliderValue}
              step={step}
              connect={[true, false]}
              onUpdate={this.onSliderUpdate}
              onChange={this.onSliderChange}
            />
          </div>
        </div>
      </>
    );
  }
}

export default AllocationSlider;
