import React from "react";

type PropsType = {
  colorClass: string;
  isAssetClass: boolean;
  labelName: string;
  sliderValue: number;
};

class AllocationSliderReadOnly extends React.Component<PropsType> {
  render(): JSX.Element {
    const { colorClass, isAssetClass, labelName, sliderValue } = this.props;

    return (
      <>
        <div className="pb-1">
          <label className={isAssetClass ? "font-weight-bolder h5 pl-2" : ""}>{labelName}</label>
          <span className="float-right slider-value">{sliderValue}%</span>
        </div>
        <div className="row">
          <div className="col-12">
            <div className="progress w-100" style={{ height: "8px" }}>
              <div
                className={`progress-bar ${colorClass}`}
                role="progressbar"
                style={{ width: `${sliderValue}%` }}
                aria-valuenow={sliderValue}
                aria-valuemin={0}
                aria-valuemax={100}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default AllocationSliderReadOnly;
