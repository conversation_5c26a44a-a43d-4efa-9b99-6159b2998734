import React from "react";

type PropsType = {
  isActive: boolean;
  href: string;
  label: string;
  Icon: any;
  activeIconClass: string;
  inactiveIconClass: string;
};

class AsideMenuOption extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isActive, href, label, Icon, activeIconClass, inactiveIconClass } = this.props;

    return (
      <li className={`menu-item menu-item-submenu ${isActive ? "menu-item-active" : ""}`}>
        <a className="menu-link" href={href}>
          <span className={`svg-icon svg-icon-2x menu-icon ${isActive ? activeIconClass : inactiveIconClass}`}>
            <Icon />
          </span>
          <span className="menu-text">{label}</span>
        </a>
      </li>
    );
  }
}

export default AsideMenuOption;
