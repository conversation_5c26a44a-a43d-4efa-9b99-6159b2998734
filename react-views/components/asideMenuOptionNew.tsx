import React from "react";

type PropsType = {
  isActive: boolean;
  href: string;
  label: string;
  materialIcon: string;
  className: string;
};

class AsideMenuOptionNew extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isActive, href, label, materialIcon, className } = this.props;

    return (
      <a
        href={href}
        className={`d-flex wh-side-option text-muted text-decoration-none mb-4 px-3 py-2 text-start ${
          isActive ? "wh-side-option-selected align-self-center" : ""
        }`}
      >
        <span
          className={`material-symbols-outlined align-self-center me-3 ${className}`}
          style={{ fontSize: "18px" }}
        >
          {materialIcon}
        </span>
        {label}
      </a>
    );
  }
}

export default AsideMenuOptionNew;
