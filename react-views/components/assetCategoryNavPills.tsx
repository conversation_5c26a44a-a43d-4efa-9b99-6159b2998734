import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import HorizontalScroller from "./horizontalScroller";

type PropsType = {
  activeCategory: investmentUniverseConfig.AssetCategoryType;
  assetCategories: investmentUniverseConfig.AssetCategoryType[];
  onAssetTypeSelection: (assetCategory: investmentUniverseConfig.AssetCategoryType) => void;
  className?: string;
};

class AssetCategoryNavPills extends React.Component<PropsType> {
  render(): JSX.Element {
    const { assetCategories, className, onAssetTypeSelection, activeCategory } = this.props;

    return (
      <>
        <div className={`d-flex flex-row flex-nowrap overflow-auto no-scroll-bar ${className}`}>
          <HorizontalScroller id={"asset-classes-scroller"} showScroll={false}>
            {assetCategories.map((assetCategory) => (
              <div
                key={assetCategory}
                className={
                  "cursor-pointer col py-2 px-3 text-center align-self-center text-center fw-bold text-muted text-nowrap " +
                  (assetCategory == activeCategory ? "active-sector" : "")
                }
                style={assetCategory == activeCategory ? { background: "#536AE3" } : {}}
                onClick={() => onAssetTypeSelection(assetCategory)}
              >
                {assetCategory == "stock" ? "Individual stocks" : "ETFs"}
              </div>
            ))}
          </HorizontalScroller>
        </div>
      </>
    );
  }
}

export default AssetCategoryNavPills;
