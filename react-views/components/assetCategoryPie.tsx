import React from "react";
import { ArcElement, Chart as Chart<PERSON><PERSON>, ChartEvent, Legend, Tooltip } from "chart.js";
import { Doughn<PERSON> } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { investmentUniverseConfig, localeConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels as any);

const getPieOptions = (
  userLocale: localeConfig.LocaleType,
  allocation: { [key in investmentUniverseConfig.AssetCategoryType]?: number },
  onAssetCategoryClick: (assetCategory: investmentUniverseConfig.AssetCategoryType) => void
) => {
  return {
    cutout: "70%",
    aspectRatio: 1,
    maintainAspectRatio: false,
    elements: {
      arc: {
        borderWidth: 0
      }
    },
    responsive: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label(context: any): string {
            return `${context.label}: ${context.raw.toLocaleString(userLocale, {
              style: "percent",
              maximumFractionDigits: 2
            })}`;
          }
        },
        bodyFont: {
          size: 16
        },
        displayColors: false
      },
      datalabels: {
        formatter: (): string => {
          return "";
        }
      }
    },
    layout: {
      padding: {
        top: 15,
        bottom: 15,
        left: 15,
        right: 15
      }
    },
    onClick: (_: ChartEvent, elements: { index: number }[]) => {
      if (elements.length) {
        const index = elements[0].index;
        const assetCategoryKey = Object.keys(allocation)[index] as investmentUniverseConfig.AssetCategoryType;
        onAssetCategoryClick(assetCategoryKey);
      }
    }
  };
};

type PropsType = {
  allocation: { [key in investmentUniverseConfig.AssetCategoryType]?: number };
  onAssetCategoryClick: (assetCategory: investmentUniverseConfig.AssetCategoryType) => void;
  selectedCategory: investmentUniverseConfig.AssetCategoryType;
};

class AssetCategoryPie extends React.Component<PropsType> {
  constructor(props: PropsType) {
    super(props);
  }

  private _getData = () => {
    const { allocation, selectedCategory } = this.props;
    return {
      datasets: [
        {
          data: Object.values(allocation).map((value) => value / 100),
          backgroundColor: Object.keys(allocation).map((assetType) => {
            const isSelectedCategory = selectedCategory === assetType;
            return isSelectedCategory ? "#536AE3" : "#DCE2FD";
          })
        }
      ],
      labels: Object.keys(allocation).map((assetType) => (assetType == "stock" ? "Individual stocks" : "ETFs"))
    };
  };

  render(): JSX.Element {
    const { allocation, selectedCategory, onAssetCategoryClick } = this.props;
    const { locale } = this.context as GlobalContextType;

    return (
      <div className="chart-outbox position-relative">
        <div className="chart-wrapper">
          <Doughnut
            options={getPieOptions(locale, allocation, onAssetCategoryClick)}
            data={this._getData()}
            height={300}
          />
        </div>
        {allocation[selectedCategory] && (
          <div className="position-absolute top-50 start-50 translate-middle fw-bolder">
            <h2 className="fw-bolder m-0 pb-2">
              {allocation[selectedCategory].toLocaleString(locale, { maximumFractionDigits: 1 })}%
            </h2>
            <p className="m-0" style={{ color: "#536AE3" }}>
              {selectedCategory == "stock" ? "Individual stocks" : "ETFs"}
            </p>
          </div>
        )}
      </div>
    );
  }
}

AssetCategoryPie.contextType = GlobalContext;

export default AssetCategoryPie;
