import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import HorizontalScroller from "./horizontalScroller";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  assetClasses: investmentUniverseConfig.AssetClassType[];
  onAssetClassSelection: (assetClass: investmentUniverseConfig.AssetClassType) => void;
  className?: string;
};

class AssetClassNavPills extends React.Component<PropsType> {
  private _isAssetClassSelected(assetClass: investmentUniverseConfig.AssetClassType): boolean {
    const { activeAssetClass } = this.props;
    return assetClass == activeAssetClass;
  }

  render(): JSX.Element {
    const { assetClasses, className, onAssetClassSelection } = this.props;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);

    return (
      <>
        {/*<AssetClassNavPills*/}
        <div className={`d-flex flex-row flex-nowrap overflow-auto no-scroll-bar ${className}`}>
          <HorizontalScroller id={"asset-classes-scroller"} showScroll={false}>
            {assetClasses.map((assetClass) => (
              <div
                key={assetClass}
                className={
                  "cursor-pointer col py-2 px-3 text-center align-self-center text-center fw-bold text-muted text-nowrap " +
                  (this._isAssetClassSelected(assetClass) ? "active-sector" : "")
                }
                style={
                  this._isAssetClassSelected(assetClass)
                    ? { background: ASSET_CLASS_CONFIG[assetClass].colorClass }
                    : {}
                }
                onClick={() => onAssetClassSelection(assetClass)}
              >
                {ASSET_CLASS_CONFIG[assetClass].fieldName}
              </div>
            ))}
          </HorizontalScroller>
        </div>
        {/*/!* AssetClassNav *!/*/}
      </>
    );
  }
}

AssetClassNavPills.contextType = GlobalContext;

export default AssetClassNavPills;
