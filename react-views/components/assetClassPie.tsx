import React from "react";
import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, <PERSON>, ChartEvent } from "chart.js";
import { Doughn<PERSON> } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { investmentUniverseConfig, localeConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels as any);

const getPieOptions = (
  userLocale: localeConfig.LocaleType,
  allocation: { [key in investmentUniverseConfig.AssetClassType]?: number },
  onAssetClassClick: (key: investmentUniverseConfig.AssetClassType) => void
) => {
  return {
    cutout: "70%",
    aspectRatio: 1,
    maintainAspectRatio: false,
    elements: {
      arc: {
        borderWidth: 0
      }
    },
    responsive: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label(context: any): string {
            return `${context.label}: ${context.raw.toLocaleString(userLocale, {
              style: "percent",
              maximumFractionDigits: 2
            })}`;
          }
        },
        bodyFont: {
          size: 16
        },
        displayColors: false
      },
      datalabels: {
        formatter: (): string => {
          return "";
        }
      }
    },
    layout: {
      padding: {
        top: 15,
        bottom: 15,
        left: 15,
        right: 15
      }
    },
    onClick: (_: ChartEvent, elements: { index: number }[]) => {
      if (elements.length) {
        const index = elements[0].index;
        const assetClassKey = Object.keys(allocation)[index] as investmentUniverseConfig.AssetClassType;
        onAssetClassClick(assetClassKey);
      }
    }
  };
};

type PropsType = {
  allocation: { [key in investmentUniverseConfig.AssetClassType]?: number };
  onAssetClassClick: (assetClass: investmentUniverseConfig.AssetClassType) => void;
  selectedAssetClass: investmentUniverseConfig.AssetClassType;
};

class AssetClassPie extends React.Component<PropsType> {
  constructor(props: PropsType) {
    super(props);
  }

  private _getData = () => {
    const { allocation, selectedAssetClass } = this.props;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    return {
      datasets: [
        {
          data: Object.values(allocation).map((value) => value / 100),
          backgroundColor: Object.keys(allocation).map((assetClassKey) => {
            const isCurrentAssetClassSelected = selectedAssetClass === assetClassKey;
            const assetClassColor =
              ASSET_CLASS_CONFIG[assetClassKey as investmentUniverseConfig.AssetClassType].colorClass;
            return isCurrentAssetClassSelected ? assetClassColor : `${assetClassColor}30`;
          })
        }
      ],
      labels: Object.keys(allocation).map(
        (assetClassKey: investmentUniverseConfig.AssetClassType) => ASSET_CLASS_CONFIG[assetClassKey].fieldName
      )
    };
  };

  render(): JSX.Element {
    const { allocation, selectedAssetClass, onAssetClassClick } = this.props;
    const { locale } = this.context as GlobalContextType;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);

    return (
      <div className="chart-outbox position-relative">
        <div className="chart-wrapper">
          <Doughnut
            options={getPieOptions(locale, allocation, onAssetClassClick)}
            data={this._getData()}
            height={300}
          />
        </div>
        {allocation[selectedAssetClass] && (
          <div className="position-absolute top-50 start-50 translate-middle fw-bolder">
            <h2 className="fw-bolder m-0 pb-2">
              {allocation[selectedAssetClass].toLocaleString(locale, { maximumFractionDigits: 1 })}%
            </h2>
            <p className="m-0" style={{ color: ASSET_CLASS_CONFIG[selectedAssetClass].colorClass }}>
              {ASSET_CLASS_CONFIG[selectedAssetClass].fieldName}
            </p>
          </div>
        )}
      </div>
    );
  }
}

AssetClassPie.contextType = GlobalContext;

export default AssetClassPie;
