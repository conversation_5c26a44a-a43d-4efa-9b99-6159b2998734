import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AssetClassSelectionOption from "./assetClassSelectionOption";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  selectedAssetClasses: investmentUniverseConfig.AssetClassType[];
  onSelectionChange: (assetClasses: investmentUniverseConfig.AssetClassType[]) => void;
};
type StateType = {
  selectedAssetClasses: investmentUniverseConfig.AssetClassType[];
};

class AssetClassSelection extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      selectedAssetClasses: props.selectedAssetClasses
    };
  }

  private _setActiveAssetClasses = (updatedAssetClass: investmentUniverseConfig.AssetClassType): void => {
    // 1. Update selected asset classes
    const { selectedAssetClasses } = this.state;
    const assetClassIndex = selectedAssetClasses.findIndex((assetClass) => assetClass === updatedAssetClass);
    if (assetClassIndex > -1) {
      // asset class is already selected -> remove it from selected ones
      selectedAssetClasses.splice(assetClassIndex, 1);
    } else {
      // asset class is not selected -> add it to selected ones
      selectedAssetClasses.push(updatedAssetClass);
    }

    // 2. Set state & run callback to update parent
    this.setState({ selectedAssetClasses }, () => {
      const { onSelectionChange } = this.props;
      onSelectionChange(selectedAssetClasses);
    });
  };

  render(): JSX.Element {
    const { selectedAssetClasses } = this.state;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses(
      (this.context as GlobalContextType).user.companyEntity,
      true
    );

    return (
      <div className="container-fluid p-0 m-0">
        <div className="row justify-content-center m-0">
          {Object.values(ASSET_CLASS_CONFIG).map(({ icon, fieldName, keyName, whatIs }, index) => (
            <div className="col-6 p-md-3 p-2 align-self-center" key={index}>
              <AssetClassSelectionOption
                icon={icon}
                keyName={keyName as investmentUniverseConfig.AssetClassType}
                name={fieldName}
                isSelected={Boolean(selectedAssetClasses.find((assetClass) => assetClass === keyName))}
                whatIs={whatIs}
                onChange={this._setActiveAssetClasses}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }
}

AssetClassSelection.contextType = GlobalContext;

export default AssetClassSelection;
