import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InfoModal from "./modals/infoModal";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  icon: string;
  isSelected?: boolean;
  keyName: investmentUniverseConfig.AssetClassType;
  name: string;
  whatIs: string;
  onChange?: (assetClass: investmentUniverseConfig.AssetClassType) => void;
};

type StateType = {
  isSelected: boolean;
  showInfoDialog: boolean;
};

class AssetClassSelectionOption extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isSelected: props.isSelected,
      showInfoDialog: false
    };
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  private _changeSelectionState = (): void => {
    this.setState(
      (prevState) => {
        const { isSelected } = prevState;
        return { isSelected: !isSelected };
      },
      () => {
        const { keyName, onChange } = this.props;
        onChange && onChange(keyName);
      }
    );
  };

  render(): JSX.Element {
    const { icon, keyName, name, whatIs } = this.props;
    const { isSelected, showInfoDialog } = this.state;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const style = isSelected ? { background: ASSET_CLASS_CONFIG[keyName].colorClass, color: "#ffffff" } : {};

    return (
      <>
        <div
          key={keyName}
          className="card card-body wh-simple-card cursor-pointer border-0 text-center justify-content-center bg-hover-light"
          style={style}
          onClick={this._changeSelectionState}
        >
          <div className="row text-end">
            <div className="col">
              {isSelected ? (
                <i
                  className="material-symbols-outlined align-self-center"
                  onClick={(event) => {
                    event.stopPropagation();
                    this._setShowInfoDialog(true);
                  }}
                >
                  info
                </i>
              ) : (
                <i
                  className="material-symbols-outlined icon-primary align-self-center"
                  onClick={(event) => {
                    event.stopPropagation();
                    this._setShowInfoDialog(true);
                  }}
                >
                  info
                </i>
              )}
            </div>
          </div>
          <div className="row">
            <div className="col">
              <img alt="icon" className="align-self-center" src={icon} style={{ width: "90px", height: "90px" }} />
            </div>
          </div>
          <div className="row my-md-3">
            <div className="col">
              <div className="font-weight-bolder">{name}</div>
            </div>
          </div>
        </div>

        <InfoModal title={null} show={showInfoDialog} handleClose={() => this._setShowInfoDialog(false)}>
          <div dangerouslySetInnerHTML={{ __html: whatIs }} />
        </InfoModal>
      </>
    );
  }
}

AssetClassSelectionOption.contextType = GlobalContext;

export default AssetClassSelectionOption;
