import React, { Component } from "react";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import HorizontalScroller from "./horizontalScroller";
import ConfigUtil from "../../utils/configUtil";
type PropsType = {
  showInfo: () => void;
  onAssetClassClick: (assetClass: investmentUniverseConfig.AssetClassType) => void;
  companyEntity: entitiesConfig.CompanyEntityEnum;
};

export default class AssetDiscoverAssetClassSection extends Component<PropsType> {
  private _getAssetClassCell(icon: string, name: string): JSX.Element {
    return (
      <div
        className="card card-body wh-simple-card cursor-pointer border-0 text-center justify-content-center bg-hover-light"
        style={{ width: "160px", height: "160px" }}
      >
        <div className="row">
          <div className="col">
            <img alt="icon" className="align-self-center" src={icon} style={{ width: "72px", height: "72px" }} />
          </div>
        </div>
        <div className="row my-md-1">
          <div className="col">
            <div className="fw-bolder-black text-nowrap">{name}</div>
          </div>
        </div>
      </div>
    );
  }

  render() {
    const { showInfo, onAssetClassClick, companyEntity } = this.props;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses(companyEntity, true);

    return (
      <div className="container-fluid p-0 m-0">
        <div className="d-flex align-items-center mb-3">
          <h5 className="m-0">Asset classes</h5>
          <i
            className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
            onClick={() => {
              showInfo();
            }}
            style={{ fontSize: "20px" }}
          >
            info
          </i>
        </div>
        <div className="row justify-content-center m-0">
          <HorizontalScroller
            id={"asset-class-scroller"}
            className={"mb-5 px-lg-2 px-md-5 px-0"}
            scrollingDistance={450}
            showScrollDots={false}
          >
            {Object.values(ASSET_CLASS_CONFIG).map(({ icon, fieldName, keyName }, index) => (
              <div
                className="align-self-center"
                key={index}
                onClick={() => onAssetClassClick(keyName as investmentUniverseConfig.AssetClassType)}
              >
                {this._getAssetClassCell(icon, fieldName)}
              </div>
            ))}
          </HorizontalScroller>
        </div>
      </div>
    );
  }
}
