import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import <PERSON><PERSON>Scroller from "./horizontalScroller";
import { AssetCollectionsType } from "../types/assetDiscovery";

const COLLECTION_CONFIG: Record<string, investmentUniverseConfig.InvestmentCollectionType[]> = {
  firstRow: ["us_stocks", "uk_stocks", "eu_stocks", "china_stocks", "japan_stocks"],
  secondRow: ["big_tech", "ai_robotics", "cloud_computing", "banks", "crypto_currencies"],
  thirdRow: ["fighting_cancer", "biotech", "pharma", "female_ceos"]
};

type PropsType = {
  assetCollections: AssetCollectionsType;
  onCollectionClick: (collection: investmentUniverseConfig.InvestmentCollectionType) => void;
};

export default class AssetDiscoveryCollectionSection extends Component<PropsType> {
  private _getCollectionsRow(
    collections: investmentUniverseConfig.InvestmentCollectionType[],
    className?: string
  ): JSX.Element {
    const { assetCollections, onCollectionClick } = this.props;

    return (
      <div className={`d-flex ${className}`}>
        {collections.map((collection) => (
          <div
            className="asset-collection-pill me-2 cursor-pointer"
            key={collection}
            onClick={() => onCollectionClick(collection)}
          >{`${assetCollections[collection].emoji} ${assetCollections[collection].label}`}</div>
        ))}
      </div>
    );
  }

  render() {
    return (
      <div className="container-fluid p-0 m-0">
        <div className="d-flex align-items-center mb-3">
          <h5 className="m-0">Collections</h5>
        </div>
        <div className="row justify-content-center m-0">
          <HorizontalScroller
            id={"notifications-scroller"}
            className={"mb-5 px-lg-2 px-md-5 px-0"}
            scrollingDistance={450}
            showScrollAfterNElements={0}
            showScrollDots={false}
          >
            <div className="d-flex flex-column">
              {this._getCollectionsRow(COLLECTION_CONFIG.firstRow, "mb-3")}
              {this._getCollectionsRow(COLLECTION_CONFIG.secondRow, "mb-3")}
              {this._getCollectionsRow(COLLECTION_CONFIG.thirdRow)}
            </div>
          </HorizontalScroller>
        </div>
      </div>
    );
  }
}
