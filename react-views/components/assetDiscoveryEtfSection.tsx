import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { getAssetIconUrl } from "../utils/universeUtil";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  topEtfs: investmentUniverseConfig.AssetType[];
  showInfo: () => void;
  onSeeAllClick: () => void;
};

export default class AssetDiscoveryEtfSection extends Component<PropsType> {
  private _createPortfolioSetupAssetRow = (assetKey: investmentUniverseConfig.AssetType): JSX.Element => {
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];

    return (
      <PortfolioSetupAssetRow
        key={assetKey}
        category={category}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(assetKey)}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
      />
    );
  };

  render() {
    const { topEtfs, showInfo, onSeeAllClick } = this.props;

    return (
      <div className="container-fluid p-0 mb-5">
        <div className="d-flex justify-content-between mb-3">
          <div className="d-flex align-items-center">
            <h5 className="m-0">ETFs</h5>
            <i
              className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
              onClick={() => {
                showInfo();
              }}
              style={{ fontSize: "20px" }}
            >
              info
            </i>
          </div>
          <h5 className="text-primary m-0 cursor-pointer" onClick={() => onSeeAllClick()}>
            See all
          </h5>
        </div>
        <div>
          {topEtfs.map((asset, index) => (
            <div key={index} className={index !== topEtfs.length ? "mb-3" : ""}>
              {this._createPortfolioSetupAssetRow(asset)}
            </div>
          ))}
        </div>
      </div>
    );
  }
}
