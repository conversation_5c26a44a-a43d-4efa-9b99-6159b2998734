import React, { Component } from "react";
import { investmentUniverseConfig, localeConfig } from "@wealthyhood/shared-configs";
import { PortfolioDocument } from "../../models/Portfolio";
import { AssetCollectionsType } from "../types/assetDiscovery";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { compareAssets, compareBondCategories, getAssetIconUrl } from "../utils/universeUtil";
import AssetClassNavPills from "./assetClassNavPills";
import HorizontalScroller from "./horizontalScroller";
import { formatPercentage } from "../utils/formatterUtil";
import { formatCurrency } from "../utils/currencyUtil";
import { eventEmitter, EVENTS } from "../utils/eventService";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";
import AssetDiscoverySearchBar from "./assetDiscoverySearchBar";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

const { AssetClassArray, InvestmentSectorArray, ASSET_CONFIG, BOND_CATEGORY_CONFIG, InvestmentCollectionArray } =
  investmentUniverseConfig;

/**
 * TYPES
 */

export type AssetDiscoveryListViewModeType =
  | "all"
  | "etfs"
  | investmentUniverseConfig.InvestmentSectorType
  | investmentUniverseConfig.AssetClassType
  | investmentUniverseConfig.InvestmentCollectionType;
type SectorType = investmentUniverseConfig.InvestmentSectorType | "all";
type SectorConfigType = {
  fieldName: string;
  keyName: investmentUniverseConfig.InvestmentSectorType;
  icon: string;
  serviceUrlMapping: string;
  coreAssets: Record<investmentUniverseConfig.InvestmentGeographyType, investmentUniverseConfig.AssetType[]>;
  selectable: boolean;
  whatIs: string;
  colorClass: string;
  sorting: number;
};

type PropsType = {
  viewMode: AssetDiscoveryListViewModeType;
  assetCollections?: AssetCollectionsType;
  investmentProducts: InvestmentProductDocument[];
  portfolio: PortfolioDocument;
  universe: Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType>;
};

type StateType = {
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  activeSector: SectorType;
  isUserSearching: boolean;
};

export default class AssetDiscoveryList extends Component<PropsType, StateType> {
  private _investmentProductDict: Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;

  constructor(props: PropsType) {
    super(props);
    this._investmentProductDict = Object.fromEntries(
      props.investmentProducts.map((investmentProduct) => [investmentProduct.commonId, investmentProduct])
    ) as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;

    this.state = {
      activeAssetClass: "equities",
      activeSector: "all",
      isUserSearching: false
    };
  }
  componentDidMount() {
    window.scrollTo(0, 0);
  }

  private _onUserSearch = (active: boolean) => {
    this.setState({ isUserSearching: active });
  };

  private _isAssetCollectionViewMode(viewMode: AssetDiscoveryListViewModeType): boolean {
    return InvestmentCollectionArray.includes(viewMode as investmentUniverseConfig.InvestmentCollectionType);
  }
  private _isSectorViewMode(viewMode: AssetDiscoveryListViewModeType): boolean {
    return InvestmentSectorArray.includes(viewMode as investmentUniverseConfig.InvestmentSectorType);
  }

  private _isAssetClassViewMode(viewMode: AssetDiscoveryListViewModeType): boolean {
    return AssetClassArray.includes(viewMode as investmentUniverseConfig.AssetClassType);
  }

  private _isEtfViewMode(viewMode: AssetDiscoveryListViewModeType): boolean {
    return viewMode === "etfs";
  }

  private _getHeader(): JSX.Element {
    const { viewMode, assetCollections } = this.props;
    let title: string;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);

    if (this._isEtfViewMode(viewMode)) {
      title = "ETFs";
    } else if (this._isAssetCollectionViewMode(viewMode)) {
      title = assetCollections[viewMode as investmentUniverseConfig.InvestmentCollectionType].label;
    } else if (this._isSectorViewMode(viewMode)) {
      title = SECTOR_CONFIG[viewMode as investmentUniverseConfig.InvestmentSectorType].fieldName;
    } else if (this._isAssetClassViewMode(viewMode)) {
      title = ASSET_CLASS_CONFIG[viewMode as investmentUniverseConfig.AssetClassType].fieldName;
    } else {
      title = "See all";
    }

    const className = this._isAssetCollectionViewMode(viewMode) ? "mb-5" : viewMode !== "all" ? "mb-3" : "";
    return (
      <div className={className}>
        {/* Title */}
        <h4>{title}</h4>
        {/* End Title */}
      </div>
    );
  }

  private _getActiveAssets = (
    assetClass: investmentUniverseConfig.AssetClassType,
    options: {
      sector?: investmentUniverseConfig.InvestmentSectorType;
      bondCategory?: investmentUniverseConfig.BondCategoryType;
    } = {}
  ): investmentUniverseConfig.AssetType[] => {
    const { sector, bondCategory } = options;
    const { universe } = this.props;

    if (assetClass === "equities" && sector) {
      return Object.keys(universe).filter(
        (assetKey) => universe[assetKey as investmentUniverseConfig.AssetType].sector === sector
      ) as investmentUniverseConfig.AssetType[];
    } else if (assetClass === "bonds" && bondCategory) {
      return Object.keys(universe).filter((assetKey) => {
        const asset = universe[assetKey as investmentUniverseConfig.AssetType];

        return (asset as investmentUniverseConfig.ETFAssetConfigType)?.bondCategory === bondCategory;
      }) as investmentUniverseConfig.AssetType[];
    } else {
      return Object.keys(universe).filter(
        (assetKey) => universe[assetKey as investmentUniverseConfig.AssetType].assetClass === assetClass
      ) as investmentUniverseConfig.AssetType[];
    }
  };

  private _setActiveAssetClass = (activeAssetClass: investmentUniverseConfig.AssetClassType): void => {
    this.setState({ activeAssetClass });
  };

  private _setActiveSector = (activeSector: SectorType): void => {
    this.setState({ activeSector });
  };

  private static _formatPercentage(percentage: number, locale: localeConfig.LocaleType): JSX.Element {
    const formattedPercentage = formatPercentage(percentage, locale, 2, 2);

    if (percentage >= 0) {
      return (
        <span className="d-flex text-success">
          <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "14px" }}>
            trending_up
          </span>
          {formattedPercentage}
        </span>
      );
    } else {
      return (
        <span className="d-flex text-danger">
          <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "14px" }}>
            trending_down
          </span>
          {formattedPercentage}
        </span>
      );
    }
  }

  private _createPortfolioSetupAssetRow = (assetKey: investmentUniverseConfig.AssetType): JSX.Element => {
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];

    if (!this._investmentProductDict[assetKey] || !this._investmentProductDict[assetKey].currentTicker) {
      return null;
    }

    const tickerPrice = this._investmentProductDict[assetKey].tradedPrice;
    const tradedCurrency = this._investmentProductDict[assetKey].tradedCurrency;
    const { locale } = this.context as GlobalContextType;

    return (
      <PortfolioSetupAssetRow
        key={assetKey}
        category={category}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(assetKey)}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
        className="mb-3"
      >
        <div className="d-flex flex-column align-items-end">
          <span className="fw-bold d-block pb-1">{formatCurrency(tickerPrice, tradedCurrency, locale)}</span>
          <span className="d-block">
            {AssetDiscoveryList._formatPercentage(
              this._investmentProductDict[assetKey].currentTicker.dailyReturnPercentage,
              locale
            )}
          </span>
        </div>
      </PortfolioSetupAssetRow>
    );
  };

  private _getAssetClassNav(): JSX.Element {
    const { viewMode } = this.props;
    const { activeAssetClass } = this.state;

    if (viewMode !== "all") return <></>;

    return (
      <div className="d-flex align-self-center">
        <AssetClassNavPills
          className={"mt-3 mb-3"}
          activeAssetClass={activeAssetClass}
          assetClasses={AssetClassArray}
          onAssetClassSelection={(assetClass) => this._setActiveAssetClass(assetClass)}
        />
      </div>
    );
  }

  private _getSectorNav(): JSX.Element {
    const { viewMode } = this.props;
    const { activeSector, activeAssetClass } = this.state;

    if (viewMode !== "all" || activeAssetClass !== "equities") return <></>;

    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);

    return (
      <HorizontalScroller id={"discovery-scroller"} className="mb-md-5 mb-2">
        {(["all", ...InvestmentSectorArray] as (investmentUniverseConfig.InvestmentSectorType | "all")[]).map(
          (sector, index) => (
            <div
              key={`tenor-nav-${index}`}
              className={
                "cursor-pointer col py-2 px-3 align-self-center text-muted fw-bold text-center text-nowrap " +
                (activeSector === sector ? "active-sector" : "")
              }
              onClick={() => this._setActiveSector(sector)}
            >
              {sector != "all" ? SECTOR_CONFIG[sector].fieldName : "All"}
            </div>
          )
        )}
      </HorizontalScroller>
    );
  }

  private _getEquitiesAssetList(sector?: SectorType): JSX.Element {
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);

    if (sector === "all") {
      return (
        <>
          {Object.entries(SECTOR_CONFIG).map(([key, sector]: [string, SectorConfigType]) => (
            <div key={`sector-${key}`} className="row m-0">
              <div className="fw-bolder p-0 my-4">{sector.fieldName}</div>
              {this._getActiveAssets("equities", { sector: key as investmentUniverseConfig.InvestmentSectorType })
                .sort(compareAssets)
                .map(this._createPortfolioSetupAssetRow)}
            </div>
          ))}
        </>
      );
    }

    return (
      <>
        {this._getActiveAssets("equities", { sector: sector as investmentUniverseConfig.InvestmentSectorType })
          .sort(compareAssets)
          .map(this._createPortfolioSetupAssetRow)}
      </>
    );
  }

  private _getBondsAssetList(): JSX.Element {
    return (
      <>
        {Object.values(BOND_CATEGORY_CONFIG)
          .sort(compareBondCategories)
          .map(({ keyName, fieldName }: investmentUniverseConfig.BondCategoryConfigType) => {
            return (
              <div key={`bond-${keyName}`} className="row m-0">
                <div className="fw-bolder p-0 my-4">{fieldName}</div>
                {this._getActiveAssets("bonds", { bondCategory: keyName })
                  .sort(compareAssets)
                  .map(this._createPortfolioSetupAssetRow)}
              </div>
            );
          })}
      </>
    );
  }

  private _getCompleteAssetList(
    assetClass: investmentUniverseConfig.AssetClassType,
    sector: SectorType
  ): JSX.Element {
    if (assetClass === "equities") {
      return this._getEquitiesAssetList(sector);
    } else if (assetClass === "bonds") {
      return this._getBondsAssetList();
    } else {
      return <>{this._getActiveAssets(assetClass).sort(compareAssets).map(this._createPortfolioSetupAssetRow)}</>;
    }
  }

  private _getAssetCollectionList(collection: investmentUniverseConfig.InvestmentCollectionType): JSX.Element {
    const { assetCollections } = this.props;

    return <>{assetCollections[collection].assets.map(this._createPortfolioSetupAssetRow)}</>;
  }

  private _getSectorAssetList(sector: investmentUniverseConfig.InvestmentSectorType): JSX.Element {
    const { universe } = this.props;

    const sectorAssets = Object.entries(universe).filter(([, assetConfig]) => assetConfig?.sector === sector);
    const etfs = sectorAssets.filter(([, assetConfig]) => assetConfig.category === "etf").map((entry) => entry[0]);
    const stocks = sectorAssets
      .filter(([, assetConfig]) => assetConfig.category === "stock")
      .map((entry) => entry[0]);

    return (
      <>
        <div className="row m-0">
          <div className="fw-bolder p-0 my-4">ETFs</div>
          {etfs.sort(compareAssets).map(this._createPortfolioSetupAssetRow)}
        </div>
        <div className="row m-0">
          <div className="fw-bolder p-0 my-4">Stocks</div>
          {stocks.sort(compareAssets).map(this._createPortfolioSetupAssetRow)}
        </div>
      </>
    );
  }

  private _getEtfsList(): JSX.Element {
    const etfs = investmentUniverseConfig.AssetArrayConst.filter(
      (assetCommonId) => ASSET_CONFIG[assetCommonId].category === "etf"
    );

    return <>{etfs.sort(compareAssets).map(this._createPortfolioSetupAssetRow)}</>;
  }

  private _getAssetList(): JSX.Element {
    const { viewMode } = this.props;
    const { activeAssetClass, activeSector } = this.state;

    if (viewMode === "all") {
      return this._getCompleteAssetList(activeAssetClass, activeSector);
    }

    if (this._isEtfViewMode(viewMode)) {
      return this._getEtfsList();
    }

    if (this._isAssetCollectionViewMode(viewMode)) {
      return this._getAssetCollectionList(viewMode as investmentUniverseConfig.InvestmentCollectionType);
    }

    if (this._isAssetClassViewMode(viewMode)) {
      return this._getCompleteAssetList(viewMode as investmentUniverseConfig.AssetClassType, "all");
    }

    if (this._isSectorViewMode(viewMode)) {
      return this._getSectorAssetList(viewMode as investmentUniverseConfig.InvestmentSectorType);
    }
  }

  render() {
    const { isUserSearching } = this.state;
    return (
      <>
        {/* Header */}
        {this._getHeader()}
        {/* End Header */}

        <div className="mt-4">
          <AssetDiscoverySearchBar
            universe={this.props.universe}
            onUserSearch={this._onUserSearch}
            investmentProducts={this.props.investmentProducts}
          ></AssetDiscoverySearchBar>
        </div>

        {!isUserSearching && (
          <>
            {this._getAssetClassNav()}
            {this._getSectorNav()}
            {this._getAssetList()}
          </>
        )}
      </>
    );
  }
}

AssetDiscoveryList.contextType = GlobalContext;
