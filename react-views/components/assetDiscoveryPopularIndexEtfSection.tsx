import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { getAssetIconUrl } from "../utils/universeUtil";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  popularIndexEtfs: investmentUniverseConfig.AssetType[];
  showInfo: () => void;
};

export default class AssetDiscoveryPopularIndexEtfSection extends Component<PropsType> {
  private _createPortfolioSetupAssetRow = (assetKey: investmentUniverseConfig.AssetType): JSX.Element => {
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];

    return (
      <PortfolioSetupAssetRow
        key={assetKey}
        category={category}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(assetKey)}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
      />
    );
  };

  render() {
    const { popularIndexEtfs, showInfo } = this.props;

    return (
      <div className="container-fluid p-0 mb-5">
        <div className="d-flex justify-content-between mb-3">
          <div className="d-flex align-items-center">
            <h5 className="m-0">Popular Index ETFs</h5>
            <i
              className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
              onClick={() => {
                showInfo();
              }}
              style={{ fontSize: "20px" }}
            >
              info
            </i>
          </div>
        </div>
        <div>
          {popularIndexEtfs.map((asset, index) => (
            <div key={index} className={index !== popularIndexEtfs.length ? "mb-3" : ""}>
              {this._createPortfolioSetupAssetRow(asset)}
            </div>
          ))}
        </div>
      </div>
    );
  }
}
