import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { eventEmitter, EVENTS } from "../utils/eventService";
import HorizontalScroller from "./horizontalScroller";
import { getAssetIconUrl } from "../utils/universeUtil";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  popularAssets: investmentUniverseConfig.AssetType[];
  showInfo: () => void;
};

export default class AssetDiscoveryPopularSection extends Component<PropsType> {
  private _createPortfolioSetupAssetRow = (assetKey: investmentUniverseConfig.AssetType): JSX.Element => {
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];

    return (
      <PortfolioSetupAssetRow
        key={assetKey}
        category={category}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(assetKey)}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
      />
    );
  };

  private _splitPopularAssetsIntoColumns(
    popularAssets: investmentUniverseConfig.AssetType[]
  ): [investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetType][] {
    const columns: [investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetType][] = [];

    for (let i = 0; i < popularAssets.length; i += 2) {
      columns.push([popularAssets[i], popularAssets[i + 1]]);
    }

    return columns;
  }

  private _getPopularAssetColumn(
    [firstAsset, secondAsset]: [investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetType],
    key: number
  ): JSX.Element {
    return (
      <div key={key} className="pe-3" style={{ minWidth: "100px" }}>
        <div className="mb-4">{this._createPortfolioSetupAssetRow(firstAsset)}</div>
        {/* We need this check because in the case of odd number of popular assets second asset is undefined the this code breaks */}
        {secondAsset && this._createPortfolioSetupAssetRow(secondAsset)}
      </div>
    );
  }

  render() {
    const { popularAssets, showInfo } = this.props;

    return (
      <div className="container-fluid p-0 m-0">
        <div className="d-flex align-items-center mb-3">
          <h5 className="m-0">Popular this week</h5>
          <i
            className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
            onClick={() => {
              showInfo();
            }}
            style={{ fontSize: "20px" }}
          >
            info
          </i>
        </div>
        <div className="row justify-content-center m-0">
          <HorizontalScroller
            id={"popular-assets-scroller"}
            className={"mb-5 px-lg-2 px-md-5 px-0"}
            scrollingDistance={450}
            showScrollDots={false}
          >
            {this._splitPopularAssetsIntoColumns(popularAssets).map((column, index) =>
              this._getPopularAssetColumn(column, index)
            )}
          </HorizontalScroller>
        </div>
      </div>
    );
  }
}
