import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { getAssetIconUrl } from "../utils/universeUtil";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";
import { read } from "fs";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  readyMadeEtfs: investmentUniverseConfig.AssetType[];
  showInfo: () => void;
};

export default class AssetDiscoveryReadyMadeSection extends Component<PropsType> {
  private _createPortfolioSetupAssetRow = (assetKey: investmentUniverseConfig.AssetType): JSX.Element => {
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];

    return (
      <PortfolioSetupAssetRow
        key={assetKey}
        category={category}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(assetKey)}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
      />
    );
  };

  render() {
    const { readyMadeEtfs, showInfo } = this.props;

    if (!readyMadeEtfs || readyMadeEtfs.length === 0) {
      return null;
    }

    return (
      <div className="container-fluid p-0 mb-5">
        <div className="d-flex justify-content-between mb-3">
          <div className="d-flex align-items-center">
            <h5 className="m-0">Ready-made portfolios</h5>
            <i
              className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
              onClick={() => {
                showInfo();
              }}
              style={{ fontSize: "20px" }}
            >
              info
            </i>
          </div>
        </div>
        <div>
          {readyMadeEtfs.map((asset, index) => (
            <div key={index} className={index !== readyMadeEtfs.length ? "mb-3" : ""}>
              {this._createPortfolioSetupAssetRow(asset)}
            </div>
          ))}
        </div>
      </div>
    );
  }
}
