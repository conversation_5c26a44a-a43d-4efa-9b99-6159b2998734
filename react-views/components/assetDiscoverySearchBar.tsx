import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentUniverseAssets } from "../../utils/configUtil";
import PortfolioSearchAssetDiscoveryRow from "./portfolioSearchAssetDiscoveryRow";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { getAssetsFilteredOnSearchTerm } from "../utils/universeUtil";

type PropsType = {
  universe: InvestmentUniverseAssets;
  onUserSearch: (active: boolean) => void;
  investmentProducts?: InvestmentProductDocument[];
};

type StateType = {
  searchTerm: string;
  filteredAssets: investmentUniverseConfig.AssetType[];
};

class AssetDiscoverySearchBar extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      searchTerm: "",
      filteredAssets: []
    };
  }

  private _handleSearchTermChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = event.target.value;

    this.setState({ searchTerm }, () => {
      const filteredAssets = getAssetsFilteredOnSearchTerm(searchTerm, this.props.universe);
      this.setState({ filteredAssets }, () => {
        if (searchTerm === "") {
          this.props.onUserSearch(false);
        } else {
          this.props.onUserSearch(true);
        }
      });
    });
  };

  render(): React.ReactNode {
    const { searchTerm, filteredAssets } = this.state;
    return (
      <>
        <div className="d-flex p-3 mb-4 assets-search-container">
          <span className="material-symbols-outlined me-2 icon-primary">search</span>
          <input
            className="fw-bold assets-search-input border-0"
            type="text"
            placeholder="Search by name or type, eg. ‘tech’, ‘ETF’"
            onChange={this._handleSearchTermChange}
          ></input>
        </div>
        {searchTerm &&
          (filteredAssets.length > 0 ? (
            <PortfolioSearchAssetDiscoveryRow
              filteredAssets={filteredAssets}
              searchTerm={searchTerm}
              investmentProducts={this.props?.investmentProducts}
            />
          ) : (
            <div className="text-center py-5">No results found</div>
          ))}
      </>
    );
  }
}

export default AssetDiscoverySearchBar;
