import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { formatPercentage } from "../utils/formatterUtil";
import { eventEmitter, EVENTS } from "../utils/eventService";
import HorizontalScroller from "../components/horizontalScroller";
import { getAssetIconUrl } from "../utils/universeUtil";
import { TopMoversType, TopMoverCellType } from "../types/assetDiscovery";
import AssetIcon from "../components/assetIcon";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

const { ASSET_CONFIG } = investmentUniverseConfig;

type TopMoverPair = {
  best: TopMoverCellType;
  worst: TopMoverCellType;
};

type PropsType = {
  topMovers: TopMoversType;
  showInfo: () => void;
};

export default class AssetDiscoveryTopMoversSection extends Component<PropsType> {
  private _splitTopMoversIntoColumns(topMovers: TopMoversType): TopMoverPair[] {
    const tranformedTopMovers: TopMoverPair[] = [];

    // best and worst top mover arrays will always be of the same length
    for (let i = 0; i < topMovers.best.length; i++) {
      tranformedTopMovers.push({
        best: topMovers.best[i],
        worst: topMovers.worst[i]
      });
    }

    return tranformedTopMovers;
  }

  private _getTopMoverCell(topMoverCell: TopMoverCellType, className?: string): JSX.Element {
    const { locale } = this.context as GlobalContextType;
    const { asset, returnPercentage } = topMoverCell;
    const { category, tickerWithCurrency } = ASSET_CONFIG[asset];

    const iconUrl = getAssetIconUrl(asset);
    const returnPercentageClassname = returnPercentage > 0 ? "text-success" : "text-danger";

    return (
      <div
        className={`d-flex align-items-center flex-column cursor-pointer ${className}`}
        onClick={() => eventEmitter.emit(EVENTS.investmentProductModal, asset)}
      >
        <AssetIcon size="lg" category={category} iconUrl={iconUrl} />
        <span className="mt-1">{tickerWithCurrency}</span>
        <div className={`d-flex ${returnPercentageClassname}`}>
          <i
            className={"material-symbols-outlined align-self-center cursor-pointer " + returnPercentageClassname}
            style={{ fontSize: "20px" }}
          >
            {returnPercentage > 0 ? "arrow_drop_up" : "arrow_drop_down"}
          </i>
          {formatPercentage(returnPercentage, locale, 2, 2)}
        </div>
      </div>
    );
  }

  private _getTopMoverPairColumn(topMoverPair: TopMoverPair, key: number): JSX.Element {
    return (
      <div key={key} className="pe-2" style={{ minWidth: "100px" }}>
        {this._getTopMoverCell(topMoverPair.best, "mb-4")}
        {this._getTopMoverCell(topMoverPair.worst)}
      </div>
    );
  }

  render() {
    const { topMovers, showInfo } = this.props;

    return (
      <div className="container-fluid p-0 m-0">
        <div className="d-flex align-items-center mb-3">
          <h5 className="m-0">Top movers</h5>
          <i
            className="material-symbols-outlined icon-primary align-self-center ms-2 cursor-pointer"
            onClick={() => {
              showInfo();
            }}
            style={{ fontSize: "20px" }}
          >
            info
          </i>
        </div>
        <div className="row justify-content-center m-0">
          <HorizontalScroller
            id={"top-movers-scroller"}
            className={"mb-5 px-lg-2 px-md-5 px-0"}
            scrollingDistance={450}
            showScrollDots={false}
          >
            {this._splitTopMoversIntoColumns(topMovers).map((topMoverPair, index) =>
              this._getTopMoverPairColumn(topMoverPair, index)
            )}
          </HorizontalScroller>
        </div>
      </div>
    );
  }
}

AssetDiscoveryTopMoversSection.contextType = GlobalContext;
