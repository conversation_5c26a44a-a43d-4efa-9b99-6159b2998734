import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AssetGeographySelectionOption from "./assetGeographySelectionOption";
import { GlobalContext } from "../contexts/globalContext";
import { GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  selectedGeography?: investmentUniverseConfig.InvestmentGeographyType;
  onSelectionChange: (geography: investmentUniverseConfig.InvestmentGeographyType) => void;
};
type StateType = {
  selectedGeography: investmentUniverseConfig.InvestmentGeographyType;
};

class AssetGeographySelection extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      selectedGeography: props.selectedGeography || "global"
    };
  }

  private _setActiveGeography = (geography: investmentUniverseConfig.InvestmentGeographyType): void =>
    this.setState({ selectedGeography: geography }, () => {
      const { onSelectionChange } = this.props;
      onSelectionChange(geography);
    });

  render(): JSX.Element {
    const { selectedGeography } = this.state;
    const GEOGRAPHY_CONFIG = ConfigUtil.getGeographies((this.context as GlobalContextType).user.companyEntity);

    return (
      <div className="container-fluid p-0">
        {Object.values(GEOGRAPHY_CONFIG).map(({ name, explanation, icon, keyName }) => (
          <div key={keyName} className="row m-0 py-md-3 py-2">
            <div className="col p-0">
              <AssetGeographySelectionOption
                name={name}
                explanation={explanation}
                icon={icon}
                isSelected={selectedGeography === keyName}
                keyName={keyName}
                onSelectionCb={this._setActiveGeography}
              />
            </div>
          </div>
        ))}
      </div>
    );
  }
}

AssetGeographySelection.contextType = GlobalContext;

export default AssetGeographySelection;
