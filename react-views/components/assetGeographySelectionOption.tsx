import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  explanation: string;
  icon: string;
  isSelected?: boolean;
  name: string;
  keyName: investmentUniverseConfig.InvestmentGeographyType;
  onSelectionCb: (geography: string) => void;
};

class AssetGeographySelectionOption extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, explanation, icon, keyName, name, onSelectionCb } = this.props;
    const GEOGRAPHY_CONFIG = ConfigUtil.getGeographies((this.context as GlobalContextType).user.companyEntity);
    const style = isSelected
      ? { background: (GEOGRAPHY_CONFIG[keyName] as any).colorClass, color: "#ffffff" }
      : {};

    return (
      <div
        className="card card-body wh-simple-card cursor-pointer border-0"
        style={style}
        onClick={(): void => onSelectionCb(keyName)}
      >
        <div className="row w-100 p-0 justify-content-start">
          <div className="col-4 text-start align-self-center text-center p-0">
            <img
              alt="icon"
              className="h-100 align-self-center"
              src={icon}
              style={{ width: "100px", height: "100px" }}
            />
          </div>
          <div className="col-8 text-start p-0 align-self-center">
            <h5 className="fw-bold">{name}</h5>
            <p className="m-0">{explanation}</p>
          </div>
        </div>
      </div>
    );
  }
}

AssetGeographySelectionOption.contextType = GlobalContext;

export default AssetGeographySelectionOption;
