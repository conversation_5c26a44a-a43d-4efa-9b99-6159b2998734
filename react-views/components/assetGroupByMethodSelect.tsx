import React from "react";
import AssetGroupByMethodSelectItem from "./assetGroupByMethodSelectItem";
import { GroupByMethodEnum } from "./groupBy";
import { GROUP_BY_METHOD_CONFIG } from "./groupBy";

type PropsType = {
  selectedGroupByMethod: GroupByMethodEnum;
  onSelectedGroupByMethodChange: (selectedGroupByMethod: string) => void;
  groupByMethods: GroupByMethodEnum[];
};

class AssetGroupByMethodSelect extends React.Component<PropsType> {
  private _setGroupByMethod(groupByMethod: string) {
    const { onSelectedGroupByMethodChange } = this.props;
    this.setState({ groupByMethod }, () => {
      onSelectedGroupByMethodChange(groupByMethod);
    });
  }

  render(): JSX.Element {
    const { groupByMethods, selectedGroupByMethod } = this.props;
    return (
      <div className="p-4">
        {groupByMethods.map((item) => (
          <AssetGroupByMethodSelectItem
            key={GROUP_BY_METHOD_CONFIG[item].name}
            isSelected={selectedGroupByMethod == GROUP_BY_METHOD_CONFIG[item].name}
            name={GROUP_BY_METHOD_CONFIG[item].name}
            description={GROUP_BY_METHOD_CONFIG[item].description}
            onChange={() => this._setGroupByMethod(GROUP_BY_METHOD_CONFIG[item].name)}
          />
        ))}
      </div>
    );
  }
}

export default AssetGroupByMethodSelect;
