import React from "react";

type PropsType = {
  isSelected: boolean;
  name: string;
  description: string;
  onChange?: () => void;
};

class AssetGroupByMethodSelectItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, name, description, onChange } = this.props;

    return (
      <div
        className={
          "row m-0 wh-account-card-option mb-3 d-flex justify-content-around" +
          (isSelected ? " wh-account-card-option-selected" : "")
        }
        onClick={onChange}
      >
        <div className="col-9 align-self-center">
          {/* Group by Method Details*/}
          <div className="d-flex flex-column">
            <span className=" fw-bold">{name}</span>
            <div className="text-muted font-weight-bold">
              <span>{description}</span>
            </div>
          </div>
          {/* End Group by Method Details */}
        </div>
        <div className="col-1 p-0 pe-2 text-center">
          <input className="form-check-input" type="radio" checked={isSelected} />
        </div>
      </div>
    );
  }
}

export default AssetGroupByMethodSelectItem;
