import React, { Component } from "react";

type PropsType = {
  category: "etf" | "stock";
  iconUrl: string;
  className?: string;
  size: "lg" | "md";
};

export default class AssetIcon extends Component<PropsType> {
  render(): JSX.Element {
    const { iconUrl, className, size } = this.props;

    return (
      <div
        className={`card card-body asset-card-${size} border-0 text-center justify-content-center m-0 ${
          className ?? ""
        }`}
      >
        <div className="row my-4 justify-content-center">
          <div className="col p-0 align-self-center">
            <img
              src={iconUrl}
              className="w-100 align-self-center border-light asset-icon"
              alt="provider logo"
              loading="lazy"
            />
          </div>
        </div>
      </div>
    );
  }
}
