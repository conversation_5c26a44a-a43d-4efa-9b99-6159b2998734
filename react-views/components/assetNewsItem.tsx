import React from "react";
import { AssetNewsDocument } from "../../models/AssetNews";
import AssetNewsSentimentIcon from "./assetNewsSentimentIcon";

type AssetNewsItemPropType = {
  newsItem: AssetNewsDocument;
};

class AssetNewsItem extends React.Component<AssetNewsItemPropType> {
  render(): JSX.Element {
    const { newsItem } = this.props;
    return (
      <>
        <a
          className="d-flex cursor-pointer gap-3 no-decoration"
          style={{ marginBottom: "20px", color: "#11152E" }}
          href={newsItem.newsUrl}
          target="_blank noreferrer"
        >
          <div className="asset-news-image-container pe-3">
            <img className="asset-news-image" src={newsItem.imageUrl} />
          </div>
          <div
            className="d-flex flex-column justify-content-center gap-3"
            id="text-column that takes 8 and i also a flexbox"
          >
            <h5 className="fw-bolder asset-news-title m-0">{newsItem.title}</h5>
            <div className="d-flex align-items-center" style={{ color: "#757575", fontSize: "14px" }}>
              <AssetNewsSentimentIcon sentiment={newsItem.sentiment} />
              <span className="mx-2">•</span>
              <span
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "130px"
                }}
              >
                {newsItem.source}
              </span>
              <span className="mx-2">•</span>
              <span>{newsItem.displayDate}</span>
            </div>
          </div>
        </a>
      </>
    );
  }
}

export default AssetNewsItem;
