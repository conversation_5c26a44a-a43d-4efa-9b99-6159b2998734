import React from "react";
import { AssetNewsSentimentEnum } from "../../models/AssetNews";

type SentimentIconProps = {
  sentiment: AssetNewsSentimentEnum;
};

const SentimentIcon: React.FC<SentimentIconProps> = ({ sentiment }) => {
  const iconDetails = {
    [AssetNewsSentimentEnum.Positive]: {
      icon: "thumb_up_alt",
      color: "#23846A",
      background: "#DCFAF1"
    },
    [AssetNewsSentimentEnum.Negative]: {
      icon: "thumb_down_alt",
      color: "#D63C3C",
      background: "#FFEFF4"
    },
    [AssetNewsSentimentEnum.Neutral]: {
      icon: "thumbs_up_down",
      color: "#D59A04",
      background: "#FFEDBF"
    }
  };

  const sentimentConfig = iconDetails[sentiment];

  if (!sentimentConfig) return null;

  return (
    <div className="d-flex gap-2 align-items-center">
      <span
        className="material-icons align-self-center asset-news-icon"
        style={{ color: sentimentConfig.color, background: sentimentConfig.background }}
      >
        {sentimentConfig.icon}
      </span>
    </div>
  );
};

export default SentimentIcon;
