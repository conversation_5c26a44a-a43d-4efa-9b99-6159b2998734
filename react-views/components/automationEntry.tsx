import React, { Component } from "react";
import ToggleSwitch from "./toggleSwitch";

type PropsType = {
  title: string;
  subtitleContainer: JSX.Element;
  active: boolean;
  icon: string;
  className?: string;
  onContainerClick: () => void;
  onInfoClick: (event: any) => void;
  onToggleSwitch: (event: React.MouseEvent) => Promise<void>;
};

export default class AutomationEntry extends Component<PropsType> {
  render() {
    const { title, subtitleContainer, active, icon, onInfoClick, onContainerClick, onToggleSwitch, className } =
      this.props;

    return (
      <div
        className={`row m-0 wh-automation-option mb-3 ${active ? "cursor-pointer" : ""} ${className}`}
        onClick={onContainerClick}
      >
        <div className="col-2 p-0 align-self-center">
          <img
            className="h-100 align-self-center"
            src={icon}
            alt={"Top-Up"}
            style={{ height: "48px !important", width: "48px !important" }}
          />
        </div>
        <div className="col-8 align-self-center">
          <div className="d-flex flex-column">
            <div className="d-flex align-items-center">
              <span className=" fw-bold">{title}</span>
              <span
                className={"ms-1 cursor-pointer material-symbols-outlined"}
                style={{ fontSize: "16px", color: "#536AE3" }}
                onClick={onInfoClick}
              >
                info
              </span>
            </div>
            {subtitleContainer}
          </div>
        </div>
        <div className="col-2 p-0 text-center">
          <ToggleSwitch checked={!!active} customonclick={onToggleSwitch} />
        </div>
      </div>
    );
  }
}
