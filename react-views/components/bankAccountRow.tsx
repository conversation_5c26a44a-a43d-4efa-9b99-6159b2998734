import React from "react";
import { LinkedBankAccount } from "../types/bank";

type PropsType = {
  bankAccount: LinkedBankAccount;
  showMoreButton: boolean;
  handleClick?: () => void;
  handleMoreButtonClick?: () => void;
  displayNameClass?: string;
  includeEasyTags?: boolean;
};

class BankAccountRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { bankAccount, handleClick, handleMoreButtonClick, showMoreButton, includeEasyTags, displayNameClass } =
      this.props;

    return (
      <div className="d-flex justify-content-between" onClick={() => handleClick && handleClick()}>
        <div className="d-flex align-items-center">
          <img
            className="h-100 align-self-center me-3 rounded"
            src={bankAccount.bankIconURL}
            style={{ maxHeight: "44px", maxWidth: "44px" }}
          />
          <div className="d-flex flex-column">
            <div className="d-flex flex-row">
              <span className={`${displayNameClass} t-875`}>{bankAccount.displayBankName}</span>
              {includeEasyTags && bankAccount.supportsEasyTransfer && (
                <div className="wh-primary-label align-self-center t-75 ms-2" style={{ padding: "2px 6px" }}>
                  Easy
                </div>
              )}
            </div>
            <span className="text-muted t-75">{`${bankAccount.displayAccountIdentifier}`}</span>
          </div>
        </div>

        {showMoreButton && (
          <span
            className="material-icons cursor-pointer align-self-center align-self-center mb-2"
            onClick={() => handleMoreButtonClick()}
            style={{
              fontSize: "30px",
              color: "#171717"
            }}
          >
            more_vert
          </span>
        )}
      </div>
    );
  }
}

export default BankAccountRow;
