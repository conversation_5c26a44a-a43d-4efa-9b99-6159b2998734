import React from "react";
import SelectBank from "./selectBank";
import { ProviderType } from "../../services/truelayerService";
import ValidationInput from "./validationInput";

type BankDetails = {
  accountHolderName: string;
  bank: ProviderType;
  accountNumber: string;
  sortCode: string;
};

type PropsType = {
  onBankDetailsChange: (inputName: "accountHolderName" | "sortCode" | "accountNumber", value: string) => void;
  onBankProviderChange: (provider: ProviderType) => void;
  bankDetails: BankDetails;
};

const ACCOUNT_NUMBER_LENGTH = 8;
const SORT_CODE_LENGTH = 6;

class BankDetailsForm extends React.Component<PropsType> {
  private _validateAndUpdate =
    (inputName: "accountHolderName" | "sortCode" | "accountNumber") =>
    (event: any): void => {
      const { onBankDetailsChange } = this.props;

      // the event may be direct value
      let value = event.target?.value ?? event;
      if (inputName === "accountHolderName") {
        // Don't allow special characters in account holder name
        const specialCharsRegex = new RegExp(/[`~!@#$%^&*()_|+\-=?;:'",.<>{}[\]\\/]/, "gi");
        value = value.replace(specialCharsRegex, "");
      } else if (inputName === "sortCode") {
        if (value.length > SORT_CODE_LENGTH) return;
        const specialCharsRegex = new RegExp(/[^0-9]/, "gi");
        value = value.replace(specialCharsRegex, "");
      } else if (inputName === "accountNumber") {
        if (value.length > ACCOUNT_NUMBER_LENGTH) return;
        const specialCharsRegex = new RegExp(/[^0-9]/, "gi");
        value = value.replace(specialCharsRegex, "");
      }

      onBankDetailsChange(inputName, value);
    };

  render(): JSX.Element {
    const { onBankProviderChange, bankDetails } = this.props;

    return (
      <form>
        <div className="row mb-2">
          <div className="col">
            {/* Bank Select */}
            <div className="form-group">
              <SelectBank defaultValue="" name="bank" onSelectionChange={onBankProviderChange} />
            </div>
            {/* End Bank Select */}
          </div>
        </div>
        <div className="row mb-2">
          {/* Account Holder Name Input */}
          <div className="col">
            <div className="form-group">
              <label className="form-label">Your full name</label>
              <input
                type="text"
                className="verification-input"
                name="accountHolderName"
                placeholder="Your first name and last name"
                value={bankDetails.accountHolderName}
                onChange={this._validateAndUpdate("accountHolderName")}
                required
              />
            </div>
          </div>
          {/* End Account Holder Name Input */}
        </div>
        <div className="row mb-2">
          <div className="col-6">
            {/* Sort Code Input */}
            <div className="form-group">
              <label className="form-label">Sort code</label>
              <ValidationInput
                // FIXME: implement real time validation from fetchify
                isValid={() => true}
                onChange={this._validateAndUpdate("sortCode")}
                value={bankDetails.sortCode}
                placeholder="123456"
                errorMessage="This bank is not supported"
                required={true}
              />
            </div>
            {/* End Sort Code Input */}
          </div>
          <div className="col-6">
            {/* Account Number Input */}
            <div className="form-group">
              <label className="form-label">Account number</label>
              <ValidationInput
                // FIXME: implement real time validation from fetchify
                isValid={() => true}
                onChange={this._validateAndUpdate("accountNumber")}
                value={bankDetails.accountNumber}
                placeholder="********"
                errorMessage="These details do not correspond to a bank"
                required={true}
              />
            </div>
            {/* End Account Number Input */}
          </div>
        </div>
      </form>
    );
  }
}

export default BankDetailsForm;
