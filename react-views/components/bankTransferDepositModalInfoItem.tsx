import React from "react";
import { GlobalContext } from "../contexts/globalContext";

type PropsType = {
  iconName: string;
  content: string;
};

class BankTransferDepositModalInfoItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { iconName, content } = this.props;

    return (
      <div className={"d-flex align-items-center mb-3"}>
        <span
          className="material-symbols-outlined border-light me-3 p-2"
          style={{
            fontSize: "15px",
            color: "#1C1B1F"
          }}
        >
          {iconName}
        </span>
        <p className={"t-75 mb-0"}>{content}</p>
      </div>
    );
  }
}

BankTransferDepositModalInfoItem.contextType = GlobalContext;

export default BankTransferDepositModalInfoItem;
