import React from "react";
import { GlobalContext } from "../contexts/globalContext";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

type PropsType = {
  label: string;
  value: string;
  includeBottomSeparator?: boolean;
};

class BankTransferInfoRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { label, value, includeBottomSeparator } = this.props;

    return (
      <>
        <div className="row d-flex justify-content-center mb-2">
          <div className={"col-10"}>
            <div className="d-flex justify-content-between align-items-start flex-column">
              <p className={"text-muted mb-0"}>{label}</p>
              <span style={{ fontWeight: 500 }}>{value}</span>
            </div>
          </div>
          <div className={"col-2 d-flex"}>
            <span
              onClick={async () => {
                await navigator.clipboard.writeText(value);
                emitToast({
                  content: "Copied to clipboard!",
                  toastType: ToastTypeEnum.success
                });
              }}
              className="material-icons icon-primary cursor-pointer align-self-center text-primary-blue ms-2"
              style={{
                fontSize: "18px"
              }}
            >
              content_copy
            </span>
          </div>
        </div>
        {includeBottomSeparator ?? true ? <hr style={{ color: "#F1F3FD", opacity: 1 }} /> : <></>}
      </>
    );
  }
}

BankTransferInfoRow.contextType = GlobalContext;

export default BankTransferInfoRow;
