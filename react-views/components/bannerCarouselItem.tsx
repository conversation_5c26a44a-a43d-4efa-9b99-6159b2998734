import React from "react";
import { BANNER_CARD_CONFIG, BannerEnum, bannerTagConfig, BannerTagEnum } from "../configs/bannerConfig";

export type BannersCarouselItemPropsType = {
  id: BannerEnum;
  title: string;
  canBeDismissed: boolean;
  hasRightMargin: boolean;
  imageURL?: string;
  onBannerClickHandler: (id: string) => void;
  onHideBannerClickHandler: (id: string) => void;
};

export default class BannerCarouselItem extends React.Component<BannersCarouselItemPropsType> {
  private _getBannerTag(tag: BannerTagEnum): JSX.Element {
    const config = bannerTagConfig[tag];

    return (
      <div style={{ color: config.color }} className="banner-tag">
        <span
          style={{ backgroundColor: config.backgroundColor }}
          className={`${config.iconClass} align-self-center banner-tag-icon`}
        >
          {config.icon}
        </span>
        <span className="fw-normal"> {tag} </span>
      </div>
    );
  }

  render() {
    const { id, title, hasRightMargin, canBeDismissed, imageURL, onBannerClickHandler, onHideBannerClickHandler } =
      this.props;

    const { imageSource, styles, tag } = BANNER_CARD_CONFIG[id];

    const gradientStyle = {
      background: `linear-gradient(270deg, ${styles.gradientStart}, ${styles.gradientMiddle}, ${styles.gradientEnd})`
    };

    return (
      <>
        <div
          className={`d-flex wh-carouselItem-card m-0 align-self-center align-items-center cursor-pointer position-relative ${
            hasRightMargin ? "me-3" : ""
          }`}
          onClick={() => onBannerClickHandler(id)}
          style={gradientStyle}
        >
          <img
            style={{ width: "80px", height: "80px", borderRadius: "8px", objectFit: "cover" }}
            src={imageURL || imageSource}
          />
          <div className="d-flex flex-column ms-md-6 ms-4 align-self-center justify-content-center w-100">
            {tag && <div> {this._getBannerTag(tag)}</div>}
            <h5
              className={`m-0 line-clamp-2 ${styles.color === "light" ? "text-light" : ""} ${
                tag ? "banner-title-with-tag" : ""
              }`}
            >
              {title}
            </h5>
          </div>
          {canBeDismissed && (
            <div className="d-flex flex-column align-items-center justify-content-center">
              <i
                className="material-symbols-outlined align-self-center "
                onClick={(event) => {
                  // stop propagation as whole card has also click handler
                  onHideBannerClickHandler(id);
                  event.stopPropagation();
                }}
              >
                close
              </i>
            </div>
          )}
        </div>
      </>
    );
  }
}
