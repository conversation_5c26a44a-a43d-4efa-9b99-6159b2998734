import React from "react";
import BannersCarousel from "./bannersCarousel";
import BannerModal from "./modals/bannerModal";
import Cookies from "universal-cookie";
import { getBannerCookieName } from "../utils/bannerUtil";
import { BANNER_CARD_CONFIG, BannerDataType, BannerEnum } from "../configs/bannerConfig";

type StateType = {
  bannersToShow: BannerDataType[];
  showBannerModal: boolean;
  selectedBannerId?: BannerEnum;
};

export type BannersPropsType = {
  banners: BannerDataType[];
  userHasSubscription: boolean;
};

export default class Banners extends React.Component<BannersPropsType, StateType> {
  private _cookiesManager: Cookies;
  constructor(props: BannersPropsType) {
    super(props);
    this._cookiesManager = new Cookies();
    this.state = {
      bannersToShow: [],
      showBannerModal: false
    };

    this._onBannerClickHandler = this._onBannerClickHandler.bind(this);
    this._onHideBannerClickHandler = this._onHideBannerClickHandler.bind(this);
    this._onHideModalClickHandler = this._onHideModalClickHandler.bind(this);
  }

  private _hideBanner(bannerId: BannerEnum): void {
    this._cookiesManager.set(getBannerCookieName(bannerId), new Date());

    this.setState((prevState) => ({
      bannersToShow: prevState.bannersToShow.filter((banner) => banner.bannerId !== bannerId)
    }));
  }

  private _onBannerClickHandler(id: BannerEnum): void {
    const { userHasSubscription, banners } = this.props;
    const selectedBannerData = banners.find((banner) => banner.bannerId === id);

    const carouselItemData = BANNER_CARD_CONFIG[id];
    if (carouselItemData.directAction) {
      this.setState(
        {
          selectedBannerId: id
        },
        () =>
          carouselItemData.directAction({
            hasSubscription: userHasSubscription,
            analystInsightId: selectedBannerData?.data?.analystInsightId,
            learningGuideSlug: selectedBannerData?.data?.slug
          })
      );
      return;
    } else if (carouselItemData.opensModal) {
      this.setState({
        selectedBannerId: id,
        showBannerModal: true
      });
      return;
    }
  }

  private _onHideBannerClickHandler(id: BannerEnum) {
    this._hideBanner(id);
  }

  private _onHideModalClickHandler() {
    this.setState({
      showBannerModal: false
    });
  }

  componentDidMount() {
    const banners = this.props.banners;

    const bannersToShow = banners.map((banner) => {
      if (banner.data?.title) {
        const title = banner.data.title;

        return {
          ...banner,
          data: {
            ...banner.data,
            title: title
          }
        };
      }
      return banner;
    });

    this.setState({ bannersToShow: bannersToShow });
  }

  render() {
    const { banners } = this.props;
    const { bannersToShow, selectedBannerId, showBannerModal } = this.state;

    const selectedBannerData = banners.find((banner) => banner.bannerId === selectedBannerId);
    const numberOfBannersToShow = bannersToShow.length;

    return (
      <>
        {numberOfBannersToShow > 0 && (
          <>
            <BannersCarousel
              banners={bannersToShow}
              onBannerClickHandler={this._onBannerClickHandler}
              onHideBannerClickHandler={this._onHideBannerClickHandler}
            />
            {selectedBannerData && showBannerModal && (
              <BannerModal
                show={showBannerModal}
                handleClose={this._onHideModalClickHandler}
                title={selectedBannerData.data.modalTitle}
                imageSource={BANNER_CARD_CONFIG[selectedBannerId].imageSource}
                paragraphs={selectedBannerData.data.modalContent}
                buttonText={selectedBannerData.data.modalButtonText}
                buttonAction={BANNER_CARD_CONFIG[selectedBannerId].modalButtonAction}
              />
            )}
          </>
        )}
      </>
    );
  }
}
