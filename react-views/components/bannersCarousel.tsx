import React from "react";
import HorizontalScroller from "./horizontalScroller";
import BannerCarouselItem from "./bannerCarouselItem";
import {
  BANNER_CARD_CONFIG,
  BannerDataType,
  BannerEnum,
  NotDismissableBannersArray
} from "../configs/bannerConfig";

type BannersCarouselPropsType = {
  banners: BannerDataType[];
  onBannerClickHandler: (id: BannerEnum) => void;
  onHideBannerClickHandler: (id: BannerEnum) => void;
};
export default class BannersCarousel extends React.Component<BannersCarouselPropsType> {
  private _getSweatcoinBannerCarouselItem(id: BannerEnum): JSX.Element {
    const { imageSource } = BANNER_CARD_CONFIG[id];

    return (
      <div key={id} onClick={() => this.props.onBannerClickHandler(id)} className={"cursor-pointer me-3"}>
        <img src={imageSource} style={{ maxWidth: "450px", minWidth: "450px" }} />
      </div>
    );
  }

  render() {
    const bannersJSX = this.props.banners.map(({ bannerId, data }) => {
      if (bannerId === BannerEnum.SweatcoinLifetimeGoldDeal) {
        return this._getSweatcoinBannerCarouselItem(bannerId);
      }

      return (
        <BannerCarouselItem
          key={bannerId}
          id={bannerId}
          title={data?.title}
          imageURL={data?.imageURL}
          canBeDismissed={!NotDismissableBannersArray.includes(bannerId)}
          hasRightMargin={this.props.banners.length > 1}
          onBannerClickHandler={this.props.onBannerClickHandler}
          onHideBannerClickHandler={this.props.onHideBannerClickHandler}
        />
      );
    });

    return (
      <>
        {bannersJSX.length > 0 && (
          <HorizontalScroller
            id={"notifications-scroller"}
            className={`${bannersJSX.length > 1 ? "mb-5 px-lg-2 px-md-5 px-0" : "mb-4 px-md-5 px-0"}`}
            scrollingDistance={450}
            showScrollDots={false}
          >
            {bannersJSX}
          </HorizontalScroller>
        )}
      </>
    );
  }
}
