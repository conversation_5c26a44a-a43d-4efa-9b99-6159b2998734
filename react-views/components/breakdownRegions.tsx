import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AllocationSlider from "../components/allocationSlider";
import AllocationSliderReadOnly from "../components/allocationSliderReadOnly";
import ButtonGroup from "../components/buttonGroup";

const DISPLAY_MODES = {
  simple: "simpleName",
  advanced: "advancedName"
} as const;
const BTN_CONFIG: { label: string; btnId: keyof typeof DISPLAY_MODES }[] = [
  { label: "Simple", btnId: "simple" },
  { label: "Advanced", btnId: "advanced" }
];

type PropsType = {
  breakdownConfig: { commonId: investmentUniverseConfig.AssetType; allocationPercentage: number }[];
  colorclass: string;
  label: string;
  readOnly: boolean;
  onSliderChange: (sliderCommonId: string, value: number) => void;
};
type StateType = {
  sliderDisplayMode: keyof typeof DISPLAY_MODES;
};

class BreakdownRegions extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      sliderDisplayMode: BTN_CONFIG[0].btnId
    };
  }

  private _getSliderName(assetKeyname: investmentUniverseConfig.AssetType): string {
    const { sliderDisplayMode } = this.state;
    const displayMode = DISPLAY_MODES[sliderDisplayMode];
    return investmentUniverseConfig.ASSET_CONFIG[assetKeyname][displayMode];
  }

  private _setSliderDislpayMode = (buttonIndex: number): void => {
    const sliderDisplayMode = BTN_CONFIG[buttonIndex].btnId;
    this.setState({ sliderDisplayMode });
  };

  render(): JSX.Element {
    const { breakdownConfig, colorclass, label, readOnly, onSliderChange } = this.props;
    return (
      <>
        <div className="d-block">
          <div className="d-flex align-items-center justify-content-between pt-3 mb-10">
            <h3 className="font-weight-bolder text-light">{label}</h3>
            <ButtonGroup
              activeButtonClasses="focus border-0 shadow-none"
              buttonClasses="btn btn-sm btn-primary p-1"
              buttonLabels={BTN_CONFIG.map(({ label }) => label)}
              onBtnClick={this._setSliderDislpayMode}
            />
          </div>
          {breakdownConfig.map(({ allocationPercentage, commonId }) => {
            return (
              <div className="region-row row align-items-center pt-2 pb-3" key={commonId}>
                <div className="col-12">
                  <div className="row align-items-end">
                    <div className="col-12">
                      {readOnly ? (
                        <AllocationSliderReadOnly
                          colorClass={colorclass}
                          isAssetClass={false}
                          labelName={this._getSliderName(commonId)}
                          sliderValue={allocationPercentage}
                        />
                      ) : (
                        <AllocationSlider
                          key={`${commonId}_${allocationPercentage}`}
                          labelName={this._getSliderName(commonId)}
                          colorClass={colorclass}
                          componentName={commonId}
                          isAssetClass={false}
                          isEnabled={true}
                          range={{ min: 0, max: 100 }}
                          start={allocationPercentage}
                          step={1}
                          onSliderChangeCb={onSliderChange}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </>
    );
  }
}

export default BreakdownRegions;
