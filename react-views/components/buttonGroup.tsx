import React from "react";

type PropsType = {
  activeButtonClasses: string;
  buttonClasses: string;
  buttonLabels: string[];
  onBtnClick: (buttonIndex: number) => void;
};
type StateType = {
  activeButtonIndex: number;
};

class ButtonGroup extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      activeButtonIndex: 0
    };
  }

  private _setActiveButton = (buttonIndex: number): void => {
    this.setState({ activeButtonIndex: buttonIndex });
  };

  render(): JSX.Element {
    const { activeButtonClasses, buttonClasses, buttonLabels, onBtnClick } = this.props;
    const { activeButtonIndex } = this.state;

    return (
      <div className="btn-group" role="group">
        {buttonLabels.map((label, index) => (
          <button
            className={`${buttonClasses} ${index === activeButtonIndex ? activeButtonClasses : ""}`}
            onClick={(): void => {
              this._setActiveButton(index);
              onBtnClick(index);
            }}
            type="button"
            key={`button-${index}-${label}`}
          >
            {label}
          </button>
        ))}
      </div>
    );
  }
}

export default ButtonGroup;
