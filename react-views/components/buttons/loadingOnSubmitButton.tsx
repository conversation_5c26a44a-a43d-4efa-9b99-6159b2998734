import React from "react";
import { captureException } from "@sentry/react";

type PropsType = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  enableOnCompletion?: boolean;
  customonclick: () => Promise<void>;
};
type StateType = {
  isTrackButtonLoading: boolean;
};

class LoadingOnSubmitButton extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isTrackButtonLoading: false
    };
  }

  private _onClick = (): void => {
    const { enableOnCompletion, customonclick } = this.props;

    (async (): Promise<void> => {
      this.setState({ isTrackButtonLoading: true }, async () => {
        try {
          await customonclick();
          if (enableOnCompletion) {
            this.setState({ isTrackButtonLoading: false });
          }
        } catch (err) {
          captureException(err);
          this.setState({ isTrackButtonLoading: false });
        }
      });
    })();
  };

  render(): JSX.Element {
    // Destructuring customonclick to ignore it from button props
    const { children, customonclick, ...htmlProps } = this.props;
    const { isTrackButtonLoading } = this.state;

    return (
      <>
        {isTrackButtonLoading ? (
          <button {...htmlProps} disabled>
            <span className="spinner-border spinner-border-sm" />
          </button>
        ) : (
          <button onClick={this._onClick} {...htmlProps}>
            {children}
          </button>
        )}
      </>
    );
  }
}

export default LoadingOnSubmitButton;
