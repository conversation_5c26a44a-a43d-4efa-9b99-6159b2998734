import React from "react";

type PropsType = {
  onClick: () => void;
  title?: string;
  subtitle?: string;
};

class TransactionModalBackButton extends React.Component<PropsType> {
  render(): JSX.Element {
    const { onClick, title, subtitle } = this.props;

    return (
      <div className="d-flex align-items-center">
        <span
          className="material-icons icon-primary cursor-pointer align-self-center"
          onClick={onClick}
          style={{
            fontSize: "18px"
          }}
        >
          arrow_back_ios_new
        </span>
        {(title || subtitle) && (
          <div className="ms-3">
            <h5 className={"m-0"}>{title}</h5>
            {subtitle && <p className="fw-light mb-0 mt-1">{subtitle}</p>}
          </div>
        )}
      </div>
    );
  }
}

export default TransactionModalBackButton;
