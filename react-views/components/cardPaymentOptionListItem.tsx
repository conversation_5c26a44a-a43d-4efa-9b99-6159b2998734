import React from "react";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import { UserDocument } from "../../models/User";

type PropsType = {
  user: UserDocument;
  isSelected: boolean;
  paymentMethod: PaymentMethodDocument;
  showRadio: boolean;
  onChange: () => void;
};

class CardPaymentOptionListItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, paymentMethod, onChange, user, showRadio } = this.props;

    return (
      <div
        className={"row m-0 wh-account-card-option mb-3" + (isSelected ? " wh-account-card-option-selected" : "")}
        onClick={onChange}
      >
        <div className="col-2 p-0 d-flex justify-content-center align-self-center">
          {/* Provider Icon */}
          <img
            className="h-100 align-self-center"
            style={{ maxHeight: "40px", borderRadius: "6px" }}
            src={`/images/card-brands/${paymentMethod.brand}.png`}
          />
          {/* End Provider Icon */}
        </div>
        <div className="col-9 ps-1 align-self-center">
          {/* Account Details */}
          <div className="d-flex flex-column">
            <span className=" fw-bold">{`${user.firstName} ${user.lastName}`}</span>
            <div className="text-muted font-weight-bold">
              <span className="me-2">••••</span>
              <span>{paymentMethod.lastFourDigits}</span>
            </div>
          </div>
          {/* End Account Details */}
        </div>
        {showRadio ? (
          <div className="col-1 p-0 pe-2 text-center">
            <input className="form-check-input" type="radio" checked={isSelected} />
          </div>
        ) : (
          <></>
        )}
      </div>
    );
  }
}

export default CardPaymentOptionListItem;
