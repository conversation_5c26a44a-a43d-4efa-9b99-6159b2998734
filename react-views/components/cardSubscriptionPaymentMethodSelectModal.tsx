import React from "react";
import { Modal } from "react-bootstrap";
import { UserDocument } from "../../models/User";
import { plansConfig } from "@wealthyhood/shared-configs";
import { loadStripe, StripeElementsOptions } from "@stripe/stripe-js";
import { Elements, ElementsConsumer } from "@stripe/react-stripe-js";
import StripeAddNewCardForm from "./stripeAddNewCardForm";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import axios from "axios";
import SelectedSubscriptionPaymentMethod from "./selectedSubscriptionPaymentMethod";
import CardPaymentOptionListItem from "./cardPaymentOptionListItem";
import { SubscriptionDocument } from "../../models/Subscription";
import ToggleSwitch from "./toggleSwitch";
import ConfigUtil from "../../utils/configUtil";

const stripePromise = loadStripe(process.env.STRIPE_KEY);

type ViewModeType = "INITIAL" | "PAYMENT_METHOD_SELECT" | "ADD_PAYMENT_METHOD";

type PropsType = {
  user: UserDocument;
  paymentMethods: PaymentMethodDocument[];
  subscription?: SubscriptionDocument;
  handleClose: () => void;
  show: boolean;
  newPrice?: plansConfig.PriceType;
  source: "select-plan" | "change-plan" | "billing";
};

type StateType = {
  yearlyPlanSelected: boolean;
  viewMode: ViewModeType;
  selectedPaymentMethod: PaymentMethodDocument;
};

class CardSubscriptionPaymentMethodSelectModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      yearlyPlanSelected: false,
      viewMode: props.paymentMethods.length === 0 ? "ADD_PAYMENT_METHOD" : "INITIAL",
      selectedPaymentMethod: props.paymentMethods?.[0]
    };
  }

  private _getDisclaimer(): JSX.Element {
    const { subscription } = this.props;

    if (subscription?.hasUsedFreeTrial) {
      return <></>;
    }

    return (
      <div className={"text-muted text-center text-secondary t-875 mt-4 mb-0"}>
        <p className={"mb-0"}>You’ll not be charged until your free trial ends.</p>
        <p className={"my-0"}>You can cancel anytime.</p>
      </div>
    );
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private _setYearlyPlanSelected(yearlyPlanSelected: boolean) {
    this.setState({ yearlyPlanSelected });
  }

  private _setPaymentMethod(selectedPaymentMethod: PaymentMethodDocument) {
    this.setState({ selectedPaymentMethod });
  }

  private async _onSubmit() {
    const { newPrice, subscription, source } = this.props;
    const { selectedPaymentMethod, yearlyPlanSelected } = this.state;

    const finalTargetPrice = yearlyPlanSelected ? this._getEquivalentYearlyPrice(newPrice) : newPrice;

    const res = await axios.post(`/investor/subscriptions/initiate-stripe`, {
      price: finalTargetPrice,
      paymentMethod: selectedPaymentMethod.providers.stripe.id
    });

    if (!res.data.subscription) {
      const stripe = await loadStripe(process.env.STRIPE_KEY);

      const { paymentIntent } = await stripe.confirmCardPayment(res.data.clientSecret, {
        payment_method: selectedPaymentMethod.providers.stripe.id,
        return_url: process.env.DOMAIN_URL
      });

      await axios.post(`/investor/subscriptions/complete-stripe`, { paymentIntentId: paymentIntent.id });
    }

    if (source === "change-plan") {
      window.location.href = `/investor/plan-update-success?from=${subscription?.price}&to=${newPrice}`;
    } else {
      window.location.href = "/";
    }
  }

  private _getEquivalentYearlyPrice(price: plansConfig.PriceType): plansConfig.PriceType {
    const { user } = this.props;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return Object.values(PRICE_CONFIG).find(
      (priceConfig) => priceConfig.plan === PRICE_CONFIG[price].plan && priceConfig.recurrence === "yearly"
    ).keyName;
  }

  render(): JSX.Element {
    const { show, paymentMethods, user, newPrice, handleClose, source } = this.props;
    const { viewMode, selectedPaymentMethod, yearlyPlanSelected } = this.state;

    const options = {
      mode: "setup",
      currency: user.currency.toLowerCase(),
      appearance: {
        theme: "stripe"
      }
    };

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5 max-w-600px">
        {viewMode == "INITIAL" && (
          <>
            <Modal.Header className="border-bottom-0" closeButton>
              <Modal.Title />
            </Modal.Header>
            <div className="fade-in">
              <Modal.Body className="p-0 px-3">
                <div className="d-flex align-self-center justify-content-center">
                  <div className="w-100" style={{ maxWidth: "400px" }}>
                    {/* Action Title */}
                    <h5 className="fw-bolder text-center mb-5 mt-3">Continue with</h5>
                    {/* End Action Title */}

                    <SelectedSubscriptionPaymentMethod
                      user={user}
                      selectedPaymentMethod={selectedPaymentMethod}
                      onClick={() => this._setViewMode("PAYMENT_METHOD_SELECT")}
                    />

                    {PRICE_CONFIG[newPrice].recurrence === "monthly" && (
                      <div className={"pt-4 row"}>
                        <div className="col-10 d-flex align-self-center">
                          <span>
                            Save {PRICE_CONFIG[this._getEquivalentYearlyPrice(newPrice)].savePercentage} with an
                            annual plan!
                          </span>
                        </div>
                        <div className="col-2 text-center">
                          <ToggleSwitch
                            checked={yearlyPlanSelected}
                            customonclick={async () => {
                              this._setYearlyPlanSelected(!yearlyPlanSelected);
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer className="pt-4 pb-5 justify-content-center fade-in" style={{ borderTop: "none" }}>
                <div
                  className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                  style={{ maxWidth: "400px" }}
                >
                  <LoadingOnSubmitButton
                    type="button"
                    className="btn btn-primary fw-100"
                    enableOnCompletion={true}
                    customonclick={async () => {
                      await this._onSubmit();
                    }}
                  >
                    Next
                  </LoadingOnSubmitButton>
                  {this._getDisclaimer()}
                </div>
              </Modal.Footer>
            </div>
          </>
        )}
        {viewMode == "PAYMENT_METHOD_SELECT" && (
          <>
            <Modal.Header className="border-bottom-0" closeButton>
              {/* Back Button */}
              <div className={"row p-0 m-0 ps-2 mt-2"}>
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => this._setViewMode("INITIAL")}
                    style={{
                      fontSize: "24px"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}

              <Modal.Title />
            </Modal.Header>
            <div className="fade-in">
              <Modal.Body className="p-0 px-3">
                <div className="d-flex align-self-center justify-content-center">
                  <div className="w-100" style={{ maxWidth: "400px" }}>
                    {/* Action Title */}
                    <h5 className="fw-bolder text-center mb-5 mt-3">Choose payment method</h5>
                    {/* End Action Title */}

                    {paymentMethods.map((paymentMethod) => (
                      <CardPaymentOptionListItem
                        key={`card-payment-${paymentMethod.id}`}
                        paymentMethod={paymentMethod}
                        user={user}
                        isSelected={selectedPaymentMethod.id == paymentMethod.id}
                        showRadio={true}
                        onChange={(): void => {
                          this._setPaymentMethod(paymentMethod);
                          this._setViewMode("INITIAL");
                        }}
                      />
                    ))}
                    <div
                      className={"row m-0 wh-account-card-option mb-3"}
                      onClick={() => this._setViewMode("ADD_PAYMENT_METHOD")}
                    >
                      <div className="col-2 p-0 d-flex justify-content-center align-self-center">
                        {/* Provider Icon */}
                        <img
                          className="h-100 align-self-center"
                          style={{ maxHeight: "40px" }}
                          src={`/images/icons/add-new-card.png`}
                          alt={"Add card"}
                        />
                        {/* End Provider Icon */}
                      </div>
                      <div className="col-9 align-self-center">
                        <span className="fw-bold">Add new card</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer className="py-3 fade-in" style={{ borderTop: "none" }} />
            </div>
          </>
        )}
        {viewMode == "ADD_PAYMENT_METHOD" && (
          <>
            <Modal.Header className="border-bottom-0" closeButton>
              {/* Back Button */}
              <div className={`row p-0 m-0 ps-2 mt-2 ${paymentMethods.length === 0 ? "d-none" : ""}`}>
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => this._setViewMode("INITIAL")}
                    style={{
                      fontSize: "24px"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}

              <Modal.Title />
            </Modal.Header>
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center flex-column">
                {/* Action Title */}
                <h5 className="fw-bolder text-center mb-3">Add your payment information</h5>
                {/* End Action Title */}

                {/* Stripe Payment Details */}
                <div className={"p-4"}>
                  <Elements stripe={stripePromise} options={options as StripeElementsOptions}>
                    <ElementsConsumer>
                      {({ stripe, elements }) => (
                        <StripeAddNewCardForm
                          stripe={stripe}
                          elements={elements}
                          selectedPrice={newPrice}
                          source={source}
                        />
                      )}
                    </ElementsConsumer>
                  </Elements>
                </div>
                {/* End Stripe Payment Details */}
              </div>
            </Modal.Body>
          </>
        )}
      </Modal>
    );
  }
}

export default CardSubscriptionPaymentMethodSelectModal;
