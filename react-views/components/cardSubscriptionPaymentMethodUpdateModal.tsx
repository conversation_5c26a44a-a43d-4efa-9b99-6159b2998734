import React from "react";
import { Modal } from "react-bootstrap";
import { UserDocument } from "../../models/User";
import { loadStripe, StripeElementsOptions } from "@stripe/stripe-js";
import { Elements, ElementsConsumer } from "@stripe/react-stripe-js";
import StripeAddNewCardForm from "./stripeAddNewCardForm";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import axios from "axios";
import CardPaymentOptionListItem from "./cardPaymentOptionListItem";
import { eventEmitter, EVENTS } from "../utils/eventService";

const stripePromise = loadStripe(process.env.STRIPE_KEY);

type ViewModeType = "PAYMENT_METHOD_SELECT" | "ADD_PAYMENT_METHOD";

type PropsType = {
  user: UserDocument;
  paymentMethods: PaymentMethodDocument[];
  initialSelectedPaymentMethod?: PaymentMethodDocument;
  handleClose: () => void;
  show: boolean;
};

type StateType = {
  viewMode: ViewModeType;
};

class CardPaymentSubscriptionPaymentMethodUpdateModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: props.paymentMethods.length === 0 ? "ADD_PAYMENT_METHOD" : "PAYMENT_METHOD_SELECT"
    };
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private async _updatePaymentMethod(paymentMethod: PaymentMethodDocument) {
    eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

    await axios.post(`/investor/subscriptions/payment-method`, {
      paymentMethod: paymentMethod.providers.stripe.id
    });

    window.location.href = "/investor/billing";
  }

  render(): JSX.Element {
    const { show, paymentMethods, user, handleClose, initialSelectedPaymentMethod } = this.props;
    const { viewMode } = this.state;

    const options = {
      mode: "setup",
      currency: user.currency.toLowerCase(),
      appearance: {
        theme: "stripe"
      }
    };

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5 max-w-600px">
        {viewMode == "PAYMENT_METHOD_SELECT" && (
          <>
            <Modal.Header className="border-bottom-0" closeButton>
              <Modal.Title />
            </Modal.Header>
            <div className="fade-in">
              <Modal.Body className="p-0 px-3">
                <div className="d-flex align-self-center justify-content-center">
                  <div className="w-100" style={{ maxWidth: "400px" }}>
                    {/* Action Title */}
                    <h5 className="fw-bolder text-center mb-5 mt-3">Choose payment method</h5>
                    {/* End Action Title */}

                    {paymentMethods
                      .filter((paymentMethod) => !paymentMethod.wallet)
                      .map((paymentMethod) => (
                        <CardPaymentOptionListItem
                          key={`card-payment-${paymentMethod.id}`}
                          paymentMethod={paymentMethod}
                          user={user}
                          showRadio={false}
                          isSelected={initialSelectedPaymentMethod.id == paymentMethod.id}
                          onChange={async (): Promise<void> => {
                            if (paymentMethod.id !== initialSelectedPaymentMethod.id) {
                              await this._updatePaymentMethod(paymentMethod);
                            } else handleClose();
                          }}
                        />
                      ))}
                    <div
                      className={"row m-0 wh-account-card-option mb-3"}
                      onClick={() => this._setViewMode("ADD_PAYMENT_METHOD")}
                    >
                      <div className="col-2 p-0 d-flex justify-content-center align-self-center">
                        {/* Provider Icon */}
                        <img
                          className="h-100 align-self-center"
                          style={{ maxHeight: "40px" }}
                          src={`/images/icons/add-new-card.png`}
                        />
                        {/* End Provider Icon */}
                      </div>
                      <div className="col-9 align-self-center">
                        <span className="fw-bold">Add new card</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer className="py-3 fade-in" style={{ borderTop: "none" }} />
            </div>
          </>
        )}
        {viewMode == "ADD_PAYMENT_METHOD" && (
          <>
            <Modal.Header className="border-bottom-0" closeButton>
              {/* Back Button */}
              <div className={`row p-0 m-0 ps-2 mt-2 ${paymentMethods.length === 0 ? "d-none" : ""}`}>
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => this._setViewMode("PAYMENT_METHOD_SELECT")}
                    style={{
                      fontSize: "24px"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}

              <Modal.Title />
            </Modal.Header>
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center flex-column">
                {/* Action Title */}
                <h5 className="fw-bolder text-center mb-3">Add your payment information</h5>
                {/* End Action Title */}

                {/* Stripe Payment Details */}
                <div className={"p-4"}>
                  <Elements stripe={stripePromise} options={options as StripeElementsOptions}>
                    <ElementsConsumer>
                      {({ stripe, elements }) => (
                        <StripeAddNewCardForm stripe={stripe} elements={elements} source={"billing"} />
                      )}
                    </ElementsConsumer>
                  </Elements>
                </div>
                {/* End Stripe Payment Details */}
              </div>
            </Modal.Body>
          </>
        )}
      </Modal>
    );
  }
}

export default CardPaymentSubscriptionPaymentMethodUpdateModal;
