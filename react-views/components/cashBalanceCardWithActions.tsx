import React from "react";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { TransactionDocument } from "../../models/Transaction";
import Decimal from "decimal.js";
import IncomingCashFlowModal from "./modals/incomingCashFlowModal";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderType } from "../../services/truelayerService";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { UserDocument } from "../../models/User";
import { entitiesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  cashBalance: string;
  incomingCashflowTransactions: TransactionDocument[];
  investmentProducts: InvestmentProductDocument[];
  truelayerProviders: ProviderType[];
  openTransactionModal: (transaction: TransactionDocument) => void;
  canUserDeposit: boolean;
};

type StateType = {
  showIncomingCashFlowModal: boolean;
};

const getButtons = (user: UserDocument) => [
  {
    icon: "add",
    popoverText: "Add money",
    handleClick: () =>
      user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        ? eventEmitter.emit(EVENTS.depositMethodsModal)
        : eventEmitter.emit(EVENTS.depositModal)
  },
  {
    icon: "remove",
    popoverText: "Withdraw",
    handleClick: () => eventEmitter.emit(EVENTS.withdrawalModal)
  },
  {
    icon: "more_horiz",
    popoverText: "More",
    handleClick: () => eventEmitter.emit(EVENTS.bankAccountList)
  }
];

class CashBalanceCardWithActions extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showIncomingCashFlowModal: false
    };
  }

  private _pendingCashFlowsAmount(): number {
    const { incomingCashflowTransactions } = this.props;
    const displayAmountSum = incomingCashflowTransactions.reduce(
      (sum, transaction) => sum.plus(transaction.displayAmount),
      new Decimal(0)
    );

    return displayAmountSum.div(100).toNumber();
  }

  private _setShowIncomingCashflowModal(value: boolean) {
    this.setState({ showIncomingCashFlowModal: value });
  }

  render(): JSX.Element {
    const {
      cashBalance,
      investmentProducts,
      truelayerProviders,
      canUserDeposit,
      incomingCashflowTransactions,
      openTransactionModal
    } = this.props;
    const { showIncomingCashFlowModal } = this.state;

    const pendingCashflowAmount = this._pendingCashFlowsAmount();
    const user = (this.context as GlobalContextType).user;
    const locale = (this.context as GlobalContextType).locale;

    return (
      <div className="d-flex flex-column">
        <div className="wh-account-card-small bg-white d-flex flex-column justify-content-center align-items-center shadow-sm px-4 py-5">
          <p className="text-muted mb-2">Cash balance</p>
          <h1 className="text-black fw-600 m-0">{cashBalance}</h1>
          {pendingCashflowAmount > 0 && (
            <button
              className="btn d-flex cash-balance-incoming mt-2"
              onClick={() => this._setShowIncomingCashflowModal(true)}
            >
              <span>{`+${formatCurrency(pendingCashflowAmount, user.currency, locale)}`}</span>
              <span className="material-icons material-symbols-outlined ms-1">update</span>
            </button>
          )}
        </div>
        <div className="row m-0 w-100 mt-5 justify-content-center">
          {canUserDeposit &&
            getButtons(user).map(({ icon, popoverText, handleClick }, index) => (
              <div
                key={`wealthyhood-cash-balance-${icon}`}
                className="col p-0 text-center d-flex flex-column align-items-center"
              >
                <button
                  className="d-flex align-items-center justify-content-center text-muted text-decoration-none text-start btn btn-light btn-small mx-4 account-card-btn"
                  data-toggle="popover"
                  data-content={popoverText}
                  data-placement="bottom"
                  key={`cash-balance-btn-${index}`}
                  onClick={handleClick}
                >
                  <div className="d-flex h-100 align-self-center justify-content-center">
                    <span
                      className="mx-auto material-symbols-outlined align-self-center px-3"
                      style={{ fontSize: "20px", color: "#000" }}
                    >
                      {icon}
                    </span>
                  </div>
                </button>
                <div className="fw-bolder t-875 mt-2">{popoverText}</div>
              </div>
            ))}
        </div>

        <IncomingCashFlowModal
          title="Incoming cash flows"
          show={showIncomingCashFlowModal}
          transactions={incomingCashflowTransactions}
          handleClose={() => this._setShowIncomingCashflowModal(false)}
          truelayerProviders={truelayerProviders}
          investmentProducts={investmentProducts}
          onTransactionSelected={(transaction: TransactionDocument) => {
            this._setShowIncomingCashflowModal(false);
            openTransactionModal(transaction);
          }}
        />
      </div>
    );
  }
}

CashBalanceCardWithActions.contextType = GlobalContext;

export default CashBalanceCardWithActions;
