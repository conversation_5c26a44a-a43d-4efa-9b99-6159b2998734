import React from "react";
import { TransactionPreview } from "../types/transactionPreview";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

export type CashbackPreviewRowProps = {
  transactionPreview: TransactionPreview;
  onInfoClick: () => void;
};

class CashbackPreviewRow extends React.Component<CashbackPreviewRowProps> {
  render(): JSX.Element {
    const { transactionPreview, onInfoClick } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    if (!user.isCashbackFeatureEnabled) {
      return <></>;
    }

    return (
      <div className="row pb-3 mb-3 border-bottom">
        <div className="col text-start d-flex">
          Cashback
          <i
            className="material-symbols-outlined align-self-center text-primary ms-2 cursor-pointer"
            style={{ fontSize: "16px", color: "#536AE3" }}
            onClick={() => onInfoClick()}
          >
            info
          </i>
        </div>
        <div className="col text-end">
          <span className="fw-bolder">
            {formatCurrency(transactionPreview?.cashback ?? 0, user.currency, locale, 2, 2)}
          </span>
        </div>
      </div>
    );
  }
}

CashbackPreviewRow.contextType = GlobalContext;

export default CashbackPreviewRow;
