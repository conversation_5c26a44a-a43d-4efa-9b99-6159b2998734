import React from "react";
import { DualExecutionValue, FeesType } from "../types/transactionPreview";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { ExecutionModeType } from "../types/executionMode";
import { getTotalCommission } from "../utils/feesUtil";

export type CommissionPreviewRowProps = {
  fees: DualExecutionValue<FeesType>;
  executionMode?: ExecutionModeType;
  isRepeatingInvestment?: boolean;
  hasETFOrders?: boolean;
  isETFBuy?: boolean;
  isETFSell?: boolean;
};

class CommissionPreviewRow extends React.Component<CommissionPreviewRowProps> {
  private _getDisplayedCommission(
    fees: DualExecutionValue<FeesType>,
    executionMode?: ExecutionModeType
  ): JSX.Element {
    const { user, locale } = this.context as GlobalContextType;

    if (fees.smart && !fees.express) {
      return (
        <span className="fw-bolder">
          {formatCurrency(getTotalCommission(fees.smart), user.currency, locale, 2, 2)}
        </span>
      );
    } else if (fees.express && !fees.smart) {
      return (
        <span className="fw-bolder">
          {formatCurrency(getTotalCommission(fees.express), user.currency, locale, 2, 2)}
        </span>
      );
    } else {
      if (executionMode === "SMART") {
        return (
          <>
            <span className="fw-lighter text-muted strikethrough">
              {formatCurrency(getTotalCommission(fees.express), user.currency, locale, 2, 2)}
            </span>
            <span className="fw-bolder">
              {" "}
              {formatCurrency(getTotalCommission(fees.smart), user.currency, locale, 2, 2)}
            </span>
          </>
        );
      } else {
        return (
          <span className="fw-bolder">
            {formatCurrency(getTotalCommission(fees.express), user.currency, locale, 2, 2)}
          </span>
        );
      }
    }
  }

  private _getHoverText(): string {
    const { user } = this.context as GlobalContextType;
    const { isRepeatingInvestment, hasETFOrders, isETFBuy, isETFSell } = this.props;

    if (!user.isRealtimeETFExecutionEnabled) {
      return "Wealthyhood is commission-free, which means we charge ZERO COMMISSIONS for all stocks and ETFs.";
    }

    if (isRepeatingInvestment) {
      return "All repeating investments are executed through our Smart Execution process and are COMMISSION-FREE.";
    }

    if (isETFBuy) {
      return "With Smart Execution your ETF buy order is COMMISSION-FREE. If you de-activate smart execution, you are charged €1 per ETF order.";
    }

    if (isETFSell) {
      return "Only express execution is available for ETF sell orders and you are charged a flat €1 per order.";
    }

    if (!hasETFOrders) {
      return "Your order is commission-free, as we charge ZERO COMMISSIONS for all stocks!";
    }

    return "With Smart Execution your Portfolio Buy order is COMMISSION-FREE.\n\nIf you de-activate smart execution, you are charged €1 per ETF order. All orders for individual stocks remain commission-free.";
  }

  render(): JSX.Element {
    const { fees, executionMode } = this.props;

    return (
      <div className="row pb-3 mb-3 border-bottom">
        <div className="col text-start">
          <div className="d-flex w-100">
            <p className="m-0 align-self-center text-nowrap me-2">Commission</p>
            <HoverableInfoIcon hoverText={this._getHoverText()} colorHex={"#536AE3"} />
          </div>
        </div>
        <div className="col text-end">{this._getDisplayedCommission(fees, executionMode)}</div>
      </div>
    );
  }
}

CommissionPreviewRow.contextType = GlobalContext;

export default CommissionPreviewRow;
