import React from "react";
import CompoundCalculatorAnnualReturnSlider, {
  CompoundCalculatorAnnualReturnSliderPropsType
} from "./compoundCalculatorAnnualReturnSlider";
import CompoundCalculatorMonthlyInvestmentSlider, {
  CompoundCalculatorMonthlyInvestmentSliderPropsType
} from "./compoundCalculatorMonthlyInvestmentSlider";
import InfoModal from "./modals/infoModal";

type CompoundCalculatorPropsType = CompoundCalculatorMonthlyInvestmentSliderPropsType &
  CompoundCalculatorAnnualReturnSliderPropsType;

type StateType = {
  showInfoModal: boolean;
};

class CompoundCalculator extends React.Component<CompoundCalculatorPropsType, StateType> {
  constructor(props: CompoundCalculatorPropsType) {
    super(props);

    this.state = { showInfoModal: false };
  }

  private _setShowInfoModal(showInfoModal: boolean) {
    this.setState({ showInfoModal });
  }

  render(): JSX.Element {
    const {
      maxMonthlyInvestment,
      minMonthlyInvestment,
      onMonthlyInvestmentChange,
      selectedMonthlyInvestment,
      maxAnnualReturn,
      minAnnualReturn,
      onAnnualReturnChange,
      portfolioPastPerfomance,
      selectedAnnualReturn
    } = this.props;

    const { showInfoModal } = this.state;

    return (
      <>
        <div className="row m-0 mt-3 p-0">
          <div className="col align-self-center p-0">
            <div className="d-flex align-self-center">
              <h5 className="m-0 me-1">Compound return calculator</h5>
              <span
                className="material-symbols-outlined text-primary cursor-pointer align-self-center"
                style={{ fontSize: "16px" }}
                onClick={() => this._setShowInfoModal(true)}
              >
                info
              </span>
            </div>
          </div>
          {/* Monthly Investment Input */}
          <CompoundCalculatorMonthlyInvestmentSlider
            selectedMonthlyInvestment={selectedMonthlyInvestment}
            onMonthlyInvestmentChange={onMonthlyInvestmentChange}
            maxMonthlyInvestment={maxMonthlyInvestment}
            minMonthlyInvestment={minMonthlyInvestment}
          />
          {/* End Monthly Investment Input */}
          {/* Annual Input */}
          <CompoundCalculatorAnnualReturnSlider
            selectedAnnualReturn={selectedAnnualReturn}
            portfolioPastPerfomance={portfolioPastPerfomance}
            onAnnualReturnChange={onAnnualReturnChange}
            maxAnnualReturn={maxAnnualReturn}
            minAnnualReturn={minAnnualReturn}
          />
          {/* End Annual Input */}
        </div>

        <InfoModal title={null} show={showInfoModal} handleClose={() => this._setShowInfoModal(false)}>
          <h5 className="mb-4">Compound return calculator</h5>
          <p className="text-muted">
            Our ’Compound return calculator’ assumes you’re investing the amount you’ve selected in a portfolio
            earning the annualised return you’ve defined.
          </p>
          <p className="text-muted">
            We’ve also marked the expected return of your portfolio to give you a snapshot of potential outcomes
            and what to expect.
          </p>
          <p className="text-muted">
            Disclaimer: Keep in mind the future is unpredictable, and markets may perform better or worse than
            expected.
          </p>
        </InfoModal>
      </>
    );
  }
}

const MemoizedCompoundCalculator = React.memo(CompoundCalculator, (prevProps, nextProps) => {
  return (
    prevProps.selectedAnnualReturn === nextProps.selectedAnnualReturn &&
    prevProps.selectedMonthlyInvestment === nextProps.selectedMonthlyInvestment
  );
});
// See: https://github.com/jsx-eslint/eslint-plugin-react/blob/master/docs/rules/display-name.md
MemoizedCompoundCalculator.displayName = "MemoizedCompoundCalculator";

export default MemoizedCompoundCalculator;
