import Nouislider from "nouislider-react";
import React from "react";
import InfoModal from "./modals/infoModal";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

const NO_UI_SLIDER_ID = "compound-calc-annual-return-slider";
const TOOLTIP_TEXT_ID = "your-portfolio-id";

export type CompoundCalculatorAnnualReturnSliderPropsType = {
  selectedAnnualReturn: number;
  portfolioPastPerfomance: {
    roundedToHalfAnnualReturn: number;
    annualReturn: number;
  };
  maxAnnualReturn: number;
  minAnnualReturn: number;
  onAnnualReturnChange: (value: number) => void;
};

type StateType = {
  showInfoModal: boolean;
};

class CompoundCalculatorAnnualReturnSlider extends React.Component<
  CompoundCalculatorAnnualReturnSliderPropsType,
  StateType
> {
  private isClickListenerActive = false;
  private isTooltipAdjusted = false;
  private pollingInterval: NodeJS.Timeout;

  constructor(props: CompoundCalculatorAnnualReturnSliderPropsType) {
    super(props);

    this.state = { showInfoModal: false };
  }

  private _setShowInfoModal(showInfoModal: boolean) {
    this.setState({ showInfoModal });
  }

  private _roundToOneDigit(value: number): number {
    return Math.round(value * 10) / 10;
  }

  private _formatSelectedAnnualReturn(value: number): string {
    const { locale } = this.context as GlobalContextType;

    /**
     * Javascript edge case regarding float precision causing:
     * '12' to be '12.000000000000002'
     * That's why we round to 1 decimal digit, because slider step is .5
     */
    const valueRoundedToOneDigit = this._roundToOneDigit(value);

    /**
     * Because the slider has step 0.5, we round user's portfolio annual return to half
     * When the selected value is 11
     * and portfolioPastPerfomance.annualReturn 11.24, the respective rounded value will be 11
     * in this case we want to present the actual annual return which is 11.24
     */
    const { annualReturn, roundedToHalfAnnualReturn } = this.props.portfolioPastPerfomance;
    const num = valueRoundedToOneDigit === roundedToHalfAnnualReturn ? annualReturn : value;

    return Math.abs(num / 100).toLocaleString(locale, {
      style: "percent",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  private _handleElementClick = () => {
    this._setShowInfoModal(true);
  };

  private _addCustomClasses(baseClasses: string): string {
    const { selectedAnnualReturn, portfolioPastPerfomance } = this.props;
    let finalClassname = baseClasses;

    finalClassname +=
      this._roundToOneDigit(selectedAnnualReturn) >= portfolioPastPerfomance.roundedToHalfAnnualReturn
        ? " isDummyHandleColoredWhite"
        : "";
    finalClassname +=
      this._roundToOneDigit(selectedAnnualReturn) === portfolioPastPerfomance.roundedToHalfAnnualReturn
        ? " isPortfolioAnnualReturnSelected"
        : "";

    return finalClassname;
  }

  private _adjustTooltipPositionForEdgeCases(tooltip: HTMLElement, slider: HTMLElement) {
    const initialValue = this.props.portfolioPastPerfomance.roundedToHalfAnnualReturn;
    const sliderWidth = slider.offsetWidth;
    const tooltipWidth = tooltip.offsetWidth;
    const distanceToStart =
      initialValue * (sliderWidth / (this.props.maxAnnualReturn - this.props.minAnnualReturn));
    const distanceToEnd = sliderWidth - distanceToStart - tooltipWidth;
    // Add a class based on the position of the tooltip
    if (distanceToStart < 100) {
      // Add padding to the right if the tooltip is close to the start
      tooltip.classList.add("tooltip-overflows-left");
    } else if (distanceToEnd < 100) {
      // Add padding to the left if the tooltip is close to the end
      tooltip.classList.add("tooltip-overflows-right");
    }
    this.isTooltipAdjusted = true;
  }

  private _makeTooltipClickable(tooltip: HTMLElement) {
    tooltip.addEventListener("click", this._handleElementClick);
    this.isClickListenerActive = true;
  }

  startPollingForElement() {
    this.pollingInterval = setInterval(() => {
      const tooltip = document.getElementById(TOOLTIP_TEXT_ID);
      const slider = document.getElementById(NO_UI_SLIDER_ID + "-dummy");

      if (tooltip && slider && !this.isClickListenerActive && !this.isTooltipAdjusted) {
        this._adjustTooltipPositionForEdgeCases(tooltip, slider);
        this._makeTooltipClickable(tooltip);
        clearInterval(this.pollingInterval);
      }
    }, 100);
  }

  componentDidMount(): void {
    //  We poll here because at some cases the tooltip and slider are not initailized when componentDidMount runs.
    this.startPollingForElement();
  }

  componentWillUnmount() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }
    // Remove the click event listener when the component unmounts
    const element = document.getElementById(TOOLTIP_TEXT_ID);
    if (element && this.isClickListenerActive) {
      element.removeEventListener("click", this._handleElementClick);
      this.isClickListenerActive = false;
    }
  }

  render(): JSX.Element {
    const {
      maxAnnualReturn,
      minAnnualReturn,
      onAnnualReturnChange,
      selectedAnnualReturn,
      portfolioPastPerfomance
    } = this.props;

    const { showInfoModal } = this.state;

    return (
      <>
        <div className="row m-0 mb-3 mt-4  p-0">
          <div className="col-6 p-0 text-start">
            <label className="text-muted">Annual Return</label>
          </div>
          <div className="col-6 p-0 text-end">
            <span className="fw-bold">{this._formatSelectedAnnualReturn(selectedAnnualReturn)}</span>
          </div>
        </div>
        <div className="row m-0 p-0">
          <div className={this._addCustomClasses("col p-0 ")}>
            <Nouislider
              id={NO_UI_SLIDER_ID}
              animate={false}
              connect={[true, false]}
              onUpdate={(render, handle, value) => {
                onAnnualReturnChange(value[0] ?? 0);
              }}
              onSet={(render, handle, value) => {
                onAnnualReturnChange(value[0] ?? 0);
              }}
              range={{
                min: minAnnualReturn,
                max: maxAnnualReturn
              }}
              start={[selectedAnnualReturn]}
              step={0.5}
            />
            <Nouislider
              id={NO_UI_SLIDER_ID + "-dummy"}
              animate={false}
              connect={[false, false]}
              range={{
                min: minAnnualReturn,
                max: maxAnnualReturn
              }}
              start={[portfolioPastPerfomance.roundedToHalfAnnualReturn]}
              tooltips={[
                {
                  to: () =>
                    `<p id="${TOOLTIP_TEXT_ID}" style="margin: 6px 0 0 0;transform: scaleX(-1);color: #536AE3;">Your Portfolio</p>`,
                  from: () => portfolioPastPerfomance.roundedToHalfAnnualReturn
                }
              ]}
              disabled
              step={0.5}
            />
          </div>
        </div>

        <InfoModal title={null} show={showInfoModal} handleClose={() => this._setShowInfoModal(false)}>
          <h5 className="mb-4">Compound return calculator</h5>
          <p className="text-muted">
            The expected return of your portfolio is based on the past performance of its individual assets.
          </p>
          <p className="text-muted">
            We calculate the past performance based on a 10-year period. If an ETF or stock has less than 10 years
            of data, we use the relevant benchmark index for ETFs and available data since the initial public
            offering (IPO) date for stocks.
          </p>
          <p className="text-muted">Disclaimer: Past performance is not a guarantee of future performance.</p>
        </InfoModal>
      </>
    );
  }
}

CompoundCalculatorAnnualReturnSlider.contextType = GlobalContext;

export default CompoundCalculatorAnnualReturnSlider;
