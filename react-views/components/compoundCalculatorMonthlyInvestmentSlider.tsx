import Nouislider from "nouislider-react";
import React from "react";
import { formatCurrency } from "../utils/currencyUtil";

const NO_UI_SLIDER_ID = "compound-calc-monthly-investment-slider";

export type CompoundCalculatorMonthlyInvestmentSliderPropsType = {
  selectedMonthlyInvestment: number;
  maxMonthlyInvestment: number;
  minMonthlyInvestment: number;
  onMonthlyInvestmentChange: (value: number) => void;
};

class CompoundCalculatorMonthlyInvestmentSlider extends React.Component<CompoundCalculatorMonthlyInvestmentSliderPropsType> {
  constructor(props: CompoundCalculatorMonthlyInvestmentSliderPropsType) {
    super(props);
  }

  render(): JSX.Element {
    const { maxMonthlyInvestment, minMonthlyInvestment, onMonthlyInvestmentChange, selectedMonthlyInvestment } =
      this.props;

    return (
      <>
        <div className="row m-0 mb-3 mt-4  p-0">
          <div className="col-6 p-0 text-start">
            <label className="text-muted">Monthly Investment</label>
          </div>
          <div className="col-6 p-0 text-end">
            <span className="fw-bold">{formatCurrency(selectedMonthlyInvestment)}</span>
          </div>
        </div>
        <div className="row m-0 p-0">
          <div className="col p-0">
            <Nouislider
              id={NO_UI_SLIDER_ID}
              animate={false}
              connect={[true, false]}
              onUpdate={(render, handle, value) => {
                onMonthlyInvestmentChange(value[0] ?? 0);
              }}
              onSet={(render, handle, value) => {
                onMonthlyInvestmentChange(value[0] ?? 0);
              }}
              range={{
                min: minMonthlyInvestment,
                max: maxMonthlyInvestment
              }}
              start={selectedMonthlyInvestment}
              step={50}
            />
          </div>
        </div>
      </>
    );
  }
}

export default CompoundCalculatorMonthlyInvestmentSlider;
