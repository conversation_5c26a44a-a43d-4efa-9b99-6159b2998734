import React from "react";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";

type PropsType = {
  actions: {
    previousStep?: {
      disabled?: boolean;
      action: () => void;
      label: string;
    };
    nextStep?: {
      disabled?: boolean;
      action: () => void;
      label: string;
    };
    abortWizard?: {
      action: () => void;
      label: string;
    };
    completeWizard?: {
      disabled?: boolean;
      action: () => Promise<void>;
      label: string;
    };
  };
  fixed: boolean;
};

class ConfigurableWizardActions extends React.Component<PropsType> {
  private _completeWizard = async (): Promise<void> => {
    const { actions } = this.props;
    const { completeWizard } = actions;

    await completeWizard.action();
  };

  render(): JSX.Element {
    const { actions } = this.props;
    const { nextStep, previousStep, completeWizard } = actions;

    return (
      <>
        {/* <!-- Nav buttons --> */}
        <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
          {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
          <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
          <div className="col-md-5 p-0 border-top bg-white">
            <div className="row m-0 px-md-5 px-3 h-100 overflow-hidden justify-content-end">
              <div className="d-flex p-0 justify-content-end align-self-center">
                {previousStep && (
                  <button
                    type="button"
                    className="btn btn-secondary me-4"
                    onClick={previousStep.action}
                    disabled={previousStep.disabled}
                  >
                    {previousStep.label}
                  </button>
                )}
                {nextStep &&
                  (nextStep.disabled ? (
                    <button type="button" className="btn btn-primary w-100" disabled>
                      <span>{nextStep.label}</span>
                    </button>
                  ) : (
                    <LoadingOnSubmitButton
                      type="button"
                      className="btn btn-primary w-100"
                      customonclick={async () => nextStep.action()}
                      enableOnCompletion={true}
                    >
                      <span>{nextStep.label}</span>
                    </LoadingOnSubmitButton>
                  ))}
                {completeWizard &&
                  (completeWizard.disabled ? (
                    <button type="button" className="btn btn-primary w-100" disabled>
                      <span>{completeWizard.label}</span>
                    </button>
                  ) : (
                    <LoadingOnSubmitButton
                      type="button"
                      className="btn btn-primary w-100"
                      customonclick={async () => this._completeWizard()}
                      enableOnCompletion={true}
                    >
                      {completeWizard.label}
                    </LoadingOnSubmitButton>
                  ))}
              </div>
            </div>
          </div>
        </div>
        {/* <!-- /Nav buttons --> */}

        {/* <!-- Dummy spacer to solve overlapping issue with fixed bottom nav buttons --> */}
        <div className="row" style={{ height: "73px" }} />
      </>
    );
  }
}

export default ConfigurableWizardActions;
