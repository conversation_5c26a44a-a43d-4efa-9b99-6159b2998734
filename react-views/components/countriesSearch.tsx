import React from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";
import axios from "axios";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import CountryRow from "./countryRow";
import InfoModal from "./modals/infoModal";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

const pinnedCountries: { code: countriesConfig.CountryCodesType; name: string }[] = [
  { code: "GB", name: "United Kingdom" },
  { code: "GR", name: "Greece" },
  { code: "CY", name: "Cyprus" }
];

const nonPinnedCountries = countriesConfig.availableCountries.filter(
  (country) => !pinnedCountries.some((pinnedCountry) => pinnedCountry.code === country.code)
);

interface CountriesWizardState {
  searchTerm: string;
  selectedCountryCode: countriesConfig.CountryCodesType;
  selectedCountryName: string;
  showUnsupportedCountryInfoModal: boolean;
  isSelectedCountrySupported: boolean;
}

class CountriesSearch extends React.Component<Record<string, never>, CountriesWizardState> {
  constructor(props: any) {
    super(props);

    this.state = {
      searchTerm: "",
      selectedCountryCode: null,
      selectedCountryName: "",
      isSelectedCountrySupported: false,
      showUnsupportedCountryInfoModal: false
    };

    this._handleSearchTermChange = this._handleSearchTermChange.bind(this);
  }

  private _setShowUnsupportedCountryInfoModal(showUnsupportedCountryInfoModal: boolean) {
    this.setState({ showUnsupportedCountryInfoModal });
  }

  private _handleSearchTermChange(event: React.ChangeEvent<HTMLInputElement>) {
    const userInput = event.target.value.trim();
    this.setState({ searchTerm: userInput });
  }

  private _handleSelectedCountry(countryCode: countriesConfig.CountryCodesType) {
    const { user } = this.context as GlobalContextType;

    const selectedCountry = countriesConfig.countries.find((country) => country.code === countryCode);

    if (!ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, selectedCountry.code)) {
      this.setState({
        selectedCountryCode: countryCode,
        selectedCountryName: selectedCountry.name,
        isSelectedCountrySupported: false,
        showUnsupportedCountryInfoModal: true
      });
    } else {
      this.setState({
        selectedCountryCode: countryCode,
        selectedCountryName: selectedCountry.name,
        isSelectedCountrySupported: true,
        showUnsupportedCountryInfoModal: false
      });
    }
  }

  private _submitResidencyCountry = async (): Promise<void> => {
    const { selectedCountryCode } = this.state;
    try {
      await axios({
        method: "POST",
        url: "/investor/residency-country",
        data: {
          residencyCountry: selectedCountryCode
        }
      });
      window.location.href = "/investor/id-verification";
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  };

  render(): JSX.Element {
    const {
      searchTerm,
      isSelectedCountrySupported,
      selectedCountryCode,
      selectedCountryName,
      showUnsupportedCountryInfoModal
    } = this.state;
    const { user } = this.context as GlobalContextType;

    const sortedCountries = nonPinnedCountries.filter(
      (country) =>
        country.code.toLowerCase().startsWith(searchTerm.toLowerCase()) ||
        country.name.toLowerCase().startsWith(searchTerm.toLowerCase())
    );

    const supportedCountries = sortedCountries.filter((country) =>
      ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)
    );
    const unsupportedCountries = sortedCountries.filter(
      (country) => !ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)
    );

    return (
      <>
        <div className="search-bar">
          <span className="material-symbols-outlined me-2 search-bar-icon">search</span>
          <input
            className="search-bar-input"
            type="text"
            placeholder="Search your country"
            onChange={this._handleSearchTermChange}
          ></input>
        </div>
        {searchTerm.length > 0 && sortedCountries.length == 0 && (
          <div className="search-results-none">
            <h5 className="search-text-not-found">No results found.</h5>
            <h6 className="search-text-shorten-search">Try shortening your search.</h6>
          </div>
        )}
        <div className="search-results">
          {searchTerm.length == 0 && pinnedCountries.length && (
            <div id="pinned-countries" className="mt-4 mb-4 border-bottom-countries-list">
              {pinnedCountries.map((country, key) => {
                return (
                  <CountryRow
                    id={key}
                    countryCode={country.code as countriesConfig.CountryCodesType}
                    countryName={country.name}
                    isSelected={selectedCountryCode === country.code}
                    handleSelectedCountry={() =>
                      this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                    }
                    isAvailable={ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)}
                    key={key}
                  />
                );
              })}
            </div>
          )}
          {supportedCountries.length > 0 && (
            <div className="mt-4 mb-4 border-bottom-countries-list">
              {supportedCountries.map((country, key) => {
                return (
                  <CountryRow
                    id={key}
                    countryCode={country.code as countriesConfig.CountryCodesType}
                    countryName={country.name}
                    isSelected={selectedCountryCode === country.code}
                    handleSelectedCountry={() =>
                      this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                    }
                    isAvailable={true}
                    key={key}
                  />
                );
              })}
            </div>
          )}

          <div className="mt-4 mb-4">
            {unsupportedCountries.map((country, key) => {
              return (
                <CountryRow
                  id={key}
                  countryCode={country.code as countriesConfig.CountryCodesType}
                  countryName={country.name}
                  isSelected={selectedCountryCode === country.code}
                  handleSelectedCountry={() =>
                    this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                  }
                  isAvailable={false}
                  key={key}
                />
              );
            })}
          </div>
        </div>

        <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
          {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
          <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
          <div className="col-md-5 p-0 border-top bg-white">
            <div className="row m-0 px-md-5 px-3 h-100 overflow-hidden justify-content-end">
              <div className="d-flex p-0 justify-content-end align-self-center">
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary w-100 float-end"
                  disabled={!isSelectedCountrySupported}
                  customonclick={this._submitResidencyCountry}
                >
                  {isSelectedCountrySupported ? "Next" : "Select Residency Country"}
                </LoadingOnSubmitButton>
              </div>
            </div>
          </div>
        </div>
        <InfoModal
          title={null}
          show={showUnsupportedCountryInfoModal}
          handleClose={() => this._setShowUnsupportedCountryInfoModal(false)}
        >
          <h5 className="fw-bold mb-5">We’re coming soon!</h5>
          <p className="text-muted">
            Unfortunately, Wealthyhood is not available in your country yet. Please rest assured, we’re working
            hard to bring Wealthyhood to {selectedCountryName} as soon as possible!
          </p>
        </InfoModal>
      </>
    );
  }
}

CountriesSearch.contextType = GlobalContext;

export default CountriesSearch;
