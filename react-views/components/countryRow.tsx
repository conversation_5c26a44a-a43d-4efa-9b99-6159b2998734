import React from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";
import Flag from "react-world-flags";

type PropsType = {
  id: number;
  countryCode: countriesConfig.CountryCodesType;
  countryName: string;
  isSelected: boolean;
  isAvailable: boolean;
  handleSelectedCountry: (countryCode: countriesConfig.CountryCodesType) => void;
};

class CountryRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { countryCode, countryName, isSelected, isAvailable, handleSelectedCountry } = this.props;

    return (
      <li
        id={`country-list-item-${this.props.id}`}
        onClick={() => handleSelectedCountry(countryCode)}
        className={`cursor-pointer search-elements-list-item ${isSelected ? "selected-country" : ""}`}
      >
        <div className={`flag-container me-3 ${isAvailable ? "" : "country-not-available-flag"}`}>
          <Flag code={countryCode} height="100%" />
        </div>
        <div className="country-code">
          <span>{countryCode}</span>
        </div>
        <div className="country-name">
          <span>{countryName}</span>
        </div>
        {!isAvailable && (
          <div className="country-not-available-text">
            <span>Not available </span>
          </div>
        )}
      </li>
    );
  }
}

export default CountryRow;
