import React from "react";

type PropsType = {
  id: string;
  title: string;
  className: string;
  expand: boolean;
  onClick: () => void;
};

class CustomAccordion extends React.Component<PropsType> {
  render(): JSX.Element {
    const { title, className, expand, onClick, children, id } = this.props;

    return (
      <div id={id} className={`row m-0 ${className}`}>
        <div
          className={`row m-0 cursor-pointer accordion-header p-3 ${expand ? "accordion-header-active" : ""}`}
          onClick={onClick}
        >
          <div className="col-11 p-0 align-self-center">
            <span className="fw-bold">{title}</span>
          </div>
          <div className="col-1 p-0 align-self-center text-end d-flex align-items-center justify-content-end">
            <span className="material-symbols-outlined align-self-center text-primary me-2">
              {expand ? "expand_less" : "expand_more"}
            </span>
          </div>
        </div>
        <div className={`row m-0 mt-3 accordion-body-container ${expand ? "expand" : ""}`}>
          <div className="col p-0">{children}</div>
        </div>
      </div>
    );
  }
}

export default CustomAccordion;
