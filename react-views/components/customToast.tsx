import React from "react";
import { Toast } from "react-bootstrap";
import { ToastTypeEnum, TOAST_CONFIG, TOAST_DELAY } from "../configs/toastConfig";
type PropsType = {
  delay?: number;
  content: string;
  toastType: ToastTypeEnum;
  show: boolean;
  onClose: () => void;
};

class CustomToast extends React.Component<PropsType> {
  render(): JSX.Element {
    const { content, toastType, onClose, show, delay } = this.props;

    const { bgColor, textColor, icon } = TOAST_CONFIG[toastType];

    return (
      <Toast
        className="bg-success text-light rounded border-0 w-100 justify-content-end mt-2"
        show={show}
        onClose={onClose}
        delay={delay || TOAST_DELAY}
        autohide
        style={{
          top: 80,
          right: 0,
          flexBasis: 0
        }}
      >
        <Toast.Header className={`rounded border-0 p-2 ${bgColor}`}>
          <i className={`toast-icon me-2 mr-6 ${icon} ${textColor}`} />
          <h6 className={`me-auto font-weight-bold py-0 my-0 ${textColor}`}>{content}</h6>
        </Toast.Header>
      </Toast>
    );
  }
}

export default CustomToast;
