import React from "react";

type PropsType = {
  timestamp: number;
};

const BACKFILL_DATE = 1738800000000;

class DailySummaryBackfillDisclaimer extends React.Component<PropsType> {
  render(): JSX.Element {
    const { timestamp } = this.props;

    if (timestamp < BACKFILL_DATE) {
      return (
        <div className="my-4 text-muted">
          Note: Historical data prior to February 6, 2025 has been reconstructed to enhance your experience, so
          slight variations may exist in these earlier records.
        </div>
      );
    } else return <></>;
  }
}

export default DailySummaryBackfillDisclaimer;
