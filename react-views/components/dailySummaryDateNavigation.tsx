import React from "react";
import { DailySummaryWithDataType } from "../pages/dailySummaryPage";

type PropsType = {
  currentIndex: number;
  summaries: DailySummaryWithDataType[];
  onSelectSummary: (summary: DailySummaryWithDataType) => void;
};

class DailySummaryDateNavigation extends React.Component<PropsType> {
  render(): JSX.Element {
    const { currentIndex, summaries, onSelectSummary } = this.props;
    return (
      // Container with relative positioning for the mask overlays
      <div className="position-relative w-full max-w-md mx-auto mb-5">
        {/* Gradient masks for left and right fade effects */}
        <div
          className="position-absolute start-0 top-0 h-100"
          style={{
            width: "64px",
            background: "linear-gradient(to right, #F2F4FD 5%, transparent 100%)",
            zIndex: 101,
            pointerEvents: "none"
          }}
        />
        <div
          className="position-absolute end-0 top-0 h-100"
          style={{
            width: "64px",
            background: "linear-gradient(to left, #F2F4FD 5%, transparent 100%)",
            zIndex: 101,
            pointerEvents: "none"
          }}
        />

        {/* Main container with overflow hidden */}
        <div className="position-relative overflow-hidden">
          <div className="d-flex align-items-center justify-content-center px-16">
            {[-1, 0, 1].map((offset) => {
              const summaryIndex = currentIndex + offset;
              const summary = summaries[summaryIndex];

              if (!summary) return <div key={`empty-${offset}`} className="w-20" />;

              if (offset === -1) {
                return (
                  <div
                    key={summary.timestamp}
                    className={`d-flex justify-content-start text-start position-absolute w-100`}
                  >
                    <div
                      className={"cursor-pointer"}
                      style={{ zIndex: 100 }}
                      onClick={() => onSelectSummary(summary)}
                    >
                      {summary.shortDateLabel}
                    </div>
                  </div>
                );
              } else if (offset === 0) {
                return (
                  <div
                    key={summary.timestamp}
                    onClick={() => onSelectSummary(summary)}
                    className={`text-center cursor-pointer fw-bold underlined-date`}
                  >
                    {summary.shortDateLabel}
                  </div>
                );
              } else {
                return (
                  <div
                    key={summary.timestamp}
                    className={`d-flex justify-content-end text-end position-absolute w-100`}
                  >
                    <div
                      className={"cursor-pointer"}
                      style={{ zIndex: 100 }}
                      onClick={() => onSelectSummary(summary)}
                    >
                      {summary.shortDateLabel}
                    </div>
                  </div>
                );
              }
            })}
          </div>
        </div>
      </div>
    );
  }
}

export default DailySummaryDateNavigation;
