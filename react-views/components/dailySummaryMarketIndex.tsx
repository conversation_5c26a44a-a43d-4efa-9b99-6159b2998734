import React from "react";
import { ReturnsType } from "../pages/dailySummaryPage";

type PropsType = { indexName: string; returns: ReturnsType };

class DailySummaryMarketIndex extends React.Component<PropsType> {
  render(): JSX.Element {
    const { indexName, returns } = this.props;

    return (
      <div
        key={indexName}
        style={{
          display: "flex",
          alignItems: "center",
          verticalAlign: "middle",
          justifyContent: "center",
          color: returns.downBy ? "#D63C3C" : "#23846A"
        }}
        className={"markets-carousel-card"}
      >
        {indexName}
        {returns.upBy && (
          <>
            <span className={`material-symbols-outlined align-self-center mx-1`} style={{ fontSize: "18px" }}>
              arrow_drop_up
            </span>
            {returns.upBy}
          </>
        )}
        {returns.downBy && (
          <>
            <span className={`material-symbols-outlined align-self-center mx-1`} style={{ fontSize: "18px" }}>
              arrow_drop_down
            </span>
            {returns.downBy}
          </>
        )}
      </div>
    );
  }
}

export default DailySummaryMarketIndex;
