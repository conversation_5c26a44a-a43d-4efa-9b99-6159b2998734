import React from "react";
import { MarketSummaryType } from "../pages/dailySummaryPage";

type DailySummaryMarketSummaryCollapsedProps = {
  content: MarketSummaryType;
  onExpand?: () => void;
  onInfoClick: () => void;
};

class DailySummaryMarketSummaryCollapsed extends React.Component<DailySummaryMarketSummaryCollapsedProps> {
  render(): JSX.Element {
    const { content, onExpand, onInfoClick } = this.props;

    return (
      <div
        className="my-4 daily-market-summary-clamped rounded-4"
        style={{ padding: "18px", backgroundColor: "#F1F3FD" }}
      >
        <div className="d-flex align-items-center pb-4 mb-1">
          <h4 className="text-lg font-semibold mb-0">🤓 Daily market summary</h4>
          <span
            className="material-symbols-outlined ms-2 cursor-pointer"
            style={{ fontSize: "22px", color: "#536AE3" }}
            onClick={() => onInfoClick()}
          >
            info
          </span>
        </div>
        <p>{content.overview}</p>
        <a href="#" className="btn rounded-5" style={{ backgroundColor: "#E4E8FB" }} onClick={onExpand}>
          Read more
        </a>
      </div>
    );
  }
}

export default DailySummaryMarketSummaryCollapsed;
