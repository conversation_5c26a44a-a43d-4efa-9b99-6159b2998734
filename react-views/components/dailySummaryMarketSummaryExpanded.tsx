import React from "react";
import { MarketSummaryType } from "../pages/dailySummaryPage";
import DailySummaryMarketSummarySection from "./dailySummaryMarketSummarySection";

type DailySummaryMarketSummaryExpandedProps = {
  content: MarketSummaryType;
  fullDate?: string;
  onBack: () => void;
};

class DailySummaryMarketSummaryExpanded extends React.Component<DailySummaryMarketSummaryExpandedProps> {
  render(): JSX.Element {
    const { content, onBack, fullDate } = this.props;

    return (
      <div className="mb-5">
        <div className="row p-0 m-0 mb-md-5 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center"
              onClick={() => onBack()}
              style={{ fontSize: "24px" }}
            >
              arrow_back
            </span>
          </div>
        </div>

        <h3 className="text-lg font-semibold mb-3">Daily market summary</h3>
        <h6 className="mb-4" style={{ color: "#536AE3" }}>
          {fullDate}
        </h6>
        <div id="market-summary-expanded" className="mb-5">
          <div className="mb-5">
            <h5 className="mb-3">Summary</h5>
            <p>{content.overview}</p>
          </div>
          {content.sections?.map((section, index) => (
            <DailySummaryMarketSummarySection
              key={index}
              title={section.title}
              assetReturns={section.assetReturns}
              tickerSymbol={section.tickerSymbol}
              content={section.content}
              assetId={section.assetId}
              tag={section.tag}
            />
          ))}
        </div>
      </div>
    );
  }
}

export default DailySummaryMarketSummaryExpanded;
