import React from "react";
import AssetIcon from "./assetIcon";
import { GlobalContext } from "../contexts/globalContext";
import { ReturnsType } from "../pages/dailySummaryPage";
import { publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import { getPublicAssetIconUrl } from "../utils/universeUtil";
import DailySummaryMarketSummarySectionSubtitle from "./dailySummaryMarketSummarySectionSubtitle";

type PropsType = {
  title?: string; // Concatenation of asset name and ticker
  tickerSymbol?: string; // Stored section.ticker
  assetReturns?: ReturnsType; // Only if assetId exists
  content?: string; // Stored section.content
  assetId?: publicInvestmentUniverseConfig.PublicAssetType;
  tag?: string; // Section category tag when no ticker is available
};

class DailySummaryMarketSummarySection extends React.Component<PropsType> {
  render(): JSX.Element {
    const { title, tickerSymbol, assetReturns, content, assetId, tag } = this.props;
    const logoUrl = assetId
      ? getPublicAssetIconUrl(assetId)
      : "/images/icons/market-summary-asset-default-icon.svg";

    return (
      <div className="market-summary-section mb-5">
        <div className="m-0 my-2 d-flex align-items-center">
          <div className="p-0 mb-2 me-2 align-self-center text-center">
            <AssetIcon category={"stock"} iconUrl={logoUrl} size="lg" />
          </div>
          <div className="">
            <div className="d-flex align-items-center mb-1">
              <h6 className="fw-bold m-0">{title}</h6>
            </div>
            <DailySummaryMarketSummarySectionSubtitle
              tickerSymbol={tickerSymbol}
              assetReturns={assetReturns}
              tag={tag}
            />
          </div>
        </div>
        <p>{content}</p>
      </div>
    );
  }
}

DailySummaryMarketSummarySection.contextType = GlobalContext;

export default DailySummaryMarketSummarySection;
