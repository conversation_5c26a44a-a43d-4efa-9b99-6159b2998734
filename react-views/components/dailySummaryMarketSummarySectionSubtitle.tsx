import React from "react";
import { GlobalContext } from "../contexts/globalContext";
import { ReturnsType } from "../pages/dailySummaryPage";

type PropsType = {
  tickerSymbol?: string; // Stored section.ticker
  assetReturns?: ReturnsType; // Only if assetId exists
  tag?: string; // Section category tag when no ticker is available
};

class dailySummaryMarketSummarySectionSubtitle extends React.Component<PropsType> {
  render(): JSX.Element {
    const { tickerSymbol, assetReturns, tag } = this.props;
    const subtitle = tickerSymbol?.length > 0 ? tickerSymbol : tag;

    const performanceColor = assetReturns?.upBy ? "success" : "danger";
    const performanceSign =
      !assetReturns?.upBy && !assetReturns?.downBy ? (
        <></>
      ) : assetReturns.upBy ? (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          arrow_drop_up
        </span>
      ) : (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          arrow_drop_down
        </span>
      );

    return (
      <div className="d-flex t-875">
        {subtitle && <div className="fw-boldtext-nowrap text-muted me-2">{subtitle}</div>}
        {assetReturns && (
          <div className={`d-flex justify-content-end text-${performanceColor} fw-bold`}>
            {performanceSign}
            {assetReturns.upBy ?? assetReturns?.downBy}
          </div>
        )}
      </div>
    );
  }
}

dailySummaryMarketSummarySectionSubtitle.contextType = GlobalContext;

export default dailySummaryMarketSummarySectionSubtitle;
