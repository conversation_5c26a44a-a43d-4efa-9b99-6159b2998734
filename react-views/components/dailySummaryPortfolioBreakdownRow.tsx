import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";
import AssetIcon from "./assetIcon";
import { GlobalContext } from "../contexts/globalContext";
import { ReturnsType } from "../pages/dailySummaryPage";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  category: "etf" | "stock";
  advancedName: string;
  investmentAmountWithCurrency?: string;
  allocation: string;
  logoUrl: string;
  performance: ReturnsType;
  simpleName: string;
  onAssetClick: () => void;
  assetCommonId: investmentUniverseConfig.AssetType;
  minimal: boolean;
};

const { ASSET_CONFIG } = investmentUniverseConfig;

class DailySummaryPortfolioBreakdownRow extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-75 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 4px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  render(): JSX.Element {
    const {
      advancedName,
      allocation,
      investmentAmountWithCurrency,
      logoUrl,
      performance,
      simpleName,
      onAssetClick,
      category,
      assetCommonId,
      minimal
    } = this.props;

    const assetDetails = ASSET_CONFIG[assetCommonId];

    const performanceColor = performance.upBy ? "success" : "danger";
    const performanceSign =
      !performance.upBy && !performance.downBy ? (
        <></>
      ) : performance.upBy ? (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          arrow_drop_up
        </span>
      ) : (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          arrow_drop_down
        </span>
      );

    const allocationPercentageComp = (
      <span className="align-self-center cursor-pointer">
        <OverlayTrigger
          placement="bottom"
          overlay={
            <Popover id="popover-explanation">
              <Popover.Content>
                <span>Refers to holding allocation in portfolio</span>
              </Popover.Content>
            </Popover>
          }
        >
          <span>{allocation}</span>
        </OverlayTrigger>
      </span>
    );

    return (
      <div className="row m-0 my-2 cursor-pointer" onClick={onAssetClick}>
        <div className="col-2 p-0 mb-2 align-self-center text-center">
          <div className="position-relative" style={{ width: "fit-content" }}>
            <AssetIcon category={category} iconUrl={logoUrl} size="lg" />
            {DailySummaryPortfolioBreakdownRow._getLabel(category)}
          </div>
        </div>
        <div className="col-7 pt-md-3 ps-md-2 pt-3 ps-4">
          <div className="d-flex align-items-center mb-1">
            <h6 className="fw-bold m-0">{simpleName}</h6>
          </div>
          <div className="d-flex fw-bold t-875 text-nowrap text-muted">
            {advancedName}
            <span className="px-1 fw-bolder align-self-center">•</span>
            {minimal ? (
              <span className="text-truncate">{assetDetails.shortDescription}</span>
            ) : (
              <span>
                {/* If the portfolio is real than display next to the investment amount the allocation percentage. */}
                {allocationPercentageComp}
              </span>
            )}
          </div>
        </div>
        <div className="col-3 text-center align-self-center">
          {/* Investment Amount & Returns */}
          {investmentAmountWithCurrency && !minimal && (
            <div className="d-flex flex-row align-items-middle justify-content-end">
              <span className="fw-bold d-block pe-1">{investmentAmountWithCurrency}</span>
            </div>
          )}
          {/* If the portfolio is real display on the bottom the returns and for virtuals the allocation percentage. */}
          {/* For virtual portfolios we don't have a way to calculate asset money-weighted returns. */}
          {
            <OverlayTrigger
              placement="bottom"
              overlay={
                <Popover id="popover-explanation">
                  <Popover.Content>
                    <span>Money-weighted return since you first bought this ETF.</span>
                  </Popover.Content>
                </Popover>
              }
            >
              <div className={`d-flex justify-content-end text-${performanceColor} fw-bold`}>
                {performanceSign}
                {performance.upBy ?? performance.downBy}
              </div>
            </OverlayTrigger>
          }
          {/* End Investment Amount & Returns */}
        </div>
      </div>
    );
  }
}

DailySummaryPortfolioBreakdownRow.contextType = GlobalContext;

export default DailySummaryPortfolioBreakdownRow;
