import React from "react";
import {
  DailySummaryPortfolioType,
  PortfolioKeyType,
  ReturnsType,
  SavingsInterestDetailsType
} from "../pages/dailySummaryPage";
import DailySummaryPortfolioCardReturns from "./dailySummaryPortfolioCardReturns";
import DailySummaryPortfolioCardSavings from "./dailySummaryPortfolioCardSavings";

type PropsType = {
  activeCard: PortfolioKeyType;
  onChangeActiveCard: (activeCard: PortfolioKeyType) => void;
  portfolio: DailySummaryPortfolioType;
};

export const PORTFOLIO_CARD_CONFIG: Record<PortfolioKeyType, { label: string; order: number; url?: string }> = {
  cash: { label: "Cash", order: 4, url: "/investor/cash" },
  holdings: { label: "Investments", order: 2, url: "/investor/investments" },
  savings: { label: "Savings", order: 3, url: "/investor/cash?selectedAccount=savings" },
  total: { label: "Total account value", order: 1 }
};

class DailySummaryPortfolioCardCarousel extends React.Component<PropsType> {
  private _handlePrevClick = () => {
    const { activeCard, onChangeActiveCard } = this.props;

    const previousCard = Object.entries(PORTFOLIO_CARD_CONFIG).find(
      ([, value]) => value.order === PORTFOLIO_CARD_CONFIG[activeCard].order - 1
    )?.[0];

    onChangeActiveCard(previousCard as PortfolioKeyType);
  };

  private _handleNextClick = () => {
    const { activeCard, onChangeActiveCard } = this.props;

    const nextCard = Object.entries(PORTFOLIO_CARD_CONFIG).find(
      ([, value]) => value.order === PORTFOLIO_CARD_CONFIG[activeCard].order + 1
    )?.[0];

    onChangeActiveCard(nextCard as PortfolioKeyType);
  };

  render(): JSX.Element {
    const { portfolio, activeCard } = this.props;
    const portfolioEntries = Object.entries(portfolio);

    return (
      <div className="d-flex align-items-center justify-content-center position-relative px-lg-2 px-md-5 px-0">
        <button
          className={`btn btn-clean position-absolute start-0 ${
            PORTFOLIO_CARD_CONFIG[activeCard].order === 1 ? "d-none" : ""
          }`}
          style={{
            height: "100%",
            backgroundColor: "rgba(220, 226, 253, 0.5)",
            borderRadius: "0px 16px 16px 0px"
          }}
          onClick={this._handlePrevClick}
        >
          <i className="fa-solid text-primary fa-chevron-left" />
        </button>

        <div className="carousel-container" style={{ width: "85%", overflow: "hidden" }}>
          <div
            className="carousel-slide"
            style={{
              transform: `translateX(-${(PORTFOLIO_CARD_CONFIG[activeCard].order - 1) * 100}%)`,
              transition: "transform 0.3s ease-in-out",
              display: "flex"
            }}
          >
            {portfolioEntries.map(([key, value]) => {
              const { label, order, url } = PORTFOLIO_CARD_CONFIG[key as PortfolioKeyType];
              return (
                <div
                  key={key}
                  className="daily-summary-card bg-white d-flex flex-column justify-content-center align-items-center shadow-sm px-4 py-5"
                  style={{
                    minWidth: "100%",
                    order: order,
                    cursor: url ? "pointer" : "inherit"
                  }}
                  onClick={() => url && (window.location.href = url)}
                >
                  <p className="mb-2 fw-light" style={{ color: "#536AE3" }}>
                    {label}
                  </p>
                  <h1 className="fw-bolder m-0">{value.displayValue}</h1>
                  {key === "holdings" && <DailySummaryPortfolioCardReturns returns={value as {} & ReturnsType} />}
                  {key === "savings" && (
                    <DailySummaryPortfolioCardSavings savings={value as {} & SavingsInterestDetailsType} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <button
          className={`btn btn-clean position-absolute end-0 ${
            PORTFOLIO_CARD_CONFIG[activeCard].order === 4 ? "d-none" : ""
          }`}
          style={{
            height: "100%",
            backgroundColor: "rgba(220, 226, 253, 0.5)",
            borderRadius: "16px 0px 0px 16px"
          }}
          onClick={this._handleNextClick}
        >
          <i className="fa-solid text-primary fa-chevron-right" />
        </button>
      </div>
    );
  }
}

export default DailySummaryPortfolioCardCarousel;
