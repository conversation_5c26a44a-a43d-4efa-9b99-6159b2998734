import React from "react";
import { ReturnsType } from "../pages/dailySummaryPage";

type PropsType = { returns: ReturnsType };

const DARK_GREEN_HEX = "#23846A";
const DARK_RED_HEX = "#D63C3C";

class DailySummaryPortfolioCardReturns extends React.Component<PropsType> {
  render(): JSX.Element {
    const { returns } = this.props;

    if (!returns.upBy && !returns.downBy) {
      return null;
    }

    return returns.upBy ? (
      <div
        className={"d-flex border-radius-xxl my-2 p-2 w-fit-content"}
        style={{
          border: "1px solid #DCFAF1"
        }}
      >
        <i
          className="material-symbols-outlined align-self-center text-decoration-none"
          style={{ color: DARK_GREEN_HEX }}
        >
          arrow_drop_up
        </i>
        <p className={"mb-0"} style={{ color: DARK_GREEN_HEX }}>
          {returns.upBy}
        </p>
      </div>
    ) : (
      <div
        className={"d-flex border-radius-xxl my-2 p-2 w-fit-content"}
        style={{
          border: "1px solid #FFE6E6"
        }}
      >
        <i
          className="material-symbols-outlined align-self-center text-decoration-none"
          style={{ color: DARK_RED_HEX }}
        >
          arrow_drop_down
        </i>
        <p className={"mb-0"} style={{ color: DARK_RED_HEX }}>
          {returns.downBy}
        </p>
      </div>
    );
  }
}

export default DailySummaryPortfolioCardReturns;
