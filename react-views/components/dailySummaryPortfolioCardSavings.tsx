import React from "react";
import { SavingsInterestDetailsType } from "../pages/dailySummaryPage";

type PropsType = { savings: SavingsInterestDetailsType };

const DARK_BLUE_HEX = "#536AE3";
const LIGHT_BLUE_HEX = "#F1F3FD";
const DARK_ORANGE_HEX = "#C78830";
const LIGHT_ORANGE_HEX = "#FFEDBF";

class dailySummaryPortfolioCardSavings extends React.Component<PropsType> {
  render(): JSX.Element {
    const { savings } = this.props;

    const mainColor = savings.estimated ? DARK_ORANGE_HEX : DARK_BLUE_HEX;
    const backgroundColor = savings.estimated ? LIGHT_ORANGE_HEX : LIGHT_BLUE_HEX;

    if (!savings.unrealisedMonthlyInterest && !savings.dailyInterest) {
      return null;
    }

    return (
      <div
        className={"d-flex border-radius-xxl my-2 p-1 w-fit-content align-items-center"}
        style={{ backgroundColor: backgroundColor }}
      >
        <i
          className="material-symbols-outlined align-self-center text-decoration-none ms-2"
          style={{ color: mainColor, fontSize: "16px" }}
        >
          humidity_percentage
        </i>
        <p className="mb-0 mx-2" style={{ color: mainColor }}>
          {savings.unrealisedMonthlyInterest}
        </p>
        <p
          className="mb-0 px-2 border-radius-xxl"
          style={{ color: mainColor, backgroundColor: "#fff", lineHeight: "2rem" }}
        >
          {savings.dailyInterest}
        </p>
      </div>
    );
  }
}

export default dailySummaryPortfolioCardSavings;
