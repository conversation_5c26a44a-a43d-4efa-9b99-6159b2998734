import React from "react";
import { Line } from "react-chartjs-2";
import {
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  ChartOptions
} from "chart.js";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const MIDDLE_DATA_POINT_INDEX = 3;

type DataPoint = {
  value: number;
  label: string;
  isOnlyForChartLabel?: boolean;
};

type PropsType = {
  active: boolean;
  activeIndex: number;
  data: DataPoint[];
};

class DailySummaryPortfolioChart extends React.Component<PropsType> {
  private readonly containerRef: React.RefObject<HTMLDivElement>;

  constructor(props: PropsType) {
    super(props);
    this.containerRef = React.createRef();
  }

  componentDidMount() {
    this.centerChartOnActiveIndex();
  }

  componentDidUpdate(prevProps: PropsType) {
    if (prevProps.activeIndex !== this.props.activeIndex) {
      this.centerChartOnActiveIndex();
    }
  }

  centerChartOnActiveIndex = () => {
    const container = this.containerRef.current;
    if (!container) return;

    const { activeIndex } = this.props;
    const pointWidth = 150;
    const containerWidth = container.clientWidth;
    const scrollPosition = activeIndex * pointWidth - containerWidth / 2 + pointWidth / 2;

    container.scrollTo({
      left: scrollPosition,
      behavior: "smooth"
    });
  };

  getChartData = () => {
    const { data, activeIndex } = this.props;

    const startIndex = Math.max(0, activeIndex - 3);
    const endIndex = Math.min(data.length - 1, activeIndex + 3);
    const visibleData = data.slice(startIndex, endIndex + 1);

    const labels = visibleData.map(({ label }) => label.split("\n"));
    const values = visibleData.map(({ value, isOnlyForChartLabel }) => (isOnlyForChartLabel ? null : value));

    return {
      labels,
      datasets: [
        {
          label: "Value",
          data: values,
          borderColor: "rgba(83, 106, 227, 1)",
          backgroundColor: "rgba(83, 106, 227, 1)",
          pointRadius: visibleData.map((point, index) => {
            if (point.isOnlyForChartLabel) return 0;
            return index === MIDDLE_DATA_POINT_INDEX ? 8 : 0;
          }),
          spanGaps: true
        }
      ]
    };
  };

  getChartOptions = () => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      clip: false,
      animation: {
        duration: 0
      },
      transitions: {
        active: {
          animation: {
            duration: 0
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: false
        }
      },
      tension: 0.5,
      scales: {
        x: {
          grid: {
            display: false,
            drawBorder: false
          },
          border: {
            display: false,
            drawBorder: false
          },
          ticks: {
            display: true,
            color: (context: any) => {
              return context.index === MIDDLE_DATA_POINT_INDEX ? "#000" : "rgba(117, 117, 117, 1)";
            },
            font: (context: any) => {
              return {
                size: 16,
                family: "'Poppins'",
                weight: context.index === MIDDLE_DATA_POINT_INDEX ? "600" : "300"
              };
            }
          }
        },
        y: {
          display: false,
          grid: {
            display: false,
            drawBorder: false
          }
        }
      },
      layout: {
        padding: 10
      },
      onClick: () => {}
    };
  };

  render(): JSX.Element {
    const { active } = this.props;

    return (
      <div className={`position-relative mt-5 ${!active ? "d-none" : ""}`} style={{ height: "400px" }}>
        <div className="mx-auto p-4 position-absolute w-100">
          <div style={{ height: "300px" }}>
            <Line data={this.getChartData()} options={this.getChartOptions() as ChartOptions<"line">} />
            <div
              style={{
                position: "absolute",
                left: "50%",
                top: "50%",
                transform: "translate(-50%, -50%)",
                backgroundColor: "rgba(83, 106, 227, 0.1)",
                width: "80px",
                height: "100%",
                borderRadius: "64px"
              }}
            />
          </div>
        </div>
      </div>
    );
  }
}

export default DailySummaryPortfolioChart;
