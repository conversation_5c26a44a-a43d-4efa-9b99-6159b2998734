import React from "react";
import { SentimentLabelEnum, SentimentScoreWithLabelsType } from "../pages/dailySummaryPage";
import DailySummarySentimentScoreDoughnutChart from "./dailySummarySentimentScoreDoughnutChart";
import DailySummarySentimentScoreProgressBar from "./dailySummarySentimentScoreProgressBar";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  sentimentScore: SentimentScoreWithLabelsType;
  onInfoClick: () => void;
  onClickAnalyst: () => void;
  onClickNews: () => void;
  onClickPriceMomentum: () => void;
};

export const SENTIMENT_SCORE_CONFIG: Record<
  SentimentLabelEnum,
  { displayLabel: string; color: string; lightColor: string }
> = {
  optimal: { color: "#23846A", lightColor: "#DCFAF1", displayLabel: "Optimal" },
  suboptimal: { color: "#C78830", lightColor: "#FFEDBF", displayLabel: "Fair" },
  underperforming: { color: "#D63C3C", lightColor: "#FFE6E6", displayLabel: "Poor" }
};

class DailySummarySentimentScore extends React.Component<PropsType> {
  render(): JSX.Element {
    const { sentimentScore, onInfoClick, onClickAnalyst, onClickNews, onClickPriceMomentum } = this.props;
    const { user } = this.context as GlobalContextType;

    return (
      <div className="pb-4">
        <div className="d-flex align-items-center pb-4 mb-1">
          <h4 className="text-xl font-bold mb-0">Wealthyhood sentiment score</h4>
          <span
            className="material-symbols-outlined ms-2 cursor-pointer"
            style={{ fontSize: "22px", color: "#536AE3" }}
            onClick={() => onInfoClick()}
          >
            info
          </span>
        </div>
        <DailySummarySentimentScoreDoughnutChart sentimentScore={sentimentScore} />
        <DailySummarySentimentScoreProgressBar
          type={"News sentiment"}
          label={sentimentScore.news?.label}
          score={sentimentScore.news?.score}
          onClick={onClickNews}
        />
        <DailySummarySentimentScoreProgressBar
          type={"Analyst sentiment"}
          label={sentimentScore.analyst?.label}
          score={sentimentScore.analyst?.score}
          onClick={onClickAnalyst}
        />
        {user.isPriceMomentumSentimentEnabled && (
          <DailySummarySentimentScoreProgressBar
            type={"Price momentum"}
            label={sentimentScore.priceMomentum?.label}
            score={sentimentScore.priceMomentum?.score}
            onClick={onClickPriceMomentum}
          />
        )}
      </div>
    );
  }
}

DailySummarySentimentScore.contextType = GlobalContext;

export default DailySummarySentimentScore;
