import React from "react";
import { Doughnut } from "react-chartjs-2";
import { ArcElement, Chart as ChartJS, Tooltip } from "chart.js";
import { SentimentScoreWithLabelsType } from "../pages/dailySummaryPage";
import { SENTIMENT_SCORE_CONFIG } from "./dailySummarySentimentScore";

ChartJS.register(ArcElement, Tooltip);

type PropsType = { sentimentScore: SentimentScoreWithLabelsType };

class DailySummarySentimentScoreDoughnutChart extends React.Component<PropsType> {
  render(): JSX.Element {
    const { sentimentScore } = this.props;

    const data = {
      datasets: [
        {
          data: [sentimentScore.total.score, 100 - sentimentScore.total.score],
          backgroundColor: [SENTIMENT_SCORE_CONFIG[sentimentScore.total.label].color, "transparent"],
          borderWidth: 0,
          cutout: "70%",
          borderRadius: {
            outerStart: 30,
            outerEnd: 30,
            innerStart: 30,
            innerEnd: 30
          }
        }
      ]
    };

    const backgroundChartData = {
      datasets: [
        {
          data: [100],
          backgroundColor: [SENTIMENT_SCORE_CONFIG[sentimentScore.total.label].lightColor, "#fff"],
          borderWidth: 0,
          cutout: "70%",
          borderRadius: {
            outerStart: 30,
            outerEnd: 30,
            innerStart: 30,
            innerEnd: 30
          }
        }
      ]
    };

    const options = {
      responsive: true,
      maintainAspectRatio: false,
      rotation: -90,
      circumference: 180,
      hover: { mode: null } as any,
      plugins: {
        tooltip: { enabled: false }
      },
      elements: {
        arc: {
          borderWidth: 0
        }
      }
    };

    return (
      <div
        className="d-flex justify-content-center position-relative noto-sans mb-4 py-4 mx-auto"
        style={{ height: "250px", width: "320px" }}
      >
        <div className={"position-absolute"} style={{ zIndex: 2 }}>
          <Doughnut data={data} options={options} />
        </div>
        <div className={"position-absolute"} style={{ zIndex: 1 }}>
          <Doughnut data={backgroundChartData} options={options} />
        </div>
        <div className="d-flex align-items-end px-4 w-100">
          <p
            style={{
              left: "32px",
              zIndex: 3
            }}
            className="position-absolute text-center text-muted fw-light"
          >
            0
          </p>
          <p
            style={{
              right: "28px",
              zIndex: 3
            }}
            className="position-absolute text-center text-muted fw-light"
          >
            100
          </p>
        </div>
        <h3
          style={{
            transform: "translate(-50%, 20%)",
            top: "50%",
            left: "50%",
            zIndex: 3,
            color: SENTIMENT_SCORE_CONFIG[sentimentScore.total.label].color
          }}
          className="align-self-start position-absolute"
        >
          {sentimentScore.total.score}
        </h3>
        <h5
          style={{
            transform: "translate(-50%, 20%)",
            top: "65%",
            left: "50%",
            zIndex: 3
          }}
          className="align-self-start position-absolute"
        >
          {SENTIMENT_SCORE_CONFIG[sentimentScore.total.label].displayLabel}
        </h5>
      </div>
    );
  }
}

export default DailySummarySentimentScoreDoughnutChart;
