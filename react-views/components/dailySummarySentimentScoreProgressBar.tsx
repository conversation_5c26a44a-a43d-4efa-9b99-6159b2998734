import React from "react";
import { SentimentLabelEnum } from "../pages/dailySummaryPage";
import { SENTIMENT_SCORE_CONFIG } from "./dailySummarySentimentScore";

type PropsType = { type: string; score: number; label: SentimentLabelEnum; onClick: () => void };

class DailySummarySentimentScoreProgressBar extends React.Component<PropsType> {
  render(): JSX.Element {
    const { type, score, label, onClick } = this.props;

    return (
      <div className="row p-0 m-0 mb-4 w-100 cursor-pointer" onClick={() => onClick()}>
        <div className="m-0 p-0 w-100">
          <div className={"d-flex flex-row justify-content-between"}>
            <p className={"mb-0"}>{type}</p>
            <p className={"mb-0"} style={{ color: SENTIMENT_SCORE_CONFIG[label]?.color ?? "#536AE3" }}>
              {score ?? "-"}
            </p>
          </div>
          <div
            className="progress-bar-left mt-2"
            style={{ backgroundColor: SENTIMENT_SCORE_CONFIG[label]?.lightColor ?? "#F1F3FD" }}
          >
            <div
              className="progress-left"
              style={{
                width: `${score ?? "1"}%`,
                backgroundColor: SENTIMENT_SCORE_CONFIG[label]?.color ?? "#536AE3"
              }}
            />
          </div>
        </div>
        <div className="col-4 m-0 p-0 d-flex justify-content-end" />
      </div>
    );
  }
}

export default DailySummarySentimentScoreProgressBar;
