import React from "react";
import { TodayMarketsType } from "../pages/dailySummaryPage";
import DailySummaryMarketIndex from "./dailySummaryMarketIndex";

type PropsType = { todayMarkets: TodayMarketsType };

class DailySummaryTodayMarkets extends React.Component<PropsType> {
  render(): JSX.Element {
    const { todayMarkets } = this.props;

    return (
      <div className={"py-4"}>
        <h4 className="text-lg font-semibold mb-4">Today's markets</h4>
        <hr className={"my-0 fade-line"} />
        <div className="markets-carousel my-1">
          <div className="markets-carousel-group">
            {todayMarkets.map((market, index) => (
              <DailySummaryMarketIndex key={index + "1"} indexName={market.label} returns={market.returns} />
            ))}
          </div>
          <div aria-hidden className="markets-carousel-group">
            {todayMarkets.map((market, index) => (
              <DailySummaryMarketIndex key={index + "2"} indexName={market.label} returns={market.returns} />
            ))}
          </div>

          <style>
            {`
              @keyframes scrolling {
                0% {
                  transform: translateX(0);
                }
                100% {
                  transform: translateX(-100%);
                }
              }
            `}
          </style>
        </div>
        <hr className={"my-0 fade-line"} />
      </div>
    );
  }
}

export default DailySummaryTodayMarkets;
