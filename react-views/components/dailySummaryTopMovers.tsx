import React from "react";
import { InvestmentsWithPerformanceType, InvestmentWithPerformanceType } from "../pages/dailySummaryPage";
import { getAssetIconUrl } from "../utils/universeUtil";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { eventEmitter, EVENTS } from "../utils/eventService";
import DailySummaryPortfolioBreakdownRow from "./dailySummaryPortfolioBreakdownRow";
import TrendingUpIcon from "./icons/trendingUpIcon";
import TrendingDownIcon from "./icons/trendingDownIcon";

const { ASSET_CONFIG } = investmentUniverseConfig;

const TOP_MOVERS_TO_SHOW_WHEN_COLLAPSED = 3;

type PropsType = {
  allInvestments: InvestmentsWithPerformanceType;
  topPerformers?: InvestmentsWithPerformanceType;
  worstPerformers?: InvestmentsWithPerformanceType;
  expanded: boolean;
  onBack?: () => void;
  fullDate?: string;
  onExpand?: () => void;
};

class DailySummaryTopMovers extends React.Component<PropsType> {
  _renderPerformerRow(performer: InvestmentWithPerformanceType, minimal: boolean) {
    const assetCommonId = performer.assetId;
    return (
      <DailySummaryPortfolioBreakdownRow
        key={`performance-row-${assetCommonId}`}
        advancedName={ASSET_CONFIG[assetCommonId].tickerWithCurrency}
        investmentAmountWithCurrency={performer.value}
        allocation={performer.weight}
        logoUrl={getAssetIconUrl(assetCommonId)}
        category={ASSET_CONFIG[assetCommonId].category}
        performance={{ upBy: performer.upBy, downBy: performer.downBy }}
        simpleName={ASSET_CONFIG[assetCommonId].simpleName}
        onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetCommonId)}
        assetCommonId={assetCommonId}
        minimal={minimal}
      />
    );
  }

  renderExpandedView() {
    const { allInvestments, onBack, fullDate } = this.props;

    return (
      <div className="mt-6">
        <div className="row p-0 m-0 mb-md-5 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center"
              onClick={() => onBack()}
              style={{ fontSize: "24px" }}
            >
              arrow_back
            </span>
          </div>
        </div>

        <h3 className="text-lg font-semibold mb-3">Your investments</h3>
        <h6 className="mb-4" style={{ color: "#536AE3" }}>
          {fullDate}
        </h6>
        <div className="space-y-4">
          {allInvestments?.map((performer) => this._renderPerformerRow(performer, false))}
        </div>
      </div>
    );
  }

  renderCollapsedView() {
    const { topPerformers, worstPerformers, onExpand } = this.props;

    return (
      <div className="py-4">
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h4 className="text-lg font-semibold">Your top movers</h4>
          <a
            href="#"
            className="btn bg-transparent border-0 p-0"
            onClick={() => onExpand()}
            style={{ color: "#536AE3" }}
          >
            See all
          </a>
        </div>
        {topPerformers?.length > 0 && (
          <>
            <h5 className="d-flex align-items-center" style={{ color: "#23846A", fontSize: "18px", gap: "8px" }}>
              Best <TrendingUpIcon />
            </h5>
            <div className="my-4">
              {topPerformers
                .slice(0, TOP_MOVERS_TO_SHOW_WHEN_COLLAPSED)
                .map((performer) => this._renderPerformerRow(performer, true))}
            </div>
          </>
        )}
        {worstPerformers?.length > 0 && (
          <>
            <h5 className="d-flex align-items-center" style={{ color: "#D63C3C", fontSize: "18px", gap: "8px" }}>
              Worst <TrendingDownIcon />
            </h5>
            <div className="my-4">
              {worstPerformers
                .slice(0, TOP_MOVERS_TO_SHOW_WHEN_COLLAPSED)
                .map((performer) => this._renderPerformerRow(performer, true))}
            </div>
          </>
        )}
      </div>
    );
  }

  render(): JSX.Element | null {
    const { expanded, topPerformers, worstPerformers } = this.props;

    if (!topPerformers?.length && !worstPerformers?.length) {
      return null;
    }

    return expanded ? this.renderExpandedView() : this.renderCollapsedView();
  }
}

export default DailySummaryTopMovers;
