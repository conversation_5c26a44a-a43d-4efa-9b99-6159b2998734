import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AssetClassNavPills from "./assetClassNavPills";
import PortfolioBreakdownRow from "./portfolioBreakdownRow";
import { mapHoldingsToAllocationCategoryFormat, mapHoldingsToAllocationClassFormat } from "../utils/portfolioUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { HoldingsType } from "../../models/Portfolio";
import AssetClassPie from "./assetClassPie";
import AssetCategoryPie from "./assetCategoryPie";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";
import { formatCurrency } from "../utils/currencyUtil";
import { getAssetIconUrl } from "../utils/universeUtil";
import { AssetCategoryType, AssetType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import AssetCategoryNavPills from "./assetCategoryNavPills";
import { GroupByMethodEnum } from "./groupBy";

const { AssetClassArray, AssetCategoryArray, ASSET_CONFIG, InvestmentSectorArray } = investmentUniverseConfig;

export declare type AllocationCategoryType = {
  assetCategory: {
    [key in AssetCategoryType]?: number;
  };
  assets: {
    [key in AssetType]?: number;
  };
};

type PropsType = {
  holdingsArray: (HoldingsType & { sinceBuyReturns?: number })[];
  onAssetClick: (assetCommonId: investmentUniverseConfig.AssetType) => void;
  groupByMethod?: GroupByMethodEnum;
};
type StateType = {
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  activeAssetCategory: investmentUniverseConfig.AssetCategoryType;
};

class DashboardPortfolioBreakdown extends React.Component<PropsType, StateType> {
  private _holdingsDict: Record<
    investmentUniverseConfig.AssetType,
    { asset: InvestmentProductDocument; quantity: number; sinceBuyReturns?: number }
  >;

  constructor(props: PropsType) {
    super(props);
    this.state = {
      activeAssetClass: this._getHeldAssetClasses()[0],
      activeAssetCategory: this._getHeldAssetCategories()[0] ?? "stock"
    };
    this._holdingsDict = this._holdingsArrayToDict(props.holdingsArray);
  }

  private _getAssetInvestedAmountWithCurrency = (assetId: investmentUniverseConfig.AssetType): string => {
    const { user, locale } = this.context as GlobalContextType;

    const { quantity, asset } = this._holdingsDict[assetId];
    const investmentAmount = quantity * (asset?.currentTicker?.pricePerCurrency[user.currency] || 0);

    return formatCurrency(investmentAmount, user.currency, locale);
  };

  private _getHeldAssetClasses = (): investmentUniverseConfig.AssetClassType[] => {
    const { holdingsArray } = this.props;
    const assetClassUniverse = new Set(
      holdingsArray.map(({ assetCommonId }) => ASSET_CONFIG[assetCommonId].assetClass)
    );

    return AssetClassArray.filter((assetClassKey) => assetClassUniverse.has(assetClassKey));
  };

  private _getHeldAssetCategories = (): investmentUniverseConfig.AssetCategoryType[] => {
    const { holdingsArray } = this.props;
    const assetCategoryUniverse = new Set(
      holdingsArray.map(({ assetCommonId }) => ASSET_CONFIG[assetCommonId].category)
    );

    return AssetCategoryArray.filter((assetCategoryKey) => assetCategoryUniverse.has(assetCategoryKey));
  };

  private _holdingsArrayToDict = (
    holdingsArray: (HoldingsType & { sinceBuyReturns?: number })[]
  ): Record<
    investmentUniverseConfig.AssetType,
    { asset: InvestmentProductDocument; quantity: number; sinceBuyReturns?: number }
  > => {
    const holdingsDict: any = {};
    holdingsArray.forEach(({ asset, assetCommonId, quantity, sinceBuyReturns }) => {
      holdingsDict[assetCommonId] = { asset, quantity, sinceBuyReturns };
    });
    return holdingsDict;
  };

  private _setActiveAssetClass = (activeAssetClass: investmentUniverseConfig.AssetClassType): void => {
    this.setState({ activeAssetClass });
  };

  private _setActiveAssetCategory = (activeAssetCategory: investmentUniverseConfig.AssetCategoryType): void => {
    this.setState({ activeAssetCategory });
  };

  private static _getDisplayedAssetClassAllocation(allocation: investmentUniverseConfig.AllocationType): {
    [key in investmentUniverseConfig.AssetClassType]?: number;
  } {
    return Object.fromEntries(
      Object.entries(allocation.assetClasses)
        .filter(([, assetClassAllocation]) => assetClassAllocation > 0)
        .map(([assetClass, assetClassAllocation]) => [assetClass, assetClassAllocation * 100])
    );
  }

  private static _getDisplayedCategoryAllocation(allocation: AllocationCategoryType): {
    [key in investmentUniverseConfig.AssetCategoryType]?: number;
  } {
    return Object.fromEntries(
      Object.entries(allocation.assetCategory)
        .filter(([, assetClassAllocation]) => assetClassAllocation > 0)
        .map(([assetClass, assetClassAllocation]) => [assetClass, assetClassAllocation * 100])
    );
  }
  private _getFilteredAssetsByType(assetCategory: string): investmentUniverseConfig.AssetType[] {
    const filteredAssets: investmentUniverseConfig.AssetType[] = Object.entries(
      ConfigUtil.getInvestmentUniverseAssets((this.context as GlobalContextType)?.user?.companyEntity)
    )
      .filter(([, assetConfig]) => assetConfig.category === assetCategory)
      .map(([assetType]) => assetType as investmentUniverseConfig.AssetType);

    return filteredAssets;
  }

  renderAssetRows = (
    assets: investmentUniverseConfig.AssetType[],
    allocation: investmentUniverseConfig.AllocationType,
    onAssetClick: (assetCommonId: investmentUniverseConfig.AssetType) => void
  ) => {
    return assets
      .filter((assetCommonId) => allocation.assets[assetCommonId] > 0)
      .sort((a, b) => allocation.assets[b] - allocation.assets[a])
      .map((assetCommonId) => (
        <PortfolioBreakdownRow
          advancedName={ASSET_CONFIG[assetCommonId].tickerWithCurrency}
          investmentAmountWithCurrency={this._getAssetInvestedAmountWithCurrency(assetCommonId)}
          allocationPercent={allocation.assets[assetCommonId]}
          logoUrl={getAssetIconUrl(assetCommonId)}
          category={ASSET_CONFIG[assetCommonId].category}
          performance={this._holdingsDict[assetCommonId]?.sinceBuyReturns}
          simpleName={ASSET_CONFIG[assetCommonId].simpleName}
          onAssetClick={(): void => onAssetClick(assetCommonId)}
          key={`performance-row-${assetCommonId}`}
        />
      ));
  };

  render(): JSX.Element {
    const { holdingsArray, groupByMethod, onAssetClick } = this.props;
    const { activeAssetClass, activeAssetCategory } = this.state;
    const { user } = this.context as GlobalContextType;
    const categoryAllocation = mapHoldingsToAllocationCategoryFormat(
      holdingsArray.map(({ asset, assetCommonId, quantity }) => ({
        asset,
        assetCommonId,
        quantity
      })),
      user.currency
    );
    const allocation = mapHoldingsToAllocationClassFormat(
      holdingsArray.map(({ asset, assetCommonId, quantity }) => ({
        asset,
        assetCommonId,
        quantity
      })),
      user.currency
    );
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses(user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors(user.companyEntity);
    const assets = Object.values(ASSET_CLASS_CONFIG)
      .map((assetClass) => assetClass.assets)
      .flat();

    const assetsFilteredByType = this._getFilteredAssetsByType(activeAssetCategory);

    return (
      <>
        {groupByMethod === GroupByMethodEnum.SingleList && (
          <>
            {/* Active Asset Class Performance Breakdown */}
            {this.renderAssetRows(assets, allocation, onAssetClick)}
          </>
        )}
        {groupByMethod === GroupByMethodEnum.AssetType && (
          <>
            {/* Asset Category Pie Chart */}
            <div className="row wh-card m-0 p-0">
              <div className="col p-0">
                <AssetCategoryPie
                  selectedCategory={activeAssetCategory}
                  allocation={DashboardPortfolioBreakdown._getDisplayedCategoryAllocation(categoryAllocation)}
                  onAssetCategoryClick={this._setActiveAssetCategory}
                />
              </div>
            </div>
            <div className="d-flex align-self-center justify-content-center">
              {/* Asset Classes Nav */}
              <AssetCategoryNavPills
                className={"mt-3 mb-3"}
                assetCategories={this._getHeldAssetCategories()}
                activeCategory={activeAssetCategory}
                onAssetTypeSelection={this._setActiveAssetCategory}
              />
              {/* End Asset Classes Nav */}
            </div>
            {/* End Asset Category Pie Chart */}
            {this.renderAssetRows(assetsFilteredByType, allocation, onAssetClick)}
          </>
        )}
        {groupByMethod === GroupByMethodEnum.AssetClassAndSector && (
          <>
            {/* Asset Class Pie Chart */}
            <div className="row wh-card m-0 p-0">
              <div className="col p-0">
                <AssetClassPie
                  selectedAssetClass={activeAssetClass}
                  allocation={DashboardPortfolioBreakdown._getDisplayedAssetClassAllocation(allocation)}
                  onAssetClassClick={this._setActiveAssetClass}
                />
              </div>
            </div>
            {/* End Asset Class Pie Chart */}
            <div className="row m-0">
              <div className="col p-0">
                <div className="d-flex align-self-center justify-content-center">
                  {/* Asset Classes Nav */}
                  <AssetClassNavPills
                    className={"mt-3 mb-3"}
                    assetClasses={this._getHeldAssetClasses()}
                    activeAssetClass={activeAssetClass}
                    onAssetClassSelection={this._setActiveAssetClass}
                  />
                  {/* End Asset Classes Nav */}
                </div>
                {/* Active Asset Class Performance Breakdown */}
                {activeAssetClass === "equities"
                  ? InvestmentSectorArray.map((sector) => {
                      const sectorAssets = ASSET_CLASS_CONFIG[activeAssetClass].assets.filter(
                        (asset) =>
                          ASSET_CONFIG[asset].sector === sector &&
                          holdingsArray.some((holding) => holding.assetCommonId === asset)
                      );
                      if (sectorAssets.length === 0) {
                        return null;
                      }

                      return (
                        <div className="row my-4" key={`onboarding-editable-mobile-sector-${sector}`}>
                          <div className="col h6 fw-bold">{SECTOR_CONFIG[sector].fieldName}</div>
                          {this.renderAssetRows(sectorAssets, allocation, onAssetClick)}
                        </div>
                      );
                    })
                  : this.renderAssetRows(ASSET_CLASS_CONFIG[activeAssetClass].assets, allocation, onAssetClick)}
                {/* End Active Asset Class Performance Breakdown */}
              </div>
            </div>
          </>
        )}
      </>
    );
  }
}

DashboardPortfolioBreakdown.contextType = GlobalContext;

export default DashboardPortfolioBreakdown;
