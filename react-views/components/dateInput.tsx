import React from "react";
import { dateFriendlyFormatToISO, dateIsValid } from "../utils/dateUtil";
import MaskedInput from "react-text-mask";

type PropsType = {
  handleChange: (event: any) => void;
  placeholder: string;
  inputName: string;
  date: string;
};

type StateType = {
  clickedOutsideInput: boolean;
};

class DateInput extends React.Component<PropsType, StateType> {
  private readonly wrapperRef: React.RefObject<HTMLDivElement>;

  constructor(props: PropsType) {
    super(props);
    this.state = {
      clickedOutsideInput: false
    };

    this.wrapperRef = React.createRef();
    this._handleClickOutside = this._handleClickOutside.bind(this);
  }

  componentDidMount() {
    document.addEventListener("mousedown", this._handleClickOutside);
  }

  componentWillUnmount() {
    document.removeEventListener("mousedown", this._handleClickOutside);
  }

  private _setClickedOutsideInput(clickedOutsideInput: boolean) {
    this.setState({ clickedOutsideInput });
  }

  private _handleClickOutside(event: any) {
    const { date } = this.props;

    if (date && this.wrapperRef && !(this.wrapperRef.current as any).contains(event.target)) {
      this._setClickedOutsideInput(true);
    }
  }

  render(): JSX.Element {
    const { date, handleChange, placeholder, inputName } = this.props;
    const { clickedOutsideInput } = this.state;

    return (
      <>
        <div className="position-relative my-2" ref={this.wrapperRef}>
          <MaskedInput
            mask={[/[0-3]/, /[0-9]/, "/", /[0-1]/, /[0-9]/, "/", /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/]}
            className="verification-input input-warning-icon"
            name={inputName}
            placeholder={placeholder}
            value={date}
            guide={true}
            onChange={handleChange}
          />
          <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
            event
          </span>
        </div>
        {date && clickedOutsideInput && !dateIsValid(new Date(dateFriendlyFormatToISO(date))) && (
          <span className="d-block text-danger pt-2">Date is not valid</span>
        )}
      </>
    );
  }
}

export default DateInput;
