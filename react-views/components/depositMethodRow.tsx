import React from "react";

type PropsType = {
  title: string;
  description: string;
  iconSrc: string;
  iconAlt: string;
  onClick: () => void;
  disabled?: boolean;
  comingSoon?: boolean;
};

class DepositMethodRow extends React.Component<PropsType> {
  render(): JSX.Element {
    const { title, description, iconSrc, iconAlt, onClick, disabled = false } = this.props;

    return (
      <div
        className={`d-flex justify-content-between mb-3 deposit-options-item ${!disabled && "cursor-pointer"}`}
        onClick={disabled ? undefined : onClick}
      >
        <div className="d-flex align-items-center">
          <span className="asset-icon border-light me-3 p-2">
            <img src={iconSrc} alt={iconAlt} width={"24px"} height={"24px"} />
          </span>
          <div className={"d-flex flex-column"}>
            <div className="d-flex align-items-center">
              <span className={"t-875"}>{title}</span>
              {disabled && (
                <span className="wh-coming-soon-card ms-2" style={{ fontSize: "10px" }}>
                  Coming soon
                </span>
              )}
            </div>
            <p className={"text-muted mb-0 t-75"}>{description}</p>
          </div>
        </div>
        <span
          className="material-icons align-self-center align-self-center mb-0"
          style={{
            fontSize: "30px",
            color: disabled ? "#9e9e9e" : "#171717"
          }}
        >
          chevron_right
        </span>
      </div>
    );
  }
}

export default DepositMethodRow;
