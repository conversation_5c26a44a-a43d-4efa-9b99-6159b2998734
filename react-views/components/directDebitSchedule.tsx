import React from "react";
import { getOnTheXthDateString } from "../utils/dateUtil";
import { DepositCashTransactionDocument } from "../../models/Transaction";
import { SavingsTopUpAutomationDocument, TopUpAutomationDocument } from "../../models/Automation";
import { MandateDocument } from "../../models/Mandate";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { entitiesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  automation: TopUpAutomationDocument | SavingsTopUpAutomationDocument;
  deposit: DepositCashTransactionDocument;
};

class DirectDebitSchedule extends React.Component<PropsType> {
  private _getScheduleTitle(): string {
    const { automation } = this.props;

    if (automation.category === "TopUpAutomation") {
      return "Repeating investment schedule";
    } else return "Monthly deposit schedule";
  }

  private _getAmountOfDaysToReachWealthyhood() {
    const { user } = this.context as GlobalContextType;
    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return "3 to 4";
    }
    return "4 to 5";
  }

  private _getSettingUpMandateExplanation(): string {
    const { automation } = this.props;

    if (automation.category === "TopUpAutomation") {
      return "We're setting up your Direct Debit mandate for your bank account. This is only for your first repeating investment.";
    } else
      return "We’re setting up your Direct Debit mandate for your bank account. This is only for your first monthly deposit.";
  }

  private _getExecutionStepContent(): JSX.Element {
    const { automation } = this.props;

    if (automation.category === "TopUpAutomation") {
      return (
        <>
          <h6 className="fw-bold">Order placed</h6>
          <p className="text-muted">Once your deposit is reconciled (up to one day), your order will be placed.</p>
        </>
      );
    } else
      return (
        <>
          <h6 className="fw-bold">Moved to interest account</h6>
          <p className="text-muted">
            Once your deposit is reconciled, the money is moved to your interest account.
          </p>
        </>
      );
  }

  render(): JSX.Element {
    const { deposit, automation } = this.props;

    return (
      <>
        {/* Action Title */}
        <h5 className="fw-bolder text-center mb-5">{this._getScheduleTitle()}</h5>
        {/* End Action Title */}
        <ol className="timeline">
          {deposit.shouldIncludeMandateStep && (
            <li
              className={`timeline-item ${
                (automation.mandate as MandateDocument).status === "Active"
                  ? "timeline-item-active"
                  : "timeline-item-half-active"
              }`}
            >
              <span className="timeline-item-icon | faded-white-icon">
                <i className="material-symbols-outlined text-primary align-self-center">rule_settings</i>
              </span>
              <div className="timeline-item-description">
                <h6 className="fw-bold">Setting up your mandate</h6>
                <p className="text-muted">{this._getSettingUpMandateExplanation()}</p>
              </div>
            </li>
          )}
          <li
            className={`timeline-item ${
              deposit.isDirectDebitPaymentCollected ? "timeline-item-active" : "timeline-item-inactive"
            }`}
          >
            <span className="timeline-item-icon | faded-white-icon">
              <i className="material-symbols-outlined text-primary align-self-center">assured_workload</i>
            </span>
            <div className="timeline-item-description">
              <h6 className="fw-bold">Bank account charged</h6>
              <p className="text-muted">
                {deposit.shouldIncludeMandateStep
                  ? "Your money leaves your bank account."
                  : `${getOnTheXthDateString({ capitalFirst: true }, automation.dayOfMonth)} of every month.`}
              </p>
            </div>
          </li>
          <li
            className={`timeline-item ${
              !deposit.isDirectDebitPaymentCollected
                ? "timeline-item-inactive"
                : deposit.isMoneyReceived
                ? "timeline-item-active"
                : "timeline-item-half-active"
            }`}
          >
            <span className="timeline-item-icon | faded-white-icon">
              <i className="material-symbols-outlined text-primary align-self-center">schedule</i>
            </span>
            <div className="timeline-item-description">
              <p className="text-muted">
                Due to Direct Debit timings, it usually takes {this._getAmountOfDaysToReachWealthyhood()} business
                days for your money to land at Wealthyhood after it leaves your bank accounts.
              </p>
            </div>
          </li>
          <li
            className={`timeline-item ${
              deposit.isMoneyReceived ? "timeline-item-active" : "timeline-item-inactive"
            }`}
          >
            <span className="timeline-item-icon | faded-white-icon">
              <i className="material-symbols-outlined text-primary align-self-center">payments</i>
            </span>
            <div className="timeline-item-description">
              <h6 className="fw-bold">Money received</h6>
              <p className="text-muted">Your money lands at Wealthyhood.</p>
            </div>
          </li>
          <li className={"timeline-item timeline-item-inactive"}>
            <span className="timeline-item-icon | faded-white-icon">
              <i className="material-symbols-outlined text-primary align-self-center">receipt_long</i>
            </span>
            <div className="timeline-item-description">{this._getExecutionStepContent()}</div>
          </li>
        </ol>
      </>
    );
  }
}

DirectDebitSchedule.contextType = GlobalContext;

export default DirectDebitSchedule;
