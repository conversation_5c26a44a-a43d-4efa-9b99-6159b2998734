import React, { Component } from "react";
import { Modal } from "react-bootstrap";
import { countriesConfig, entitiesConfig, localeConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../utils/currencyUtil";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { LinkedBankAccount } from "../types/bank";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { formatDateToDDMONYY } from "../utils/dateUtil";

type DirectDebitSetupProps = {
  selectedLinkedBankAccount: LinkedBankAccount;
  orderAmount: string;
  onSetupAutomation: () => Promise<void>;
  onViewDirectDebitInfo: () => void;
  description: string;
  investmentType?: "Repeating investment" | "Repeating savings";
};

class DirectDebitSetup extends Component<DirectDebitSetupProps> {
  renderUKMandateInfo = (): JSX.Element => {
    const { user, locale } = this.context as GlobalContextType;

    const { selectedLinkedBankAccount, orderAmount, onViewDirectDebitInfo, description, investmentType } =
      this.props;

    return (
      <div className="row m-0 mb-4">
        <h5 className="mb-4 fw-bolder">Set up a Direct Debit</h5>
        <p className="text-muted">{description}</p>
        <h6 className={"mt-3 fw-bolder"}>Check your payment details</h6>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Name</div>
          <div className="col p-0 fw-bold text-end">{user.firstName}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Surname</div>
          <div className="col p-0 fw-bold text-end">{user.lastName}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Email</div>
          <div className="col p-0 fw-bold text-end">{user.email}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Sort code</div>
          <div className="col p-0 fw-bold text-end">{selectedLinkedBankAccount.sortCode}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Account number</div>
          <div className="col p-0 fw-bold text-end">{selectedLinkedBankAccount.number}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Address</div>
          <div className="col p-0 fw-bold text-end">{`${user.addresses[0].line1}, ${user.addresses[0].postalCode}`}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Country</div>
          <div className="col p-0 fw-bold text-end">
            {countriesConfig.countries.find((config) => config.code == user.addresses[0].countryCode)?.name}
          </div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">{investmentType}</div>
          <div className="col p-0 fw-bold text-end">
            {formatCurrency(new Decimal(orderAmount).toNumber(), user.currency, locale)} / month
          </div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee name</div>
          <div className="col p-0 fw-bold text-end">Wealthkernel Ltd</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee email</div>
          <div className="col p-0 fw-bold text-end"><EMAIL></div>
        </div>
        <div className="row py-3 m-0">
          <div className="col p-0 text-muted text-start">Date</div>
          <div className="col p-0 fw-bold text-end">{formatDateToDDMONYY(new Date())}</div>
        </div>
        <h6 className={"mt-4 mb-3 fw-bolder"}>Please note</h6>
        <ul className={"ps-4 pe-4 text-muted"}>
          <li>By proceeding, you confirm that no other person is required to authorise this Direct Debit.</li>
          <li>
            The name that will appear on your bank statement is 'Wealthkernel Ltd'. Wealthkernel is our custodian.
          </li>
          <li>You may cancel this Direct Debit at any time by contacting us or your bank.</li>
          <li>
            Read more about the{" "}
            <a
              className="text-decoration-none"
              href="javascript:void(0)"
              onClick={onViewDirectDebitInfo}
              rel="noreferrer"
            >
              Direct Debit Guarantee
            </a>
            .
          </li>
          <li>
            Payments are processed by GoCardless. Read the GoCardless{" "}
            <a
              className="text-decoration-none"
              href="https://gocardless.com/privacy/"
              target="_blank"
              rel="noreferrer"
            >
              privacy notice
            </a>
            .
          </li>
        </ul>
      </div>
    );
  };

  renderEuropeMandateInfo = (): JSX.Element => {
    const { user, locale } = this.context as GlobalContextType;

    const { selectedLinkedBankAccount, orderAmount, description, investmentType } = this.props;

    return (
      <div className="row m-0 mb-4">
        <h5 className="mb-4 fw-bolder">Set up a Direct Debit</h5>
        <p className="text-muted">{description}</p>
        <h6 className={"mt-3 fw-bolder"}>Check your payment details</h6>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Name</div>
          <div className="col p-0 fw-bold text-end">{user.firstName}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Surname</div>
          <div className="col p-0 fw-bold text-end">{user.lastName}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Email</div>
          <div className="col p-0 fw-bold text-end">{user.email}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">IBAN</div>
          <div className="col p-0 fw-bold text-end">{selectedLinkedBankAccount.iban}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Address</div>
          <div className="col p-0 fw-bold text-end">{`${user.addresses[0].line1}, ${user.addresses[0].postalCode}`}</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Country</div>
          <div className="col p-0 fw-bold text-end">
            {countriesConfig.countries.find((config) => config.code == user.addresses[0].countryCode)?.name}
          </div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">{investmentType}</div>
          <div className="col p-0 fw-bold text-end">
            {formatCurrency(new Decimal(orderAmount).toNumber(), user.currency, locale)} / month
          </div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee name</div>
          <div className="col p-0 fw-bold text-end">Wealthyhood Europe AEPEY</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee address</div>
          <div className="col p-0 fw-bold text-end">Solonos 60, Athens, 10672, GR</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee ID</div>
          <div className="col p-0 fw-bold text-end">CR0000W6EFZ5T1</div>
        </div>
        <div className="row py-3 m-0 border-bottom">
          <div className="col p-0 text-muted text-start">Payee email</div>
          <div className="col p-0 fw-bold text-end"><EMAIL></div>
        </div>
        <div className="row py-3 m-0">
          <div className="col p-0 text-muted text-start">Date</div>
          <div className="col p-0 fw-bold text-end">{formatDateToDDMONYY(new Date())}</div>
        </div>
        <h6 className={"mt-4 mb-3 fw-bolder"}>Mandate Consent:</h6>
        <ul className={"ps-4 pe-4 text-muted"}>
          <li>By proceeding, you confirm that no other person is required to authorise this Direct Debit.</li>
          <li>The name that will appear on your bank statement is Wealthyhood Europe</li>
          <li>You may cancel this Direct Debit at any time by contacting us or your bank.</li>
          <li>
            Payments securely processed by GoCardless. GoCardless SAS (company registration number ***********,
            R.C.S. PARIS) is authorised by the ACPR (French Prudential Supervision and Resolution Authority), Bank
            Code (CIB) 17118, for the provision of payment services. GoCardless uses personal data as described in
            their{" "}
            <a
              className="text-decoration-none"
              href="https://gocardless.com/privacy/"
              target="_blank"
              rel="noreferrer"
            >
              privacy notice
            </a>
            .
          </li>
          <li>
            We will notify you at least 3 working days in advance of any changes to your payment date, frequency,
            or amount.
          </li>
          <li>
            By signing this mandate form, you authorise (A) Wealthyhood Europe to send instructions to your bank to
            debit your account and (B) your bank to debit your account in accordance with the instruction
            from  Wealthyhood Europe. As part of your rights, you are entitled to a refund from your bank under the
            terms and conditions of your agreement with your bank. A refund must be claimed within 8 weeks starting
            from the date on which your account was debited. Your rights are explained in a statement that you can
            obtain from your bank.
          </li>
        </ul>
      </div>
    );
  };

  render(): JSX.Element {
    const { onSetupAutomation } = this.props;
    const { user } = this.context as GlobalContextType;

    return (
      <div className="fade-in">
        <Modal.Body className="p-0 px-md-3 px-3 fade-in">
          <div className="d-flex align-self-center justify-content-center">
            <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
              {user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
                ? this.renderUKMandateInfo()
                : this.renderEuropeMandateInfo()}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="pb-5 justify-content-center fade-in" style={{ borderTop: "none" }}>
          <div className="d-flex justify-content-center align-self-center w-100" style={{ maxWidth: "400px" }}>
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-primary fw-100"
              customonclick={onSetupAutomation}
            >
              Set up payment method
            </LoadingOnSubmitButton>
          </div>
        </Modal.Footer>
      </div>
    );
  }
}

DirectDebitSetup.contextType = GlobalContext;

export default DirectDebitSetup;
