import React from "react";
import { formatDateToDayDashHHMM } from "../utils/dateUtil";
import { ExecutionWindowType } from "../../models/Transaction";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { getLocalExecutionWindowStart } from "../utils/executionWindowUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type EtfsExecutionWindowPreviewRowProps = {
  executionWindow: ExecutionWindowType;
  showBottomBorder: boolean;
};

class EtfsExecutionWindowPreviewRow extends React.Component<EtfsExecutionWindowPreviewRowProps> {
  private _getFormattedExecutionWindow(executionWindow: ExecutionWindowType): string {
    if (executionWindow.executionType === "MARKET_HOURS") {
      return formatDateToDayDashHHMM(new Date(executionWindow.start));
    } else {
      return "Instant";
    }
  }

  private _getHoverText(): string {
    const { user } = this.context as GlobalContextType;

    if (user.isRealtimeETFExecutionEnabled) {
      return `ETFs execution time depends on the smart execution mode.\n\nWith smart execution on, ETF orders are executed at our daily trading window at ${getLocalExecutionWindowStart()} every weekday, enjoying ZERO COMMISSIONS.\n\nWith smart execution off (express execution), ETF orders are executed instantly during European market hours (8:00 AM - 4:30 PM GMT) and face a €1 commission per ETF.\n\nETF orders placed outside market hours are executed at the next market opening.`;
    }

    return `ETF orders are executed within a dedicated trading window at ${getLocalExecutionWindowStart()} every weekday. This allows us to provide our clients real commission-free investing across all ETFs.`;
  }

  render(): JSX.Element {
    const { showBottomBorder, executionWindow } = this.props;

    return (
      <>
        <div className={`row pb-3 mb-3 ${showBottomBorder ? "border-bottom" : ""}`}>
          <div className="col text-start">
            <div className="d-flex w-100">
              <p className="m-0 align-self-center text-nowrap me-2">ETFs execution time</p>
              <HoverableInfoIcon hoverText={this._getHoverText()} colorHex={"#536AE3"} />
            </div>
          </div>
          <div className="col text-end">
            <span className="fw-bolder text-nowrap">{this._getFormattedExecutionWindow(executionWindow)}</span>
          </div>
        </div>
      </>
    );
  }
}

EtfsExecutionWindowPreviewRow.contextType = GlobalContext;

export default EtfsExecutionWindowPreviewRow;
