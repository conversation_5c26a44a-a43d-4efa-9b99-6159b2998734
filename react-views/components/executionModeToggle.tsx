import React from "react";
import { GlobalContext } from "../contexts/globalContext";
import { ExecutionModeType } from "../types/executionMode";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { getLocalExecutionWindowStart } from "../utils/executionWindowUtil";
import ToggleSwitch from "./toggleSwitch";

export type ExecutionModeToggleProps = {
  executionMode: ExecutionModeType;
  onChange: (event: React.MouseEvent) => void;
};

class ExecutionModeToggle extends React.Component<ExecutionModeToggleProps> {
  render(): JSX.Element {
    const { onChange, executionMode } = this.props;

    return (
      <div className={"row pb-3 mb-3"}>
        <div className="col-8"></div>
        <div className="col text-start">
          <div className="d-flex w-100">
            <p className="m-0 align-self-center text-nowrap me-2">Smart execution</p>
            <HoverableInfoIcon
              hoverText={`With smart execution, you can enjoy ZERO COMMISSIONS on your Portfolio Buy order!

ETFs:
- All ETF orders are executed at our daily trading window at ${getLocalExecutionWindowStart()} every weekday.
- Commission: €0

Stocks:
- Stock orders are executed instantly during US market hours (9:30 AM - 4:00 PM ET).
- Orders outside market hours are executed at next market opening.
Commission: €0

Express execution
You can always de-activate smart execution to enjoy instant ETF orders during European market hours (8:00 AM - 4:30 PM GMT).

With express execution:
- ETFs: Instant execution (€1 commission per ETF)
- Stocks: Instant execution (always €0 commission)

Orders placed outside market hours are executed at the next market opening.`}
              colorHex={"#536AE3"}
            />
          </div>
        </div>
        <div className="col-4 text-end">
          <ToggleSwitch
            checked={executionMode === "SMART"}
            customonclick={(event: React.MouseEvent) => Promise.resolve(onChange(event))}
          />
        </div>
      </div>
    );
  }
}

ExecutionModeToggle.contextType = GlobalContext;

export default ExecutionModeToggle;
