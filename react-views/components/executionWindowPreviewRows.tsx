import React from "react";
import StocksExecutionWindowPreviewRow from "./stocksExecutionWindowPreviewRow";
import EtfsExecutionWindowPreviewRow from "./etfsExecutionWindowPreviewRow";
import { ExecutionWindowsType } from "../../models/Transaction";
import { DualExecutionValue } from "../types/transactionPreview";
import { ExecutionModeType } from "../types/executionMode";

type ExecutionWindowPreviewRowsProps = {
  executionWindow?: DualExecutionValue<ExecutionWindowsType>;
  showBottomBorder?: boolean;
  executionMode?: ExecutionModeType;
};

class ExecutionWindowPreviewRows extends React.Component<ExecutionWindowPreviewRowsProps> {
  private _getDisplayedExecutionWindow(
    executionWindow: DualExecutionValue<ExecutionWindowsType>,
    executionMode?: ExecutionModeType
  ): ExecutionWindowsType {
    if (executionWindow.smart && !executionWindow.express) {
      return executionWindow.smart;
    } else if (executionWindow.express && !executionWindow.smart) {
      return executionWindow.express;
    } else {
      if (executionMode === "SMART") {
        return executionWindow.smart;
      } else return executionWindow.express;
    }
  }

  render(): JSX.Element {
    const { executionWindow, showBottomBorder, executionMode } = this.props;

    const windowToUse = this._getDisplayedExecutionWindow(executionWindow, executionMode);

    return (
      <>
        {windowToUse.stocks && (
          <StocksExecutionWindowPreviewRow
            executionWindow={windowToUse.stocks}
            showBottomBorder={!!windowToUse.etfs}
          />
        )}
        {windowToUse.etfs && (
          <EtfsExecutionWindowPreviewRow
            executionWindow={windowToUse.etfs}
            showBottomBorder={showBottomBorder ?? false}
          />
        )}
      </>
    );
  }
}

export default ExecutionWindowPreviewRows;
