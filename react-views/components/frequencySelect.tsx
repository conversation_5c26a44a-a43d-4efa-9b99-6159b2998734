import React from "react";
import { FrequencySetting } from "../types/modal";
import { FREQUENCY_CONFIG } from "../configs/frequencyConfig";
import { LinkedBankAccount } from "../types/bank";
import { getEveryMonthOnTheXthDateString, getEveryXthOfTheMonthDateString } from "../utils/dateUtil";
import { TopUpAutomationDocument } from "../../models/Automation";
import { MandateDocument } from "../../models/Mandate";
import { BankAccountDocument } from "../../models/BankAccount";
import Decimal from "decimal.js";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  initialFrequencySelected: FrequencySetting;
  onFrequencyChange: (frequency: FrequencySetting) => void;
  linkedBankAccounts: LinkedBankAccount[];
  existingTopUpAutomation: TopUpAutomationDocument;
  activeOrPendingMandates: MandateDocument[];
};

type StateType = {
  frequency: FrequencySetting;
};

class FrequencySelect extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      frequency: this.props.initialFrequencySelected
    };
  }

  private _setFrequency = (frequency: FrequencySetting): void => {
    this.setState({ frequency });
    this.props.onFrequencyChange(frequency);
  };

  private _getExistingAutomationBankIcon = (): string => {
    const { linkedBankAccounts, activeOrPendingMandates, existingTopUpAutomation } = this.props;

    const existingAutomationBankAccount = activeOrPendingMandates.find(
      (mandate) => mandate._id === (existingTopUpAutomation.mandate as MandateDocument)._id
    )?.bankAccount as BankAccountDocument;

    return linkedBankAccounts.find(
      (bankAccount) => bankAccount.id === existingAutomationBankAccount._id.toString()
    )?.bankIconURL;
  };

  render(): JSX.Element {
    const { existingTopUpAutomation } = this.props;
    const { frequency } = this.state;
    const { locale } = this.context as GlobalContextType;

    return (
      <>
        {/* One Time Buy */}
        <div
          className={
            "row m-0 wh-account-card-option mb-3" +
            (frequency === "ONE_TIME" ? " wh-account-card-option-selected" : "")
          }
          onClick={() => this._setFrequency("ONE_TIME")}
        >
          <div className="col-2 p-0 align-self-center">
            <i className="d-flex material-symbols-outlined justify-content-center align-self-center">
              {FREQUENCY_CONFIG["ONE_TIME"].iconKey}
            </i>
          </div>
          <div className="col-9 align-self-center">
            {/* Account Details */}
            <div className="d-flex flex-column">
              <span className="fw-bold">{FREQUENCY_CONFIG["ONE_TIME"].nameDisplay}</span>
            </div>
            {/* End Account Details */}
          </div>
        </div>
        {/* End One Time Buy */}

        {/* Monthly */}
        <div
          className={
            "row m-0 wh-account-card-option mb-3 " +
            (frequency === "MONTHLY" ? " wh-account-card-option-selected" : "")
          }
          onClick={() => this._setFrequency("MONTHLY")}
        >
          <div className="col-2 p-0 align-self-center">
            <i className="d-flex material-symbols-outlined justify-content-center align-self-center">
              {FREQUENCY_CONFIG["MONTHLY"].iconKey}
            </i>
          </div>
          <div className="col-10 align-self-center">
            {/* Account Details */}
            <div className="d-flex flex-column">
              <span className=" fw-bold">{FREQUENCY_CONFIG["MONTHLY"].nameDisplay}</span>
              {existingTopUpAutomation ? (
                <div className="d-flex align-items-center">
                  <img style={{ width: "14px", height: "14px" }} alt="" src={"/images/icons/tick.svg"} />
                  <img
                    className="ms-1 me-1"
                    src={this._getExistingAutomationBankIcon()}
                    style={{ height: "20px", width: "20px" }}
                  />
                  <span className="t-825" style={{ color: "#536AE3" }}>
                    {formatCurrency(
                      Decimal.div(existingTopUpAutomation.consideration.amount, 100).toNumber(),
                      existingTopUpAutomation.consideration.currency,
                      locale,
                      0,
                      0
                    )}{" "}
                    {getEveryMonthOnTheXthDateString({ capitalFirst: false }, existingTopUpAutomation.dayOfMonth)}
                  </span>
                </div>
              ) : (
                <span className="text-muted">{getEveryXthOfTheMonthDateString({ capitalFirst: true })}</span>
              )}
            </div>
            {/* End Account Details */}
          </div>
        </div>
        {/* End Monthly */}
      </>
    );
  }
}

FrequencySelect.contextType = GlobalContext;

export default FrequencySelect;
