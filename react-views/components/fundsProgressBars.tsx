import React from "react";

type PropsType = {
  progressBarsConfig: { assetName: string; commonId: string; assetPercentage: number }[];
};

class FundsProgressBars extends React.Component<PropsType> {
  render(): JSX.Element {
    const { progressBarsConfig } = this.props;

    return (
      <>
        <div className="tab-content">
          <div className="tab-pane active show" role="tabpanel">
            <div className="row align-items-end" style={{ height: "70%" }}>
              <div className="col-12">
                {progressBarsConfig.map(({ assetName, commonId, assetPercentage }, index) => {
                  return (
                    <div key={`${commonId}-fundsLabel`}>
                      <div className="pb-0">
                        <label className="mb-0">{`${assetName}`}</label>
                        <span className="float-right">{assetPercentage}%</span>
                      </div>
                      <div className="mb-3">
                        <div className="row">
                          <div className="col-12">
                            <div className="progress w-100" style={{ height: "8px" }}>
                              <div
                                className={`progress-bar progress-bg-option${index}`}
                                role="progressbar"
                                style={{ width: `${assetPercentage}%` }}
                                aria-valuenow={assetPercentage}
                                aria-valuemin={0}
                                aria-valuemax={100}
                                key={`${commonId}-progressBar`}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default FundsProgressBars;
