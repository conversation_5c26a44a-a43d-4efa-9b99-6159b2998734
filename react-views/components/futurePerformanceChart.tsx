import React from "react";
import {
  CategoryScale,
  Chart as ChartJS,
  ChartOptions,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from "chart.js";
import { Line } from "react-chartjs-2";
import { PartialRecord } from "../types/utils";
import { formatCurrency } from "../utils/currencyUtil";
import { Nav } from "react-bootstrap";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { DurationType } from "./portfolioAllocation";

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const DURATION_CONFIG: Record<DurationType, string> = {
  "1m": "1 month",
  "6m": "6 months",
  "1y": "1 year",
  "2y": "2 years",
  "3y": "3 years",
  "5y": "5 years",
  "10y": "10 years",
  "15y": "15 years",
  "20y": "20 years",
  "30y": "30 years"
};

const DURATION_YEARS_NUMBERS: PartialRecord<DurationType, number> = {
  "1y": 1,
  "2y": 2,
  "3y": 3,
  "5y": 5,
  "10y": 10,
  "15y": 15,
  "20y": 20,
  "30y": 30
};

type PropsType = {
  data: PartialRecord<DurationType, { date: string; value: number }[]>;
  upperBoundData?: PartialRecord<DurationType, { date: string; value: number }[]>;
  lowerBoundData?: PartialRecord<DurationType, { date: string; value: number }[]>;
  durations: DurationType[];
  activeDuration: DurationType;
  onDurationChange: (activeDuration: DurationType) => void;
  description: (duration: string) => JSX.Element;
};

class FuturePerformanceChart extends React.Component<PropsType> {
  constructor(props: PropsType) {
    super(props);
  }

  private _getLineChartOptions() {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: false
        },
        datalabels: {
          display: false
        }
      },
      hover: {
        mode: "nearest"
      },
      elements: {
        line: {
          tension: 0.1
        },
        point: {
          radius: 0
        }
      },
      scales: {
        x: {
          display: false
        },
        y: {
          display: false
        }
      },
      layout: {
        padding: {
          left: 0,
          right: 0,
          top: 20,
          bottom: 10
        }
      }
    };
  }

  private _transformData(data: { date: string; value: number }[]): number[] {
    const result: number[] = [];
    const dataPoints = data;

    for (let it = 0; it < dataPoints.length; it += 1) {
      result.push(dataPoints[it].value);
    }

    return result;
  }

  private _getLineChartData = (): any => {
    const { data, upperBoundData, lowerBoundData, activeDuration } = this.props;

    const dataPoints = this._transformData(data[activeDuration]);

    const labels = Array.from({ length: dataPoints.length }, (_, index) => `Y${index}`); //dummyDataPoints.map((value) => value.x);

    const chartData: any = {
      labels,
      datasets: [
        {
          data: dataPoints,
          borderColor: "#5d78ff",
          borderWidth: 3,
          pointBorderColor: "#546BE5",
          pointBackgroundColor: "#546BE5",
          pointHoverBackgroundColor: "#546BE5",
          pointHoverBorderColor: "#546BE5",
          pointHoverRadius: 4,
          pointRadius: 0,
          fill: false,
          label: "avgChart"
        }
      ]
    };

    if (upperBoundData) {
      const upperBoundDataPoints = this._transformData(upperBoundData[activeDuration]);
      chartData.datasets.push({
        data: upperBoundDataPoints,
        borderColor: "#1C6C57",
        borderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 0,
        fill: false,
        borderDash: [10, 5],
        label: "upperBoundChart"
      });
    }

    if (lowerBoundData) {
      const lowerBoundDataPoints = this._transformData(lowerBoundData[activeDuration]);
      chartData.datasets.push({
        data: lowerBoundDataPoints,
        borderColor: "#171717",
        borderWidth: 2,
        pointRadius: 0,
        pointHoverRadius: 0,
        fill: false,
        borderDash: [10, 5],
        label: "lowerBoundChart"
      });
    }

    return chartData;
  };

  private _getPortfolioValues(): {
    currentPortfolioValue: number;
    upperBoundPortfolioValue?: number;
    lowerBoundPortfolioValue?: number;
  } {
    const { data, upperBoundData, lowerBoundData, activeDuration } = this.props;

    const dataPoints = this._transformData(data[activeDuration]);

    const portfolioValues: {
      currentPortfolioValue: number;
      upperBoundPortfolioValue?: number;
      lowerBoundPortfolioValue?: number;
    } = {
      currentPortfolioValue: dataPoints[dataPoints.length - 1]
    };

    if (upperBoundData) {
      const upperBoundDataPoints = this._transformData(upperBoundData[activeDuration]);
      portfolioValues.upperBoundPortfolioValue = upperBoundDataPoints[upperBoundDataPoints.length - 1];
    }

    if (lowerBoundData) {
      const lowerBoundDataPoints = this._transformData(lowerBoundData[activeDuration]);
      portfolioValues.lowerBoundPortfolioValue = lowerBoundDataPoints[lowerBoundDataPoints.length - 1];
    }

    return portfolioValues;
  }

  private _handleDurationSelect = (eventKey: DurationType): void => {
    this.setState({ yAxisIndex: DURATION_YEARS_NUMBERS[eventKey] * 12 });
    this.props.onDurationChange(eventKey);
  };

  shouldComponentUpdate(nextProps: PropsType) {
    return (
      this.props.data !== nextProps.data ||
      this.props.activeDuration !== nextProps.activeDuration ||
      this.props?.upperBoundData !== nextProps?.upperBoundData ||
      this.props?.lowerBoundData !== nextProps?.lowerBoundData
    );
  }

  render(): JSX.Element {
    const { description, activeDuration, durations } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const { currentPortfolioValue, upperBoundPortfolioValue, lowerBoundPortfolioValue } =
      this._getPortfolioValues();

    return (
      <>
        <div className="row px-3 m-0 mb-3">
          <div className="col p-0">{description(DURATION_CONFIG[activeDuration])}</div>
        </div>
        <div className="row m-0">
          <div className="px-3 py-0 position-relative" style={{ height: "210px" }}>
            <Line data={this._getLineChartData()} options={this._getLineChartOptions() as ChartOptions<"line">} />
            <div className="position-absolute" style={{ left: "20px", top: "-5px" }}>
              {!!upperBoundPortfolioValue && (
                <>
                  <span style={{ color: "#1C6C57", fontWeight: "500" }}>
                    {formatCurrency(upperBoundPortfolioValue, user.currency, locale, 0, 0)}
                  </span>
                  <br />
                  <span style={{ color: "#1C6C57", fontSize: "14px" }}>{" • "}</span>
                  <span style={{ color: "#5D5D5D", fontSize: "14px" }}>{"Better"}</span>
                  <br />
                </>
              )}
              <span style={{ color: "#4957D5", fontWeight: "500" }}>
                {formatCurrency(currentPortfolioValue, user.currency, locale, 0, 0)}
              </span>
              <br />
              <span style={{ color: "#4957D5", fontSize: "14px" }}>{" • "}</span>
              <span style={{ color: "#5D5D5D", fontSize: "14px" }}>{"Expected"}</span>
              {!!lowerBoundPortfolioValue && (
                <>
                  <br />
                  <span style={{ color: "#171717", fontWeight: "500" }}>
                    {formatCurrency(lowerBoundPortfolioValue, user.currency, locale, 0, 0)}
                  </span>
                  <br />
                  <span style={{ color: "#171717", fontSize: "14px" }}>{" • "}</span>
                  <span style={{ color: "#5D5D5D", fontSize: "14px" }}>{"Worse"}</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="row m-0 pb-3">
          <div className="col p-0">
            <Nav
              variant="pills"
              defaultActiveKey={activeDuration}
              className="justify-content-center"
              onSelect={this._handleDurationSelect}
            >
              {durations.map((tenor, index) => (
                <Nav.Item key={`tenor-nav-${index}`}>
                  <Nav.Link eventKey={tenor} className="p-2 mx-2 font-size-lg">
                    {tenor.toUpperCase()}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </div>
        </div>
      </>
    );
  }
}

FuturePerformanceChart.contextType = GlobalContext;

export default FuturePerformanceChart;
