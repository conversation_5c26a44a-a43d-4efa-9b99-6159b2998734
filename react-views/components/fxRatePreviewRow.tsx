import React from "react";
import { currenciesConfig, fees, localeConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import { ForeignCurrencyRatesType } from "../types/transactionPreview";
import { formatCurrency } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { DisplayExchangeRateType } from "../../models/Order";

const { FX_RATES } = fees;

export type FxRatePreviewRowProps = {
  foreignCurrencyRates?: ForeignCurrencyRatesType;
  showBottomBorder: boolean;
};

class FxRatePreviewRow extends React.Component<FxRatePreviewRowProps> {
  public static displayExchangeRateToDisplayExchangeRateType(
    displayExchangeRate: DisplayExchangeRateType
  ): ForeignCurrencyRatesType | undefined {
    if (displayExchangeRate && displayExchangeRate.currency && displayExchangeRate.rate) {
      return {
        [displayExchangeRate.currency]: displayExchangeRate.rate
      };
    }
    return undefined;
  }

  public static getForeignCurrencyEquality(
    foreignCurrencyRates: ForeignCurrencyRatesType,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string {
    return Object.entries(foreignCurrencyRates).reduce((acc, [currencyKey, fxRate]) => {
      return acc + ` = ${formatCurrency(fxRate, currencyKey as currenciesConfig.MainCurrencyType, locale, 3, 3)}`;
    }, `${formatCurrency(1, currency, locale, 0, 0)}`);
  }

  render(): JSX.Element {
    const { foreignCurrencyRates, showBottomBorder } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const plan = PRICE_CONFIG[user.subscription.price].plan;

    const fxRatePercent = Decimal.mul(FX_RATES[plan], 100).toNumber();

    const show = foreignCurrencyRates && Object.keys(foreignCurrencyRates).length > 0;

    return (
      <>
        {show && (
          <div className={`row pb-3 mb-3 ${showBottomBorder ? "border-bottom" : ""}`}>
            <div className="col text-start">
              <div className="d-flex w-100">
                <p className="m-0 align-self-center text-nowrap me-2">FX rate</p>
                <HoverableInfoIcon
                  hoverText={`This is an estimated FX rate based on the latest data. The actual FX rate may differ slightly. The FX rate you get is the interbank rate plus a ${fxRatePercent}% fee.`}
                  colorHex={"#536AE3"}
                />
              </div>
            </div>
            <div className="col text-end">
              <span className="fw-bolder">
                {FxRatePreviewRow.getForeignCurrencyEquality(foreignCurrencyRates, user.currency, locale)}
              </span>
            </div>
          </div>
        )}
      </>
    );
  }
}

FxRatePreviewRow.contextType = GlobalContext;

export default FxRatePreviewRow;
