import React from "react";
import { Modal } from "react-bootstrap";
import AssetGroupByMethodSelect from "./assetGroupByMethodSelect";

export enum GroupByMethodEnum {
  AssetClassAndSector = "Asset class & sector",
  AssetType = "Asset type",
  SingleList = "Single list with all investments"
}
export const GROUP_BY_METHOD_CONFIG: {
  [key in GroupByMethodEnum]: { name: string; description: string; sortingMethod: string };
} = {
  [GroupByMethodEnum.AssetClassAndSector]: {
    name: "Asset class & sector",
    sortingMethod: "by asset class & sector",
    description: "Stocks, Bonds, Commodities & Real estate"
  },
  [GroupByMethodEnum.AssetType]: {
    name: "Asset type",
    sortingMethod: "by asset type",
    description: "Individual stocks or ETFs"
  },
  [GroupByMethodEnum.SingleList]: {
    name: "Single list with all investments",
    sortingMethod: "from largest to smallest value",
    description: "From largest to smallest value"
  }
};

type PropsType = {
  title: string;
  activeGroupByMethod: GroupByMethodEnum;
  groupByMethods: GroupByMethodEnum[];
  onSelectGroupByMethodClick: (selectedGroupByMethod: GroupByMethodEnum) => void;
};

type StateType = {
  showGroupByModal: boolean;
};

class GroupBy extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showGroupByModal: false
    };
  }

  private _toggleGroupByModal(showGroupByModal: boolean) {
    this.setState({ showGroupByModal });
  }

  private _onSelectedGroupByMethodChange(selectedGroupByMethod: GroupByMethodEnum) {
    const { onSelectGroupByMethodClick } = this.props;
    this.setState({ showGroupByModal: false }, () => onSelectGroupByMethodClick(selectedGroupByMethod));
  }

  render(): JSX.Element {
    const { title, activeGroupByMethod } = this.props;

    return (
      <>
        <div className="d-flex mb-4" style={{ alignItems: "center", justifyContent: "space-between" }}>
          <div className="d-flex" style={{ flexDirection: "column", alignItems: "start" }}>
            <h5 className="fw-bolder">{title}</h5>
            <h6 style={{ color: "#536AE3", fontWeight: "lighter" }}>
              {GROUP_BY_METHOD_CONFIG[activeGroupByMethod].sortingMethod}
            </h6>
          </div>
          <button type="button" className="btn group-by-radio-btn" onClick={() => this._toggleGroupByModal(true)}>
            <div className="d-flex align-self-center justify-content-center">
              <span className="material-symbols-outlined align-self-center me-2">donut_large</span>
              <span className="material-symbols-outlined align-self-center">expand_more</span>
            </div>
          </button>
        </div>
        <Modal show={this.state.showGroupByModal} onHide={() => this._toggleGroupByModal(false)}>
          <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton></Modal.Header>
          <h5 className="px-md-5 px-3 font-weight-bolder">Group your investments by</h5>
          <Modal.Body>
            <AssetGroupByMethodSelect
              selectedGroupByMethod={activeGroupByMethod}
              groupByMethods={this.props.groupByMethods}
              onSelectedGroupByMethodChange={(selectedGroupByMethod: GroupByMethodEnum) =>
                this._onSelectedGroupByMethodChange(selectedGroupByMethod)
              }
            />
          </Modal.Body>
        </Modal>
      </>
    );
  }
}

export default GroupBy;
