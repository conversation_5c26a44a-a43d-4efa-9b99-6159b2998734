import React, { Children } from "react";

type PropsType = {
  id: string;
  scrollingDistance?: number;
  showScrollAfterNElements?: number;
  showScroll?: boolean;
  showScrollDots?: boolean;
  className?: string;
  scrollDotsClassName?: string;
};

type StateType = {
  selectedScrollDot: number;
};

class HorizontalScroller extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      selectedScrollDot: 0
    };
  }

  private readonly _containerRef = React.createRef<HTMLDivElement>();

  private _sideScroll = (element: HTMLDivElement, direction: "left" | "right", distance = 463) => {
    const speed = 25;
    const stepOffset = 20;
    const step = direction == "left" ? -stepOffset : stepOffset;

    let scrollAmount = 0;
    const slideTimer = setInterval(() => {
      element.scrollLeft += step;
      scrollAmount += Math.abs(step);
      if (scrollAmount >= distance) {
        clearInterval(slideTimer);
      }
    }, speed);
  };

  render(): JSX.Element {
    const {
      id,
      children,
      className,
      scrollDotsClassName,
      scrollingDistance,
      showScroll,
      showScrollDots,
      showScrollAfterNElements
    } = this.props;
    const { selectedScrollDot } = this.state;

    const numberOfChildren = React.Children.count(children);
    const showScrollAfterNElementsToUse = showScrollAfterNElements ?? 1;
    const showScrollToUse = showScroll ?? true;

    return (
      <>
        <div className={`d-flex align-self-center p-0 ${className}`}>
          {showScrollToUse && numberOfChildren > showScrollAfterNElementsToUse && (
            <div className="align-self-center pe-0 d-none d-sm-block">
              <button
                className="btn btn-clean"
                onClick={() => this._sideScroll(this._containerRef.current, "left", scrollingDistance)}
              >
                <i className="fa-solid text-primary fa-chevron-left" />
              </button>
            </div>
          )}
          <div ref={this._containerRef} className="d-flex flex-row flex-nowrap overflow-auto no-scroll-bar">
            {Children.toArray(children).map((child, index) => (
              <div
                id={`scroll-child-${id}-${index}`}
                key={`scroll-child-${id}-${index}`}
                className="d-flex m-0 p-1"
                onClick={() => {
                  this.setState({ selectedScrollDot: index }, () =>
                    document
                      .getElementById(`scroll-child-${id}-${index}`)
                      .scrollIntoView({ block: "center", inline: "center", behavior: "smooth" })
                  );
                }}
              >
                {child}
              </div>
            ))}
          </div>
          {showScrollToUse && numberOfChildren > showScrollAfterNElementsToUse && (
            <div className="align-self-center pe-0 d-none d-sm-block">
              <button
                className="btn btn-clean"
                onClick={() => this._sideScroll(this._containerRef.current, "right", scrollingDistance)}
              >
                <i className="fa-solid text-primary fa-chevron-right" />
              </button>
            </div>
          )}
        </div>

        {showScrollDots && numberOfChildren > 1 && (
          <div className={`d-flex mt-3 mb-4 justify-content-center ${scrollDotsClassName ?? ""}`}>
            {Children.toArray(children).map((child, index) => (
              <div
                id={`scroll-child-${id}-${index}`}
                key={`scroll-child-${id}-${index}`}
                className="d-flex m-0 p-1"
                onClick={() => {
                  this.setState({ selectedScrollDot: index }, () =>
                    document
                      .getElementById(`scroll-child-${id}-${index}`)
                      .scrollIntoView({ block: "center", inline: "center", behavior: "smooth" })
                  );
                }}
              >
                <i
                  className={`material-icons align-self-center cursor-pointer ${
                    selectedScrollDot == index ? "text-primary" : "not-selected-carousel-dot"
                  }`}
                  style={{ fontSize: "18px" }}
                >
                  fiber_manual_record
                </i>
              </div>
            ))}
          </div>
        )}
      </>
    );
  }
}

export default HorizontalScroller;
