import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";

type HoverableInfoIconProps = {
  hoverText: string;
  colorHex?: string;
};

class HoverableInfoIcon extends React.Component<HoverableInfoIconProps> {
  render(): JSX.Element {
    const { hoverText, colorHex } = this.props;

    return (
      <OverlayTrigger
        placement="bottom"
        overlay={
          <Popover id="popover-explanation">
            <Popover.Content>
              <span style={{ whiteSpace: "pre-line" }}>{hoverText}</span>
            </Popover.Content>
          </Popover>
        }
      >
        <i
          className="material-symbols-outlined align-self-center cursor-pointer"
          style={{ fontSize: "16px", color: colorHex }}
        >
          info
        </i>
      </OverlayTrigger>
    );
  }
}

export default HoverableInfoIcon;
