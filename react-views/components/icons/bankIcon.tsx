import React from "react";

class BankIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg
        version="1.0"
        id="Layer_1"
        xmlns="http://www.w3.org/2000/svg"
        width="149px"
        height="149px"
        viewBox="0 0 64 64"
        enableBackground="new 0 0 64 64"
        xmlSpace="preserve"
        fill="#000000"
      >
        <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          {" "}
          <g>
            {" "}
            <circle fill="#3699FF" cx="32" cy="14" r="3"></circle>{" "}
            <path
              fill="#3699FF"
              d="M4,25h56c1.794,0,3.368-1.194,3.852-2.922c0.484-1.728-0.242-3.566-1.775-4.497l-28-17 C33.438,0.193,32.719,0,32,0s-1.438,0.193-2.076,0.581l-28,17c-1.533,0.931-2.26,2.77-1.775,4.497C0.632,23.806,2.206,25,4,25z M32,9c2.762,0,5,2.238,5,5s-2.238,5-5,5s-5-2.238-5-5S29.238,9,32,9z"
            ></path>{" "}
            <rect x="34" y="27" fill="#3699FF" width="8" height="25"></rect>{" "}
            <rect x="46" y="27" fill="#3699FF" width="8" height="25"></rect>{" "}
            <rect x="22" y="27" fill="#3699FF" width="8" height="25"></rect>{" "}
            <rect x="10" y="27" fill="#3699FF" width="8" height="25"></rect>{" "}
            <path fill="#3699FF" d="M4,58h56c0-2.209-1.791-4-4-4H8C5.791,54,4,55.791,4,58z"></path>{" "}
            <path
              fill="#3699FF"
              d="M63.445,60H0.555C0.211,60.591,0,61.268,0,62v2h64v-2C64,61.268,63.789,60.591,63.445,60z"
            ></path>{" "}
          </g>{" "}
        </g>
      </svg>
    );
  }
}

export default BankIcon;
