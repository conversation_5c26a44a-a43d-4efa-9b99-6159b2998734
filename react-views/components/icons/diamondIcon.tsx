import React from "react";

class DiamondIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <rect x="0" y="0" width="24" height="24"></rect>
          <polygon fill="#000000" opacity="0.3" points="5 3 19 3 23 8 1 8"></polygon>
          <polygon fill="#000000" points="23 8 12 20 1 8"></polygon>
        </g>
      </svg>
    );
  }
}

export default DiamondIcon;
