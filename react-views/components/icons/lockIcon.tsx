import React from "react";

type PropsType = {
  fill?: string;
  style?: React.CSSProperties;
};

class LockIcon extends React.Component<PropsType> {
  render(): JSX.Element {
    const { fill = "#ffffff", style } = this.props;

    return (
      <svg
        width="12"
        height="16.5"
        className="lock-icon"
        viewBox="0 0 8 11"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={style} // ✅ Now the style prop works!
      >
        <path
          d="M1 11C0.725 11 0.4875 10.9042 0.2875 10.7125C0.0958334 10.5125 5.96046e-08 10.275 5.96046e-08 10V5C5.96046e-08 4.725 0.0958334 4.49167 0.2875 4.3C0.4875 4.1 0.725 4 1 4H1.5V3C1.5 2.30833 1.74167 1.72083 2.225 1.2375C2.71667 0.745833 3.30833 0.5 4 0.5C4.69167 0.5 5.27917 0.745833 5.7625 1.2375C6.25417 1.72083 6.5 2.30833 6.5 3V4H7C7.275 4 7.50833 4.1 7.7 4.3C7.9 4.49167 8 4.725 8 5V10C8 10.275 7.9 10.5125 7.7 10.7125C7.50833 10.9042 7.275 11 7 11H1ZM4 8.5C4.275 8.5 4.50833 8.40417 4.7 8.2125C4.9 8.0125 5 7.775 5 7.5C5 7.225 4.9 6.99167 4.7 6.8C4.50833 6.6 4.275 6.5 4 6.5C3.725 6.5 3.4875 6.6 3.2875 6.8C3.09583 6.99167 3 7.225 3 7.5C3 7.775 3.09583 8.0125 3.2875 8.2125C3.4875 8.40417 3.725 8.5 4 8.5ZM2.5 4H5.5V3C5.5 2.58333 5.35417 2.22917 5.0625 1.9375C4.77083 1.64583 4.41667 1.5 4 1.5C3.58333 1.5 3.22917 1.64583 2.9375 1.9375C2.64583 2.22917 2.5 2.58333 2.5 3V4Z"
          fill={fill}
        />
      </svg>
    );
  }
}

export default LockIcon;
