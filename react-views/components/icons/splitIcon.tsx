import React from "react";

class SplitIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg
        fill="#000000"
        width="24px"
        height="24px"
        viewBox="0 0 32 32"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>split</title>
        <path d="M0.032 26.336q-0.096-0.576 0.128-1.12t0.704-0.864 1.152-0.352h1.984v-4q0-2.464 1.76-4.224t4.256-1.76h1.984q0.832 0 1.408-0.576t0.608-1.44v-9.984q0-0.736 0.384-1.248t1.024-0.608 1.152 0 1.024 0.608 0.416 1.248v9.984q0 0.832 0.576 1.44t1.408 0.576h2.016q2.464 0 4.224 1.76t1.76 4.224v4h2.016q0.64 0 1.152 0.384t0.704 0.864 0.096 1.12-0.544 1.056l-4 4q-0.64 0.608-1.44 0.608t-1.376-0.608l-4-4q-0.48-0.448-0.576-1.056t0.128-1.152 0.704-0.864 1.152-0.352h1.984v-4q0-0.832-0.576-1.408t-1.408-0.576h-2.016q-2.272 0-4-1.568-1.728 1.568-4 1.568h-1.984q-0.832 0-1.44 0.576t-0.576 1.408v4h2.016q0.64 0 1.152 0.384t0.704 0.864 0.096 1.12-0.544 1.056l-4 4q-0.64 0.608-1.44 0.608t-1.408-0.608l-4-4q-0.48-0.48-0.544-1.088z"></path>
      </svg>
    );
  }
}

export default SplitIcon;
