import React from "react";

class TrendingDownIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M9.44167 6.27083L6.75833 3.5875L5.54792 4.79792C5.26597 5.07986 4.93056 5.22083 4.54167 5.22083C4.15278 5.22083 3.81736 5.07986 3.53542 4.79792L0.61875 1.88125C0.472917 1.73542 0.4 1.57014 0.4 1.38542C0.409722 1.19097 0.4875 1.02569 0.633333 0.889583C0.779167 0.74375 0.949306 0.670833 1.14375 0.670833C1.33819 0.670833 1.50347 0.74375 1.63958 0.889583L4.54167 3.79167L5.75208 2.58125C6.03403 2.29931 6.36944 2.15833 6.75833 2.15833C7.14722 2.15833 7.48264 2.29931 7.76458 2.58125L10.4479 5.26458V4.36042C10.4479 4.16597 10.516 4.00069 10.6521 3.86458C10.7979 3.71875 10.9681 3.64583 11.1625 3.64583C11.3569 3.64583 11.5222 3.71875 11.6583 3.86458C11.8042 4.00069 11.8771 4.16597 11.8771 4.36042V6.98542C11.8771 7.17986 11.809 7.35 11.6729 7.49583C11.5368 7.63194 11.3667 7.7 11.1625 7.7H8.5375C8.33333 7.7 8.16319 7.63194 8.02708 7.49583C7.89097 7.35 7.82292 7.17986 7.82292 6.98542C7.82292 6.79097 7.89097 6.62569 8.02708 6.48958C8.16319 6.34375 8.33333 6.27083 8.5375 6.27083H9.44167Z"
          fill="#D63C3C"
        />
      </svg>
    );
  }
}

export default TrendingDownIcon;
