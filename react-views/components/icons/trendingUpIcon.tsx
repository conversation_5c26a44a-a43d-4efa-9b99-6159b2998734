import React from "react";

class TrendingUpIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M0.633333 6.92292C0.4875 6.77708 0.409722 6.61181 0.4 6.42708C0.4 6.23264 0.472917 6.05764 0.61875 5.90208L3.53542 3.01458C3.81736 2.73264 4.15278 2.59167 4.54167 2.59167C4.93056 2.59167 5.26597 2.73264 5.54792 3.01458L6.75833 4.21042L9.44167 1.52708H8.5375C8.33333 1.52708 8.16319 1.45903 8.02708 1.32292C7.89097 1.18681 7.82292 1.02153 7.82292 0.827083C7.82292 0.622916 7.89097 0.452777 8.02708 0.316666C8.16319 0.170833 8.33333 0.0979159 8.5375 0.0979159H11.1625C11.3667 0.0979159 11.5368 0.170833 11.6729 0.316666C11.809 0.452777 11.8771 0.618055 11.8771 0.812499V3.4375C11.8771 3.64167 11.809 3.81181 11.6729 3.94792C11.5368 4.08403 11.3667 4.15208 11.1625 4.15208C10.9681 4.15208 10.7979 4.08403 10.6521 3.94792C10.516 3.81181 10.4479 3.64653 10.4479 3.45208V2.54792L7.76458 5.23125C7.48264 5.51319 7.14722 5.65417 6.75833 5.65417C6.36944 5.65417 6.03403 5.51319 5.75208 5.23125L4.54167 4.02083L1.63958 6.92292C1.50347 7.06875 1.33819 7.14167 1.14375 7.14167C0.949306 7.14167 0.779167 7.06875 0.633333 6.92292Z"
          fill="#23846A"
        />
      </svg>
    );
  }
}

export default TrendingUpIcon;
