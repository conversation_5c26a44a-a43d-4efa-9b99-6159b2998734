import React from "react";

class UpRightIcon extends React.Component {
  render(): JSX.Element {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <polygon points="0 0 24 0 24 24 0 24" />
          <rect
            fill="#000000"
            opacity="0.3"
            transform="translate(11.646447, 12.853553) rotate(-315.000000) translate(-11.646447, -12.853553) "
            x="10.6464466"
            y="5.85355339"
            width="2"
            height="14"
            rx="1"
          />
          <path
            d="M8.1109127,8.90380592 C7.55862795,8.90380592 7.1109127,8.45609067 7.1109127,7.90380592 C7.1109127,7.35152117 7.55862795,6.90380592 8.1109127,6.90380592 L16.5961941,6.90380592 C17.1315855,6.90380592 17.5719943,7.32548256 17.5952502,7.8603687 L17.9488036,15.9920967 C17.9727933,16.5438602 17.5449482,17.0106003 16.9931847,17.0345901 C16.4414212,17.0585798 15.974681,16.6307346 15.9506913,16.0789711 L15.6387276,8.90380592 L8.1109127,8.90380592 Z"
            fill="#000000"
            fillRule="nonzero"
          />
        </g>
      </svg>
    );
  }
}

export default UpRightIcon;
