import React from "react";
import { RewardDocument } from "../../models/Reward";
import { TransactionDocument } from "../../models/Transaction";
import MainCard from "../layouts/mainCard";
import InvestorTransactionRow from "./investorTransactionRow";
import InvestorRewardRow from "./investorRewardRow";
import HorizontalScroller from "./horizontalScroller";
import LoadingSpinner from "./loadingSpinner";
import { INVESTMENT_ACTIVITY_FILTER_CONFIG, InvestmentActivityFilterEnum } from "../configs/activityConfig";
import { TransactionActivityItemType } from "../types/transaction";
import { formatDateToMONYYYY } from "../utils/dateUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderType } from "../../services/truelayerService";

export type InvestmentActivityPropsType = {
  investmentProducts: InvestmentProductDocument[];
  activity: TransactionActivityItemType[];
  truelayerProviders: ProviderType[];
  openTransactionModal: (options: { transaction?: TransactionDocument; reward?: RewardDocument }) => void;
};

type StateType = {
  showConfirmationModal: boolean;
  visibleTransactionsNum: number;
  showIncomingCashFlowModal: boolean;
  isLoadingTransactions: boolean;
  activeTransactionFilter?: InvestmentActivityFilterEnum;
};

export default class InvestmentActivity extends React.Component<InvestmentActivityPropsType, StateType> {
  constructor(props: InvestmentActivityPropsType) {
    super(props);
    this.state = {
      showConfirmationModal: false,
      isLoadingTransactions: false,
      visibleTransactionsNum: 20,
      showIncomingCashFlowModal: false,
      activeTransactionFilter: "All"
    };
  }

  private _onTransactionFilterChange(transactionFilter: InvestmentActivityFilterEnum) {
    this.setState({ activeTransactionFilter: transactionFilter });
  }

  private _increaseVisibleTransactions = () => {
    const { visibleTransactionsNum } = this.state;
    this.setState({ visibleTransactionsNum: visibleTransactionsNum + 20 });
  };

  private _groupTransactionsByMonth(
    transactions: (RewardDocument | TransactionDocument)[]
  ): Record<string, (RewardDocument | TransactionDocument)[]> {
    return transactions.reduce(
      (
        acc: Record<string, (RewardDocument | TransactionDocument)[]>,
        transaction: RewardDocument | TransactionDocument
      ) => {
        const monthYearKey = formatDateToMONYYYY(new Date(transaction.displayDate));
        if (!acc[monthYearKey]) {
          acc[monthYearKey] = [];
        }
        acc[monthYearKey].push(transaction);
        return acc;
      },
      {}
    );
  }

  render() {
    const { truelayerProviders, investmentProducts, openTransactionModal, activity } = this.props;
    const { visibleTransactionsNum, activeTransactionFilter } = this.state;
    const transactionsActivityItemsData = activity
      .filter((transaction) => {
        if (activeTransactionFilter === "All") return true;
        return transaction.activityFilter === activeTransactionFilter;
      })
      .map((transaction) => transaction.item);
    const transactionItemsToDisplay = transactionsActivityItemsData.slice(0, visibleTransactionsNum);
    const groupedTransactions = this._groupTransactionsByMonth(transactionItemsToDisplay);

    return (
      <>
        <MainCard className={"px-md-0 px-0"} disableMarginBottom>
          <h5 className="fw-bolder mb-4 px-md-5 px-0">Investment Activity</h5>
          {/* Activity Filters */}
          <div>
            <HorizontalScroller id={"discovery-scroller"}>
              {(Object.keys(INVESTMENT_ACTIVITY_FILTER_CONFIG) as InvestmentActivityFilterEnum[])
                .sort(
                  (a: InvestmentActivityFilterEnum, b: InvestmentActivityFilterEnum) =>
                    INVESTMENT_ACTIVITY_FILTER_CONFIG[a].order - INVESTMENT_ACTIVITY_FILTER_CONFIG[b].order
                )
                .map((transactionFilter: InvestmentActivityFilterEnum) => (
                  <div
                    key={`tenor-nav-${transactionFilter}`}
                    className={
                      "cursor-pointer col py-2 px-3 align-self-center text-muted fw-bold text-center text-nowrap " +
                      (activeTransactionFilter === transactionFilter ? "active-transaction-filter" : "")
                    }
                    onClick={() => this._onTransactionFilterChange(transactionFilter)}
                  >
                    {transactionFilter}
                  </div>
                ))}
            </HorizontalScroller>
          </div>
          {/* End Activity Filters */}
          <div className="row m-0 px-md-5 px-2">
            {this.state.isLoadingTransactions ? (
              <div className="col p-0">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="col p-0">
                {transactionItemsToDisplay.length > 0 ? (
                  Object.entries(groupedTransactions).map(
                    ([monthYear, transactions]: [string, (RewardDocument | TransactionDocument)[]], index) => (
                      <div key={index}>
                        <h5 className="row m-0 pt-4 mt-4">{monthYear}</h5>
                        {/* Display the month and year */}
                        {transactions.map((transaction, index) => {
                          if ("category" in transaction) {
                            return (
                              <InvestorTransactionRow
                                onClick={() => openTransactionModal({ transaction })}
                                transaction={transaction}
                                truelayerProviders={truelayerProviders}
                                investmentProducts={investmentProducts}
                                key={`transaction_${index}`}
                              />
                            );
                          } else {
                            const reward = transaction as RewardDocument;
                            return (
                              <InvestorRewardRow
                                onClick={() => openTransactionModal({ reward })}
                                reward={reward}
                                key={`reward_${index}`}
                              />
                            );
                          }
                        })}
                      </div>
                    )
                  )
                ) : (
                  <>
                    <p className="text-center mt-4">No activity found.</p>
                    <p className="text-muted text-center">
                      You haven&apos;t {INVESTMENT_ACTIVITY_FILTER_CONFIG[activeTransactionFilter].notFoundMessage}{" "}
                      yet.
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
          {transactionsActivityItemsData.length > visibleTransactionsNum && (
            <div className="row m-0 mt-3 px-md-5 px-2 justify-content-center">
              <button className="btn btn-secondary fw-100" onClick={this._increaseVisibleTransactions}>
                Load more
              </button>
            </div>
          )}
        </MainCard>
      </>
    );
  }
}
