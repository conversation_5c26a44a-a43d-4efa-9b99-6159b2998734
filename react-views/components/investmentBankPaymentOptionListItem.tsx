import React from "react";
import { LinkedBankAccount } from "../types/bank";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { entitiesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  isSelected: boolean;
  linkedBankAccount: LinkedBankAccount;
  onChange: () => void;
  oneTimePayment: boolean;
};

class InvestmentBankPaymentOptionListItem extends React.Component<PropsType> {
  private _isAvailable = (): boolean => {
    const { linkedBankAccount } = this.props;
    return linkedBankAccount.isAvailableForDirectDebit || this.props.oneTimePayment;
  };

  private _showNotAvailableToast = () => {
    const { user } = this.context as GlobalContextType;
    const message =
      user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        ? "You cannot use this bank account for direct debit."
        : "You can only use a EUR bank account with an EU IBAN to set up a direct debit.";
    emitToast({
      content: message,
      toastType: ToastTypeEnum.error
    });
  };
  render(): JSX.Element {
    const { isSelected, linkedBankAccount, onChange } = this.props;
    const { displayAccountIdentifier, bankIconURL, displayBankName } = linkedBankAccount;
    const isAvailable = this._isAvailable();

    return (
      <div
        className={"row m-0 wh-account-card-option mb-3" + (isSelected ? " wh-account-card-option-selected" : "")}
        onClick={isAvailable ? onChange : () => this._showNotAvailableToast()}
      >
        <div className="col-2 p-0 d-flex justify-content-center align-self-center">
          {/* Provider Icon */}
          <img
            className="h-100 align-self-center rounded"
            style={{ maxHeight: "44px", maxWidth: "44px" }}
            src={bankIconURL}
          />
          {/* End Provider Icon */}
        </div>
        <div className="col-9 align-self-center">
          {/* Account Details */}
          <div className="d-flex flex-column">
            <div>
              <span className="fw-bold">{displayBankName}</span>{" "}
              {!isAvailable && <span className="wh-coming-soon-card ms-2 t-875">Not available</span>}
            </div>
            <div className="text-muted font-weight-bold">
              <span>{displayAccountIdentifier}</span>
            </div>
          </div>
          {/* End Account Details */}
        </div>
        <div className="col-1 p-0 pe-2 text-center">
          {isAvailable && <input className="form-check-input" type="radio" checked={isSelected} />}
        </div>
      </div>
    );
  }
}

InvestmentBankPaymentOptionListItem.contextType = GlobalContext;

export default InvestmentBankPaymentOptionListItem;
