import React from "react";

type PropsType = {
  isSelected: boolean;
  name: string;
  description: string;
  onChange: () => void;
};

class InvestmentCashPaymentOptionListItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, name, description, onChange } = this.props;

    return (
      <div
        className={"row m-0 wh-account-card-option mb-3" + (isSelected ? " wh-account-card-option-selected" : "")}
        onClick={onChange}
      >
        <div className="col-2 p-0 align-self-center text-center">
          {/* Provider Icon */}
          <img className="h-100" src={"/images/icons/logo-dark.svg"} />
          {/* End Provider Icon */}
        </div>
        <div className="col-9 align-self-center">
          {/* Account Details */}
          <div className="d-flex flex-column">
            <span className=" fw-bold">{name}</span>
            <div className="text-muted font-weight-bold">
              <span>{description}</span>
            </div>
          </div>
          {/* End Account Details */}
        </div>
        <div className="col-1 p-0 pe-2 text-center">
          <input className="form-check-input" type="radio" checked={isSelected} />
        </div>
      </div>
    );
  }
}

export default InvestmentCashPaymentOptionListItem;
