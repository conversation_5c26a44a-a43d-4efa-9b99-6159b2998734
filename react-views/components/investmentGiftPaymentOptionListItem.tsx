import React from "react";
import { GiftDocument } from "../../models/Gift";
import { formatCurrency } from "../utils/currencyUtil";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  isSelected: boolean;
  gift: GiftDocument;
  onChange: () => void;
};

class InvestmentGiftPaymentOptionListItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, onChange, gift } = this.props;
    const { locale } = this.context as GlobalContextType;

    return (
      <div
        className={"row m-0 wh-account-card-option mb-3" + (isSelected ? " wh-account-card-option-selected" : "")}
        style={{ minWidth: "400px" }}
        onClick={onChange}
      >
        <div className="col-2 p-0 align-self-center text-center">
          <img className="h-100" src={"/images/icons/logo-dark.svg"} />
        </div>
        <div className="col-9 align-self-center">
          <div className="d-flex flex-column">
            <span className="fw-bold">
              Redeem my{" "}
              {formatCurrency(
                Decimal.div(gift.consideration.amount, 100).toNumber(),
                gift.consideration.currency,
                locale,
                0,
                0
              )}{" "}
              gift
            </span>
            <div className="text-muted font-weight-bold">
              <span>
                {formatCurrency(
                  Decimal.div(gift.consideration.amount, 100).toNumber(),
                  gift.consideration.currency,
                  locale,
                  0,
                  0
                )}{" "}
                available
              </span>
            </div>
          </div>
        </div>
        <div className="col-1 p-0 pe-2 text-center">
          <input className="form-check-input" type="radio" checked={isSelected} />
        </div>
      </div>
    );
  }
}

InvestmentGiftPaymentOptionListItem.contextType = GlobalContext;

export default InvestmentGiftPaymentOptionListItem;
