import React from "react";
import { currenciesConfig } from "@wealthyhood/shared-configs";

const { CURRENCY_SYMBOLS } = currenciesConfig;

type PropsType = {
  checked: boolean;
  name: string;
  size?: "lg" | "sm" | "";
  onSwitchChange: () => void;
  userCurrency: currenciesConfig.MainCurrencyType;
};

class InvestmentModeSwitch extends React.Component<PropsType> {
  render(): JSX.Element {
    const { checked, name, onSwitchChange, userCurrency } = this.props;

    return (
      <>
        <style
          dangerouslySetInnerHTML={{
            __html: `
              .switch.switch-icon input:empty ~ span::after {
                font-weight: bolder;
                content: "${CURRENCY_SYMBOLS[userCurrency]}";
                font-size: 1.3em;
              }

              .switch.switch-icon input:checked ~ span::after {
                font-weight: bolder;
                content: "%";
                font-size: 1.3em;
              }
            `
          }}
        />
        <span className="switch switch-icon">
          <label>
            <input type="checkbox" checked={checked} onChange={onSwitchChange} name={name} />
            <span />
          </label>
        </span>
      </>
    );
  }
}

export default InvestmentModeSwitch;
