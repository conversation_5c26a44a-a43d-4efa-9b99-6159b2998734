import React from "react";

type PropsType = {
  isSelected: boolean;
  onChange: () => void;
};

class InvestmentPaymentOptionsListItem extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isSelected, onChange, children } = this.props;
    const borderColor = isSelected ? "border-primary" : "";

    return (
      <label className={`option ${borderColor} mb-3 w-100 outline-hover`}>
        {/* Radio Selection */}
        <span className="option-control">
          <span className="radio h-100">
            <input type="radio" value={1} checked={isSelected} onChange={onChange} />
            <span />
          </span>
        </span>
        {/* End Radio Selection */}
        {children}
      </label>
    );
  }
}

export default InvestmentPaymentOptionsListItem;
