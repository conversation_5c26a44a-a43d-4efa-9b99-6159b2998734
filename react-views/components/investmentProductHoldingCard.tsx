import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";

type PropsType = {
  name: string;
  weight: string;
  logoUrl: string;
};

class InvestmentProductHoldingCard extends React.Component<PropsType> {
  render(): JSX.Element {
    const { name, weight, logoUrl } = this.props;

    return (
      <div className="card border-radius-lg border-1 border-secondary">
        <div className="card-body py-2 px-4">
          <div className="d-flex flex-row align-items-center">
            {logoUrl && (
              <span className="symbol symbol-25 symbol-white mr-5">
                <span className="symbol-label align-items-center">
                  {/*Pattern for loading image with fallback options */}
                  <object className="w-100" data={logoUrl} type="image/png">
                    <div></div>
                  </object>
                </span>
              </span>
            )}
            <div className="d-flex flex-column align-items-start">
              <OverlayTrigger
                placement="bottom"
                overlay={
                  <Popover id="popover-explanation">
                    <Popover.Content>{name}</Popover.Content>
                  </Popover>
                }
              >
                <p className="font-weight-bolder text-truncate w-275px w-lg-110px mb-0">{name}</p>
              </OverlayTrigger>
              <span className="text-primary">{weight}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default InvestmentProductHoldingCard;
