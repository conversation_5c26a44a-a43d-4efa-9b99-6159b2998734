import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InvestmentThemeSelectionOption from "./investmentThemeSelectionOption";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  selectedSectors: investmentUniverseConfig.InvestmentSectorType[];
  onSelectionChange: (assetClasses: investmentUniverseConfig.InvestmentSectorType[]) => void;
};
type StateType = {
  selectedSectors: investmentUniverseConfig.InvestmentSectorType[];
};

class InvestmentThemeSelection extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      selectedSectors: props.selectedSectors
    };
  }

  private _setActiveSectors = (updatedSector: investmentUniverseConfig.InvestmentSectorType): void => {
    // 1. Update selected asset classes
    const { selectedSectors } = this.state;
    const sectorIndex = selectedSectors.findIndex((sector) => sector === updatedSector);
    if (sectorIndex > -1) {
      // asset class is already selected -> remove it from selected ones
      selectedSectors.splice(sectorIndex, 1);
    } else {
      // asset class is not selected -> add it to selected ones
      selectedSectors.push(updatedSector);
    }

    // 2. Set state & run callback to update parent
    this.setState({ selectedSectors }, () => {
      const { onSelectionChange } = this.props;
      onSelectionChange(selectedSectors);
    });
  };

  render(): JSX.Element {
    const { selectedSectors } = this.state;
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);

    return (
      <div className="container-fluid p-0">
        <div className="row m-0">
          {Object.keys(SECTOR_CONFIG)
            .filter(
              (sectorKeyname: investmentUniverseConfig.InvestmentSectorType) =>
                SECTOR_CONFIG[sectorKeyname].selectable
            )
            .map((sectorKeyname: investmentUniverseConfig.InvestmentSectorType, index) => {
              const { fieldName, icon, whatIs } = SECTOR_CONFIG[sectorKeyname];
              return (
                <div className="col-6 p-md-3 p-2 align-self-center" key={`sector-${index}`}>
                  <div className={"row m-0 justify-content-" + (index % 2 == 0 ? "end" : "start")}>
                    <InvestmentThemeSelectionOption
                      icon={icon}
                      keyName={sectorKeyname}
                      name={fieldName}
                      isSelected={Boolean(selectedSectors.find((sector) => sector === sectorKeyname))}
                      whatIs={whatIs}
                      onChange={this._setActiveSectors}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    );
  }
}

InvestmentThemeSelection.contextType = GlobalContext;

export default InvestmentThemeSelection;
