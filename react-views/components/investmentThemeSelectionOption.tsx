import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InfoModal from "./modals/infoModal";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  icon: string;
  keyName: investmentUniverseConfig.InvestmentSectorType;
  name: string;
  isSelected?: boolean;
  whatIs: string;
  onChange?: (sector: investmentUniverseConfig.InvestmentSectorType) => void;
};
type StateType = {
  isSelected: boolean;
  showInfoDialog: boolean;
};

class InvestmentThemeSelectionOption extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isSelected: props.isSelected,
      showInfoDialog: false
    };
  }

  private _changeSelectionState = (): void => {
    this.setState(
      (prevState) => {
        const { isSelected } = prevState;
        return { isSelected: !isSelected };
      },
      () => {
        const { keyName, onChange } = this.props;
        onChange && onChange(keyName);
      }
    );
  };

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  render(): JSX.Element {
    const { icon, keyName, name, whatIs } = this.props;
    const { isSelected, showInfoDialog } = this.state;
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);
    const style = isSelected ? { background: (SECTOR_CONFIG[keyName] as any).colorClass, color: "#ffffff" } : {};

    return (
      <>
        <div
          key={keyName}
          style={style}
          className="card card-body wh-simple-card cursor-pointer border-0 text-center justify-content-center bg-hover-light"
          onClick={this._changeSelectionState}
        >
          <div className="row text-end">
            <div className="col">
              {isSelected ? (
                <i
                  className="material-symbols-outlined align-self-center"
                  onClick={(event) => {
                    event.stopPropagation();
                    this._setShowInfoDialog(true);
                  }}
                >
                  info
                </i>
              ) : (
                <i
                  className="material-symbols-outlined icon-primary align-self-center"
                  onClick={(event) => {
                    event.stopPropagation();
                    this._setShowInfoDialog(true);
                  }}
                >
                  info
                </i>
              )}
            </div>
          </div>
          <div className="row">
            <div className="col">
              <img alt="icon" className="align-self-center" src={icon} style={{ width: "90px", height: "90px" }} />
            </div>
          </div>
          <div className="row my-md-3">
            <div className="col">
              <div className="font-weight-bolder text-nowrap">{name}</div>
            </div>
          </div>
        </div>

        <InfoModal title={null} show={showInfoDialog} handleClose={() => this._setShowInfoDialog(false)}>
          <div dangerouslySetInnerHTML={{ __html: whatIs }} />
        </InfoModal>
      </>
    );
  }
}

InvestmentThemeSelectionOption.contextType = GlobalContext;

export default InvestmentThemeSelectionOption;
