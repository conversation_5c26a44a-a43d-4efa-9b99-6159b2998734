import React from "react";
import { TransactionDocument } from "../../models/Transaction";
import MainCard from "../layouts/mainCard";
import InvestorTransactionRow from "./investorTransactionRow";
import HorizontalScroller from "./horizontalScroller";
import LoadingSpinner from "./loadingSpinner";
import { CASH_ACTIVITY_FILTER_CONFIG, CashActivityFilterEnum } from "../configs/activityConfig";
import { TransactionActivityTransactionItemType } from "../types/transaction";
import { formatDateToMONYYYY } from "../utils/dateUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderType } from "../../services/truelayerService";

export type CashActivityPropsType = {
  investmentProducts: InvestmentProductDocument[];
  activity: TransactionActivityTransactionItemType[];
  truelayerProviders: ProviderType[];
  openTransactionModal: (transaction: TransactionDocument) => void;
};

type StateType = {
  showConfirmationModal: boolean;
  visibleTransactionsNum: number;
  showIncomingCashFlowModal: boolean;
  isLoadingTransactions: boolean;
  activeTransactionFilter?: CashActivityFilterEnum;
};

export default class InvestorCashActivity extends React.Component<CashActivityPropsType, StateType> {
  constructor(props: CashActivityPropsType) {
    super(props);
    this.state = {
      showConfirmationModal: false,
      isLoadingTransactions: false,
      visibleTransactionsNum: 5,
      showIncomingCashFlowModal: false,
      activeTransactionFilter: "All"
    };
  }

  private _onTransactionFilterChange(transactionFilter: CashActivityFilterEnum) {
    this.setState({ activeTransactionFilter: transactionFilter });
  }

  private _increaseVisibleTransactions = () => {
    const { visibleTransactionsNum } = this.state;
    this.setState({ visibleTransactionsNum: visibleTransactionsNum + 20 });
  };

  private _groupTransactionActivityItemsByMonth(
    transactionActivityItems: TransactionActivityTransactionItemType[]
  ): Record<string, TransactionActivityTransactionItemType[]> {
    return transactionActivityItems.reduce(
      (
        acc: Record<string, TransactionActivityTransactionItemType[]>,
        transactionActivityItem: TransactionActivityTransactionItemType
      ) => {
        const monthYearKey = formatDateToMONYYYY(new Date(transactionActivityItem.item.displayDate));
        if (!acc[monthYearKey]) {
          acc[monthYearKey] = [];
        }
        acc[monthYearKey].push(transactionActivityItem);
        return acc;
      },
      {}
    );
  }

  render() {
    const { truelayerProviders, investmentProducts, openTransactionModal, activity } = this.props;
    const { visibleTransactionsNum, activeTransactionFilter } = this.state;
    const transactionsActivityItemsData = activity.filter((transaction) => {
      if (activeTransactionFilter === "All") return true;
      return transaction.activityFilter === activeTransactionFilter;
    });

    const transactionActivityItemsToDisplay = transactionsActivityItemsData.slice(0, visibleTransactionsNum);
    const groupedTransactionActivityItems = this._groupTransactionActivityItemsByMonth(
      transactionActivityItemsToDisplay
    );

    return (
      <>
        <MainCard className={"px-md-0 px-0"} disableMarginBottom>
          <h5 className="fw-bolder mb-4 px-md-5 px-0">Cash Activity</h5>
          {/* Activity Filters */}
          <div>
            <HorizontalScroller id={"discovery-scroller"}>
              {(Object.keys(CASH_ACTIVITY_FILTER_CONFIG) as CashActivityFilterEnum[])
                .sort(
                  (a: CashActivityFilterEnum, b: CashActivityFilterEnum) =>
                    CASH_ACTIVITY_FILTER_CONFIG[a].order - CASH_ACTIVITY_FILTER_CONFIG[b].order
                )
                .map((transactionFilter: CashActivityFilterEnum) => (
                  <div
                    key={`tenor-nav-${transactionFilter}`}
                    className={
                      "cursor-pointer col py-2 px-3 align-self-center text-muted fw-bold text-center text-nowrap " +
                      (activeTransactionFilter === transactionFilter ? "active-transaction-filter" : "")
                    }
                    onClick={() => this._onTransactionFilterChange(transactionFilter)}
                  >
                    {transactionFilter}
                  </div>
                ))}
            </HorizontalScroller>
          </div>
          {/* End Activity Filters */}
          <div className="row m-0 px-md-5 px-2">
            {this.state.isLoadingTransactions ? (
              <div className="col p-0">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="col p-0">
                {transactionActivityItemsToDisplay.length > 0 ? (
                  Object.entries(groupedTransactionActivityItems).map(
                    (
                      [monthYear, transactionActivityItems]: [string, TransactionActivityTransactionItemType[]],
                      index
                    ) => (
                      <div key={index}>
                        <h5 className="row m-0 pt-4 mt-4">{monthYear}</h5>
                        {/* Display the month and year */}
                        {transactionActivityItems.map((transaction, index) => (
                          <InvestorTransactionRow
                            onClick={() => openTransactionModal(transaction.item)}
                            transaction={transaction.item}
                            cashFlowSign={transaction.cashFlowSign}
                            truelayerProviders={truelayerProviders}
                            investmentProducts={investmentProducts}
                            key={`transaction_${index}`}
                            isWithinCashActivity={true}
                          />
                        ))}
                      </div>
                    )
                  )
                ) : (
                  <>
                    <p className="text-center mt-4">No activity found.</p>
                    <p className="text-muted text-center">
                      You haven&apos;t {CASH_ACTIVITY_FILTER_CONFIG[activeTransactionFilter].notFoundMessage} yet.
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
          {transactionsActivityItemsData.length > visibleTransactionsNum && (
            <div className="row m-0 mt-3 px-md-5 px-2 justify-content-center">
              <button className="btn btn-secondary fw-100" onClick={this._increaseVisibleTransactions}>
                Load more
              </button>
            </div>
          )}
        </MainCard>
      </>
    );
  }
}
