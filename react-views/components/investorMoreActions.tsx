import React from "react";
import { canSendGift } from "../utils/userUtil";
import ConfigUtil from "../../utils/configUtil";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { entitiesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  activePage: string;
  onOptionClick: () => void;
};

class InvestorMoreActions extends React.Component<PropsType> {
  private _getHelpCentreLink = (): string => {
    const { user } = this.context as GlobalContextType;

    switch (user.companyEntity) {
      case entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK:
        return "https://www.wealthyhood.com/uk/help-centre";

      case entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE:
        if (user.residencyCountry === "GR") {
          return "https://www.wealthyhood.com/en-gr/help-centre";
        } else {
          return "https://www.wealthyhood.com/eu/help-centre";
        }

      default:
        return "https://www.wealthyhood.com/help-centre";
    }
  };

  render(): JSX.Element {
    const { onOptionClick } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const hasFreePlan = PRICE_CONFIG[user.subscription?.price]?.plan == "free";

    return (
      <>
        {user.subscription && (
          <div className="row m-0 p-0 mb-4">
            <div className="col p-0">
              <a
                href="/investor/change-plan"
                className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
              >
                <div className="d-flex justify-content-between">
                  <div className="d-flex">
                    <i className="wh-card material-symbols-outlined align-self-center text-center me-2">savings</i>
                    <span className={`align-self-center text-nowrap ${hasFreePlan ? "me-3" : "me-4"}`}>
                      {hasFreePlan ? "Upgrade your plan" : "Plans"}
                    </span>
                  </div>
                  <i
                    className="material-symbols-outlined align-self-center text-end fw-bolder"
                    style={{ fontSize: "14px" }}
                  >
                    arrow_forward_ios
                  </i>
                </div>
              </a>
            </div>
          </div>
        )}
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href="/investor/earn-free-shares"
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">groups</i>
                  <span className="align-self-center text-nowrap me-4">Earn free shares</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        {canSendGift(user) && (
          <div className="row m-0 p-0 mb-4">
            <div className="col p-0">
              <a
                href="/investor/send-gift"
                className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
              >
                <div className="d-flex justify-content-between">
                  <div className="d-flex">
                    <i className="wh-card material-symbols-outlined align-self-center text-center me-2">
                      celebration
                    </i>
                    <span className="align-self-center text-nowrap me-2">
                      Send {formatCurrency(20, user.currency, locale, 0, 0)} to a friend
                    </span>
                  </div>
                  <i
                    className="material-symbols-outlined align-self-center text-end fw-bolder"
                    style={{ fontSize: "14px" }}
                  >
                    arrow_forward_ios
                  </i>
                </div>
              </a>
            </div>
          </div>
        )}
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href="/investor/account/details"
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">badge</i>
                  <span className="align-self-center text-nowrap me-4">Account details</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        {user.shouldShowStatements && (
          <div className="row m-0 p-0 mb-4">
            <div className="col p-0">
              <a
                href="/investor/statements"
                className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
              >
                <div className="d-flex">
                  <div className="d-flex position-relative">
                    <i className="wh-card material-symbols-outlined align-self-center text-center me-2">news</i>
                    <span className="align-self-center me-4">Statements</span>
                  </div>
                  <i
                    className="material-symbols-outlined align-self-center w-100 text-end fw-bolder"
                    style={{ fontSize: "14px" }}
                  >
                    arrow_forward_ios
                  </i>
                </div>
              </a>
            </div>
          </div>
        )}
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href="/investor/billing"
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex position-relative">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">
                    receipt_long
                  </i>
                  <span className="align-self-center me-4">Billing</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href={"#"}
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
              onClick={() => {
                onOptionClick();
                eventEmitter.emit(EVENTS.bankAccountList);
              }}
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex position-relative">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">
                    account_balance
                  </i>
                  <span className="align-self-center me-4">Bank Accounts</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href={"/investor/notification-settings"}
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">
                    notifications_active
                  </i>
                  <span className="align-self-center me-4">Email Settings</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="col p-0">
            <a
              href={this._getHelpCentreLink()}
              target="_blank"
              className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
            >
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">help</i>
                  <span className="align-self-center me-4">Help Centre</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
        <div className="row m-0 p-0">
          <div className="col p-0">
            <a href="/logout" className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0">
              <div className="d-flex justify-content-between">
                <div className="d-flex">
                  <i className="wh-card material-symbols-outlined align-self-center text-center me-2">logout</i>
                  <span className="align-self-center me-4">Logout</span>
                </div>
                <i
                  className="material-symbols-outlined align-self-center text-end fw-bolder"
                  style={{ fontSize: "14px" }}
                >
                  arrow_forward_ios
                </i>
              </div>
            </a>
          </div>
        </div>
      </>
    );
  }
}

InvestorMoreActions.contextType = GlobalContext;

export default InvestorMoreActions;
