import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { formatDateToDDMONYY } from "../utils/dateUtil";
import Decimal from "decimal.js";
import { OrderDocument } from "../../models/Order";
import { AssetPriceInfo } from "../types/price";
import { formatCurrency } from "../utils/currencyUtil";
import { TransactionDocument } from "../../models/Transaction";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { getAssetIconUrl } from "../utils/universeUtil";
import { OrderSideType } from "../../services/wealthkernelService";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  parentTransaction: TransactionDocument;
  order: OrderDocument;
  currentPriceInfo?: AssetPriceInfo;
  onClick: () => void;
  enableBadge?: boolean;
};

const ORDER_ACTION_CONFIG: Record<OrderSideType, { color: string; label: string }> = {
  Buy: { color: "success", label: "Buy" },
  Sell: { color: "danger", label: "Sell" }
};

class InvestorOrderRow extends React.Component<PropsType> {
  private _getOrderName = (): string => {
    const { order } = this.props;
    return ASSET_CONFIG[order.commonId as investmentUniverseConfig.AssetType].simpleName;
  };

  private _getOrderSubtitle = (): JSX.Element => {
    const { order } = this.props;

    const { displayDate, status, isMatched, displayQuantity } = order;

    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    let orderDetails;
    if (displayQuantity) {
      const formattedQuantity = new Decimal(displayQuantity).toDecimalPlaces(4);
      orderDetails = <span className="ms-1">{` • ${formattedQuantity} shares`}</span>;
    }

    if (isMatched) {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {orderDetails}
        </span>
      );
    }

    if (status === "Cancelled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-danger me-1" style={{ fontSize: "16px" }}>
            cancel
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {orderDetails}
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-warning">Pending</span>
        {orderDetails}
      </span>
    );
  };

  private _getOrderIcon() {
    const { order } = this.props;

    return (
      <div className="d-flex justify-content-center align-content-center flex-wrap asset-card-md m-0 me-2">
        <img
          src={getAssetIconUrl(order.commonId as investmentUniverseConfig.AssetType)}
          style={{ height: "52px" }}
          className="border-light asset-icon"
          alt="provider logo"
        />
      </div>
    );
  }

  private _getOrderActionLabel(): JSX.Element {
    const { order } = this.props;
    const actionConfig = ORDER_ACTION_CONFIG[order.side];

    // If the order is cancelled, we want to override the color
    const color = order.status == "Cancelled" ? "muted" : actionConfig.color;
    return <span className={`fw-bold text-${color}`}>{actionConfig.label}</span>;
  }

  private _getOrderBadge(): JSX.Element {
    const { parentTransaction } = this.props;
    if (parentTransaction.category === "RebalanceTransaction") {
      return (
        <div className="wh-primary-label align-self-center t-75 ms-2" style={{ padding: "2px 6px" }}>
          Rebalance
        </div>
      );
    }
  }

  render(): JSX.Element {
    const { onClick, enableBadge } = this.props;
    const { locale } = this.context as GlobalContextType;

    return (
      <div onClick={onClick} className="row m-0 pt-4 clickable-transaction">
        <div className="col-9 p-0">
          <div className="d-flex">
            {this._getOrderIcon()}
            <div className="d-flex flex-column">
              <div className="d-flex">
                <span className="fw-normal">{this._getOrderName()}</span>
                {enableBadge && this._getOrderBadge()}
              </div>
              <div className="transaction-row-subtitle"> {this._getOrderSubtitle()}</div>
            </div>
          </div>
        </div>
        {this.props.order.displayAmount && (
          <div className="col-3 p-0 text-end">
            <div className="d-flex flex-column text-end transaction-row-subtitle">
              <span className="fw-bold d-block ">
                {formatCurrency(
                  new Decimal(this.props.order.displayAmount).div(100).toNumber(),
                  this.props.order.consideration.currency,
                  locale,
                  2,
                  2
                )}
              </span>
              {this._getOrderActionLabel()}
            </div>
          </div>
        )}
      </div>
    );
  }
}

InvestorOrderRow.contextType = GlobalContext;

export default InvestorOrderRow;
