import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { RewardDocument } from "../../models/Reward";
import { formatDateToDDMONYY } from "../utils/dateUtil";
import Decimal from "decimal.js";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { getAssetIconUrl } from "../utils/universeUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  onClick: () => void;
  reward: RewardDocument;
};

class InvestorRewardRow extends React.Component<PropsType> {
  private _getAssetTransactionSubtitle(reward: RewardDocument): JSX.Element {
    const { displayDate, status, quantity } = reward;

    if (status === "Settled") {
      const formattedDate = formatDateToDDMONYY(new Date(displayDate));
      const formattedQuantity = new Decimal(quantity).toDecimalPlaces(4);
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          <span className="ms-1">{` • ${formattedQuantity} shares`}</span>
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-warning">Pending</span>
      </span>
    );
  }

  render(): JSX.Element {
    const { reward, onClick } = this.props;
    const { locale } = this.context as GlobalContextType;

    const { displayAmount, consideration, status, asset } = reward;
    const amount = new Decimal(displayAmount).div(100).toNumber();

    const isClickable = onClick && status == "Settled";

    return (
      <div
        className={`row m-0 pt-4 ${isClickable ? "clickable-transaction" : ""}`}
        onClick={isClickable ? onClick : () => null}
      >
        <div className="col-8 p-0">
          <div className="d-flex">
            <div className="d-flex justify-content-center align-content-center flex-wrap asset-card-md m-0 me-2">
              <img
                src={getAssetIconUrl(asset)}
                style={{ height: "52px" }}
                className="border-light asset-icon"
                alt="provider logo"
              />
            </div>
            <div className="d-flex flex-column">
              <span className="fw-normal">{ASSET_CONFIG[asset].simpleName}</span>
              <div className="transaction-row-subtitle">{this._getAssetTransactionSubtitle(reward)}</div>
            </div>
          </div>
        </div>
        <div className="col-4 p-0 text-end">
          <div className="d-flex flex-column text-end transaction-row-subtitle">
            <span className="fw-bold d-block">{formatCurrency(amount, consideration.currency, locale, 2, 2)}</span>
            <span className="fw-bold text-primary">Reward</span>
          </div>
        </div>
      </div>
    );
  }
}

InvestorRewardRow.contextType = GlobalContext;

export default InvestorRewardRow;
