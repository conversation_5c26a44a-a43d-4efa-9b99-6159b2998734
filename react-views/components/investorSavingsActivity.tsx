import React, { Component } from "react";
import { TransactionDocument } from "../../models/Transaction";
import MainCard from "../layouts/mainCard";
import InvestorTransactionRow from "./investorTransactionRow";
import LoadingSpinner from "./loadingSpinner";
import { captureException } from "@sentry/react";
import { emitToast } from "../utils/eventService";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderType } from "../../services/truelayerService";
import { ToastTypeEnum } from "../configs/toastConfig";
import axios from "axios";
import { SavingsProductActivityItemType } from "../types/savings";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  initialSavingsActivityItems: SavingsProductActivityItemType[];
  investmentProducts: InvestmentProductDocument[];
  truelayerProviders: ProviderType[];
  openTransactionModal: (transaction: TransactionDocument) => void;
};

type StateType = {
  showConfirmationModal: boolean;
  visibleTransactionsNum: number;
  showIncomingCashFlowModal: boolean;
  isLoadingTransactions: boolean;
  transactions: SavingsProductActivityItemType[];
};

export default class InvestorSavingsActivity extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showConfirmationModal: false,
      isLoadingTransactions: false,
      visibleTransactionsNum: 5,
      showIncomingCashFlowModal: false,
      transactions: this.props.initialSavingsActivityItems
    };
  }

  private _increaseVisibleTransactions = () => {
    const { visibleTransactionsNum } = this.state;
    this.setState({ visibleTransactionsNum: visibleTransactionsNum + 20 });
  };

  private async _fetchAllTransactions() {
    const { savingsProductId } = this.props;

    try {
      const response = await axios({
        method: "GET",
        url: `/savings-products/activity?savingsProductId=${savingsProductId}`,
        headers: { "Content-Type": "application/json" }
      });

      this.setState({ transactions: response.data });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred while fetching data. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  async componentDidMount() {
    this._fetchAllTransactions();
  }

  render() {
    const { truelayerProviders, investmentProducts, openTransactionModal } = this.props;
    const { visibleTransactionsNum, transactions } = this.state;
    const transactionItemsToDisplay = transactions.slice(0, visibleTransactionsNum);

    return (
      <>
        <MainCard className={"px-md-0 px-0"} disableMarginBottom>
          <h5 className="fw-bolder mb-4 px-md-5 px-0">Activity</h5>
          <div className="row m-0 px-md-5 px-2">
            {this.state.isLoadingTransactions ? (
              <div className="col p-0">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="col p-0">
                {transactionItemsToDisplay.length > 0 ? (
                  transactionItemsToDisplay.map((transaction: SavingsProductActivityItemType, index) => {
                    return (
                      <InvestorTransactionRow
                        onClick={() => openTransactionModal(transaction.item)}
                        transaction={transaction.item}
                        truelayerProviders={truelayerProviders}
                        investmentProducts={investmentProducts}
                        key={`transaction_${index}`}
                      />
                    );
                  })
                ) : (
                  <p className="text-center mt-4">No activity found.</p>
                )}
              </div>
            )}
          </div>
          {transactions.length > visibleTransactionsNum && (
            <div className="row m-0 mt-3 px-md-5 px-2 justify-content-center">
              <button className="btn btn-secondary fw-100" onClick={this._increaseVisibleTransactions}>
                Load more
              </button>
            </div>
          )}
        </MainCard>
      </>
    );
  }
}
