import React from "react";

type PropsType = {
  classNames?: string[];
  label: string;
  taskCompleted: boolean;
};

class InvestorTaskCompletionLabel extends React.Component<PropsType> {
  render(): JSX.Element {
    const { classNames, label, taskCompleted } = this.props;

    return (
      <span
        className={`label ${
          taskCompleted ? "label-light-success" : "label-light-secondary text-dark"
        } label-inline ${classNames && classNames.join(" ")}`}
      >
        {label}
      </span>
    );
  }
}

export default InvestorTaskCompletionLabel;
