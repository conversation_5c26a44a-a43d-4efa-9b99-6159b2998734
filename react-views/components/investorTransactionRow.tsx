import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import {
  AssetTransactionDocument,
  CashbackTransactionDocument,
  ChargeTransactionDocument,
  DepositCashTransactionDocument,
  DisplayTagEnum,
  DividendTransactionDocument,
  RebalanceTransactionDocument,
  SavingsDividendTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument,
  TransactionCategoryType,
  TransactionDocument,
  TransactionStatusType,
  WealthyhoodDividendTransactionDocument,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import { BankAccountDocument } from "../../models/BankAccount";
import { ProviderType } from "../../services/truelayerService";
import { formatDateToDDMONYY } from "../utils/dateUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import { formatCurrency } from "../utils/currencyUtil";
import { PartialRecord } from "../types/utils";
import { getAssetIconUrl } from "../utils/universeUtil";
import { isCustodyCharge, isSingleAssetTransaction, isSubscriptionCharge } from "../utils/transactionUtil";
import ConfigUtil from "../../utils/configUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

const NON_CLICKABLE_CATEGORIES = [
  "ChargeTransaction",
  "SavingsTopupTransaction",
  "SavingsWithdrawalTransaction",
  "SavingsDividendTransaction"
];

const NON_CLICKABLE_CATEGORY_STATUS_EXEMPTIONS: PartialRecord<TransactionCategoryType, TransactionStatusType[]> = {
  SavingsTopupTransaction: ["PendingDeposit"]
};

type PropsType = {
  onClick: () => void;
  transaction: TransactionDocument;
  truelayerProviders: ProviderType[];
  investmentProducts: InvestmentProductDocument[];
  isWithinCashActivity?: boolean;
  cashFlowSign?: number;
};

const ICON_CONFIG: PartialRecord<TransactionCategoryType, { icon: string; iconColorClass: string }> = {
  DepositCashTransaction: {
    icon: "add",
    iconColorClass: "primary"
  },
  WithdrawalCashTransaction: {
    icon: "remove",
    iconColorClass: "primary"
  },
  AssetTransaction: {
    icon: "donut_large",
    iconColorClass: "primary"
  },
  RebalanceTransaction: {
    icon: "donut_large",
    iconColorClass: "primary"
  },
  ChargeTransaction: {
    icon: "request_quote",
    iconColorClass: "secondary"
  },
  CashbackTransaction: {
    icon: "payments",
    iconColorClass: "primary"
  },
  WealthyhoodDividendTransaction: {
    icon: "payments",
    iconColorClass: "primary"
  },
  SavingsTopupTransaction: {
    icon: "multiple_stop",
    iconColorClass: "primary"
  },
  SavingsWithdrawalTransaction: {
    icon: "multiple_stop",
    iconColorClass: "primary"
  },
  SavingsDividendTransaction: {
    icon: "humidity_percentage",
    iconColorClass: "primary"
  }
};

const TITLE_CONFIG: PartialRecord<TransactionCategoryType, { title: string }> = {
  DepositCashTransaction: {
    title: "Deposit"
  },
  WithdrawalCashTransaction: {
    title: "Withdraw"
  },
  AssetTransaction: {
    title: "My portfolio"
  },
  RebalanceTransaction: {
    title: "My portfolio"
  },
  CashbackTransaction: {
    title: "Cashback"
  },
  WealthyhoodDividendTransaction: {
    title: "Dividend"
  }
};

type TransactionActionType = "buy" | "sell" | "bonus" | "rebalance" | "dividend" | "update";

const ACTION_CONFIG: Record<TransactionActionType, { color: string; label: string }> = {
  buy: { color: "success", label: "Buy" },
  sell: { color: "danger", label: "Sell" },
  bonus: { color: "primary", label: "Bonus" },
  rebalance: { color: "primary", label: "Rebalance" },
  dividend: { color: "primary", label: "Dividend" },
  // For legacy transactions
  update: { color: "primary", label: "Update" }
};

const DISPLAY_TAG_CONFIG: Record<DisplayTagEnum, { className: string; label: string }> = {
  AUTOPILOT: { className: "wh-primary-label", label: "Autopilot" },
  INSTANT_INVEST: { className: "wh-primary-label", label: "Instant Invest" }
};

class InvestorTransactionRow extends React.Component<PropsType> {
  private _buildTransactionAssetIcon(data: { icon: string }): JSX.Element {
    return (
      <div className="d-flex justify-content-center align-content-center flex-wrap asset-card-md m-0 me-2">
        <img src={data.icon} style={{ height: "52px" }} className="border-light asset-icon" alt="provider logo" />
      </div>
    );
  }

  private _buildTransactionSubscriptionIcon(data: { color: string }): JSX.Element {
    return (
      <div
        className="d-flex justify-content-center align-content-center flex-wrap asset-card-md m-0 border-light asset-icon me-2"
        style={{ backgroundColor: data.color }}
      >
        <img
          style={{
            height: "20px"
          }}
          src="/images/icons/logo-white.svg"
          alt="provider logo"
        />
      </div>
    );
  }

  private _getTransactionIcon(transaction: TransactionDocument): JSX.Element {
    const { user } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const { category } = transaction;

    if (isSingleAssetTransaction(transaction)) {
      const { orders } = transaction as AssetTransactionDocument;
      return this._buildTransactionAssetIcon({
        icon: getAssetIconUrl(orders[0].commonId as investmentUniverseConfig.AssetType)
      });
    }

    if (category === "DividendTransaction") {
      const { asset } = transaction as DividendTransactionDocument;

      return this._buildTransactionAssetIcon({
        icon: getAssetIconUrl(asset)
      });
    }

    if (isCustodyCharge(transaction)) {
      const { color } = PLAN_CONFIG["free"];
      return this._buildTransactionSubscriptionIcon({ color });
    }

    if (isSubscriptionCharge(transaction)) {
      const { price } = transaction as ChargeTransactionDocument;
      const { color } = PLAN_CONFIG[PRICE_CONFIG[price].plan];
      return this._buildTransactionSubscriptionIcon({ color });
    }

    const { icon, iconColorClass } = ICON_CONFIG[category];
    return (
      <div className="d-flex justify-content-center align-content-center flex-wrap asset-card-md m-0 border-light asset-icon me-2">
        <span className={`material-symbols-outlined text-${iconColorClass}`} style={{ fontSize: "24px" }}>
          {icon}
        </span>
      </div>
    );
  }

  private _getTransactionTitle(transaction: TransactionDocument): string {
    const { user } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const { category } = transaction;

    if (category === "AssetTransaction") {
      const { portfolioTransactionCategory, orders } = transaction as AssetTransactionDocument;

      if (portfolioTransactionCategory === "update") {
        const { commonId } = orders[0];
        return ASSET_CONFIG[commonId as investmentUniverseConfig.AssetType].simpleName;
      }
    }

    if (category === "DividendTransaction") {
      const { asset } = transaction as DividendTransactionDocument;

      return ASSET_CONFIG[asset].simpleName;
    }

    if (isCustodyCharge(transaction)) {
      return "Custody Fee";
    }

    if (isSubscriptionCharge(transaction)) {
      const { price } = transaction as ChargeTransactionDocument;
      const { name } = PLAN_CONFIG[PRICE_CONFIG[price].plan];
      return name;
    }

    if (
      ["SavingsTopupTransaction", "SavingsWithdrawalTransaction", "SavingsDividendTransaction"].includes(category)
    ) {
      return (
        transaction as
          | SavingsTopupTransactionDocument
          | SavingsWithdrawalTransactionDocument
          | SavingsDividendTransactionDocument
      ).displayTitle;
    }

    return TITLE_CONFIG[category].title;
  }

  private _getTransactionBadge(transaction: TransactionDocument): JSX.Element {
    const { user } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const { category } = transaction;

    if (
      (transaction as DepositCashTransactionDocument | AssetTransactionDocument | RebalanceTransactionDocument)
        .linkedAutomation
    ) {
      return (
        <div className={"wh-primary-label align-self-center t-75 ms-2"} style={{ padding: "2px 6px" }}>
          Autopilot
        </div>
      );
    }

    const displayTagConfig = DISPLAY_TAG_CONFIG[transaction.displayTag];
    if (displayTagConfig) {
      return (
        <div
          className={`${displayTagConfig.className} align-self-center t-75 ms-2`}
          style={{ padding: "2px 6px" }}
        >
          {displayTagConfig.label}
        </div>
      );
    }

    if (isSubscriptionCharge(transaction)) {
      const { price } = transaction as ChargeTransactionDocument;
      const { recurrence } = PRICE_CONFIG[price];

      if (recurrence === "yearly") {
        return (
          <div className={"wh-primary-label align-self-center t-75 ms-2"} style={{ padding: "2px 6px" }}>
            Annual
          </div>
        );
      } else if (recurrence === "monthly") {
        return (
          <div className={"wh-primary-label align-self-center t-75 ms-2"} style={{ padding: "2px 6px" }}>
            Monthly
          </div>
        );
      }
    }

    if (category === "CashbackTransaction" || category === "WealthyhoodDividendTransaction") {
      const { price } = transaction as CashbackTransactionDocument | WealthyhoodDividendTransactionDocument;
      const { color, colorLight, name } = PLAN_CONFIG[PRICE_CONFIG[price].plan];

      return (
        <div
          key={"id"}
          className={"wh-primary-label align-self-center t-75 ms-2 " + color}
          style={{ padding: "2px 6px", color: `${color}`, backgroundColor: `${colorLight}` }}
        >
          {name}
        </div>
      );
    }
  }

  private _getTransactionAction(transaction: TransactionDocument): TransactionActionType {
    const { category } = transaction;

    if (category === "AssetTransaction") {
      const assetTransaction = transaction as AssetTransactionDocument;
      const { portfolioTransactionCategory, orders } = assetTransaction;

      const side =
        orders.length == 1 && portfolioTransactionCategory == "update"
          ? orders[0]?.side
          : portfolioTransactionCategory;

      return side.toLowerCase() as TransactionActionType;
    } else if (category === "CashbackTransaction") {
      return "bonus";
    } else if (category === "WealthyhoodDividendTransaction") {
      return "bonus";
    } else if (category === "RebalanceTransaction") {
      return "rebalance";
    } else if (category === "DividendTransaction") {
      return "dividend";
    }
  }

  private _getTransactionActionLabel(transaction: TransactionDocument): JSX.Element {
    const { isWithinCashActivity } = this.props;

    const transactionAction = this._getTransactionAction(transaction);

    if (!transactionAction) return;

    const actionConfig = ACTION_CONFIG[transactionAction];

    /**
     * Override color for cancelled transactions for buys, sells and rebalances.
     */
    if (
      ["buy", "sell", "rebalance", "update"].includes(transactionAction) &&
      transaction.displayStatus === "Cancelled"
    ) {
      return <span className="fw-bold text-muted">{actionConfig.label}</span>;
    }

    return (
      <span className={`fw-bold text-${isWithinCashActivity ? "primary" : actionConfig.color}`}>
        {actionConfig.label}
      </span>
    );
  }

  private _getTransactionAmount(transaction: TransactionDocument): string {
    const { locale } = this.context as GlobalContextType;
    const { isWithinCashActivity, cashFlowSign } = this.props;

    const { consideration } = transaction;

    if (!transaction.displayAmount) {
      return "";
    }

    // We transform the amount to be positive/negative depending on its cash flow effect. For example, a deposit is
    // positive because it adds to the user's portfolio, whereras a withdrawal is negative.
    const signedAmount = isWithinCashActivity
      ? Decimal.mul(transaction.displayAmount, cashFlowSign).toNumber()
      : transaction.displayAmount;

    return formatCurrency(
      Decimal.div(signedAmount, 100).toNumber(),
      consideration.currency,
      locale,
      2,
      2,
      isWithinCashActivity
    );
  }

  private _getWalletTransactionSubtitle(
    transaction: DepositCashTransactionDocument | WithdrawalCashTransactionDocument
  ): JSX.Element {
    const { displayDate, displayStatus } = transaction;
    const bankAccount = transaction.bankAccount as BankAccountDocument;
    const bankAccountDetails = bankAccount && (
      <span className="d-flex ms-1">
        {" • "}
        <span
          className="material-symbols-outlined align-self-center text-primary mx-1"
          style={{ fontSize: "16px" }}
        >
          account_balance
        </span>
        <span>{bankAccount.displayBankName}</span>
      </span>
    );

    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    if (displayStatus === "Settled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {bankAccountDetails}
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-warning">Pending</span>
        {bankAccountDetails}
      </span>
    );
  }

  private _getAssetTransactionSubtitle(transaction: AssetTransactionDocument): JSX.Element {
    const { displayDate, displayStatus, portfolioTransactionCategory, executionProgress } = transaction;

    if (["buy", "sell"].includes(portfolioTransactionCategory) && transaction.executionProgress) {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
            update
          </span>
          <span className="text-nowrap text-warning">{executionProgress.label}</span>
          <span className="ms-1">{` • ${executionProgress.matched}/${executionProgress.total} orders`}</span>
        </span>
      );
    }

    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    /**
     * Add transactions details at the end of the subtitle:
     * - For single asset transactions, display the quantity
     * - For automated investments, display the bank details
     */
    let transactionDetails;
    if (transaction?.displayQuantity) {
      const formattedQuantity = new Decimal(transaction.displayQuantity).toDecimalPlaces(4);
      transactionDetails = <span className="ms-1">{` • ${formattedQuantity} shares`}</span>;
    } else if (transaction.linkedAutomation && transaction.pendingDeposit) {
      const linkedDeposit = transaction.pendingDeposit as DepositCashTransactionDocument;
      const bankAccount = linkedDeposit.bankAccount as BankAccountDocument;
      transactionDetails = (
        <span className="d-flex ms-1">
          {" • "}
          <span
            className="material-symbols-outlined align-self-center text-primary mx-1"
            style={{ fontSize: "16px" }}
          >
            account_balance
          </span>
          <span>{bankAccount.displayBankName}</span>
        </span>
      );
    }

    if (displayStatus === "Settled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {transactionDetails}
        </span>
      );
    }

    if (displayStatus === "Cancelled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-danger me-1" style={{ fontSize: "16px" }}>
            cancel
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {transactionDetails}
        </span>
      );
    }

    if (displayStatus === "PendingDeposit" && !!transaction.linkedAutomation) {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-primary me-1" style={{ fontSize: "16px" }}>
            update
          </span>
          <span className="text-nowrap text-primary">Processing</span>
          {transactionDetails}
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-warning">Pending</span>
        {transactionDetails}
      </span>
    );
  }

  private _getRebalanceTransactionSubtitle(transaction: RebalanceTransactionDocument): JSX.Element {
    const { displayDate, displayStatus } = transaction;
    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    if (displayStatus === "Settled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
        </span>
      );
    }

    if (displayStatus === "Cancelled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-danger me-1" style={{ fontSize: "16px" }}>
            cancel
          </span>
          <span className="text-nowrap">{formattedDate}</span>
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-warning me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-warning">Pending</span>
      </span>
    );
  }

  private _getCashbackTransactionSubtitle(transaction: CashbackTransactionDocument): JSX.Element {
    const { displayDate, displayStatus } = transaction;
    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    if (displayStatus === "Settled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
        </span>
      );
    }

    if (displayStatus === "Cancelled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center text-danger me-1" style={{ fontSize: "16px" }}>
            cancel
          </span>
          <span className="text-nowrap">{formattedDate}</span>
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-primary me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-primary">Processing</span>
      </span>
    );
  }

  private _getSavingsTransferTransactionSubtitle(
    transaction: SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument
  ): JSX.Element {
    const { displayDate, displayStatus } = transaction;
    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    let bankAccountDetails;
    if ((transaction as SavingsTopupTransactionDocument)?.pendingDeposit?.bankAccount) {
      const linkedDeposit = (transaction as SavingsTopupTransactionDocument)
        .pendingDeposit as DepositCashTransactionDocument;
      const bankAccount = linkedDeposit.bankAccount as BankAccountDocument;
      bankAccountDetails = (
        <span className="d-flex ms-1">
          {" • "}
          <span
            className="material-symbols-outlined align-self-center text-primary mx-1"
            style={{ fontSize: "16px" }}
          >
            account_balance
          </span>
          <span>{bankAccount.displayBankName}</span>
        </span>
      );
    }

    if (displayStatus === "Settled") {
      return (
        <span className="d-flex text-muted text-truncate">
          <span className="material-icons align-self-center me-1" style={{ fontSize: "16px", color: "#31BA96" }}>
            check_circle
          </span>
          <span className="text-nowrap">{formattedDate}</span>
          {bankAccountDetails}
        </span>
      );
    }

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="material-icons align-self-center text-primary me-1" style={{ fontSize: "16px" }}>
          update
        </span>
        <span className="text-nowrap text-primary">Processing</span>
        {bankAccountDetails}
      </span>
    );
  }

  private _getSubscriptionChargeTransactionSubtitle(transaction: ChargeTransactionDocument): JSX.Element {
    const { displayDate, paymentMethod, chargeMethod } = transaction;
    const formattedDate = formatDateToDDMONYY(new Date(displayDate));

    return (
      <span className="d-flex text-muted text-truncate">
        <span className="text-nowrap">{formattedDate}</span>
        {chargeMethod === "card" && paymentMethod && (
          <span className="d-flex ms-1">
            <span>{" • "}</span>
            <span
              className="material-symbols-outlined align-self-center text-primary mx-1"
              style={{ fontSize: "16px" }}
            >
              credit_card
            </span>
            {` •••• ${(paymentMethod as PaymentMethodDocument).lastFourDigits}`}
          </span>
        )}
      </span>
    );
  }

  private _getTransactionSubtitle(transaction: TransactionDocument): JSX.Element {
    const { category, displayDate } = transaction;

    if (["WithdrawalCashTransaction", "DepositCashTransaction"].includes(category)) {
      return this._getWalletTransactionSubtitle(
        transaction as DepositCashTransactionDocument | WithdrawalCashTransactionDocument
      );
    }

    if (category === "AssetTransaction") {
      return this._getAssetTransactionSubtitle(transaction as AssetTransactionDocument);
    }

    if (category === "RebalanceTransaction") {
      return this._getRebalanceTransactionSubtitle(transaction as RebalanceTransactionDocument);
    }

    if (category === "CashbackTransaction") {
      return this._getCashbackTransactionSubtitle(transaction as CashbackTransactionDocument);
    }

    if (["SavingsTopupTransaction", "SavingsWithdrawalTransaction"].includes(category)) {
      return this._getSavingsTransferTransactionSubtitle(
        transaction as SavingsTopupTransactionDocument | SavingsWithdrawalTransactionDocument
      );
    }

    if (isSubscriptionCharge(transaction)) {
      return this._getSubscriptionChargeTransactionSubtitle(transaction as ChargeTransactionDocument);
    }

    /**
     * Default subtitle for all other transactions, including:
     * - Custody charge
     * - Asset Dividend
     * - Wealthyhood
     * - Savings Dividend
     */
    const formattedDate = formatDateToDDMONYY(new Date(displayDate));
    return (
      <span className="d-flex text-muted text-truncate">
        <span className="text-nowrap">{formattedDate}</span>
      </span>
    );
  }

  render(): JSX.Element {
    const { transaction, onClick } = this.props;
    const { locale } = this.context as GlobalContextType;

    const { consideration, category, displayStatus } = transaction;
    const isClickable =
      onClick &&
      (!NON_CLICKABLE_CATEGORIES.includes(category) ||
        NON_CLICKABLE_CATEGORY_STATUS_EXEMPTIONS[category]?.includes(displayStatus));

    return (
      <div
        className={`row m-0 pt-4 ${isClickable ? "clickable-transaction" : ""}`}
        onClick={isClickable ? onClick : () => null}
      >
        <div className="col-9 p-0">
          <div className="d-flex">
            {this._getTransactionIcon(transaction)}
            <div className="d-flex flex-column">
              <div className="d-flex">
                <span className="fw-normal">{this._getTransactionTitle(transaction)}</span>
                {this._getTransactionBadge(transaction)}
              </div>
              <div className="transaction-row-subtitle">{this._getTransactionSubtitle(transaction)}</div>
            </div>
          </div>
        </div>
        <div className="col-3 p-0 text-end">
          <div className="d-flex flex-column text-end transaction-row-subtitle">
            <span className="fw-bold d-block ">{this._getTransactionAmount(transaction)}</span>
            {this._getTransactionActionLabel(transaction)}
          </div>
        </div>
      </div>
    );
  }
}

InvestorTransactionRow.contextType = GlobalContext;

export default InvestorTransactionRow;
