import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";
import CircleInfoIcon from "./icons/circleInfoIcon";

type PropsType = {
  emoji: string;
  subtitle: string;
  tooltip?: string;
  value: string;
};

class KeyMetricCard extends React.Component<PropsType> {
  render(): JSX.Element {
    const { emoji, subtitle, tooltip, value } = this.props;

    return (
      <div className="card border-radius-xl shadow-xs">
        <div className="card-body p-3">
          <div className="row">
            <div className="col-3 col-lg-12 col-xl-3">
              <h1>{emoji}</h1>
            </div>
            <div className="col-9 col-lg-12 col-xl-9 pr-0">
              <h5 className="fw-bolder">{value}</h5>
              <h6 className="text-muted">
                {subtitle}{" "}
                {tooltip && (
                  <sup className="cursor-pointer">
                    <OverlayTrigger
                      placement="auto"
                      overlay={
                        <Popover id={`popover-explanation-${value}`}>
                          <Popover.Content>
                            <div
                              dangerouslySetInnerHTML={{
                                __html: tooltip
                              }}
                            />
                          </Popover.Content>
                        </Popover>
                      }
                    >
                      <span className="svg-icon svg-icon-xs">
                        <CircleInfoIcon />
                      </span>
                    </OverlayTrigger>
                  </sup>
                )}
              </h6>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default KeyMetricCard;
