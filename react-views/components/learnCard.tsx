import React from "react";
import LockIcon from "./icons/lockIcon";

type PropsType = {
  className?: string;
  title: string;
  body: string;
  bgImgSrc: string;
  imgSrc: string;
  link?: string;
  chapters: number;
  guideLink: string;
  isPayingSubscriber: boolean;
};

class LearnCard extends React.Component<PropsType> {
  render(): JSX.Element {
    const { title, body, imgSrc, bgImgSrc, chapters, className, guideLink, isPayingSubscriber } = this.props;

    return (
      <div
        onClick={() => (window.location.href = guideLink)}
        className={`wh-learning-card p-4 cursor-pointer ${className}`}
        style={{
          background: `url(${bgImgSrc})`,
          backgroundSize: "cover"
        }}
      >
        <div className="d-flex flex-column justify-content-center h-100">
          <div className="row m-0">
            <div className="col-md-9 col-8 align-self-center p-0">
              <h5 className="mb-4">{title}</h5>
              <p className="wh-learning-card-text">{body}</p>
              <a href={guideLink} className="btn btn-learning d-flex align-items-center gap-2">
                {!isPayingSubscriber && <LockIcon style={{ marginBottom: "3px" }} />}
                <span>{chapters} chapters</span>
              </a>
            </div>
            <div className="col-md-3 col-4 align-self-center p-0">
              <div className="d-flex justify-content-center">
                <img style={{ width: "165px", height: "165px" }} src={imgSrc} />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default LearnCard;
