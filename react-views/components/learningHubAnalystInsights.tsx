import React from "react";
import LearningHubArticlePreviewItem from "./learningHubArticlePreviewItem";
import { ArticleDataType } from "../configs/learningHubConfig";

type LearningHubAnalystInsightsPropType = {
  data: ArticleDataType[];
  showLoadMore: boolean;
  onLoadMoreClicked: () => void;
  isPayingSubscriber: boolean;
};
class LearningHubAnalystInsights extends React.Component<LearningHubAnalystInsightsPropType> {
  render(): JSX.Element {
    const { data, onLoadMoreClicked, showLoadMore, isPayingSubscriber } = this.props;

    return (
      <>
        <div>
          {data.map((newsItem, index) => (
            <LearningHubArticlePreviewItem key={index} data={newsItem} isPayingSubscriber={isPayingSubscriber} />
          ))}

          {showLoadMore && (
            <div className="row m-0 mt-3 px-md-5 px-2 justify-content-center">
              <button className="btn btn-secondary fw-100" onClick={onLoadMoreClicked}>
                Load more
              </button>
            </div>
          )}
        </div>
      </>
    );
  }
}

export default LearningHubAnalystInsights;
