import React from "react";
import { getRelativeTime } from "../utils/dateUtil";
import {
  ANALYST_INSIGHT_ICON_CONFIG,
  ARTICLE_ICON_CONFIG,
  ArticleContentTypeEnum,
  ArticleDataType
} from "../configs/learningHubConfig";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { isFeatureEnabled } from "../utils/featureFlagUtil";
import { ConfigCatFeatureFlags } from "../../config/featuresConfig";
import LockIcon from "./icons/lockIcon";

type LearningHubArticlePreviewItemPropType = {
  data: ArticleDataType;
  isPayingSubscriber: boolean;
};

class LearningHubArticlePreviewItem extends React.Component<LearningHubArticlePreviewItemPropType> {
  private _getArticleTypeIcon(): JSX.Element {
    const { data, isPayingSubscriber } = this.props;

    const { materialIcon, text, color, backgroundColor } = data?.analystInsightType
      ? ANALYST_INSIGHT_ICON_CONFIG[data.analystInsightType]
      : ARTICLE_ICON_CONFIG[data.contentType];

    return (
      <>
        <div style={{ color: color }} className="d-flex gap-2 align-items-center me-3">
          {isPayingSubscriber || data.contentType === ArticleContentTypeEnum.NEWS ? (
            <span
              style={{ backgroundColor: backgroundColor }}
              className="material-icons align-self-center learning-hub-article-preview-icon"
            >
              {materialIcon}
            </span>
          ) : (
            <span
              style={{ backgroundColor: backgroundColor, width: "30px", height: "30px", borderRadius: "50%" }}
              className="d-flex align-items-center justify-content-center"
            >
              <LockIcon fill={color} />
            </span>
          )}
          <span className="fw-bold">{text} </span>
        </div>
      </>
    );
  }

  private _getRedirectUrl(): string {
    const featureFlags = (this.context as GlobalContextType).featureFlags;
    const isAnalystInsightMigrationEnabled = isFeatureEnabled(
      ConfigCatFeatureFlags.ANALYST_INSIGHT_MIGRATION,
      featureFlags
    );
    const { data } = this.props;

    if (isAnalystInsightMigrationEnabled && data.contentType === ArticleContentTypeEnum.ANALYST_INSIGHT) {
      return `/investor/learning-hub/articles/analyst-insights/${data.id}`;
    } else if (data.contentType === ArticleContentTypeEnum.ANALYST_INSIGHT) {
      return `/investor/learning-hub/articles/analyst-insights/${data.key}`;
    } else if (data.contentType === ArticleContentTypeEnum.NEWS) {
      return `/investor/learning-hub/articles/news/${data.key}`;
    }
  }

  render(): JSX.Element {
    const { data } = this.props;
    const date =
      data.contentType === ArticleContentTypeEnum.ANALYST_INSIGHT
        ? getRelativeTime(new Date(data.publishedAt))
        : getRelativeTime(new Date(data.createdAt));

    const redirectUrl = this._getRedirectUrl();

    return (
      <>
        <div className="d-flex mb-5 cursor-pointer gap-2" onClick={() => (window.location.href = redirectUrl)}>
          <div className="learning-hub-article-preview-image-container pe-3">
            <img className="learning-hub-article-preview-image" src={data.previewImageURL} />
          </div>
          <div
            className="d-flex flex-column justify-content-center gap-3"
            id="text-column that takes 8 and i also a flexbox"
          >
            <div className="d-flex align-items-center">{this._getArticleTypeIcon()}</div>
            <h5 className="fw-bolder learning-hub-article-preview-title m-0">{data.title}</h5>
            <div className="d-flex align-items-center">
              <span>{date}</span>
              <span className="mx-2"> • </span>
              <span>{data.readingTime}</span>
            </div>
          </div>
        </div>
      </>
    );
  }
}

LearningHubArticlePreviewItem.contextType = GlobalContext;

export default LearningHubArticlePreviewItem;
