import React from "react";
import CustomAccordion from "./customAccordion";

export type GlossaryItemType = {
  createdAt: Date;
  title: string;
  definitionHTML: string;
};
export type LearningHubGlossaryPropsType = {
  glossaryItems: GlossaryItemType[];
};

type StateType = {
  expandedAccordionKey: string;
  searchTerm: string;
  filteredItems: GlossaryItemType[];
};
class LearningHubGlossary extends React.Component<LearningHubGlossaryPropsType, StateType> {
  constructor(props: LearningHubGlossaryPropsType) {
    super(props);
    this.state = {
      expandedAccordionKey: "",
      searchTerm: "",
      filteredItems: this.props.glossaryItems
    };

    this._handleSearchTermChange = this._handleSearchTermChange.bind(this);
  }

  private _onFaqCardClick(cardKey: string) {
    this.setState((prevState) => ({
      expandedAccordionKey: prevState.expandedAccordionKey !== cardKey ? cardKey : ""
    }));
  }

  private _handleSearchTermChange(event: React.ChangeEvent<HTMLInputElement>) {
    this.setState({ searchTerm: event.target.value });
  }

  render(): JSX.Element {
    const filteredItems = this.props.glossaryItems.filter((glossaryItem) => {
      if (this.state.searchTerm == "") {
        return glossaryItem;
      } else if (glossaryItem.title.toLowerCase().includes(this.state.searchTerm.toLowerCase()))
        return glossaryItem;
    });

    return (
      <>
        <div className="d-flex p-3 mb-5 glossary-search-container">
          <span className="material-symbols-outlined me-2 icon-primary">search</span>
          <input
            className="fw-bold glossary-search-input border-0"
            type="text"
            placeholder="Search Glossary"
            onChange={this._handleSearchTermChange}
          ></input>
        </div>
        {filteredItems.map((glossaryItem, index) => (
          <CustomAccordion
            id={`faq-card-${index}`}
            key={`faq-card-${index}`}
            title={glossaryItem.title}
            className="mb-1"
            expand={this.state.expandedAccordionKey === `faq-card-${index}`}
            onClick={() => this._onFaqCardClick(`faq-card-${index}`)}
          >
            <div
              className={`text-secondary accordion-html-container ${
                this.state.expandedAccordionKey === `faq-card-${index}` ? "expand" : ""
              }`}
              dangerouslySetInnerHTML={{ __html: glossaryItem.definitionHTML }}
            />
          </CustomAccordion>
        ))}
      </>
    );
  }
}

export default LearningHubGlossary;
