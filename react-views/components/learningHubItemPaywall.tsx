import React from "react";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

class LearningHubItemPaywall extends React.Component {
  private _getCheckmarkIcon(): JSX.Element {
    return (
      <svg width="8" height="6" className="me-3" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M2.67292 4.82456L7.13542 0.36206C7.20347 0.284282 7.28611 0.245393 7.38333 0.245393C7.49028 0.245393 7.5875 0.284282 7.675 0.36206C7.75278 0.439838 7.79167 0.532199 7.79167 0.639143C7.79167 0.736365 7.75278 0.823865 7.675 0.901643L3.00833 5.56831C2.92083 5.66553 2.81389 5.71414 2.6875 5.71414C2.56111 5.71414 2.44931 5.66553 2.35208 5.56831L0.339583 3.55581C0.261806 3.47803 0.218056 3.39053 0.208333 3.29331C0.208333 3.18637 0.247222 3.094 0.325 3.01623C0.4125 2.93845 0.504861 2.89956 0.602083 2.89956C0.699306 2.89956 0.786806 2.93845 0.864584 3.01623L2.67292 4.82456Z"
          fill="#536AE3"
        />
      </svg>
    );
  }
  render(): JSX.Element {
    const { user } = this.context as GlobalContextType;

    return (
      <div id="learning-hub-item-paywall" className="px-md-5 mx-auto">
        <div className="paywall-card shadow-sm">
          <h3 className="lh-sm">Master investing with our premium resources!</h3>
          <ul className="list-unstyled mt-4 mb-0 d-flex flex-column gap-3">
            <li>{this._getCheckmarkIcon()}50+ educational guides</li>
            <li>{this._getCheckmarkIcon()}Daily analyst insights</li>
            <li>{this._getCheckmarkIcon()}Quick takes</li>
            <li>{this._getCheckmarkIcon()}Weekly reviews</li>
            <li>
              {user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
                ? "Plus, better rates and zero custody fees!"
                : "Plus, better rates!"}
            </li>
          </ul>
        </div>
        <div style={{ marginTop: "40px" }}>
          <a href="/investor/change-plan?select=paid_low_yearly" className="btn btn-primary fw-100">
            Get Plus Access Now
          </a>
        </div>
      </div>
    );
  }
}

LearningHubItemPaywall.contextType = GlobalContext;

export default LearningHubItemPaywall;
