import React from "react";
import LearnCard from "./learnCard";
import { LearningGuideDataType } from "../configs/learningHubConfig";

type LearningHubLearningGuidesPropsType = {
  learningGuides: LearningGuideDataType[];
  isPayingSubscriber: boolean;
};

class LearningHubLearningGuides extends React.Component<LearningHubLearningGuidesPropsType> {
  render(): JSX.Element {
    const { learningGuides, isPayingSubscriber } = this.props;

    return (
      <>
        {learningGuides.map((guide, index) => (
          <LearnCard
            key={`learning-guide-${index}`}
            bgImgSrc={guide.webCoverImageURL}
            title={guide.title}
            body={guide.description}
            imgSrc={guide.guideIconURL}
            className="mb-4"
            chapters={guide.chapterCount}
            guideLink={`/investor/learning-hub/guides/${guide.slug}`}
            isPayingSubscriber={isPayingSubscriber}
          />
        ))}
      </>
    );
  }
}

export default LearningHubLearningGuides;
