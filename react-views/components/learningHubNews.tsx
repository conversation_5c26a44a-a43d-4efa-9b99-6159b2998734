import React from "react";
import LearningHubArticlePreviewItem from "./learningHubArticlePreviewItem";
import { ArticleDataType } from "../configs/learningHubConfig";

type LearningHubNewsPropType = {
  news: ArticleDataType[];
  isPayingSubscriber: boolean;
};

class LearningHubNews extends React.Component<LearningHubNewsPropType> {
  render(): JSX.Element {
    const { news, isPayingSubscriber } = this.props;

    return (
      <>
        <div>
          {news.map((newsItem, index) => (
            <LearningHubArticlePreviewItem key={index} data={newsItem} isPayingSubscriber={isPayingSubscriber} />
          ))}
        </div>
      </>
    );
  }
}

export default LearningHubNews;
