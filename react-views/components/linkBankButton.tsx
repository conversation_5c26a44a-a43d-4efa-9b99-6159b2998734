import React from "react";

type PropsType = {
  colorClass?: string;
};

class LinkBankButton extends React.Component<PropsType> {
  render(): JSX.Element {
    const { colorClass } = this.props;

    return (
      <a href="/investor/truelayer-auth-dialog">
        <div
          className={`d-flex flex-wrap flex-column align-items-center border-radius-xl p-8 ${
            colorClass || "bg-light bg-hover-secondary"
          }`}
        >
          <i className="ki ki-plus icon-2x" />
          <span className="text-dark-50 font-weight-light">Add Account</span>
        </div>
      </a>
    );
  }
}

export default LinkBankButton;
