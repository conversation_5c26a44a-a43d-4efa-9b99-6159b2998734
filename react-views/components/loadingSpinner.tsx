import React from "react";

type PropsType = {
  className?: string;
};

class LoadingSpinner extends React.Component<PropsType> {
  render(): JSX.Element {
    const { className } = this.props;
    return (
      <>
        <div className={`row m-0 p-0 py-4 text-center justify-content-center ${className}`}>
          <div id="cover-spin-relative" />
        </div>
        <div className="row m-0 pb-4">
          <div className="col-12 p-0">
            <p className="text-center">Loading...</p>
          </div>
        </div>
      </>
    );
  }
}

export default LoadingSpinner;
