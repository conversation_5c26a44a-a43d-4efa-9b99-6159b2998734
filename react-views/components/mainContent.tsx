import React from "react";

type PropsType = {
  asideExists?: boolean;
};

class MainContent extends React.Component<PropsType> {
  render(): JSX.Element {
    const { children, asideExists } = this.props;

    return (
      <div className="d-flex flex-column flex-root">
        <div className="d-flex flex-row flex-column-fluid page">
          <div className={`d-flex flex-column flex-row-fluid ${asideExists ? "wrapper" : ""}`} id="kt_wrapper">
            {children}
          </div>
        </div>
      </div>
    );
  }
}

export default MainContent;
