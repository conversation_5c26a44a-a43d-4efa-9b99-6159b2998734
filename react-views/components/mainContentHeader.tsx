import React from "react";

type PropsType = {
  subTitle: string;
  title: string;
};
class MainContentHeader extends React.Component<PropsType> {
  render(): JSX.Element {
    const { children, title, subTitle } = this.props;

    return (
      <div className="header" id="kt_header">
        <div className="container">
          {/* Page Title */}
          <div className="d-flex align-items-center flex-wrap mr-1 mt-5 mt-lg-0">
            <div className="d-flex align-items-baseline flex-wrap mr-5">
              <h1 className="text-dark font-weight-bolder my-1 mr-5">{title}</h1>
              <br />
              <h2>
                <small className="text-muted ml-2">{subTitle}</small>
              </h2>
            </div>
          </div>
          {/* End Page Title */}

          {/* Topbar */}
          <div className="topbar">
            {/* Topbar Actions */}
            {children}
            {/* End Topbar Actions */}

            {/* Topbar User Icon */}
            <div className="dropdown">
              <div className="topbar-item mr-3" data-toggle="dropdown">
                <div className="btn btn-lg btn-icon btn-bg-white btn-hover-primary btn-icon-primary align-items-center overflow-hidden shadow bg-navy">
                  <img
                    className="h-75 align-self-end"
                    alt="Logo"
                    src="/keen2/assets/media/svg/avatars/001-boy.svg"
                  />
                </div>
              </div>
              <div
                className="dropdown-menu dropdown-menu-anim-up dropdown-menu-right py-6 px-3"
                aria-labelledby="dropdownMenuButton"
              >
                <div className="row">
                  <div className="col-12">
                    <a
                      className="btn btn-block btn-sm btn-light-white font-weight-bolder py-3 px-6 my-1"
                      style={{ color: "#3699FF" }}
                      href="/investor/billing"
                    >
                      Billing
                    </a>
                  </div>
                </div>
                <div className="row">
                  <div className="col-12">
                    <a
                      className="btn btn-block btn-sm btn-light-primary font-weight-bolder py-3 px-6 my-1"
                      href="/logout"
                    >
                      Log Out
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {/* End Topbar User Icon */}
          </div>
          {/* End Topbar */}
        </div>
      </div>
    );
  }
}

export default MainContentHeader;
