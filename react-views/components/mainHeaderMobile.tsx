import React from "react";
import MenuIcon from "./icons/menuIcon";

class MainHeaderMobile extends React.Component {
  render(): JSX.Element {
    return (
      <div className="header-mobile bg-navy" id="kt_header_mobile">
        <a href="/">
          <img className="max-h-30px" alt="Logo" src="https://wealthyhood.com/img/logo-full-light.png" />
        </a>
        <div className="d-flex align-items-center">
          <button className="btn btn-icon btn-icon-white btn-hover-icon-white" id="kt_aside_mobile_toggle">
            <span className="svg-icon svg-icon-xxl">
              <MenuIcon />
            </span>
          </button>
        </div>
      </div>
    );
  }
}

export default MainHeaderMobile;
