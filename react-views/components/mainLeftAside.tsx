import React from "react";
import LogOutIcon from "./icons/logOutIcon";

type PropsType = {
  email: string;
};

class MainLeftAside extends React.Component<PropsType> {
  render(): JSX.Element {
    const { email, children } = this.props;

    return (
      <div className="aside aside-left aside-fixed" id="kt_aside">
        {/* Aside Brand */}
        <div className="aside-brand h-80px px-7 flex-shrink-0">
          <a className="aside-logo" href="/">
            <img className="w-100 max-h-40px" alt="Logo" src="https://wealthyhood.com/svg/logo-full-light.svg" />
          </a>
        </div>
        {/* End Aside Brand */}

        {/* Aside Menu */}
        <div
          className="aside-menu mt-5"
          id="kt_aside_menu"
          data-menu-vertical="1"
          data-menu-scroll="1"
          data-menu-dropdown-timeout="500"
        >
          <ul className="menu-nav h-100">
            {children}

            {/* Log out Menu Item */}
            <li className="menu-item w-100 p-5">
              <a
                href={`https://wealthyhood.com/referral-dashboard/?wlthd-email=${email}`}
                target="_blank"
                rel="noreferrer"
                className="btn btn-lg btn-primary font-weight-bolder"
                style={{ backgroundImage: "linear-gradient(to right, #0acffe 0%, #495aff 100%)" }}
              >
                Refer Friends
              </a>
            </li>

            <li className="menu-item w-100 pb-4">
              <a className="menu-link" href="/logout">
                <span className="svg-icon svg-icon-primary svg-icon-2x menu-icon">
                  <LogOutIcon />
                </span>
                <span className="menu-text">Log out</span>
              </a>
            </li>
            {/* End Log out Menu Item */}
          </ul>
        </div>
        {/* End Aside Menu */}
      </div>
    );
  }
}

export default MainLeftAside;
