import React from "react";
import AsideMenuOption from "../components/asideMenuOption";
import GroupIcon from "../components/icons/groupIcon";
import UpDownIcon from "./icons/upDownIcon";
import CreditCardIcon from "./icons/creditCardIcon";
import ReturnIcon from "./icons/returnIcon";
import GiftIcon from "./icons/giftIcon";
import BankIcon from "./icons/bankIcon";
import DollarIcon from "./icons/dollarIcon";

const ADMIN_ASIDE_CONFIG: {
  activePageName: string;
  href: string;
  label: string;
  Icon: any;
  activeIconClass: string;
  inactiveIconClass: string;
}[] = [
  {
    activePageName: "userlist",
    href: "/admin/users",
    label: "Users",
    Icon: GroupIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "order-management",
    href: "/admin/order-management",
    label: "Orders",
    Icon: DollarIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "assetTransactionsList",
    href: "/admin/asset-transactions",
    label: "Investments",
    Icon: UpDownIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "depositTransactionsList",
    href: "/admin/deposit-transactions",
    label: "Payments",
    Icon: CreditCardIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "withdrawalTransactionsList",
    href: "/admin/withdrawal-transactions",
    label: "Withdrawals",
    Icon: ReturnIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "bankAccounts",
    href: "/admin/bank-accounts",
    label: "Bank Accounts",
    Icon: BankIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "rewards",
    href: "/admin/rewards",
    label: "Rewards",
    Icon: GiftIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "gifts",
    href: "/admin/gifts",
    label: "Gifts",
    Icon: GiftIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  },
  {
    activePageName: "failedusers",
    href: "/admin/users/failed",
    label: "Failed Accounts",
    Icon: GroupIcon,
    activeIconClass: "",
    inactiveIconClass: "svg-icon-primary"
  }
];

type PropsType = {
  activePage: string;
};

class MainLeftAsideAdminOptions extends React.Component<PropsType> {
  render(): JSX.Element {
    const { activePage } = this.props;

    return (
      <>
        {ADMIN_ASIDE_CONFIG.map(({ activePageName, href, label, Icon, activeIconClass, inactiveIconClass }) => (
          <AsideMenuOption
            isActive={activePage === activePageName}
            href={href}
            label={label}
            Icon={Icon}
            activeIconClass={activeIconClass}
            inactiveIconClass={inactiveIconClass}
            key={`aside-option-${activePageName}`}
          />
        ))}
      </>
    );
  }
}

export default MainLeftAsideAdminOptions;
