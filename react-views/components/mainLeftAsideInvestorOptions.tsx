import React from "react";
import AsideMenuOptionNew from "./asideMenuOptionNew";

type PropsType = {
  activePage: string;
};

class MainLeftAsideInvestorOptions extends React.Component<PropsType> {
  private _getOptionsConfig = (): {
    activePageName: string;
    disabled?: boolean;
    href: string;
    label: string;
    materialIcon: string;
    className?: string;
  }[] => {
    return [
      {
        activePageName: "home",
        href: "/",
        label: "Home",
        materialIcon: "home"
      },
      {
        activePageName: "learning-hub",
        href: "/investor/learning-hub",
        label: "Learn",
        materialIcon: "emoji_objects"
      },
      {
        activePageName: "investments",
        href: "/investor/investments",
        label: "Invest",
        materialIcon: "waterfall_chart",
        className: "flip-material-icon"
      },
      {
        activePageName: "autopilot",
        href: "/investor/autopilot",
        label: "Autopilot",
        materialIcon: "autopay"
      },
      {
        activePageName: "cash",
        href: "/investor/cash",
        label: "Cash",
        materialIcon: "account_balance_wallet"
      }
    ];
  };

  render(): JSX.Element {
    const { activePage, children } = this.props;

    return (
      <>
        {children}

        {this._getOptionsConfig()
          .filter(({ disabled }) => !disabled)
          .map(({ activePageName, href, label, materialIcon, className }) => (
            <AsideMenuOptionNew
              key={`aside-menu-option-${label}`}
              isActive={activePage === activePageName}
              href={href}
              label={label}
              materialIcon={materialIcon}
              className={className}
            />
          ))}
      </>
    );
  }
}

export default MainLeftAsideInvestorOptions;
