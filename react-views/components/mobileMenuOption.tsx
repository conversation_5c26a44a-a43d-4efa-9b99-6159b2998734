import React from "react";

type PropsType = {
  isActive: boolean;
  href: string;
  label: string;
  materialIcon: string;
  className: string;
};

class MobileMenuOptionNew extends React.Component<PropsType> {
  render(): JSX.Element {
    const { isActive, href, label, materialIcon, className } = this.props;

    return (
      <a
        href={href}
        className={`align-self-center mx-3 text-decoration-none d-flex flex-row ${isActive ? "wh-card" : ""}`}
      >
        <span
          className={`material-symbols-outlined align-self-center me-2 ${className}`}
          style={{ fontSize: "18px" }}
        >
          {materialIcon}
        </span>
        {isActive && <span className="text-nowrap">{label}</span>}
      </a>
    );
  }
}

export default MobileMenuOptionNew;
