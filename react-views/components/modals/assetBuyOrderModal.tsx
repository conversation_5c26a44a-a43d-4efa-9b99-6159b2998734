import React from "react";
import { Modal } from "react-bootstrap";
import Decimal from "decimal.js";
import {
  cashbacksConfig,
  currenciesConfig,
  giftsConfig,
  investmentsConfig,
  investmentUniverseConfig,
  localeConfig,
  plansConfig
} from "@wealthyhood/shared-configs";
import { PendingOrderType } from "../../types/order";
import { formatCurrency } from "../../utils/currencyUtil";
import { formatPercentage, formatShares } from "../../utils/formatterUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { LinkedBankAccount } from "../../types/bank";
import { PaymentMethod, ViewModeType } from "../../types/modal";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import FxRatePreviewRow from "../fxRatePreviewRow";
import { TransactionPreview } from "../../types/transactionPreview";
import { OtherModalType } from "../modalsWrapper";
import { GiftDocument } from "../../../models/Gift";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import ConfigUtil from "../../../utils/configUtil";
import CommissionPreviewRow from "../commissionPreviewRow";
import ExecutionWindowPreviewRows from "../executionWindowPreviewRows";
import { MarketInfoType } from "./assetSideModal/assetSideModal.types";
import CashbackPreviewRow from "../cashbackPreviewRow";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import { ExecutionModeType } from "../../types/executionMode";
import { UserDocument } from "../../../models/User";
import ExecutionModeToggle from "../executionModeToggle";
import MarketHoursTag from "./assetSideModal/marketHoursTag";
import {
  getLocalExecutionWindowStart,
  LSE_MARKET_CLOSE,
  LSE_MARKET_OPEN,
  US_MARKET_CLOSE,
  US_MARKET_OPEN
} from "../../utils/executionWindowUtil";
import AddBankAccountButton from "../addBankAccountButton";
import { isAllowedOneStepInvest } from "../../utils/userUtil";

const { CASHBACK_RATES, MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;
const { MIN_ALLOWED_ASSET_QUANTITY, MIN_ALLOWED_ASSET_INVESTMENT } = investmentsConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

type OrderStatusType =
  | "allowedCash"
  | "allowedBank"
  | "insufficientCash"
  | "insufficientCashTopupPrompt"
  | "forbiddenOrderAmount"
  | "forbiddenOrderQuantity"
  | "noInvestmentCashBuy"
  | "noInvestmentBankBuy";
type PropsType = {
  portfolioId: string;
  assetCommonId: investmentUniverseConfig.AssetType;
  assetPrice: number;
  tradedCurrency: currenciesConfig.MainCurrencyType;
  assetPriceInUserCurrency: number;
  availableCash: number;
  show: boolean;
  handleClose: () => void;
  linkedBankAccounts: LinkedBankAccount[];
  gift: GiftDocument;
  initialSelectedLinkedBankAccount: LinkedBankAccount;
  marketInfo?: MarketInfoType;
};

// The orderAmount represents the internal state of the order.
// For browser compatibility reasons, it's been decided to be represented
// internally as a string. When used for calculations, it should be
// converted to a number. When it's displayed to the user, it should be formatted
// accordingly (as currency when the user is buying and as a number with
// 4 decimals when the user is selling).
type StateType = {
  orderAmount: string;
  paymentMethod: PaymentMethod;
  selectedLinkedBankAccount: LinkedBankAccount;
  viewMode: ViewModeType;
  transactionPreview: TransactionPreview;
  actionButtonClicked: boolean;
  viewModeBeforeCashback?: ViewModeType;
  executionMode: ExecutionModeType | undefined;
};

class AssetBuyOrderModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      paymentMethod: this._getInitialPaymentMethod(),
      orderAmount: this._getInitialOrderAmount(),
      selectedLinkedBankAccount:
        this.props.initialSelectedLinkedBankAccount ?? this.props.linkedBankAccounts?.[0] ?? null,
      viewMode: "ACTION",
      transactionPreview: null,
      actionButtonClicked: false,
      executionMode: null
    };
  }

  private _getMarketHoursContent = (): JSX.Element => {
    const { assetCommonId } = this.props;

    return investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf" ? (
      <>
        <p className="text-muted">
          The EU market is open from Monday through Friday {LSE_MARKET_OPEN.toFormat("HH:mm")} to{" "}
          {LSE_MARKET_CLOSE.toFormat("HH:mm")} local time or from {LSE_MARKET_OPEN.toLocal().toFormat("HH:mm")} to{" "}
          {LSE_MARKET_CLOSE.toLocal().toFormat("HH:mm")} your time.
        </p>
        <p className="text-muted">When your ETF order will be executed depends on the smart execution mode.</p>
        <p className="text-muted">
          With smart execution on, ETF orders are executed at our daily trading window at{" "}
          {getLocalExecutionWindowStart()} every weekday, enjoying ZERO COMMISSIONS.
        </p>
        <p className="text-muted">
          With smart execution off (express execution), ETF orders are executed instantly during European market
          hours and face a €1 commission per ETF order.
        </p>
        <p className="text-muted">
          ETF orders placed outside market hours are executed at the next market opening.
        </p>
      </>
    ) : (
      <>
        <p className="text-muted">
          The US market is open from Monday through Friday {US_MARKET_OPEN.toFormat("HH:mm")} to{" "}
          {US_MARKET_CLOSE.toFormat("HH:mm")} local time or from {US_MARKET_OPEN.toLocal().toFormat("HH:mm")} to{" "}
          {US_MARKET_CLOSE.toLocal().toFormat("HH:mm")} your time.
        </p>
        <p className="text-muted">
          Stock orders placed within market hours, will be executed instantly. If an order is placed outside market
          hours, it will be executed at the opening of the next trading session.{" "}
        </p>
      </>
    );
  };

  private _getStatusMessage = (
    orderStatus: OrderStatusType,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { availableCash } = this.props;
    const orderAmountNum = this._getOrderAmountAsFloat();

    const STATUS_MESSAGES: Record<OrderStatusType, string> = {
      allowedCash: `${formatCurrency(
        Decimal.sub(availableCash, orderAmountNum || 0).toNumber(),
        currency,
        locale
      )} remaining cash`,
      allowedBank: `${formatCurrency(
        new Decimal(orderAmountNum || 0).toNumber(),
        currency,
        locale
      )} paid with your bank account`,
      insufficientCash: `You have only ${formatCurrency(availableCash, currency, locale)}.`,
      forbiddenOrderAmount: `You cannot place a buy order for less than ${formatCurrency(
        MIN_ALLOWED_ASSET_INVESTMENT,
        currency,
        locale
      )} in value`,
      forbiddenOrderQuantity: "Order quantity can't be less than 0.0001 units",
      noInvestmentCashBuy: `${formatCurrency(availableCash, currency, locale)} remaining cash`,
      noInvestmentBankBuy: "",
      insufficientCashTopupPrompt: `Your available balance is ${formatCurrency(
        availableCash,
        currency,
        locale
      )}. Please top up your account to proceed.`
    };
    return STATUS_MESSAGES[orderStatus];
  };

  private _getActionViewCTACopy(): string {
    const { linkedBankAccounts } = this.props;
    const { paymentMethod } = this.state;

    const transactionStatus = this._getTransactionStatus();

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP" && (!linkedBankAccounts || linkedBankAccounts?.length === 0)) {
      return "Proceed";
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      return "Top up";
    } else {
      return "Review";
    }
  }

  private _renderActionButton(): JSX.Element {
    const { orderAmount } = this.state;

    const allowedTransaction = this._isTransactionAllowed();
    const ctaCopy = this._getActionViewCTACopy();

    if (!orderAmount || !allowedTransaction) {
      return (
        <button type="button" className="btn btn-primary fw-100" disabled>
          {ctaCopy}
        </button>
      );
    }

    return (
      <LoadingOnSubmitButton
        type="button"
        className="btn btn-primary fw-100"
        enableOnCompletion={true}
        customonclick={async () => {
          this._setActionButtonClicked(true);
          await this._reviewPayment();
        }}
      >
        {ctaCopy}
      </LoadingOnSubmitButton>
    );
  }

  private _reviewPayment = async () => {
    const { linkedBankAccounts } = this.props;
    const { paymentMethod } = this.state;

    if (!this._isTransactionAllowed()) {
      return;
    }

    const transactionStatus = this._getTransactionStatus();

    // When user clicks on review, we move to the 'REVIEW' view mode unless they're updating an existing automation.
    if (linkedBankAccounts.length === 0 && paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      eventEmitter.emit(EVENTS.linkBankAccountModal, {
        originModal: "assetBuy" as OtherModalType
      });
      return;
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      eventEmitter.emit(EVENTS.depositMethodsModal, {
        originModal: "assetBuy" as OtherModalType
      });
    } else {
      await this._getTransactionPreview(() => this._setViewMode("REVIEW"));
    }
  };

  private _getTransactionStatus = (): OrderStatusType => {
    const { user } = this.context as GlobalContextType;
    const { availableCash, assetPriceInUserCurrency } = this.props;
    const { paymentMethod } = this.state;

    const orderAmountNum = this._getOrderAmountAsFloat();

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP" && !orderAmountNum) {
      return "noInvestmentBankBuy";
    }

    if (orderAmountNum < MIN_ALLOWED_ASSET_INVESTMENT) {
      return "forbiddenOrderAmount";
    }

    const quantity = orderAmountNum / assetPriceInUserCurrency;
    if (quantity < MIN_ALLOWED_ASSET_QUANTITY) {
      return "forbiddenOrderQuantity";
    }

    const remainingAmount = availableCash - orderAmountNum;

    if (paymentMethod === "CASH" && remainingAmount < 0) {
      return isAllowedOneStepInvest(user) ? "insufficientCash" : "insufficientCashTopupPrompt";
    }

    return "allowedBank";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _getOrderAmountAsFloat(): number {
    const { orderAmount } = this.state;
    return orderAmount ? Number.parseFloat(orderAmount) : 0;
  }

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setPaymentMethodAndAccount = (
    paymentMethod: PaymentMethod,
    selectedLinkedBankAccount: LinkedBankAccount
  ): void => {
    this.setState({ paymentMethod, selectedLinkedBankAccount });
  };

  private _getDisclaimer = (): string => {
    const { paymentMethod } = this.state;

    if (paymentMethod === "GIFT") {
      return `Any shares bought with a voucher must be held for at least ${RESTRICTED_HOLDING_PERIOD_DAYS} days. Capital at risk.`;
    } else return "Capital at risk.";
  };

  private _getInitialPaymentMethod = (): PaymentMethod => {
    const { availableCash, gift, linkedBankAccounts } = this.props;

    if (gift && !gift.used) {
      return "GIFT";
    }

    if (availableCash > MIN_ALLOWED_ASSET_INVESTMENT || !linkedBankAccounts.length) {
      return "CASH";
    }

    return "BANK_ACCOUNT_TOP_UP";
  };

  private _getInitialOrderAmount = (): string => {
    const { gift } = this.props;

    if (this._getInitialPaymentMethod() === "GIFT") {
      return new Decimal(gift.consideration.amount).div(100).toNumber().toString();
    } else return "";
  };

  private _submitOrder = async (
    assetCommonId: investmentUniverseConfig.AssetType,
    order: PendingOrderType
  ): Promise<void> => {
    const { portfolioId, gift } = this.props;
    const { selectedLinkedBankAccount, paymentMethod, executionMode } = this.state;

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your order is being sent...");

    try {
      let body;
      let url;

      const executeEtfOrdersInRealtime = executionMode ? executionMode === "EXPRESS" : undefined;

      if (paymentMethod == "CASH") {
        body = {
          pendingOrders: {
            [assetCommonId]: order
          }
        };
        url = `/portfolios/${portfolioId}${
          executeEtfOrdersInRealtime !== undefined
            ? `?executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
      } else if (paymentMethod === "GIFT") {
        url = `/portfolios/${portfolioId}?paymentMethod=gift${
          executeEtfOrdersInRealtime !== undefined
            ? `&executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
        body = {
          pendingOrders: {
            [assetCommonId]: order
          },
          gift: gift.id
        };
      } else if (paymentMethod == "BANK_ACCOUNT_TOP_UP") {
        const bankAccountId: string = selectedLinkedBankAccount?.id;

        body = {
          asset: assetCommonId,
          orderAmount: order.money,
          bankAccountId
        };
        url = `/investor/deposit-and-buy-asset${
          executeEtfOrdersInRealtime !== undefined
            ? `?executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
      }

      const res = await axios.post(url, body);
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({ toastType: ToastTypeEnum.error, content: "Oops! Something went wrong. Please try again." });
      // rethrow error to be handled from loadingOnSubmitButton that will call this async method
      throw err;
    }
  };

  private _isTransactionAllowed = (): boolean => {
    return (["allowedCash", "allowedBank", "insufficientCashTopupPrompt"] as OrderStatusType[]).includes(
      this._getTransactionStatus()
    );
  };

  private _shouldShowExecutionToggle(
    user: UserDocument,
    assetCategory: investmentUniverseConfig.AssetCategoryType
  ): boolean {
    return user.isRealtimeETFExecutionEnabled && assetCategory === "etf";
  }

  private async _getTransactionPreview(callback: () => void) {
    if (!this._isTransactionAllowed()) {
      return;
    }

    const { assetCommonId } = this.props;
    const { orderAmount } = this.state;
    const { user } = this.context as GlobalContextType;

    try {
      const res = await axios.post("/transactions/asset/preview?portfolioTransactionType=update", {
        pendingOrders: {
          [assetCommonId]: { side: "buy", money: orderAmount }
        }
      });
      this.setState(
        {
          transactionPreview: res.data,
          executionMode: this._shouldShowExecutionToggle(
            user,
            investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category
          )
            ? "SMART"
            : undefined
        },
        () => callback()
      );
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });

      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  }

  private static _shouldShowStatusMessage(orderStatus: OrderStatusType) {
    return !["allowedCash", "allowedBank", "noInvestmentCashBuy", "noInvestmentBankBuy"].includes(orderStatus);
  }

  private _getCurrentPlan(): plansConfig.PlanType {
    const user = (this.context as GlobalContextType).user;
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return PRICE_CONFIG[user?.subscription?.price]?.plan;
  }

  private _shouldShowMarketHours(): boolean {
    const { marketInfo, assetCommonId } = this.props;
    const { user } = this.context as GlobalContextType;

    if (!marketInfo) return false;
    if (!user) return false;

    const isETF = investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf";
    const isRealtimeETFExecutionEnabled = user.isRealtimeETFExecutionEnabled;
    const hasAggregatedSubmission = investmentUniverseConfig.ASSET_CONFIG[assetCommonId].aggregatedSubmission;

    if (!isRealtimeETFExecutionEnabled && isETF) return false;
    if (hasAggregatedSubmission) return false;

    return true;
  }

  componentDidUpdate(prevProps: PropsType) {
    if (isAllowedOneStepInvest(this.context.user)) {
      if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", this.props.initialSelectedLinkedBankAccount);
      } else if (this.props.linkedBankAccounts !== prevProps.linkedBankAccounts) {
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", this.props.linkedBankAccounts[0]);
      }
    }
  }

  render(): JSX.Element {
    const {
      assetCommonId,
      show,
      handleClose,
      assetPrice,
      assetPriceInUserCurrency,
      availableCash,
      linkedBankAccounts,
      gift,
      tradedCurrency,
      marketInfo
    } = this.props;
    const {
      orderAmount,
      paymentMethod,
      selectedLinkedBankAccount,
      viewMode,
      transactionPreview,
      actionButtonClicked,
      executionMode
    } = this.state;

    const user = (this.context as GlobalContextType).user;
    const locale = (this.context as GlobalContextType).locale;
    const orderStatus = this._getTransactionStatus();
    const buyOrderQuantity = Decimal.div(this._getOrderAmountAsFloat(), assetPriceInUserCurrency).toNumber();

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);
    const plan = this._getCurrentPlan();
    const shouldShowExecutionModeToggle = this._shouldShowExecutionToggle(
      user,
      investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category
    );

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0 w-100 d-flex">
            <div className={"me-auto"}>
              <TransactionModalBackButton
                onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
                title={"Buy"}
                subtitle={investmentUniverseConfig.ASSET_CONFIG[assetCommonId].formalTicker}
              />
            </div>
            {viewMode === "ACTION" && this._shouldShowMarketHours() && (
              <div className={"ms-auto"}>
                <MarketHoursTag
                  isMarketOpen={marketInfo?.isOpen}
                  onClick={() => this._setViewMode("MARKET_HOURS")}
                />
              </div>
            )}
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Market Hours Modal Content */}
        {viewMode === "MARKET_HOURS" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-5">{this._getMarketHoursContent()}</div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Market Hours Modal Content */}

        {viewMode == "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center">
                    {paymentMethod === "GIFT" ? (
                      <p className="border-0 p-0 order-input">
                        {formatCurrency(
                          new Decimal(gift.consideration.amount).div(100).toNumber(),
                          gift.consideration.currency,
                          locale,
                          0
                        )}
                      </p>
                    ) : (
                      <OrderInput
                        amount={orderAmount}
                        placeholderAmount={0}
                        key={"buy"}
                        onAmountChange={(amount) => {
                          const { orderAmount } = this.state;
                          // if order amount is empty or reduced
                          // we clear the error message
                          if (!amount || amount != orderAmount) {
                            this._setActionButtonClicked(false);
                          }

                          this._setOrderAmount(amount);
                        }}
                        prefix={CURRENCY_SYMBOLS[user.currency]}
                        decimalLimit={2}
                      />
                    )}
                  </div>
                  {/* End Order Input */}
                  {/* Estimated Quantity */}
                  <div className="d-block text-center text-muted mb-3">
                    {buyOrderQuantity > 0 ? "~" : ""}
                    {formatShares(buyOrderQuantity, locale)} shares · 1{" "}
                    {investmentUniverseConfig.ASSET_CONFIG[assetCommonId].formalTicker} ={" "}
                    {formatCurrency(assetPrice, tradedCurrency, locale)}
                  </div>
                  {/* End Estimated Quantity */}

                  {/* Status message */}
                  {AssetBuyOrderModal._shouldShowStatusMessage(orderStatus) && orderAmount && (
                    <p className="text-center text-danger mb-4">
                      {this._getStatusMessage(orderStatus, user.currency, locale)}
                    </p>
                  )}
                  {/* End Status Message */}

                  {/* Selected Bank Account Field */}
                  {(availableCash > 0 || selectedLinkedBankAccount || gift) && (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={paymentMethod}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      availableCash={availableCash || null}
                      gift={gift}
                      frequency={"ONE_TIME"}
                    />
                  )}
                  {/* Selected Bank Account Field */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._renderActionButton()}
              </div>
            </Modal.Footer>
          </div>
        )}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {transactionPreview && (
                    <>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Amount</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(Number(orderAmount ?? 0), user.currency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start text-nowrap">No. of shares (est.)</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {executionMode === "EXPRESS"
                              ? formatShares(transactionPreview.orders.express[assetCommonId].quantity, locale)
                              : formatShares(transactionPreview.orders.smart[assetCommonId].quantity, locale)}
                          </span>
                        </div>
                      </div>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Latest price</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(assetPrice, tradedCurrency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      <CashbackPreviewRow
                        transactionPreview={transactionPreview}
                        onInfoClick={() =>
                          this.setState((prevState) => {
                            return {
                              ...prevState,
                              viewModeBeforeCashback: prevState.viewMode,
                              viewMode: "EARN_CASHBACK_INFORMATION"
                            };
                          })
                        }
                      />
                      <CommissionPreviewRow
                        fees={transactionPreview.fees}
                        executionMode={executionMode}
                        isRepeatingInvestment={false}
                        isETFBuy={investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf"}
                      />
                      <FxRatePreviewRow
                        foreignCurrencyRates={transactionPreview.foreignCurrencyRates}
                        showBottomBorder={true}
                      />
                      <ExecutionWindowPreviewRows
                        executionWindow={transactionPreview.executionWindow}
                        executionMode={executionMode}
                        showBottomBorder={shouldShowExecutionModeToggle}
                      />
                      {shouldShowExecutionModeToggle && (
                        <ExecutionModeToggle
                          executionMode={executionMode}
                          onChange={() =>
                            this.setState({ executionMode: executionMode === "EXPRESS" ? "SMART" : "EXPRESS" })
                          }
                        />
                      )}
                    </>
                  )}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton
                  className="btn btn-primary fw-100"
                  customonclick={async (): Promise<void> => {
                    const order: PendingOrderType = {
                      orderType: "buy",
                      money: Number(orderAmount)
                    };
                    await this._submitOrder(assetCommonId, order);
                  }}
                >
                  Confirm buy order
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">{this._getDisclaimer()}</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={paymentMethod}
                    availableCash={availableCash}
                    frequency={"ONE_TIME"}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) => {
                      this._setPaymentMethodAndAccount(paymentMethod, selectedLinkedBankAccount);
                      if (paymentMethod === "GIFT") {
                        this._setOrderAmount(
                          new Decimal(gift.consideration.amount).div(100).toNumber().toString()
                        );
                      } else {
                        this._setOrderAmount(""); // If not gift, reset the order amount when payment method changes
                      }
                    }}
                    gift={gift}
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {isAllowedOneStepInvest(user) && <AddBankAccountButton originModal="assetBuy" />}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* EndBank Select Modal Content */}

        {/* Earn Cashback Info */}
        {viewMode == "EARN_CASHBACK_INFORMATION" && (
          <Modal.Body className="p-0 px-md-3 px-3 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                {plan == "free" ? (
                  <div className="d-flex flex-column align-items-center p-0 m-0 mb-4">
                    <span
                      className="material-symbols-outlined icon-primary align-self-center align-self-center"
                      style={{
                        fontSize: "60px"
                      }}
                    >
                      payments
                    </span>
                    <h5 className="fw-bolder mt-4 mb-4">{`Earn cashback with ${PLAN_CONFIG.paid_low.name}!`}</h5>
                    <p className="text-muted text-center mt-2 mb-5">
                      {`Upgrade to Wealthyhood ${PLAN_CONFIG.paid_low.name} and earn ${formatPercentage(
                        CASHBACK_RATES["paid_low"],
                        locale
                      )} cash back every time you invest ${formatCurrency(
                        MINIMUM_AMOUNT_FOR_CASHBACK,
                        user.currency,
                        locale
                      )} or more! Start growing your portfolio faster today.`}
                    </p>
                    <button
                      className="btn btn-primary fw-100 my-4"
                      onClick={() => (window.location.href = "/investor/change-plan?select=paid_low_monthly")}
                    >
                      Upgrade to {PLAN_CONFIG.paid_low.name}
                    </button>
                  </div>
                ) : (
                  <div className="d-flex flex-column align-items-center p-0 m-0 mb-4">
                    <span
                      className="material-symbols-outlined icon-primary align-self-center align-self-center"
                      style={{
                        fontSize: "60px"
                      }}
                    >
                      payments
                    </span>
                    <h5 className="fw-bolder mt-4 mb-4">{`Earn cashback with ${PLAN_CONFIG[plan].name}!`}</h5>
                    <p className="text-muted text-center mt-2 mb-5">
                      {`Earn ${formatPercentage(
                        CASHBACK_RATES[plan],
                        locale
                      )} cash back every time you invest ${formatCurrency(
                        MINIMUM_AMOUNT_FOR_CASHBACK,
                        user.currency,
                        locale
                      )} or more with your Wealthyhood ${
                        PLAN_CONFIG[plan].name
                      } subscription! Make every investment a bit more rewarding!`}
                    </p>
                    <button
                      className="btn btn-primary fw-100 my-4"
                      onClick={() => this._setViewMode(this.state.viewModeBeforeCashback)}
                    >
                      Got it!
                    </button>
                  </div>
                )}
              </div>
            </div>
          </Modal.Body>
        )}
        {/* End Earn Cashback Info */}
      </Modal>
    );
  }
}

AssetBuyOrderModal.contextType = GlobalContext;

export default AssetBuyOrderModal;
