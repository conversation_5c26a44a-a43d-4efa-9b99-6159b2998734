import React, { Component } from "react";
import AssetDiscoveryList, { AssetDiscoveryListViewModeType } from "../assetDiscoveryList";
import { AssetCollectionsType } from "../../types/assetDiscovery";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
import { PortfolioDocument } from "../../../models/Portfolio";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  // AssetDiscoveryList props
  viewMode: AssetDiscoveryListViewModeType;
  assetCollections?: AssetCollectionsType;
  investmentProducts: InvestmentProductDocument[];
  portfolio: PortfolioDocument;
  universe: Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType>;
};
type StateType = {
  modalSlideAnimation: "in" | "out";
};

export default class AssetListModal extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      modalSlideAnimation: "in"
    };
    this._handleClose = this._handleClose.bind(this);
  }

  private _handleClose(): void {
    this.setState({ modalSlideAnimation: "out" }, () => {
      setTimeout(() => this.props.handleClose(), 500);
    });
  }

  render() {
    const { viewMode, assetCollections, investmentProducts, portfolio, universe } = this.props;
    const { modalSlideAnimation } = this.state;

    return (
      <div className="container-fluid p-0 m-0">
        <div
          className="row m-0 vh-100 justify-content-end fixed-top etf-side-mask"
          style={{ overflowY: "scroll" }}
          onScroll={(e) => {
            e.stopPropagation();
          }}
        >
          <div className="col-md-7 col-sm-4 cursor-pointer d-none d-md-block" onClick={this._handleClose} />
          <div className={`col-md-5 col-sm-8 p-md-5 p-3 bg-white position-relative slide-${modalSlideAnimation}`}>
            <div className="row m-0 p-xxl-5">
              {/* Back Button */}
              <div className="row p-0 m-0 mb-md-4 mb-3">
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={this._handleClose}
                    style={{
                      fontSize: "24px"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              <AssetDiscoveryList
                viewMode={viewMode}
                assetCollections={assetCollections}
                portfolio={portfolio}
                investmentProducts={investmentProducts}
                universe={universe}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}
