import React from "react";
import { Modal, OverlayTrigger, Popover } from "react-bootstrap";
import Decimal from "decimal.js";
import {
  currenciesConfig,
  investmentsConfig,
  investmentUniverseConfig,
  localeConfig
} from "@wealthyhood/shared-configs";
import { PendingOrderType } from "../../types/order";
import { formatCurrency } from "../../utils/currencyUtil";
import { formatShares } from "../../utils/formatterUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { ViewModeType } from "../../types/modal";
import { TransactionPreview } from "../../types/transactionPreview";
import FxRatePreviewRow from "../fxRatePreviewRow";
import CommissionPreviewRow from "../commissionPreviewRow";
import ExecutionWindowPreviewRows from "../executionWindowPreviewRows";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { MarketInfoType } from "./assetSideModal/assetSideModal.types";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import MarketHoursTag from "./assetSideModal/marketHoursTag";
import {
  getLocalExecutionWindowStart,
  LSE_MARKET_CLOSE,
  LSE_MARKET_OPEN,
  US_MARKET_CLOSE,
  US_MARKET_OPEN
} from "../../utils/executionWindowUtil";

const { MIN_ALLOWED_ASSET_QUANTITY, MIN_ALLOWED_ASSET_INVESTMENT } = investmentsConfig;

type OrderStatusType =
  | "allowed"
  | "allowedOnlySellAll"
  | "insufficientQuantity"
  | "forbiddenOrderAmount"
  | "forbiddenETFOrderAmount"
  | "forbiddenOrderQuantity"
  | "zeroQuantity";

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  portfolioId?: string;
  assetBoughtQuantity: number;
  assetPrice: number;
  tradedCurrency: currenciesConfig.MainCurrencyType;
  assetPriceInUserCurrency: number;
  show: boolean;
  handleClose: () => void;
  hasRestrictedQuantity: boolean;
  marketInfo?: MarketInfoType;
};

// The orderAmount represents the internal state of the order.
// For browser compatibility reasons, it's been decided to be represented
// internally as a string. When used for calculations, it should be
// converted to a number. When it's displayed to the user, it should be formatted
// accordingly (as currency when the user is buying and as a number with
// 4 decimals when the user is selling).
type StateType = {
  orderAmount: string;
  viewMode: ViewModeType;
  transactionPreview: TransactionPreview;
  actionButtonClicked: boolean;
  hasRestrictedQuantity: boolean;
};

class AssetSellOrderModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      orderAmount: "",
      viewMode: "ACTION",
      transactionPreview: null,
      actionButtonClicked: false,
      hasRestrictedQuantity: false
    };
  }

  private _getMarketHoursContent = (): JSX.Element => {
    const { assetCommonId } = this.props;

    return investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf" ? (
      <>
        <p className="text-muted">
          The EU market is open from Monday through Friday {LSE_MARKET_OPEN.toFormat("HH:mm")} to{" "}
          {LSE_MARKET_CLOSE.toFormat("HH:mm")} local time or from {LSE_MARKET_OPEN.toLocal().toFormat("HH:mm")} to{" "}
          {LSE_MARKET_CLOSE.toLocal().toFormat("HH:mm")} your time.
        </p>
        <p className="text-muted">When your ETF order will be executed depends on the smart execution mode.</p>
        <p className="text-muted">
          With smart execution on, ETF orders are executed at our daily trading window at{" "}
          {getLocalExecutionWindowStart()} every weekday, enjoying ZERO COMMISSIONS.
        </p>
        <p className="text-muted">
          With smart execution off (express execution), ETF orders are executed instantly during European market
          hours and face a €1 commission per ETF order.
        </p>
        <p className="text-muted">
          ETF orders placed outside market hours are executed at the next market opening.
        </p>
      </>
    ) : (
      <>
        <p className="text-muted">
          The US market is open from Monday through Friday {US_MARKET_OPEN.toFormat("HH:mm")} to{" "}
          {US_MARKET_CLOSE.toFormat("HH:mm")} local time or from {US_MARKET_OPEN.toLocal().toFormat("HH:mm")} to{" "}
          {US_MARKET_CLOSE.toLocal().toFormat("HH:mm")} your time.
        </p>
        <p className="text-muted">
          Stock orders placed within market hours, will be executed instantly. If an order is placed outside market
          hours, it will be executed at the opening of the next trading session.{" "}
        </p>
      </>
    );
  };

  private _getStatusMessage = (
    orderStatus: OrderStatusType,
    currency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { assetBoughtQuantity } = this.props;
    const { actionButtonClicked } = this.state;

    const orderAmountNum = this._getOrderAmountAsFloat();

    if (!actionButtonClicked) {
      return `${formatShares(
        Decimal.sub(assetBoughtQuantity, orderAmountNum).toNumber(),
        locale
      )} shares available`;
    }

    const STATUS_MESSAGES: Record<OrderStatusType, string> = {
      allowed: `${formatShares(
        Decimal.sub(assetBoughtQuantity, orderAmountNum).toNumber(),
        locale
      )} shares available`,
      allowedOnlySellAll: `Your holding's value is less than ${formatCurrency(
        MIN_ALLOWED_ASSET_INVESTMENT,
        currency,
        locale
      )}. You can only sell the full amount.`,
      insufficientQuantity: `You have only ${formatShares(assetBoughtQuantity, locale)} shares`,
      forbiddenOrderAmount: `You cannot place a sell order for less than ${formatCurrency(
        MIN_ALLOWED_ASSET_INVESTMENT,
        currency,
        locale
      )} in value.`,
      forbiddenETFOrderAmount: `You cannot sell ETF shares worth less than ${formatCurrency(
        MIN_ALLOWED_ASSET_INVESTMENT,
        currency,
        locale
      )}.`,
      forbiddenOrderQuantity: "Order quantity can't be less than 0.0001 units",
      zeroQuantity: `${formatShares(
        Decimal.sub(assetBoughtQuantity, orderAmountNum).toNumber(),
        locale
      )} shares available`
    };
    return STATUS_MESSAGES[orderStatus];
  };

  private _isSellAllUnderLimitAllowed = () => {
    const { user } = this.context as GlobalContextType;
    const { assetCommonId } = this.props;

    return (
      !user.isRealtimeETFExecutionEnabled ||
      investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "stock"
    );
  };

  private _getOrderStatus = (): OrderStatusType => {
    const { assetBoughtQuantity, assetPriceInUserCurrency } = this.props;
    const { actionButtonClicked } = this.state;

    const orderAmountNum = this._getOrderAmountAsFloat();
    const remainingQuantity = Math.floor((assetBoughtQuantity - orderAmountNum) * 10000) / 10000;
    const currentAssetValue = Decimal.mul(assetBoughtQuantity, assetPriceInUserCurrency);

    if (!actionButtonClicked) {
      return "zeroQuantity";
    }
    if (!(orderAmountNum > 0)) {
      return "zeroQuantity";
    }
    if (remainingQuantity < 0) {
      return "insufficientQuantity";
    }
    if (
      currentAssetValue.lessThan(MIN_ALLOWED_ASSET_INVESTMENT) &&
      Decimal.sub(assetBoughtQuantity, orderAmountNum).greaterThan(0) &&
      this._isSellAllUnderLimitAllowed()
    ) {
      return "allowedOnlySellAll";
    }
    // check investment thresholds
    const orderMoney = Decimal.mul(orderAmountNum, assetPriceInUserCurrency).toNumber();
    if (orderAmountNum < MIN_ALLOWED_ASSET_QUANTITY) {
      return "forbiddenOrderQuantity";
    }
    if (orderMoney < MIN_ALLOWED_ASSET_INVESTMENT && !this._isSellAllUnderLimitAllowed()) {
      return "forbiddenETFOrderAmount";
    }
    if (orderMoney < MIN_ALLOWED_ASSET_INVESTMENT && !Decimal.sub(assetBoughtQuantity, orderAmountNum).equals(0)) {
      return "forbiddenOrderAmount";
    }

    return "allowed";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _getOrderAmountAsFloat(): number {
    const { orderAmount } = this.state;
    return orderAmount ? Number.parseFloat(orderAmount) : 0;
  }

  private _onAssetOrder = async (
    assetCommonId: investmentUniverseConfig.AssetType,
    order: PendingOrderType
  ): Promise<void> => {
    const { portfolioId } = this.props;
    const pendingOrders = {
      [assetCommonId]: order
    };

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your order is being sent...");

    const body = { pendingOrders };
    try {
      const res = await axios.post(`/portfolios/${portfolioId}`, body);
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({ toastType: ToastTypeEnum.error, content: err.response.message ?? "An error occurred" });
      // rethrow error to be handled from loadingOnSubmitButton that will call this async method
      throw err;
    }
  };

  private async _getTransactionPreview(callback: () => void) {
    if (this._getOrderStatus() !== "allowed") {
      return;
    }

    const { assetCommonId } = this.props;
    const { orderAmount } = this.state;
    const { user } = this.context as GlobalContextType;

    try {
      const res = await axios.post("/transactions/asset/preview?portfolioTransactionType=update", {
        pendingOrders: {
          [assetCommonId]: { side: "sell", quantity: orderAmount }
        }
      });
      this.setState({ transactionPreview: res.data }, () => callback());

      if (res.data.willResultInLowQuantityHolding && user.isRealtimeETFExecutionEnabled) {
        emitToast({
          content:
            'Warning: If your investment falls below €1, you may not be able to sell your remaining ETF shares in the future. Consider using "Sell All" instead.',
          toastType: ToastTypeEnum.info
        });
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });

      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  }

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _shouldShowMarketHours(): boolean {
    const { marketInfo, assetCommonId } = this.props;
    const { user } = this.context as GlobalContextType;

    if (!marketInfo) return false;
    if (!user) return false;

    const isETF = investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf";
    const isRealtimeETFExecutionEnabled = user.isRealtimeETFExecutionEnabled;
    const hasAggregatedSubmission = investmentUniverseConfig.ASSET_CONFIG[assetCommonId].aggregatedSubmission;

    if (!isRealtimeETFExecutionEnabled && isETF) return false;
    if (hasAggregatedSubmission) return false;

    return true;
  }

  render(): JSX.Element {
    const {
      assetCommonId,
      show,
      handleClose,
      assetPrice,
      assetPriceInUserCurrency,
      assetBoughtQuantity,
      tradedCurrency,
      hasRestrictedQuantity,
      marketInfo
    } = this.props;
    const { orderAmount, viewMode, transactionPreview } = this.state;

    const user = (this.context as GlobalContextType).user;
    const locale = (this.context as GlobalContextType).locale;
    const orderStatus = this._getOrderStatus();
    const sellOrderMoney = Decimal.mul(this._getOrderAmountAsFloat(), assetPriceInUserCurrency).toNumber();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0 w-100 d-flex">
            <div className={"me-auto"}>
              <TransactionModalBackButton
                onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
                title={"Sell"}
                subtitle={investmentUniverseConfig.ASSET_CONFIG[assetCommonId].formalTicker}
              />
            </div>
            {viewMode === "ACTION" && this._shouldShowMarketHours() && (
              <div className={"ms-auto"}>
                <MarketHoursTag
                  isMarketOpen={marketInfo?.isOpen}
                  onClick={() => this._setViewMode("MARKET_HOURS")}
                />
              </div>
            )}
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Market Hours Modal Content */}
        {viewMode === "MARKET_HOURS" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-5">{this._getMarketHoursContent()}</div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Market Hours Modal Content */}

        {viewMode == "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-column justify-content-center">
                    <OrderInput
                      amount={orderAmount}
                      placeholderAmount={0}
                      key={"sell"}
                      onAmountChange={(amount) => {
                        const { orderAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != orderAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._setOrderAmount(amount);
                      }}
                      prefix={""}
                      decimalLimit={4}
                      maxNumberLimit={assetBoughtQuantity}
                    />
                    <div className="holding-component">
                      <span
                        className={`text-center ${
                          ["allowed", "zeroQuantity"].includes(orderStatus) ? "text-muted" : "text-danger"
                        }`}
                      >
                        {this._getStatusMessage(orderStatus, user.currency, locale)}
                      </span>
                      {hasRestrictedQuantity && (
                        <OverlayTrigger
                          delay={{ hide: 450, show: 300 }}
                          overlay={
                            <Popover id="popover-explanation" className="info-box-text">
                              <h6 className="info-box-title">Number of shares available</h6>
                              The number of &quot;shares available&quot; may sometimes be smaller than your total
                              holdings. This is because{" "}
                              <span className="info-text-bold">
                                shares received as part of our free share or gift campaigns{" "}
                              </span>{" "}
                              need to be held for at least 60 days before they can be sold. After the 60-day
                              period, they will be added to your number of shares available.{" "}
                            </Popover>
                          }
                          placement="right"
                        >
                          <span
                            className="material-symbols-outlined align-middle text-muted"
                            style={{ fontSize: "20px" }}
                          >
                            info
                          </span>
                        </OverlayTrigger>
                      )}
                    </div>

                    <p className="text-center mb-4">~{formatCurrency(sellOrderMoney, user.currency, locale)}</p>
                    {/* End Estimated Amount/Quantity */}
                    <div className="d-flex justify-content-center mb-5">
                      <button
                        type="button"
                        className="wh-card-amount text-primary"
                        onClick={() => {
                          this._setOrderAmount(assetBoughtQuantity.toString());
                        }}
                      >
                        Sell all
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {/* Order Submission Form */}
                {orderAmount ? (
                  <LoadingOnSubmitButton
                    type="button"
                    className="btn btn-primary fw-100"
                    enableOnCompletion={true}
                    customonclick={async () => {
                      this._setActionButtonClicked(true);
                      await this._getTransactionPreview(() => this._setViewMode("REVIEW"));
                    }}
                  >
                    Review
                  </LoadingOnSubmitButton>
                ) : (
                  <button type="button" className="btn btn-primary fw-100" disabled>
                    Review
                  </button>
                )}
                {/* End Order Submission Form */}
              </div>
            </Modal.Footer>
          </div>
        )}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {transactionPreview && (
                    <>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start text-nowrap">No. of shares</div>
                        <div className="col text-end">
                          <span className="fw-bolder">{formatShares(Number(orderAmount ?? 0), locale)}</span>
                        </div>
                      </div>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Amount (est.)</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(sellOrderMoney, user.currency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Latest price</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(assetPrice, tradedCurrency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      <CommissionPreviewRow
                        fees={transactionPreview.fees}
                        isRepeatingInvestment={false}
                        isETFSell={investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category === "etf"}
                      />
                      <FxRatePreviewRow
                        foreignCurrencyRates={transactionPreview.foreignCurrencyRates}
                        showBottomBorder={true}
                      />
                      <ExecutionWindowPreviewRows
                        executionWindow={transactionPreview.executionWindow}
                        showBottomBorder={false}
                      />
                    </>
                  )}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton
                  className="btn btn-primary fw-100"
                  customonclick={async (): Promise<void> => {
                    const order: PendingOrderType = {
                      orderType: "sell",
                      quantity:
                        assetBoughtQuantity - Number(orderAmount) < MIN_ALLOWED_ASSET_QUANTITY
                          ? assetBoughtQuantity
                          : Number(orderAmount)
                    };
                    await this._onAssetOrder(assetCommonId, order);
                  }}
                >
                  Confirm sell order
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">Capital at risk.</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}
      </Modal>
    );
  }
}

AssetSellOrderModal.contextType = GlobalContext;

export default AssetSellOrderModal;
