import React, { Component } from "react";

type PropsType = {
  showSellButton: boolean;
  handleBuy?: () => void;
  handleSell?: () => void;
};

export default class AssetSideModalActionButtons extends Component<PropsType> {
  render(): JSX.Element {
    const { handleBuy, handleSell, showSellButton } = this.props;

    return (
      <>
        <div className="row p-0 m-0 fixed-bottom h-10">
          <div className="col-md-7 col-sm-4" />
          <div className="col-md-5 col-sm-8 col h-100  bg-white border-top align-self-center">
            <div className="d-flex h-100 p-0 justify-content-end align-self-center">
              {showSellButton && (
                <button type="button" className="btn btn-secondary align-self-center me-4" onClick={handleSell}>
                  Sell
                </button>
              )}
              <button
                type="button"
                className="btn btn-primary w-100 align-self-center me-md-4"
                onClick={handleBuy}
              >
                Buy
              </button>
            </div>
          </div>
        </div>
        {/* Dummy spacer to solve overlapping issue with fixed bottom nav buttons */}
        <div className="row m-0" style={{ height: "90px" }} />
      </>
    );
  }
}
