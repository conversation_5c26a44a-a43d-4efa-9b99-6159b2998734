import React, { CSSProperties, Component } from "react";
import { AnalystViewsPositionType, StockAnalystViewsType } from "./assetSideModal.types";
import { InfoKeyType } from "./configuration";
import { capitalizeFirstLetter } from "../../../utils/stringUtil";

type PropsType = {
  analystViews: StockAnalystViewsType;
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

const COLOR_CONFIG: Record<AnalystViewsPositionType, string> = {
  buy: "#23846A",
  hold: "#D59A04",
  sell: "#D63C3C"
};

export default class AssetSideModalAnalystViews extends Component<PropsType> {
  private _getAnalystViewRow(
    percentage: number,
    analystPosition: AnalystViewsPositionType,
    majorityPosition: AnalystViewsPositionType
  ): JSX.Element {
    let style: CSSProperties = {};
    if (analystPosition === majorityPosition) {
      style = {
        backgroundColor: COLOR_CONFIG[analystPosition],
        color: "white",
        borderRadius: "16px"
      };
    }

    return (
      <div className="row p-0 m-0 mb-3">
        <div className="col-8 m-0 p-0">
          <div className="progress-bar-left mt-2">
            <div
              className="progress-left"
              style={{
                width: `${percentage}%`,
                backgroundColor: COLOR_CONFIG[analystPosition]
              }}
            ></div>
          </div>
        </div>
        <div className="col-4 m-0 p-0 d-flex justify-content-end">
          <p className="text-end px-3 m-0" style={style}>{`${percentage}% ${capitalizeFirstLetter(
            analystPosition
          )}`}</p>
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { onInfoButtonClick, analystViews } = this.props;

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">
              Analyst Views
              <span
                className="material-symbols-outlined cursor-pointer align-self-center text-primary ms-1"
                style={{
                  fontSize: "16px"
                }}
                onClick={() => onInfoButtonClick("analystViews")}
              >
                info
              </span>
            </h5>
            {this._getAnalystViewRow(analystViews.percentageBuy, "buy", analystViews.isMajority)}
            {this._getAnalystViewRow(analystViews.percentageHold, "hold", analystViews.isMajority)}
            {this._getAnalystViewRow(analystViews.percentageSell, "sell", analystViews.isMajority)}
            <div className="row p-0 m-0 mb-4">
              <p className="text-muted text-center m-0">{`Based on information from ${analystViews.totalAnalysts} analysts.`}</p>
            </div>

            <div className="row p-4 m-0 asset-modal-card">
              <div className="d-flex p-0 m-0 mb-2">
                <div className="col-6 p-0 m-0 pe-1 text-end">
                  <span className="fw-bolder-black">{analystViews.averagePriceTarget}</span>
                </div>
                <div className="col-6 p-0 m-0 ps-1">
                  <span
                    className={
                      "col-6 " + analystViews.isPriceTargetPercentageDifferencePositive
                        ? "text-success"
                        : "text-danger"
                    }
                  >{`(${analystViews.priceTargetPercentageDifference})`}</span>
                </div>
              </div>
              <div className="d-flex justify-content-center align-items-center flex-row p-0 m-0 ms-3">
                <p className="text-muted text-center m-0">Average price target</p>
                <span
                  className="material-symbols-outlined cursor-pointer ms-1"
                  style={{
                    fontSize: "16px"
                  }}
                  onClick={() => onInfoButtonClick("averagePriceTarget")}
                >
                  info
                </span>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}
