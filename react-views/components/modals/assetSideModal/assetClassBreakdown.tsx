import React, { Component } from "react";
import { ETFAssetClassBreakdown } from "./assetSideModal.types";
import { formatPercentage } from "../../../utils/formatterUtil";
import { GlobalContext, GlobalContextType } from "../../../contexts/globalContext";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../../../utils/configUtil";
import { getAssetClassBreakdownName } from "../../../utils/universeUtil";
import Decimal from "decimal.js/decimal";
const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
};

class AssetSideModalAssetClassBreakdown extends Component<PropsType> {
  render() {
    const { assetCommonId } = this.props;

    const { locale } = this.context as GlobalContextType;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const assetClassBreakdown = (ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType)
      .assetClassBreakdown;

    if (!assetClassBreakdown) {
      return null;
    }

    return (
      <div className="row m-0 p-0 m-0 mt-5 mb-5">
        <div className="col p-0">
          <h5 className="fw-bolder mb-4">Asset Class Breakdown</h5>
          <div className="d-flex flex-column">
            {Object.keys(assetClassBreakdown).map((assetClassKey, index) => (
              <div key={`region-${index}`} className="d-flex flex-column mb-2">
                <div className="d-flex flex-row align-items-center">
                  <p className="m-0 w-100">
                    {getAssetClassBreakdownName(assetClassKey as investmentUniverseConfig.AssetClassType)}
                  </p>
                  <div className="progress-bar">
                    <div
                      className="progress"
                      style={{
                        width: `${Decimal.mul(
                          assetClassBreakdown[assetClassKey as keyof ETFAssetClassBreakdown],
                          100
                        )}%`,
                        backgroundColor:
                          ASSET_CLASS_CONFIG[assetClassKey as investmentUniverseConfig.AssetClassType].colorClass
                      }}
                    ></div>
                  </div>
                </div>
                <div className="row">
                  <p className="text-primary text-end m-0">
                    {formatPercentage(assetClassBreakdown[assetClassKey as keyof ETFAssetClassBreakdown], locale)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

AssetSideModalAssetClassBreakdown.contextType = GlobalContext;

export default AssetSideModalAssetClassBreakdown;
