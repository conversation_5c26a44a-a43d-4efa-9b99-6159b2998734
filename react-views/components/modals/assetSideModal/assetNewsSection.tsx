import React, { Component } from "react";
import { AssetNewsDocument } from "../../../../models/AssetNews";
import AssetNewsItem from "../../../components/assetNewsItem";

type PropsType = {
  news: AssetNewsDocument[];
  onClick: () => void;
};

export default class AssetNewsSection extends Component<PropsType> {
  render(): JSX.Element {
    const { news, onClick } = this.props;

    return (
      <div className="mb-5 mt-5">
        <div className="d-flex justify-content-between align-items-center" style={{ marginBottom: "24px" }}>
          <h5 className="m-0 p-0 fw-bolder">News</h5>
          <h6 className="m-0 p-0 fw-bolder text-end text-primary cursor-pointer" onClick={onClick}>
            See all
          </h6>
        </div>
        <div>
          {news.map((newsItem, index) => (
            <AssetNewsItem key={index} newsItem={newsItem} />
          ))}
        </div>
      </div>
    );
  }
}
