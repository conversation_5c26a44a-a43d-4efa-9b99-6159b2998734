import React, { Component } from "react";
import {
  CategoryScale,
  Chart as ChartJS,
  ChartOptions,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from "chart.js";
import { Line } from "react-chartjs-2";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../../../utils/currencyUtil";
import { Nav } from "react-bootstrap";
import { InvestmentProductPricesByTenor } from "../../../types/investmentProduct";
import { TenorEnum, TenorsByOrder } from "../../../configs/durationConfig";
import Decimal from "decimal.js";
import { formatDateToDDMONYYHHMM, formatDateToDDMONYYYY } from "../../../utils/dateUtil";
import { GlobalContext, GlobalContextType } from "../../../contexts/globalContext";
import { capitalizeFirstLetter } from "../../../utils/stringUtil";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  chartData: InvestmentProductPricesByTenor;
  tradedPrice: number;
  tradedCurrency: currenciesConfig.MainCurrencyType;
};

type StateType = {
  activeChartTenor: TenorEnum;
};

class AssetSideModalPerformanceChart extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      activeChartTenor: TenorEnum.ONE_YEAR
    };
  }

  private _handleDurationSelect = (eventKey: string | null): void => {
    this.setState({ activeChartTenor: eventKey as TenorEnum });
  };

  private _getChartOptions(tradedCurrency: currenciesConfig.MainCurrencyType) {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          enabled: true,
          intersect: false,
          mode: "nearest",
          bodySpacing: 5,
          padding: { top: 10, bottom: 10, left: 10, right: 10 },
          caretPadding: 0,
          displayColors: false,
          backgroundColor: "#536AE3",
          titleColor: "#ffffff",
          cornerRadius: 4,
          callbacks: {
            label(context: any): string {
              const dataPoint = context.raw as {
                x: number;
                y: number;
                data?: { type: "buy" | "sell" | "net"; quantity: number };
              };

              if (dataPoint?.data) {
                const sign = dataPoint.data.quantity >= 0 ? "+" : "-";
                const absoluteQuantity = Math.abs(dataPoint.data.quantity);
                return `${capitalizeFirstLetter(dataPoint.data.type)} ${sign}${absoluteQuantity}`;
              }
              return formatCurrency(dataPoint.y, tradedCurrency, "en", 0);
            }
          }
        },
        datalabels: {
          display: false
        }
      },
      scales: {
        x: {
          display: false,
          grid: {
            tickLength: 30,
            color: "rgba(0, 0, 0, 0)",
            drawBorder: false
          },
          ticks: {
            display: true,
            color: "#000",
            font: { size: 16 },
            padding: 0,
            maxTicksLimit: 2,
            autoSkip: true,
            maxRotation: 0,
            minRotation: 0
          }
        },
        y: {
          display: true,
          grid: {
            color: "rgba(236, 240, 241, 1)",
            drawBorder: false,
            borderDash: [3, 4],
            lineWidth: 2
          },
          ticks: {
            display: true,
            color: "#000",
            font: { size: 16 },
            padding: 20,
            maxTicksLimit: 6,
            callback(value: any): string {
              let unit = "";
              let val = value as number;
              if (val >= 1000 && val < 1000000) {
                unit = "k";
                val = val / 1000;
              } else if (val >= 1000000) {
                unit = "M";
                val = val / 1000000;
              }
              return formatCurrency(val, tradedCurrency, "en", 0, 2) + unit;
            }
          }
        }
      },
      hover: {
        mode: "index"
      },
      elements: {
        line: {
          tension: 0.5
        },
        point: {
          radius: 0
        }
      },
      layout: {
        padding: {
          left: 0,
          right: 0,
          top: 20,
          bottom: 10
        }
      }
    };
  }

  private _getData() {
    const { chartData, assetCommonId } = this.props;
    const { activeChartTenor } = this.state;

    const { data: activeTickers, displayIntraday } = chartData[activeChartTenor];

    const dataset = activeTickers.map(({ close, data, timestamp }) => ({
      y: close,
      x: timestamp,
      data
    }));

    const labels = activeTickers.map(({ timestamp }) => {
      const date = new Date(timestamp);
      if (displayIntraday) {
        return formatDateToDDMONYYHHMM(date);
      } else {
        return formatDateToDDMONYYYY(date);
      }
    });

    const pointRadius = activeTickers.map(({ data }) => {
      if (data?.quantity >= 0) {
        return 2;
      } else if (data?.quantity < 0) {
        return 4;
      } else {
        return 0;
      }
    });
    const pointBorderWidth = activeTickers.map(({ data }) => {
      if (data?.quantity >= 0) {
        return 6;
      } else if (data?.quantity < 0) {
        return 3;
      } else {
        return 1;
      }
    });
    const pointBackgroundColor = activeTickers.map(({ data }) => {
      if (data?.quantity < 0) {
        return "rgba(255, 255, 255, 1)";
      } else {
        return "rgba(0, 0, 0, 0)";
      }
    });

    const { tickerWithCurrency } = investmentUniverseConfig.ASSET_CONFIG[assetCommonId];

    return {
      labels,
      datasets: [
        {
          label: tickerWithCurrency,
          data: dataset,
          borderColor: "#5d78ff",
          borderWidth: 2,
          pointRadius,
          pointBorderWidth,
          pointBackgroundColor,
          pointHoverBackgroundColor: pointBackgroundColor,
          pointHoverBorderWidth: pointBorderWidth,
          pointHoverRadius: pointRadius,
          fill: false
        }
      ]
    };
  }

  private _getAssetReturnInActiveTenor(): number {
    const { chartData } = this.props;
    const { activeChartTenor } = this.state;

    return chartData[activeChartTenor].returns;
  }

  private static _getPerfomanceSign(isPerformancePositive: boolean): JSX.Element {
    return isPerformancePositive ? (
      <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
        arrow_drop_up
      </span>
    ) : (
      <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
        arrow_drop_down
      </span>
    );
  }

  render(): JSX.Element {
    const { tradedPrice, tradedCurrency } = this.props;
    const { activeChartTenor: activeChartDuration } = this.state;
    const { locale } = this.context as GlobalContextType;

    const assetReturnInActiveTenor = this._getAssetReturnInActiveTenor();
    const isReturnPositive = assetReturnInActiveTenor > 0;

    return (
      <>
        <div className="d-flex p-0 ps-3 flex-column align-items-start">
          <span className="h4 fw-bolder text-primary d-block mb-1">
            {formatCurrency(tradedPrice, tradedCurrency, locale)}
          </span>
          <span className={`h6 fw-bolder d-block mb-1 ${isReturnPositive ? "text-success" : "text-danger"}`}>
            {AssetSideModalPerformanceChart._getPerfomanceSign(isReturnPositive)}
            {`${Decimal.abs(assetReturnInActiveTenor)
              .toNumber()
              .toLocaleString(locale, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}%`}
          </span>
        </div>
        <div className="row w-100 m-0">
          <div className="col p-0" style={{ height: "390px" }}>
            <Line data={this._getData()} options={this._getChartOptions(tradedCurrency) as ChartOptions<"line">} />
          </div>
        </div>
        <div className="row">
          <div className="col-12">
            <Nav
              variant="pills"
              defaultActiveKey={activeChartDuration}
              className="justify-content-center"
              onSelect={this._handleDurationSelect}
            >
              {TenorsByOrder.map((tenor, index) => (
                <Nav.Item key={`tenor-nav-${index}`}>
                  <Nav.Link eventKey={tenor} className="p-2 mx-2 font-size-lg">
                    {tenor.toUpperCase()}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </div>
        </div>
      </>
    );
  }
}

AssetSideModalPerformanceChart.contextType = GlobalContext;

export default AssetSideModalPerformanceChart;
