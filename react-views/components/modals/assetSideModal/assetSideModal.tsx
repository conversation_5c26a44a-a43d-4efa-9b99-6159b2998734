import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import LoadingSpinner from "../../loadingSpinner";
import AssetSideModalLayout from "./layout";
import AssetSideModalStockHeader from "./stockHeader";
import AssetSideModalEtfHeader from "./etfHeader";
import { captureException } from "@sentry/react";
import { emitToast } from "../../../utils/eventService";
import axios from "axios";
import {
  AssetDataResponseType,
  ETFAssetFundamentalsType,
  InvestmentDetailsType,
  MarketInfoType,
  RecentActivityItemType,
  StockAssetFundamentalsType
} from "./assetSideModal.types";
import { ToastTypeEnum } from "../../../configs/toastConfig";
import AssetSideModalPerformanceChart from "./assetPerformanceChart";
import AssetSideModalInvestmentDetails from "./investmentDetails";
import AssetSideModalRecentActivity from "./recentActivity";
import AssetSideModalEtfKeyFacts from "./etfKeyFacts";
import InfoModal from "../infoModal";
import { ASSET_CLASSES_WITH_TOP_HOLDINGS, getTooltipsConfig, InfoKeyType } from "./configuration";
import AssetSideModalRegionalBreakdown from "./regionalBreakdown";
import AssetSideModalSectorBreakdown from "./sectorBreakdown";
import AssetSideModalDocuments from "./documents";
import AssetSideModalActionButtons from "./actionButtons";
import AssetSideModalDisclaimer from "./disclaimer";
import AssetSideModalEtfAboutSection from "./etfAboutSection";
import AssetSideModalEtfMetrics from "./etfMetrics";
import AssetSideModalStockMetrics from "./stockMetrics";
import AssetSideModalStockAboutSection from "./stockAboutSection";
import AssetSideModalAssetNewsSection from "./assetSideModalAssetNewsSection";
import AssetSideModalAnalystViews from "./analystViews";
import AssetSideModalAssetClassBreakdown from "./assetClassBreakdown";
import AssetNewsSection from "./assetNewsSection";
import AssetSideModalTopHoldingsBreakdown from "./topHoldingBreakdown";
import { InvestmentProductPricesByTenor } from "../../../types/investmentProduct";

const { ASSET_CONFIG } = investmentUniverseConfig;

type ViewModeType = "asset" | "about" | "allNews";

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  handleClose: () => void;
  includeInvestmentDetails: boolean;
  includeRecentActivity: boolean;
  userCurrency: currenciesConfig.MainCurrencyType;
  includeSellButton: boolean;

  // optional
  handleBuy?: (marketInfo: MarketInfoType) => void;
  handleSell?: (marketInfo: MarketInfoType) => void;
  currentTickerPrice?: number;
};

type StateType = {
  viewMode: ViewModeType;
  modalSlideAnimation: "in" | "out";
  investmentDetails: InvestmentDetailsType;
  chartData: InvestmentProductPricesByTenor;
  assetData: AssetDataResponseType;
  showInfoDialog: boolean;
  selectedInfo: string;
  recentActivityOrders: RecentActivityItemType[];
  canShowActionButtons: boolean;
};

const ASSET_RECENT_ACTIVITY_LIMIT = 5;

class AssetSideModal extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "asset",
      modalSlideAnimation: "in",
      investmentDetails: null,
      assetData: null,
      selectedInfo: null,
      showInfoDialog: false,
      chartData: null,
      recentActivityOrders: [],
      canShowActionButtons: false
    };
    this._setSelectedInfo = this._setSelectedInfo.bind(this);
  }

  componentDidMount(): void {
    this._fetchChartData();
    this._fetchAssetData();
    if (this.props.includeInvestmentDetails) {
      this._fetchInvestmentDetails();
    }
    if (this.props.includeRecentActivity) {
      this._fetchAssetRecentActivity();
    }
    setTimeout(() => this.setState({ canShowActionButtons: true }), 500);
  }

  private _handleClose() {
    this.setState({ modalSlideAnimation: "out" }, () => {
      setTimeout(() => this.props.handleClose(), 500);
    });
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private _onBackButtonClicked() {
    const { viewMode } = this.state;

    if (viewMode === "about") {
      this._setViewMode("asset");
    } else if (viewMode === "asset") {
      this._handleClose();
    } else if (viewMode === "allNews") {
      this._setViewMode("asset");
    }
  }

  private async _fetchChartData(): Promise<void> {
    const { assetCommonId } = this.props;
    try {
      const response = await axios.get(`/investment-products/${assetCommonId}/prices-by-tenor`);
      const chartData: InvestmentProductPricesByTenor = response.data;

      this.setState({
        chartData
      });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load chart data. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _fetchAssetData(): Promise<void> {
    const { assetCommonId } = this.props;
    try {
      const response = await axios.get(`/investment-products/${assetCommonId}`);
      const assetData: AssetDataResponseType = response.data;

      this.setState({ assetData });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load asset data. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _fetchInvestmentDetails(): Promise<void> {
    const { assetCommonId } = this.props;
    try {
      const response = await axios.get(`/investment-products/${assetCommonId}/investment-details`);
      const investmentDetails: InvestmentDetailsType = response.data;

      this.setState({
        investmentDetails
      });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load investment details. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _fetchAssetRecentActivity(): Promise<void> {
    const { assetCommonId } = this.props;
    try {
      const response = await axios.get(
        `/investment-products/${assetCommonId}/recent-activity?limit=${ASSET_RECENT_ACTIVITY_LIMIT}`
      );
      const recentActivityOrders: RecentActivityItemType[] = response.data;

      this.setState({
        recentActivityOrders
      });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load recent activity orders. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  private _setSelectedInfo(infoKey: InfoKeyType) {
    const { userCurrency, assetCommonId } = this.props;

    this.setState({
      selectedInfo: getTooltipsConfig(userCurrency, investmentUniverseConfig.ASSET_CONFIG[assetCommonId].category)[
        infoKey
      ],
      showInfoDialog: true
    });
  }

  render(): JSX.Element {
    const {
      assetCommonId,
      includeInvestmentDetails,
      handleBuy,
      handleSell,
      includeRecentActivity,
      currentTickerPrice,
      includeSellButton
    } = this.props;
    const {
      viewMode,
      modalSlideAnimation,
      chartData,
      investmentDetails,
      assetData,
      showInfoDialog,
      selectedInfo,
      recentActivityOrders,
      canShowActionButtons
    } = this.state;

    const { category, assetClass } = ASSET_CONFIG[assetCommonId];
    const showPerformanceChart = chartData != null && !!assetData?.currentPrice;
    const etfAssetFundamentals = assetData?.fundamentals as ETFAssetFundamentalsType;
    const stockAssetFundamentals = assetData?.fundamentals as StockAssetFundamentalsType;
    const isEtf = category === "etf";

    const showTopHoldings =
      etfAssetFundamentals?.topHoldings?.length > 0 && ASSET_CLASSES_WITH_TOP_HOLDINGS.includes(assetClass);

    return (
      <>
        <AssetSideModalLayout
          handleClose={() => this._handleClose()}
          handleBackButtonClick={() => this._onBackButtonClicked()}
          modalSlideAnimation={modalSlideAnimation}
        >
          {viewMode === "asset" && (
            <>
              {isEtf ? (
                <AssetSideModalEtfHeader
                  assetCommonId={assetCommonId}
                  tags={assetData?.tags}
                  onInfoButtonClick={this._setSelectedInfo}
                  showTopHoldings={showTopHoldings}
                  topHoldings={etfAssetFundamentals?.topHoldings ?? []}
                  holdingsCount={etfAssetFundamentals?.holdingsCount ?? 0}
                  onClick={() => {
                    if (assetData) this._setViewMode("about");
                  }}
                />
              ) : (
                <AssetSideModalStockHeader
                  assetCommonId={assetCommonId}
                  tags={assetData?.tags}
                  onInfoButtonClick={this._setSelectedInfo}
                  onClick={() => {
                    if (assetData) this._setViewMode("about");
                  }}
                />
              )}

              {showPerformanceChart ? (
                <AssetSideModalPerformanceChart
                  assetCommonId={assetCommonId}
                  chartData={chartData}
                  tradedPrice={assetData.currentPrice}
                  tradedCurrency={assetData.tradedCurrency}
                />
              ) : (
                <LoadingSpinner />
              )}

              {includeInvestmentDetails && investmentDetails && assetData && (
                <AssetSideModalInvestmentDetails {...investmentDetails} />
              )}

              {includeRecentActivity && recentActivityOrders.length > 0 && assetData && (
                <AssetSideModalRecentActivity
                  assetCommonId={assetCommonId}
                  recentActivityOrders={recentActivityOrders}
                  currentPriceInfo={{
                    tradedPrice: assetData.currentPrice,
                    tradedCurrency: assetData.tradedCurrency,
                    currentTickerPrice: currentTickerPrice
                  }}
                />
              )}
              {assetData && (
                <>
                  {isEtf ? (
                    <>
                      <AssetSideModalEtfKeyFacts
                        baseCurrency={etfAssetFundamentals.baseCurrency}
                        expenseRatio={etfAssetFundamentals.expenseRatio}
                        assetCommonId={assetCommonId}
                        onInfoButtonClick={this._setSelectedInfo}
                      />
                      <AssetSideModalEtfMetrics
                        indexStats={etfAssetFundamentals.indexStats}
                        assetCommonId={assetCommonId}
                        onInfoButtonClick={this._setSelectedInfo}
                      />
                      <AssetSideModalAssetClassBreakdown assetCommonId={assetCommonId} />
                      <AssetSideModalRegionalBreakdown
                        geographyDistribution={etfAssetFundamentals.geographyDistribution}
                      />
                      <AssetSideModalSectorBreakdown
                        sectorDistribution={etfAssetFundamentals.sectorDistribution}
                      />
                      {showTopHoldings && (
                        <AssetSideModalTopHoldingsBreakdown topHoldings={etfAssetFundamentals.topHoldings} />
                      )}
                      {etfAssetFundamentals.news && !!etfAssetFundamentals.news.length && (
                        <AssetNewsSection
                          news={etfAssetFundamentals.news}
                          onClick={() => {
                            if (assetData) this._setViewMode("allNews");
                          }}
                        />
                      )}
                      <AssetSideModalDocuments assetCommonId={assetCommonId} />
                    </>
                  ) : (
                    <>
                      <AssetSideModalStockMetrics
                        metrics={stockAssetFundamentals.metrics}
                        onInfoButtonClick={this._setSelectedInfo}
                      />
                      {stockAssetFundamentals.analystViews && (
                        <AssetSideModalAnalystViews
                          analystViews={stockAssetFundamentals.analystViews}
                          onInfoButtonClick={this._setSelectedInfo}
                        />
                      )}
                      {etfAssetFundamentals.news && !!etfAssetFundamentals.news.length && (
                        <AssetNewsSection
                          news={etfAssetFundamentals.news}
                          onClick={() => {
                            if (assetData) this._setViewMode("allNews");
                          }}
                        />
                      )}
                    </>
                  )}
                </>
              )}

              <AssetSideModalDisclaimer />
              {/** Use `canShowActionButtons` as a condition, to delay displaying Action Button due to slide animations */}
              {modalSlideAnimation === "in" && canShowActionButtons && handleBuy && handleSell && (
                <AssetSideModalActionButtons
                  handleBuy={() => handleBuy(assetData?.marketInfo)}
                  handleSell={() => handleSell(assetData?.marketInfo)}
                  showSellButton={includeSellButton}
                />
              )}
            </>
          )}

          {viewMode === "about" && (
            <>
              {isEtf ? (
                <AssetSideModalEtfAboutSection
                  assetCommonId={assetCommonId}
                  about={etfAssetFundamentals.about}
                  topHoldings={etfAssetFundamentals?.topHoldings ?? []}
                />
              ) : (
                <AssetSideModalStockAboutSection
                  assetCommonId={assetCommonId}
                  about={stockAssetFundamentals.about}
                />
              )}
            </>
          )}

          {viewMode === "allNews" && <>{<AssetSideModalAssetNewsSection assetCommonId={assetCommonId} />}</>}
        </AssetSideModalLayout>

        <InfoModal title={null} show={showInfoDialog} handleClose={() => this._setShowInfoDialog(false)}>
          <div dangerouslySetInnerHTML={{ __html: selectedInfo }} />
        </InfoModal>
      </>
    );
  }
}

export default AssetSideModal;
