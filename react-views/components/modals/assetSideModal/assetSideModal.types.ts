import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { OrderDocument } from "../../../../models/Order";
import { AssetNewsDocument } from "../../../../models/AssetNews";
import { DividendTransactionDocument } from "../../../../models/Transaction";
import { RewardDocument } from "../../../../models/Reward";
import { PartialRecord } from "../../../types/utils";

export type InvestmentDetailsType = {
  currentValue: string;
  performanceValue: string;
  performancePercentage: string;
  numberOfShares: string;
  portfolioAllocation: string;
  isPerformancePositive: boolean;
};

/**
 * Stocks
 */
export type AnalystViewsPositionType = "buy" | "sell" | "hold";
export type StockAnalystViewsType = {
  averagePriceTarget: string;
  priceTargetPercentageDifference: string;
  isPriceTargetPercentageDifferencePositive: boolean;
  totalAnalysts: number;
  percentageBuy: number;
  percentageSell: number;
  percentageHold: number;
  isMajority: AnalystViewsPositionType;
};
export type StockAboutType = {
  ticker: string;
  exchange: string;
  sector: string;
  industry: string;
  description: string;
  employees: string;
  website: string;
  ceo: string;
  headquarters: string;
  isin: string;
};
export type StockMetricsType = {
  marketCap: string;
  peRatio: string;
  eps: string;
  dividendYield: string;
  beta: string;
  forwardPE: string;
};
export type StockAssetFundamentalsType = {
  analystViews: StockAnalystViewsType;
  metrics: StockMetricsType;
  about: StockAboutType;
  news: AssetNewsDocument[];
};

/**
 * ETFs
 */

export type ETFAssetGeographyDistributionType = { name: WorldRegion; percentage: number }[];

export type WorldRegion =
  | "North America"
  | "Latin America"
  | "UK"
  | "Europe"
  | "Asia"
  | "Africa/Middle East"
  | "Australia";
export type ETFAssetSectorDistributionType = { name: ETFAssetSector; id: ETFAssetSectorId; percentage: number }[];
export type ETFAssetSector = Capitalize<investmentUniverseConfig.InvestmentSectorType> | "Real Estate";
export type ETFAssetSectorId = investmentUniverseConfig.InvestmentSectorType | "real-estate";
export type ETFAssetClassBreakdown = PartialRecord<investmentUniverseConfig.AssetClassType, number>;

export type ETFAboutType = {
  exchange: string;
  isin: string;
  ticker: string;
  assetClass: string;
  sector: string;
  advancedName: string;
  description: string;
  provider: investmentUniverseConfig.AssetProviderType;
  income: investmentUniverseConfig.ETFAssetIncomeType;
  index: string;
  replication: investmentUniverseConfig.ReplicationType;
};
export type EODHoldingType = { name: string; weight: string; logoUrl: string };
export type IndexStatsType =
  | { expectedReturn: string; annualRisk: string; coupon: string; bondYield: string }
  | {
      expectedReturn: string;
      annualRisk: string;
      fpEarnings: string;
      dividendYield: string;
    };
export type ETFAssetFundamentalsType = {
  topHoldings: EODHoldingType[];
  holdingsCount: number;
  expenseRatio: string;
  indexStats: IndexStatsType;
  baseCurrency: string;
  geographyDistribution?: ETFAssetGeographyDistributionType;
  sectorDistribution?: ETFAssetSectorDistributionType;
  about: ETFAboutType;
  news: AssetNewsDocument[];
};

/**
 * Common
 */
export type AssetFundamentalsType = ETFAssetFundamentalsType | StockAssetFundamentalsType;

export enum AssetTagEnum {
  FRACTIONAL = "FRACTIONAL",
  ADR = "ADR",
  COMMISSION_FREE = "COMMISSION_FREE",
  SMART_EXECUTION = "SMART_EXECUTION",
  MARKET_OPEN = "MARKET_OPEN",
  MARKET_CLOSED = "MARKET_CLOSED"
}

export type MarketInfoType = {
  isOpen: boolean;
  nextMarketOpen: number; // unix milliseconds
};

export type AssetDataResponseType = {
  fundamentals: AssetFundamentalsType;
  currentPrice: number;
  tradedCurrency: currenciesConfig.MainCurrencyType;
  tags: AssetTagEnum[];
  marketInfo?: MarketInfoType;
};

/**
 * Recent Activity
 */
export type RecentActivityOrderItemType = {
  type: "order";
  item: OrderDocument & { isCancellable: boolean };
};
export type RecentActivityDividendItemType = {
  type: "dividend";
  item: DividendTransactionDocument;
};
export type RecentActivityRewardItemType = {
  type: "reward";
  item: RewardDocument;
};
export type RecentActivityItemType =
  | RecentActivityOrderItemType
  | RecentActivityRewardItemType
  | RecentActivityDividendItemType;
