import React, { Component } from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { AssetNewsDocument } from "../../../../models/AssetNews";
import axios from "axios";
import { captureException } from "@sentry/react";
import { emitToast } from "../../../utils/eventService";
import { ToastTypeEnum } from "../../../configs/toastConfig";
import AssetNewsItem from "../../assetNewsItem";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
};

type NewsSectionType = {
  sectionTitle: string;
  data: AssetNewsDocument[];
};

type StateType = {
  newsSections: NewsSectionType[];
  isLoading: boolean;
};

export default class AssetSideModalEtfAboutSection extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      newsSections: [],
      isLoading: true
    };
  }

  async componentDidMount() {
    await this._getNewsForAsset(this.props.assetCommonId);
  }

  private async _getNewsForAsset(assetCommonId: investmentUniverseConfig.AssetType): Promise<void> {
    try {
      const response = await axios("/asset-news", {
        headers: { "Content-Type": "application/json" },
        params: {
          assetCommonId: assetCommonId
        }
      });
      this.setState({ newsSections: response.data, isLoading: false }); // Update state with fetched news items and set is loading to false
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred while fetching news for the asset. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  render(): JSX.Element {
    const { assetCommonId } = this.props;
    const { newsSections } = this.state;

    const { simpleName } = ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.AssetConfigType;
    return (
      <>
        <div className="fade-in">
          <div className="row p-0 m-0">
            <h3 className="asset-news-side-title fw-semibold">{`News • ${simpleName}`}</h3>
          </div>
          <div>
            {newsSections.map((NewsSectionType, index) => (
              <div key={index} style={{ marginBottom: "32px" }}>
                <h5 className="asset-news-side-section-title fw-semibold">{NewsSectionType.sectionTitle}</h5>
                <div>
                  {NewsSectionType.data.map((newsItem, index) => (
                    <AssetNewsItem key={index} newsItem={newsItem} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  }
}
