import React, { Component } from "react";
import { AssetTagEnum } from "./assetSideModal.types";
import { InfoKeyType } from "./configuration";

type PropsType = {
  tags: AssetTagEnum[];
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

type AssetTagConfigType = {
  icon: string;
  iconColor: string;
  backgroundColor: string;
  label: string;
  order: number;
  infoKey: InfoKeyType;
};

const ASSET_TAG_CONFIG: Record<AssetTagEnum, AssetTagConfigType> = {
  [AssetTagEnum.MARKET_CLOSED]: {
    icon: "dark_mode",
    iconColor: "#C78830",
    label: "Market closed",
    backgroundColor: "#FFEDBF",
    order: 0,
    infoKey: "marketHours"
  },
  [AssetTagEnum.MARKET_OPEN]: {
    icon: "light_mode",
    iconColor: "#23846A",
    label: "Market open",
    backgroundColor: "#DCFAF1",
    order: 0,
    infoKey: "marketHours"
  },
  [AssetTagEnum.ADR]: {
    icon: "public",
    iconColor: "#536AE3",
    label: "ADR",
    backgroundColor: "#F1F3FD",
    order: 1,
    infoKey: "adr"
  },
  [AssetTagEnum.COMMISSION_FREE]: {
    icon: "money_off",
    iconColor: "#536AE3",
    label: "Commission-free",
    backgroundColor: "#F1F3FD",
    order: 2,
    infoKey: "commissionFree"
  },
  [AssetTagEnum.SMART_EXECUTION]: {
    icon: "wand_stars",
    iconColor: "#536AE3",
    label: "Smart execution",
    backgroundColor: "#F1F3FD",
    order: 3,
    infoKey: "smartExecution"
  },
  [AssetTagEnum.FRACTIONAL]: {
    icon: "layers",
    iconColor: "#536AE3",
    label: "Fractional",
    backgroundColor: "#F1F3FD",
    order: 4,
    infoKey: "fractional"
  }
};

export default class AssetTags extends Component<PropsType> {
  render() {
    const { tags, onInfoButtonClick } = this.props;
    if (!tags?.length) return <></>;

    return (
      <div
        className="d-flex flex-row flex-nowrap overflow-auto no-scroll-bar my-3"
        onClick={(e) => e.stopPropagation()}
      >
        {tags
          .map((tag) => ASSET_TAG_CONFIG[tag])
          .sort((a, b) => a.order - b.order)
          .map((assetTagConfig, index, array) => {
            const { icon, iconColor, backgroundColor, label, infoKey } = assetTagConfig;
            const isLast = index === array.length - 1;

            return (
              <div
                key={index}
                className={`asset-tag-pill text-nowrap ${isLast ? "" : "me-2"}`}
                style={{ backgroundColor }}
                onClick={() => onInfoButtonClick(infoKey)}
              >
                <span
                  className="material-symbols-outlined cursor-pointer icon-primary"
                  style={{ fontSize: "17px", color: iconColor }}
                >
                  {icon}
                </span>
                <span className="mx-1" style={{ fontSize: "14px" }}>
                  {label}
                </span>
                <span
                  className="material-symbols-outlined cursor-pointer icon-primary"
                  style={{ fontSize: "16px", color: iconColor }}
                >
                  chevron_forward
                </span>
              </div>
            );
          })}
      </div>
    );
  }
}
