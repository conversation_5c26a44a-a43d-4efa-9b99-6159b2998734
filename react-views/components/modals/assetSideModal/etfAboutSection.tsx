import React, { Component } from "react";
import { ETFAboutType } from "./assetSideModal.types";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ASSET_CLASSES_WITH_TOP_HOLDINGS } from "./configuration";
import EtfHoldingCard from "../../EtfHoldingCard";
import { formatPercentage } from "../../../utils/formatterUtil";
import { AssetClassType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { getAssetClassBreakdownName } from "../../../utils/universeUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  about: ETFAboutType;
  topHoldings: {
    name: string;
    weight: string;
    logoUrl: string;
  }[];
};

export default class AssetSideModalEtfAboutSection extends Component<PropsType> {
  private _getAboutSectionRow(label: string, value?: string): JSX.Element {
    if (!value) return null;

    return (
      <div className="row p-0 m-0 mb-3">
        <div className="col-6">
          <p className="text-muted m-0">{label}</p>
        </div>
        <div className="col-6 text-end">
          <p className="fw-bolder m-0">{value}</p>
        </div>
      </div>
    );
  }

  private _getAssetBreakdown() {
    const { assetCommonId } = this.props;
    const { assetClassBreakdown } = ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType;
    if (!assetClassBreakdown) return null;

    let breakdown = "";
    Object.keys(assetClassBreakdown).forEach((assetClassKey) => {
      breakdown += `${breakdown} ${formatPercentage(
        assetClassBreakdown[assetClassKey as AssetClassType],
        this.context.locale
      )} ${getAssetClassBreakdownName(assetClassKey as AssetClassType)} - `;
    });

    breakdown = breakdown.slice(0, -3);

    return breakdown;
  }

  render(): JSX.Element {
    const { about, assetCommonId, topHoldings } = this.props;

    const { assetClass, simpleName } = ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType;
    const showTopHoldings = topHoldings.length > 0 && ASSET_CLASSES_WITH_TOP_HOLDINGS.includes(assetClass);
    const isCommodity = assetClass === "commodities";

    return (
      <>
        <div className="fade-in">
          <div className="row p-0 m-0 mb-4">
            <h5 className="fw-bolder p-0 m-0">{`About ${simpleName}`}</h5>
          </div>
          <div className="row p-3 m-0 mb-5 asset-modal-card">
            {this._getAboutSectionRow(isCommodity ? "ETC Name" : "ETF name", about.advancedName)}
            {this._getAboutSectionRow("Ticker", about.ticker)}
            {this._getAboutSectionRow("Trading on", about.exchange)}
            {this._getAboutSectionRow("ISIN", about.isin)}
            {this._getAboutSectionRow("Provider", about.provider)}
            {this._getAboutSectionRow("Index", about.index)}
            {this._getAboutSectionRow("Replication", about.replication)}
            {this._getAboutSectionRow("Asset Breakdown", this._getAssetBreakdown())}
            <div className="d-flex justify-content-center mb-3">
              <div className="horizontal-border"></div>
            </div>
            {this._getAboutSectionRow("Asset Class", about.assetClass)}
            {this._getAboutSectionRow("Sector", about?.sector)}
          </div>

          <div className="row p-0 m-0 mb-2">
            <p className="fw-bolder p-0 m-0">Description</p>
          </div>
          <div className="row p-0 m-0 mb-4">
            <p className="p-0 text-muted">{about.description}</p>
          </div>
          {/*/!* Top Holdings *!/*/}
          {showTopHoldings && (
            <div className="row p-0 m-0 mb-4">
              <div className="col-12 p-0">
                <p className="fw-bolder mb-2">Top 10 holdings</p>
                <div className="row m-0">
                  {topHoldings.map(({ name, weight, logoUrl }, index) => (
                    <div className="col-md-4 col-6 p-0 pe-2 mb-2" key={`holding-card-${index}`}>
                      <EtfHoldingCard name={name} weight={weight} logoUrl={logoUrl} />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          {/*/!* End Top Holdings *!/*/}
        </div>
      </>
    );
  }
}
