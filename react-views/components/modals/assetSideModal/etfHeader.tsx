import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { InfoKeyType } from "./configuration";
import AssetIcon from "../../assetIcon";
import { AssetTagEnum } from "./assetSideModal.types";
import AssetTags from "./assetTags";
import { getAssetIconUrl } from "../../../utils/universeUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  showTopHoldings: boolean;
  topHoldings: {
    name: string;
    weight: string;
    logoUrl: string;
  }[];
  holdingsCount: number;
  tags: AssetTagEnum[];
  onClick: () => void;
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

class AssetSideModalEtfHeader extends Component<PropsType> {
  private static _createAlternateLogoUrl(logoUrl: string): string {
    if (!logoUrl) return null;

    const split = logoUrl.split("/");
    split[split.length - 1] = split[split.length - 1].toUpperCase().replace("PNG", "png");
    return split.join("/");
  }

  render(): JSX.Element {
    const { assetCommonId, topHoldings, showTopHoldings, holdingsCount, onClick, tags, onInfoButtonClick } =
      this.props;
    const { simpleName, tickerWithCurrency, shortDescription } = ASSET_CONFIG[
      assetCommonId
    ] as investmentUniverseConfig.ETFAssetConfigType;
    const subtitle = tickerWithCurrency + " • " + shortDescription;

    return (
      <div className="cursor-pointer" onClick={onClick}>
        <div className="d-flex p-0 fade-in">
          <AssetIcon category={"etf"} iconUrl={getAssetIconUrl(assetCommonId)} className="me-2" size="lg" />
          <div className="d-flex p-0 align-items-center flex-wrap flex-row-fluid w-100">
            <div className="col-11 d-flex p-0 flex-column pe-2 flex-grow-1">
              <h4 className="fw-bolder">{simpleName}</h4>
              <span className="t-875 d-block text-muted">{subtitle}</span>
            </div>
            <div className="col-1 align-self-center p-0">
              <span
                className="material-symbols-outlined cursor-pointer icon-primary align-self-center align-self-center p-2 cursor-pointer ms-1"
                style={{
                  background: "#F1F3FD",
                  borderRadius: "50%"
                }}
              >
                expand_more
              </span>
            </div>
          </div>
        </div>

        <AssetTags tags={tags} onInfoButtonClick={onInfoButtonClick} />

        {showTopHoldings && (
          <div className="d-flex flex-row p-0 flex-nowrap overflow-auto no-scroll-bar m-0 mt-3 mb-5">
            {topHoldings.map(({ logoUrl }, index) => (
              <div
                className="col d-flex h-100 align-self-center justify-content-center p-3 wh-holding"
                key={`holding-card-${index}`}
                style={{ maxHeight: "24px", maxWidth: "24px", minHeight: "24px", minWidth: "24px" }}
              >
                {logoUrl && (
                  <>
                    {/*Pattern for loading image with fallback options */}
                    <object
                      className="d-flex align-self-center w-100"
                      data={logoUrl}
                      type="image/png"
                      style={{
                        maxHeight: "24px",
                        maxWidth: "24px",
                        minHeight: "24px",
                        minWidth: "24px"
                      }}
                    >
                      <object
                        className="d-flex align-self-center w-100"
                        data={AssetSideModalEtfHeader._createAlternateLogoUrl(logoUrl)}
                        type="image/png"
                        style={{
                          maxHeight: "24px",
                          maxWidth: "24px",
                          minHeight: "24px",
                          minWidth: "24px"
                        }}
                      >
                        <div
                          className="w-100"
                          style={{
                            maxHeight: "24px",
                            maxWidth: "24px",
                            minHeight: "24px",
                            minWidth: "24px"
                          }}
                        />
                      </object>
                    </object>
                  </>
                )}
              </div>
            ))}
            <div
              className="col d-flex h-100 align-self-center justify-content-center p-2  wh-holding bg-dark text-light"
              key="holding-card-more"
              style={{ maxHeight: "48px", maxWidth: "48px" }}
            >
              <span className="t-75 align-self-center cursor-pointer">+{holdingsCount - 10}</span>
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default AssetSideModalEtfHeader;
