import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import Decimal from "decimal.js";
import { InfoKeyType } from "./configuration";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  expenseRatio: string;
  baseCurrency: string;
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

class AssetSideModalEtfKeyFacts extends Component<PropsType> {
  render(): JSX.Element {
    const { assetCommonId, expenseRatio, baseCurrency, onInfoButtonClick } = this.props;
    const { income, provider } = ASSET_CONFIG[assetCommonId] as investmentUniverseConfig.ETFAssetConfigType;

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Key Facts</h5>
            <div className="row m-0 py-4 asset-modal-card">
              {[
                { label: "Base Currency", value: baseCurrency, labelKey: "baseCurrency" },
                { label: "Income", value: income, labelKey: "income" },
                {
                  label: "Expense ratio",
                  value: expenseRatio + "%",
                  labelKey: "expenseRatio",
                  class: "mt-5"
                },
                { label: "Provider", value: provider, labelKey: null, class: "mt-5" }
              ].map((keyFact) => (
                <div key={keyFact.label} className={`col-6 p-0 ${keyFact.class}`}>
                  <div className="d-flex flex-column align-items-center">
                    <h6 className="fw-bolder text-primary">{keyFact.value}</h6>
                    <div className="d-flex flex-row">
                      <p className="text-muted m-0">{keyFact.label}</p>
                      {keyFact?.labelKey && (
                        <span
                          className="material-symbols-outlined cursor-pointer align-self-center ms-1"
                          style={{
                            fontSize: "16px"
                          }}
                          onClick={() => onInfoButtonClick(keyFact.labelKey as InfoKeyType)}
                        >
                          info
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default AssetSideModalEtfKeyFacts;
