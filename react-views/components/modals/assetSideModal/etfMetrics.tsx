import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { InfoKeyType } from "./configuration";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  indexStats: {
    expectedReturn: string;
    annualRisk: string;
    fpEarnings?: string;
    dividendYield?: string;
    bondYield?: string;
    coupon?: string;
  };
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

class AssetSideModalEtfMetrics extends Component<PropsType> {
  private _getMetricColumn(
    label: string,
    infoKey: InfoKeyType,
    enableMargin: boolean,
    value?: string
  ): JSX.Element {
    if (!value) return <></>;

    const { onInfoButtonClick } = this.props;

    return (
      <div key={label} className={`col-6 p-0 ${enableMargin ? "mt-5" : ""}`}>
        <div className="d-flex flex-column align-items-center">
          <h6 className="fw-bolder text-primary">{value}</h6>
          <div className="d-flex flex-row">
            <p className="text-muted m-0">{label}</p>
            <span
              className="material-symbols-outlined cursor-pointer align-self-center ms-1"
              style={{
                fontSize: "16px"
              }}
              onClick={() => onInfoButtonClick(infoKey)}
            >
              info
            </span>
          </div>
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { assetCommonId, indexStats } = this.props;
    const { assetClass } = ASSET_CONFIG[assetCommonId];

    const isCommodity = assetClass === "commodities";

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Metrics</h5>
            <div className="row m-0 py-4 asset-modal-card">
              {this._getMetricColumn("Annualised Return", "annualizedReturn", false, indexStats.expectedReturn)}
              {this._getMetricColumn("Risk", "annualRisk", false, indexStats.annualRisk)}
              {["equities", "realEstate"].includes(assetClass) && (
                <>
                  {this._getMetricColumn("Forward P/E ratio", "fpEarnings", true, indexStats.fpEarnings)}
                  {this._getMetricColumn("Dividend yield", "dividendYield", true, indexStats.dividendYield)}
                </>
              )}
              {assetClass === "bonds" && (
                <>
                  {this._getMetricColumn("Yield", "bondYield", true, indexStats.bondYield)}
                  {this._getMetricColumn("Coupon", "coupon", true, indexStats.coupon)}
                </>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default AssetSideModalEtfMetrics;
