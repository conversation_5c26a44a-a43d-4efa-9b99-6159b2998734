import React, { Component } from "react";
import { InvestmentDetailsType } from "./assetSideModal.types";

type PropsType = InvestmentDetailsType;

class AssetSideModalInvestmentDetails extends Component<PropsType> {
  render(): JSX.Element {
    const {
      currentValue,
      performanceValue,
      performancePercentage,
      numberOfShares,
      portfolioAllocation,
      isPerformancePositive
    } = this.props;

    return (
      <>
        {/* Investment Details */}
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">My Investment</h5>
            <div className="row m-0 py-4 asset-modal-card">
              <div className="col-6 p-0 mb-3">
                <div className="d-flex flex-column align-items-center">
                  <h6 className=" fw-bolder text-primary">{currentValue}</h6>
                  <p className="text-muted mb-2">Current Value</p>
                </div>
              </div>
              <div className="col-6 p-0 mb-3">
                <div className="d-flex flex-column align-items-center">
                  <h6 className={`fw-bolder ${isPerformancePositive ? "text-success" : "text-danger"}`}>
                    {performanceValue}
                  </h6>
                  <p className="text-muted mb-2">{performancePercentage}</p>
                </div>
              </div>
              <div className="col-6 p-0">
                <div className="d-flex flex-column align-items-center">
                  <h6 className=" fw-bolder text-primary">{numberOfShares}</h6>
                  <p className="text-muted mb-2">{"No. of shares"}</p>
                </div>
              </div>
              <div className="col-6 p-0">
                <div className="d-flex flex-column align-items-center">
                  <h6 className="fw-bolder text-primary">{portfolioAllocation}</h6>
                  <p className="text-muted mb-2">Portfolio Allocation</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* End Investment Details */}
      </>
    );
  }
}

export default AssetSideModalInvestmentDetails;
