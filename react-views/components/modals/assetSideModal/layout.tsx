import React, { Component } from "react";

type PropsType = {
  handleBackButtonClick: () => void;
  handleClose: () => void;
  modalSlideAnimation: "in" | "out";
};

class AssetSideModalLayout extends Component<PropsType> {
  render(): JSX.Element {
    const { handleClose, handleBackButtonClick, modalSlideAnimation, children } = this.props;

    return (
      <div className="container-fluid p-0 m-0">
        <div
          className="row m-0 vh-100 justify-content-end fixed-top-higher etf-side-mask"
          style={{ overflowY: "scroll" }}
          onScroll={(e) => {
            e.stopPropagation();
          }}
        >
          <div className="col-md-7 col-sm-4 cursor-pointer d-none d-md-block" onClick={() => handleClose()} />
          <div className={`col-md-5 col-sm-8 p-md-5 p-3 bg-white position-relative slide-${modalSlideAnimation}`}>
            <div className="row m-0 p-xxl-5">
              {/* Back Button */}
              <div className="row p-0 m-0 mb-md-5 mb-3">
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => handleBackButtonClick()}
                    style={{
                      fontSize: "24px"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}
              {children}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default AssetSideModalLayout;
