import React from "react";
import { AssetTagEnum } from "./assetSideModal.types";

type PropsType = {
  isMarketOpen: boolean;
  onClick?: () => void;
};

const MARKET_HOURS_TAG_CONFIG = {
  [AssetTagEnum.MARKET_CLOSED]: {
    icon: "dark_mode",
    iconColor: "#C78830",
    label: "Market closed",
    backgroundColor: "#FFEDBF"
  },
  [AssetTagEnum.MARKET_OPEN]: {
    icon: "light_mode",
    iconColor: "#23846A",
    label: "Market open",
    backgroundColor: "#DCFAF1"
  }
};

class MarketHoursTag extends React.Component<PropsType> {
  render() {
    const { isMarketOpen, onClick } = this.props;

    const tagType = isMarketOpen ? AssetTagEnum.MARKET_OPEN : AssetTagEnum.MARKET_CLOSED;
    const { icon, iconColor, backgroundColor, label } = MARKET_HOURS_TAG_CONFIG[tagType];

    return (
      <div className="asset-tag-pill text-nowrap cursor-pointer" style={{ backgroundColor }} onClick={onClick}>
        <span
          className="material-symbols-outlined cursor-pointer icon-primary"
          style={{ fontSize: "17px", color: iconColor }}
        >
          {icon}
        </span>
        <span className="mx-1" style={{ fontSize: "14px" }}>
          {label}
        </span>
        <span
          className="material-symbols-outlined cursor-pointer icon-primary"
          style={{ fontSize: "16px", color: iconColor }}
        >
          chevron_forward
        </span>
      </div>
    );
  }
}

export default MarketHoursTag;
