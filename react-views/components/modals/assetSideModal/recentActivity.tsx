import React, { Component } from "react";
import { RecentActivityItemType } from "./assetSideModal.types";
import {
  AssetTransactionDocument,
  DividendTransactionDocument,
  ExecutionWindowType,
  RebalanceTransactionDocument,
  TransactionDocument,
  TransactionStatusType
} from "../../../../models/Transaction";
import { RewardDocument } from "../../../../models/Reward";
import InvestorRewardRow from "../../investorRewardRow";
import InvestorOrderRow from "../../investorOrderRow";
import { OrderDocument } from "../../../../models/Order";
import { Modal, ModalBody } from "react-bootstrap";
import OrderReceipt from "../../orderReceipt";
import { GlobalContext, GlobalContextType } from "../../../contexts/globalContext";
import { OrderSideType } from "../../../../services/wealthkernelService";
import RewardReceiptModal from "../rewardReceiptModal";
import InvestorTransactionRow from "../../investorTransactionRow";
import DividendReceiptModal from "../dividendReceiptModal";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { emitToast } from "../../../utils/eventService";
import { ToastTypeEnum } from "../../../configs/toastConfig";
import axios from "axios";
import LoadingSpinner from "../../loadingSpinner";
import { AssetPriceInfo } from "../../../types/price";

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  recentActivityOrders: RecentActivityItemType[];
  currentPriceInfo: AssetPriceInfo;
};

type StateType = {
  selectedOrder: OrderDocument & { isCancellable: boolean };
  selectedReward: RewardDocument;
  selectedDividend: DividendTransactionDocument;
  showAllActivityItemsModal: boolean;
  allActivityOrders: RecentActivityItemType[];
};
//todo: add rebalance badge to InvestorOrderRow
class AssetSideModalRecentActivity extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      selectedOrder: null,
      selectedReward: null,
      selectedDividend: null,
      showAllActivityItemsModal: false,
      allActivityOrders: []
    };
  }

  private _showAllActivityItems() {
    this.setState({ showAllActivityItemsModal: true, allActivityOrders: [] }, this._fetchAssetRecentActivity);
  }

  private _closeAllActivityItems() {
    this.setState({ showAllActivityItemsModal: false, allActivityOrders: [] });
  }

  private async _fetchAssetRecentActivity(): Promise<void> {
    const { assetCommonId } = this.props;
    try {
      const response = await axios.get(`/investment-products/${assetCommonId}/recent-activity`);
      const allActivityOrders: RecentActivityItemType[] = response.data;

      this.setState({
        allActivityOrders
      });
    } catch (err) {
      emitToast({
        content: "Could not fetch all activity orders. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private _setSelectedOrder(selectedOrder: OrderDocument & { isCancellable: boolean }) {
    this.setState({ selectedOrder });
  }

  private _setSelectedReward(selectedReward: RewardDocument) {
    this.setState({ selectedReward });
  }

  private _setSelectedDividend(selectedDividend: DividendTransactionDocument) {
    this.setState({ selectedDividend });
  }

  private static _getBackgroundClass(status: TransactionStatusType, side: OrderSideType): string {
    if (status == "Settled" && side == "Buy") {
      return "bg-order-buy-receipt";
    } else if (status == "Settled" && side == "Sell") {
      return "bg-order-sell-receipt";
    } else {
      return "";
    }
  }

  private _getExecutionWindow(
    transaction: AssetTransactionDocument | RebalanceTransactionDocument
  ): ExecutionWindowType {
    const { selectedOrder } = this.state;

    if (transaction.category === "AssetTransaction") {
      return selectedOrder.assetCategory === "etf"
        ? (transaction as AssetTransactionDocument).executionWindow?.etfs
        : (transaction as AssetTransactionDocument).executionWindow?.stocks;
    } else if (selectedOrder.side === "Buy") {
      return (transaction as RebalanceTransactionDocument).buyExecutionWindow;
    } else if (selectedOrder.side === "Sell") {
      return (transaction as RebalanceTransactionDocument).sellExecutionWindow;
    }
  }

  private _isPartOfPortfolioTransaction(
    transaction: AssetTransactionDocument | RebalanceTransactionDocument
  ): boolean {
    if (transaction.category === "AssetTransaction") {
      const assetTransaction = transaction as AssetTransactionDocument;
      return assetTransaction.portfolioTransactionCategory !== "update" && assetTransaction?.orders?.length > 1;
    } else {
      return true;
    }
  }

  private _mapRecentActivityItems(recentActvityItems: RecentActivityItemType[]): JSX.Element[] {
    const { currentPriceInfo } = this.props;

    return recentActvityItems.map((recentActivityItem, index) => {
      if (recentActivityItem.type === "order") {
        const order = recentActivityItem.item;
        return (
          <InvestorOrderRow
            onClick={() => this._setSelectedOrder(order)}
            parentTransaction={order.transaction as TransactionDocument}
            currentPriceInfo={currentPriceInfo}
            order={order}
            enableBadge={true}
            key={`order_${index}`}
          />
        );
      } else if (recentActivityItem.type === "reward") {
        const reward = recentActivityItem.item as RewardDocument;
        return (
          <InvestorRewardRow
            onClick={() => this._setSelectedReward(reward)}
            reward={reward}
            key={`reward_${index}`}
          />
        );
      } else if (recentActivityItem.type === "dividend") {
        const dividend = recentActivityItem.item as TransactionDocument;
        return (
          <InvestorTransactionRow
            onClick={() => this._setSelectedDividend(dividend as DividendTransactionDocument)}
            transaction={dividend}
            truelayerProviders={[]}
            investmentProducts={[]}
            key={`reward_${index}`}
          />
        );
      }
    });
  }

  render(): JSX.Element {
    const { recentActivityOrders, currentPriceInfo } = this.props;
    const { selectedOrder, selectedReward, selectedDividend, allActivityOrders, showAllActivityItemsModal } =
      this.state;

    const transaction = selectedOrder?.transaction as AssetTransactionDocument | RebalanceTransactionDocument;
    const activeReceipt = !!selectedDividend || !!selectedOrder || !!selectedOrder;
    let price;
    if (selectedOrder?.isMatched) {
      price = selectedOrder.displayUnitPrice;
    } else {
      price = currentPriceInfo;
    }

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <div className="row p-0 m-0">
              <div className="col-6 p-0">
                <h5 className="fw-bolder">Recent Activity</h5>
              </div>
              <div className="col-6 p-0">
                <h6
                  className="fw-bolder text-end text-primary cursor-pointer"
                  onClick={() => this._showAllActivityItems()}
                >
                  See all
                </h6>
              </div>
            </div>
            {this._mapRecentActivityItems(recentActivityOrders)}
          </div>
        </div>
        {/* Order Receipt Modal*/}
        {selectedOrder && (
          <Modal show={true} onHide={() => this._setSelectedOrder(null)}>
            <div
              className={`modal-content ${AssetSideModalRecentActivity._getBackgroundClass(
                transaction.displayStatus,
                selectedOrder.side
              )}`}
            >
              <Modal.Header className="border-bottom-0" closeButton>
                <Modal.Title />
              </Modal.Header>
              <Modal.Body className="p-0 px-3 mb-5">
                <OrderReceipt
                  order={selectedOrder}
                  price={price}
                  transactionId={transaction.id}
                  executionWindow={this._getExecutionWindow(transaction)}
                  isPartOfPortfolioTransaction={this._isPartOfPortfolioTransaction(transaction)}
                />
              </Modal.Body>
            </div>
          </Modal>
        )}
        {/* End Order Receipt Modal*/}
        {/* Reward Receipt Modal*/}
        {selectedReward && (
          <RewardReceiptModal
            reward={selectedReward}
            show={true}
            handleClose={() => this._setSelectedReward(null)}
          />
        )}
        {/* End Reward Receipt Modal*/}

        {selectedDividend && (
          <DividendReceiptModal
            transaction={selectedDividend}
            show={!!selectedDividend}
            handleClose={() => this._setSelectedDividend(null)}
          />
        )}

        {/* Activity Items Modal */}

        <Modal
          onHide={() => this._closeAllActivityItems()}
          dialogClassName="p-md-5"
          size={"lg"}
          show={showAllActivityItemsModal && !activeReceipt}
        >
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          {/* Action Title */}
          <ModalBody className={"mb-5"} style={{ maxHeight: "80vh", overflowY: "auto" }}>
            <div className="row m-0 px-md-5 px-2">
              {allActivityOrders.length > 0 ? (
                <div className="col p-0">{this._mapRecentActivityItems(allActivityOrders)}</div>
              ) : (
                <LoadingSpinner />
              )}
            </div>
          </ModalBody>
        </Modal>
        {/* End Activity Items Modal */}
      </>
    );
  }
}

AssetSideModalRecentActivity.contextType = GlobalContext;

export default AssetSideModalRecentActivity;
