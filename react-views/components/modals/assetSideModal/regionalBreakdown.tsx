import Decimal from "decimal.js";
import React, { Component } from "react";
import { ETFAssetGeographyDistributionType } from "./assetSideModal.types";
import { formatPercentage } from "../../../utils/formatterUtil";
import { GlobalContext, GlobalContextType } from "../../../contexts/globalContext";

type PropsType = {
  geographyDistribution: ETFAssetGeographyDistributionType;
};

class AssetSideModalRegionalBreakdown extends Component<PropsType> {
  render() {
    const { geographyDistribution } = this.props;
    const { locale } = this.context as GlobalContextType;

    if (!geographyDistribution) return <></>;

    return (
      <div className="row m-0 p-0 m-0 mt-5 mb-5">
        <div className="col p-0">
          <h5 className="fw-bolder mb-4">Regional Breakdown</h5>
          <div className="d-flex flex-column">
            {geographyDistribution.map((el, index) => (
              <div key={`region-${index}`} className="d-flex flex-column mb-2">
                <div className="d-flex flex-row align-items-center">
                  <p className="m-0 w-100">{el.name}</p>
                  <div className="progress-bar">
                    <div
                      className="progress"
                      style={{ width: `${el.percentage}%`, backgroundColor: "#536ae3" }}
                    ></div>
                  </div>
                </div>
                <div className="row">
                  <p className="text-primary text-end m-0">
                    {formatPercentage(Decimal.div(el.percentage, 100).toNumber(), locale)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

AssetSideModalRegionalBreakdown.contextType = GlobalContext;

export default AssetSideModalRegionalBreakdown;
