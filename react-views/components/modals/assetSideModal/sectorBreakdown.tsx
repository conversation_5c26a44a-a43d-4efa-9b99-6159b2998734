import React, { Component } from "react";
import { ETFAssetSectorDistributionType, ETFAssetSectorId } from "./assetSideModal.types";
import { formatPercentage } from "../../../utils/formatterUtil";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../../contexts/globalContext";
import ConfigUtil from "../../../../utils/configUtil";

type PropsType = {
  sectorDistribution: ETFAssetSectorDistributionType;
};

class AssetSideModalSectorBreakdown extends Component<PropsType> {
  private _getSectorIcon(sectorId: ETFAssetSectorId): string {
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);
    if (sectorId === "real-estate") {
      return ASSET_CLASS_CONFIG.realEstate.icon as string;
    } else {
      return SECTOR_CONFIG[sectorId].icon;
    }
  }

  private _getSectorColor(sectorId: ETFAssetSectorId): string {
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);
    if (sectorId === "real-estate") {
      return ASSET_CLASS_CONFIG.realEstate.colorClass as string;
    } else {
      return SECTOR_CONFIG[sectorId].colorClass;
    }
  }

  render() {
    const { sectorDistribution } = this.props;
    const { locale } = this.context as GlobalContextType;

    if (!sectorDistribution) return <></>;

    return (
      <div className="row m-0 p-0 m-0 mt-4 mb-5">
        <div className="col p-0">
          <h5 className="fw-bolder mb-4">Sectors</h5>
          <div className="d-flex flex-column">
            {sectorDistribution.map((el, index) => (
              <div key={`sector-${index}`} className="d-flex flex-column mb-3">
                <div className="d-flex flex-row align-items-center">
                  <div className="d-flex flex-row align-items-center w-100">
                    <div className="sector-icon-container me-2">
                      <img
                        alt="sector logo"
                        className="align-self-center"
                        style={{ height: "56px" }}
                        src={this._getSectorIcon(el.id)}
                      />
                    </div>
                    <p className="m-0 w-100">{el.name}</p>
                  </div>
                  <div className="d-flex flex-column w-100 mt-4">
                    <div className="progress-bar">
                      <div
                        className="progress"
                        style={{
                          width: `${el.percentage}%`,
                          backgroundColor: this._getSectorColor(el.id)
                        }}
                      ></div>
                    </div>
                    <p className="text-primary text-end m-0">
                      {formatPercentage(Decimal.div(el.percentage, 100).toNumber(), locale)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

AssetSideModalSectorBreakdown.contextType = GlobalContext;

export default AssetSideModalSectorBreakdown;
