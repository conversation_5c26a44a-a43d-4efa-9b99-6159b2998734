import React, { Component } from "react";
import { StockAboutType } from "./assetSideModal.types";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  about: StockAboutType;
};

export default class AssetSideModalStockAboutSection extends Component<PropsType> {
  private _getAboutSectionRow(label: string, value?: string): JSX.Element {
    if (!value) return <></>;

    return (
      <div className="row p-0 m-0 mb-3">
        <div className="col-6">
          <p className="text-muted m-0">{label}</p>
        </div>
        <div className="col-6 text-end">
          <p className="fw-bolder m-0">{value}</p>
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { about, assetCommonId } = this.props;

    const { simpleName } = ASSET_CONFIG[assetCommonId];

    return (
      <>
        <div className="fade-in">
          <div className="row p-0 m-0 mb-4">
            <h5 className="fw-bolder p-0 m-0">{`About ${simpleName}`}</h5>
          </div>
          <div className="row p-3 m-0 mb-5 asset-modal-card">
            {this._getAboutSectionRow("Ticker", about.ticker)}
            {this._getAboutSectionRow("Trading on", about.exchange)}
            {this._getAboutSectionRow("ISIN", about.isin)}
            <div className="d-flex justify-content-center mb-3">
              <div className="horizontal-border"></div>
            </div>
            {this._getAboutSectionRow("Sector", about?.sector)}
            {this._getAboutSectionRow("Industry", about.industry)}
            <div className="d-flex justify-content-center mb-3">
              <div className="horizontal-border"></div>
            </div>
            {this._getAboutSectionRow("CEO", about?.ceo)}
            {this._getAboutSectionRow("Headquarters", about?.headquarters)}
            {this._getAboutSectionRow("Employees", about?.employees)}
            {this._getAboutSectionRow("Website", about?.website)}
          </div>

          <div className="row p-0 m-0 mb-2">
            <p className="fw-bolder p-0 m-0">Description</p>
          </div>
          <div className="row p-0 m-0 mb-4">
            <p className="p-0 text-muted">{about.description}</p>
          </div>
        </div>
      </>
    );
  }
}
