import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { getAssetIconUrl } from "../../../utils/universeUtil";
import AssetIcon from "../../assetIcon";
import AssetTags from "./assetTags";
import { AssetTagEnum } from "./assetSideModal.types";
import { InfoKeyType } from "./configuration";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetCommonId: investmentUniverseConfig.AssetType;
  tags: AssetTagEnum[];
  onClick: () => void;
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

class AssetSideModalStockHeader extends Component<PropsType> {
  render(): JSX.Element {
    const { assetCommonId, tags, onInfoButtonClick, onClick } = this.props;
    const { simpleName, tickerWithCurrency, shortDescription } = ASSET_CONFIG[
      assetCommonId
    ] as investmentUniverseConfig.StockAssetConfigType;
    const subtitle = tickerWithCurrency + " • " + shortDescription;

    return (
      <div className="cursor-pointer mb-5" onClick={onClick}>
        <div className="d-flex p-0 fade-in">
          <AssetIcon category={"stock"} iconUrl={getAssetIconUrl(assetCommonId)} className="me-2" size="lg" />

          <div className="d-flex p-0 align-items-center flex-wrap flex-row-fluid w-100">
            <div className="col-11 d-flex p-0 flex-column pe-2 flex-grow-1">
              <h4 className="fw-bolder">{simpleName}</h4>
              <span className="t-875 d-block text-muted">{subtitle}</span>
            </div>
            <div className="col-1 align-self-center p-0">
              <span
                className="material-symbols-outlined cursor-pointer icon-primary align-self-center align-self-center p-2 cursor-pointer ms-1"
                style={{
                  background: "#F1F3FD",
                  borderRadius: "50%"
                }}
              >
                expand_more
              </span>
            </div>
          </div>
        </div>

        <AssetTags tags={tags} onInfoButtonClick={onInfoButtonClick} />
      </div>
    );
  }
}

export default AssetSideModalStockHeader;
