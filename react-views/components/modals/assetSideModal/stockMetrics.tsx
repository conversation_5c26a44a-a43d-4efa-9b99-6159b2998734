import React, { Component } from "react";
import { InfoKeyType } from "./configuration";
import { StockMetricsType } from "./assetSideModal.types";

type PropsType = {
  metrics: StockMetricsType;
  onInfoButtonClick: (infoKey: InfoKeyType) => void;
};

class AssetSideModalStockMetrics extends Component<PropsType> {
  private _getMetricColumn(label: string, infoKey: InfoKeyType, cssClass: string, value?: string): JSX.Element {
    if (!value) return <></>;

    const { onInfoButtonClick } = this.props;

    return (
      <div key={label} className={`col-6 p-0 ${cssClass}`}>
        <div className="d-flex flex-column align-items-center">
          <h6 className="fw-bolder text-primary">{value}</h6>
          <div className="d-flex flex-row">
            <p className="text-muted m-0">{label}</p>
            <span
              className="material-symbols-outlined cursor-pointer align-self-center ms-1"
              style={{
                fontSize: "16px"
              }}
              onClick={() => onInfoButtonClick(infoKey)}
            >
              info
            </span>
          </div>
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { metrics } = this.props;

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Metrics</h5>
            <div className="row m-0 py-4 asset-modal-card">
              {this._getMetricColumn("Market Cap", "marketCap", "", metrics.marketCap)}
              {this._getMetricColumn("Beta", "beta", "", metrics.beta)}
              {this._getMetricColumn("P/E ratio", "peRatio", "mt-5", metrics.peRatio)}
              {this._getMetricColumn("Forward P/E ratio", "forwardPE", "mt-5", metrics.forwardPE)}
              {this._getMetricColumn("EPS", "eps", "mt5", metrics.eps)}
              {this._getMetricColumn("Dividend yield", "stockDividendYield", "mt-5", metrics.dividendYield)}
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default AssetSideModalStockMetrics;
