import React, { Component } from "react";

type PropsType = {
  topHoldings: {
    name: string;
    weight: string;
    logoUrl: string;
  }[];
};

class AssetSideModalTopHoldingsBreakdown extends Component<PropsType> {
  render() {
    const { topHoldings } = this.props;

    return (
      <div className="row m-0 p-0 m-0 mt-5 mb-5">
        <div className="col p-0">
          <h5 className="fw-bolder mb-4">Top 10 Holdings</h5>
          <div className="row">
            {topHoldings.map((el, index) => (
              <div key={`region-${index}`} className="col-6 mb-3 d-flex">
                <div
                  className={"card card-body asset-card-md border-0 text-center justify-content-center m-0 me-2"}
                >
                  <div className="row justify-content-center">
                    <div className="col p-0 align-self-center">
                      <img
                        src={el.logoUrl}
                        className="w-100 align-self-center border-light asset-icon"
                        alt="provider logo"
                      />
                    </div>
                  </div>
                </div>
                <div className="d-flex flex-column p-1">
                  <h6 className="fw-bold m-0">{el.name}</h6>
                  <p className="text-primary m-0 mt-1">{el.weight}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

export default AssetSideModalTopHoldingsBreakdown;
