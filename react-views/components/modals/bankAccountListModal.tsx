import React, { Component } from "react";
import { LinkedBankAccount } from "../../types/bank";
import BankAccountRow from "../bankAccountRow";
import { EVENTS, eventEmitter } from "../../utils/eventService";
import { Modal } from "react-bootstrap";
import { emitToast } from "../../utils/eventService";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { captureException } from "@sentry/react";
import ConfirmationModal from "./confirmationModal";
import { OtherModalType } from "../modalsWrapper";

type PropsType = {
  handleClose: () => void;
  linkedBankAccounts: LinkedBankAccount[];
};

type StateType = {
  modalSlideAnimation: "in" | "out";
  selectedBankAccount?: LinkedBankAccount;
  visibleBankAccounts: LinkedBankAccount[];
  showBankAccountDeactivationModal: boolean;
  showBankAccountActiveMandatenModal: boolean;
};

export default class BankAccountListModal extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      modalSlideAnimation: "in",
      showBankAccountDeactivationModal: false,
      showBankAccountActiveMandatenModal: false,
      visibleBankAccounts: props.linkedBankAccounts
    };
  }

  private _handleClose() {
    this.setState({ modalSlideAnimation: "out" }, () => {
      setTimeout(() => this.props.handleClose(), 500);
    });
  }

  private _handleBankAccountDeletionClick(bankAccount: LinkedBankAccount) {
    this.setState({ selectedBankAccount: bankAccount, showBankAccountDeactivationModal: true });
  }

  private _deactivateBankAccount = async () => {
    const { selectedBankAccount } = this.state;

    if (selectedBankAccount?.mandate?.isActive) {
      this._setShowBankAccountActiveMandateModal(true);
      return;
    }

    try {
      await axios.post(`/investor/bank-accounts/${selectedBankAccount.id}/deactivate`);

      this.setState((prevState) => ({
        visibleBankAccounts: prevState.visibleBankAccounts.filter((bac) => bac.id !== selectedBankAccount.id)
      }));
    } catch (err) {
      captureException(err);
      emitToast({
        content: "We couldn't delete your bank account. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setShowBankAccountActiveMandateModal(showBankAccountActiveMandatenModal: boolean): void {
    this.setState({ showBankAccountActiveMandatenModal });
  }

  render() {
    const {
      modalSlideAnimation,
      showBankAccountDeactivationModal,
      visibleBankAccounts,
      showBankAccountActiveMandatenModal
    } = this.state;

    return (
      <>
        <div className="container-fluid p-0 m-0">
          <div
            className="row m-0 vh-100 justify-content-end fixed-top-higher etf-side-mask"
            style={{ overflowY: "scroll" }}
            onScroll={(e) => {
              e.stopPropagation();
            }}
          >
            <div
              className="col-md-7 col-sm-4 cursor-pointer d-none d-md-block"
              onClick={() => this._handleClose()}
            />
            <div
              className={`col-md-5 col-sm-8 p-md-5 p-3 bg-white position-relative slide-${modalSlideAnimation}`}
            >
              <div className="row m-0 p-xxl-5">
                {/* Back Button */}
                <div className="row p-0 m-0 mb-md-3 mb-2">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center"
                      onClick={() => this._handleClose()}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}

                {/* START - Layout children */}

                <h3 className="mb-4">My bank accounts</h3>
                {visibleBankAccounts.map((bankAccount, index) => {
                  return (
                    <div key={index} className={"mb-3"}>
                      <BankAccountRow
                        bankAccount={bankAccount}
                        handleMoreButtonClick={() => this._handleBankAccountDeletionClick(bankAccount)}
                        showMoreButton={visibleBankAccounts.length > 1}
                        displayNameClass={"fw-bold"}
                      />
                    </div>
                  );
                })}

                {/* START - Add bank account row */}
                <div
                  className="d-flex justify-content-between cursor-pointer  mb-3"
                  onClick={() =>
                    eventEmitter.emit(EVENTS.linkBankAccountModal, {
                      originModal: "bankAccountListModal" as OtherModalType
                    })
                  }
                >
                  <div className="d-flex align-items-center">
                    <span
                      className="material-icons border-light bg-light asset-icon me-3 p-2"
                      style={{
                        fontSize: "30px",
                        color: "#1C1B1F"
                      }}
                    >
                      account_balance
                    </span>
                    <span>Add new bank account</span>
                  </div>

                  <span
                    className="material-icons align-self-center align-self-center mb-2"
                    style={{
                      fontSize: "30px",
                      color: "#171717"
                    }}
                  >
                    chevron_right
                  </span>
                </div>
                {/* END - Add bank account row */}

                {/* END - Layout children */}
              </div>
            </div>
          </div>
        </div>

        {showBankAccountDeactivationModal && (
          <Modal
            show={showBankAccountDeactivationModal}
            onHide={() => {
              this.setState({ showBankAccountDeactivationModal: false });
            }}
            dialogClassName="p-md-5"
            // size={"lg"}
          >
            <Modal.Footer className="justify-content-center border-top-0 py-5">
              <button
                className="btn delete-btn text-nowrap d-flex align-items-center"
                onClick={() => {
                  this.setState({ showBankAccountDeactivationModal: false });
                  this._deactivateBankAccount();
                }}
              >
                <span
                  className="material-icons cursor-pointer"
                  style={{
                    fontSize: "15px",
                    color: "#D63E3E"
                  }}
                >
                  delete
                </span>
                {"Delete payment method"}
              </button>
            </Modal.Footer>
          </Modal>
        )}

        <ConfirmationModal
          title="Bank account with active mandate"
          description="You cannot delete a bank account that has previously been used for a repeating investment and is associated with an active mandate. If you still wish to remove it please contact us."
          handleConfirmation={async () => this._setShowBankAccountActiveMandateModal(false)}
          show={showBankAccountActiveMandatenModal}
          handleClose={() => this._setShowBankAccountActiveMandateModal(false)}
          yesButtonLabel="Got it!"
          onlyYesButton
        />
      </>
    );
  }
}
