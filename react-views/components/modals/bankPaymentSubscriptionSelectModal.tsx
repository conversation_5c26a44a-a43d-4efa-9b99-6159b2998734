import { LinkedBankAccount } from "../../types/bank";
import React from "react";
import InvestmentBankPaymentOptionListItem from "../investmentBankPaymentOptionListItem";
import { Modal } from "react-bootstrap";
import { UserDocument } from "../../../models/User";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { plansConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  user: UserDocument;
  linkedBankAccounts: LinkedBankAccount[];
  handleClose: () => void;
  show: boolean;
  selectedLinkedBankAccount: LinkedBankAccount;
  // optionals
  isNextButtonDisabled?: boolean;
  onBankAccountSelect?: (linkedBankAccount: LinkedBankAccount) => void;
  onSubmit?: () => Promise<void>;
  newPrice?: plansConfig.PriceType;
};

class BankPaymentSubscriptionSelectModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const {
      linkedBankAccounts,
      show,
      selectedLinkedBankAccount,
      isNextButtonDisabled,
      onBankAccountSelect,
      onSubmit,
      handleClose
    } = this.props;

    return (
      <Modal show={show} onHide={() => handleClose()} dialogClassName="p-md-5 max-w-600px">
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        <>
          <Modal.Body className="p-0 px-md-3 px-3 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="w-100 " style={{ maxWidth: "400px" }}>
                {linkedBankAccounts.map((linkedBankAccount) => (
                  <InvestmentBankPaymentOptionListItem
                    key={`bank-payment-${linkedBankAccount.id}`}
                    linkedBankAccount={linkedBankAccount}
                    isSelected={linkedBankAccount.id == selectedLinkedBankAccount?.id}
                    onChange={(): void => {
                      if (onBankAccountSelect) {
                        onBankAccountSelect(linkedBankAccount);
                      }
                    }}
                  />
                ))}
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="py-5 justify-content-center fade-in" style={{ borderTop: "none" }}>
            <div
              className="d-flex justify-content-center align-self-center mt-4 w-100"
              style={{ maxWidth: "400px" }}
            >
              <LoadingOnSubmitButton
                type="button"
                className="btn btn-primary fw-100"
                disabled={isNextButtonDisabled != null ? isNextButtonDisabled : false}
                enableOnCompletion={true}
                customonclick={async () => {
                  if (onSubmit) {
                    await onSubmit();
                  }
                }}
              >
                Next
              </LoadingOnSubmitButton>
            </div>
          </Modal.Footer>
        </>
      </Modal>
    );
  }
}

export default BankPaymentSubscriptionSelectModal;
