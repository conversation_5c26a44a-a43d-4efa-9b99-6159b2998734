import Markdown from "marked-react";
import React, { Component } from "react";
import { Modal } from "react-bootstrap";

export type BannerModalPropsType = {
  show: boolean;
  handleClose: () => void;

  title: string;
  imageSource: string;
  paragraphs: string[];
  buttonText: string;
  buttonAction: () => void;
};

export default class BannerModal extends Component<BannerModalPropsType> {
  render() {
    const { show, handleClose, title, imageSource, paragraphs, buttonAction, buttonText } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="max-w-400px">
        <Modal.Header className="border-bottom-0 pb-0 justify-content-end" closeButton></Modal.Header>
        <Modal.Body className="pt-0 pb-5 px-5">
          <div className="d-flex align-items-center flex-column w-100">
            {/* Action Title */}
            <h4 className="fw-bolder text-center mb-3" style={{ lineHeight: "120%" }}>
              {title}
            </h4>
            {/* End Action Title */}

            <img className="mt-2 mb-4" style={{ width: "110px", height: "110px" }} src={imageSource} />
            <div className="m-0 p-0">
              {paragraphs.map((paragraphText, index) => (
                <Markdown key={index} gfm breaks value={paragraphText} />
              ))}
            </div>

            <button
              className="btn btn-primary fw-100 mt-4 mb-2"
              onClick={() => {
                handleClose();
                buttonAction();
              }}
            >
              {buttonText}
            </button>
          </div>
        </Modal.Body>
      </Modal>
    );
  }
}
