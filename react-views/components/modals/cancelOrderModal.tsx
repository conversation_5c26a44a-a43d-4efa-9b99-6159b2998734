import React from "react";
import { Modal } from "react-bootstrap";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast } from "../../utils/eventService";
import { TransactionDocument } from "../../../models/Transaction";
import axios from "axios";

type PropsType = {
  transaction: TransactionDocument;
  show: boolean;
  handleClose: () => void;
};

class CancelOrderModal extends React.Component<PropsType> {
  private _cancelTransaction = async () => {
    const { transaction } = this.props;

    try {
      const res = await axios.post(`/transactions/${transaction.id}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  render(): JSX.Element {
    const { show, handleClose } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="px-md-10">
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        <Modal.Body className="py-0">
          <div className="row">
            {/* Action Title */}
            <h5 className="fw-bolder text-center">Cancel Order</h5>
            {/* End Action Title */}
            <div className="h-100 py-5 mt-4 mt-md-0">
              <p className="text-center">Are you sure you want to cancel your order?</p>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="pt-0 justify-content-center" style={{ borderTop: "none" }}>
          {/* Portfolio Order Submission Form */}
          <LoadingOnSubmitButton
            type="button"
            className="btn btn-danger w-100"
            customonclick={this._cancelTransaction}
          >
            Cancel
          </LoadingOnSubmitButton>
        </Modal.Footer>
      </Modal>
    );
  }
}

export default CancelOrderModal;
