import React from "react";
import { Modal } from "react-bootstrap";
import {
  DepositCashTransactionDocument,
  TransactionStatusType,
  WithdrawalCashTransactionDocument
} from "../../../models/Transaction";
import { formatCurrency } from "../../utils/currencyUtil";
import { formatDateToDDMONYYHHMM } from "../../utils/dateUtil";
import config from "../../configs/transactionsTableConfig";
import { BankAccountDocument } from "../../../models/BankAccount";
import { ProviderType } from "../../../services/truelayerService";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";

type PropsType = {
  transaction: WithdrawalCashTransactionDocument | DepositCashTransactionDocument;
  truelayerProviders: ProviderType[];
  show: boolean;
  handleClose: () => void;
};

const STATUS_CONFIG: Record<TransactionStatusType, { color: string; label: string }> = {
  Cancelled: { color: "danger", label: "Cancelled" },
  Pending: { color: "warning", label: "Pending" },
  PendingReinvestment: { color: "warning", label: "Pending" },
  PendingTopUp: { color: "warning", label: "Pending" },
  PendingDeposit: { color: "warning", label: "Pending" },
  PendingWealthkernelCharge: { color: "warning", label: "Pending" },
  PendingGift: { color: "warning", label: "Pending" },
  DepositFailed: { color: "danger", label: "DepositFailed" },
  Rejected: { color: "danger", label: "Rejected" },
  Settled: { color: "success", label: "Completed" }
};

class CashReceiptModal extends React.Component<PropsType> {
  private _getPrefix() {
    const { transaction } = this.props;
    const { category } = transaction;
    return category == "DepositCashTransaction" ? "From" : "To";
  }

  render(): JSX.Element {
    const { transaction, show, handleClose, truelayerProviders } = this.props;
    const { locale } = this.context as GlobalContextType;

    const { nameDisplay, icon } = config[transaction.category];

    const { displayStatus, displayAmount, createdAt, bankAccount } = transaction;
    const bankAccountDocument = bankAccount as BankAccountDocument;
    const truelayerProvider = truelayerProviders.find(
      ({ provider_id }) => bankAccountDocument?.truelayerProviderId == provider_id
    );
    const prefix = this._getPrefix();

    return (
      <Modal show={show} onHide={handleClose}>
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        <Modal.Body className="p-0 px-3">
          <div className="d-flex align-self-center justify-content-center fade-in">
            <div className="w-100" style={{ maxWidth: "400px" }}>
              {/* Action Title */}
              <div className="d-flex w-100 justify-content-center mb-3">
                <i
                  className="material-symbols-outlined align-self-center icon-primary wh-card"
                  style={{ fontSize: "32px" }}
                >
                  {icon}
                </i>
              </div>
              <h5 className="fw-bolder text-center mb-5">{nameDisplay}</h5>
              {/* End Action Title */}
              <div className="row pb-3 m-0 mb-3 border-bottom">
                <div className="col p-0 text-start">Amount</div>
                <div className="col p-0 text-end">
                  <span className="fw-bolder">
                    {formatCurrency(
                      Decimal.div(displayAmount, 100).toNumber(),
                      transaction.consideration.currency,
                      locale,
                      2,
                      2
                    )}
                  </span>
                </div>
              </div>
              {truelayerProvider && (
                <>
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start text-nowrap">{`${prefix} bank`}</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder">{truelayerProvider.display_name}</span>
                    </div>
                  </div>
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start text-nowrap">{`${prefix} account`}</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder text-nowrap">{bankAccountDocument.displayAccountIdentifier}</span>
                    </div>
                  </div>
                </>
              )}
              <div className="row pb-3 m-0 mb-3 border-bottom">
                <div className="col p-0 text-start text-nowrap">Created</div>
                <div className="col p-0 text-end">
                  <span className="fw-bolder text-nowrap">{formatDateToDDMONYYHHMM(new Date(createdAt))}</span>
                </div>
              </div>
              <div className="row pb-3 m-0 mb-5">
                <div className="col p-0 text-start text-nowrap">Status</div>
                <div className="col p-0 text-end">
                  <span className={`fw-bolder text-nowrap text-${STATUS_CONFIG[displayStatus].color}`}>
                    {STATUS_CONFIG[displayStatus].label}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    );
  }
}

CashReceiptModal.contextType = GlobalContext;

export default CashReceiptModal;
