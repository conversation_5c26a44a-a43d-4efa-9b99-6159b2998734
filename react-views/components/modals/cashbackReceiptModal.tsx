import React, { Component } from "react";
import { cashbacksConfig } from "@wealthyhood/shared-configs";
import { formatPercentage } from "../../utils/formatterUtil";
import { Modal } from "react-bootstrap";
import { CashbackTransactionDocument } from "../../../models/Transaction";
import ConfigUtil from "../../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { formatCurrency } from "../../utils/currencyUtil";

const { CASHBACK_RATES, MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;

type PropsType = {
  transaction: CashbackTransactionDocument;
  show: boolean;
  handleClose: () => void;
};

export default class CashbackReceiptModal extends Component<PropsType> {
  render() {
    const { transaction, show, handleClose } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const plan = PRICE_CONFIG[transaction.price].plan;
    const { name, keyName } = PLAN_CONFIG[plan];

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5 max-w-500px" size={"lg"}>
        <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton></Modal.Header>
        <Modal.Body className="d-flex flex-column align-items-center px-4 pt-0">
          <span
            className={"ms-1 cursor-pointer material-symbols-outlined text-primary p-3"}
            style={{ fontSize: "30px", borderRadius: "8px", backgroundColor: "#1013270D" }}
          >
            payments
          </span>
          <h4 className="fw-bolder pt-3">Cashback</h4>
          <p className="text-center text-muted pt-3">
            {`Thanks to your Wealthyhood ${name} subscription, you earn ${formatPercentage(
              CASHBACK_RATES[keyName],
              locale
            )} cash back every time you invest ${formatCurrency(
              MINIMUM_AMOUNT_FOR_CASHBACK,
              user.currency,
              locale
            )} or more, making every investment a bit more rewarding!`}
          </p>
        </Modal.Body>
      </Modal>
    );
  }
}

CashbackReceiptModal.contextType = GlobalContext;
