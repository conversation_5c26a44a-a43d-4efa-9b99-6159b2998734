import React from "react";
import { Mo<PERSON>, OverlayTrigger, Popover } from "react-bootstrap";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { EVENTS, emitToast, eventEmitter } from "../../utils/eventService";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { UserDocument } from "../../../models/User";
import LegalDocumentUtil from "../../utils/legalDocumentUtil";

type PropsType = {
  show: boolean;
  user: UserDocument;
  handleClose: () => void;
};

type StateType = {
  canClose: boolean;
  viewMode: CloseAccountViewModeType;
  deletionFeedback: string;
};

type CloseAccountViewModeType = "DELETION_FEEDBACK" | "FREE_TO_KEEP_ACCOUNT" | "CLOSE_ACCOUNT";

class CloseAccountModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      canClose: true,
      deletionFeedback: "",
      viewMode: "FREE_TO_KEEP_ACCOUNT"
    };
  }

  private _handleClose = () => {
    const { handleClose } = this.props;
    const { canClose } = this.state;

    if (canClose) {
      handleClose();
    } else {
      return;
    }
  };

  private _sendDeletionFeedback = async () => {
    try {
      await axios.post("/investor/deletion-feedback", { deletionFeedback: this.state.deletionFeedback });
      this.setState({ viewMode: "CLOSE_ACCOUNT" });
    } catch (err) {
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _closeAccount = async () => {
    eventEmitter.emit(EVENTS.loadingSplashMask, "Your account is being closed...");

    try {
      await axios.post("/investor/close-account");
      window.location.replace("/logout");
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _getModalConfig(): {
    content: JSX.Element;
    buttons: JSX.Element;
  } {
    const { user } = this.props;
    const { viewMode } = this.state;

    if (viewMode === "FREE_TO_KEEP_ACCOUNT") {
      return {
        content: (
          <div className="d-flex flex-column justify-content-center align-items-center">
            <img
              src={"/images/close-account/astronaut-piece-sign.png"}
              style={{ height: "240px", width: "240px" }}
              className="mb-4"
              alt=""
            />
            <h4 className="fw-bolder text-center mb-4">{"It's free to keep your Wealthyhood account!"}</h4>
            <p className="text-center text-muted">
              {
                "Taking a break from investing? No need to close your account. It's free to maintain it, so you can pick up where you left off whenever you're ready!"
              }
            </p>
          </div>
        ),
        buttons: (
          <div className="m-0 p-3 pb-4 w-100">
            <div className="row m-0 mb-4 w-100 text-center">
              <button className="btn btn-primary fw-100" onClick={this._handleClose}>
                {"Maintain my account"}
              </button>
            </div>
            <div className="row m-0 w-100 text-center">
              <div
                onClick={() => this.setState({ viewMode: "DELETION_FEEDBACK" })}
                className="text-primary cursor-pointer mb-0"
              >
                {"Close account"}
              </div>
            </div>
          </div>
        )
      };
    } else if (viewMode === "DELETION_FEEDBACK") {
      return {
        content: (
          <div className="d-flex flex-column justify-content-center align-items-center">
            <img
              src={"/images/close-account/broken-heart.png"}
              style={{ height: "145px", width: "140px" }}
              className="mb-5"
              alt=""
            />
            <h4 className="fw-bolder text-center mb-5">{"We're sorry to see you go. Help us understand why!"}</h4>
            <textarea
              className="verification-input h-auto"
              value={this.state.deletionFeedback}
              onChange={(event: any) => {
                this.setState({ deletionFeedback: event?.target.value });
              }}
              placeholder="What's the main reason for deciding to close your Wealthyhood account?"
              rows={5}
              cols={60}
              style={{ border: "none", backgroundColor: "#F4F4F4", borderRadius: "16px", resize: "none" }}
            ></textarea>
          </div>
        ),
        buttons: (
          <div className="row m-0 w-100 text-center px-1 pt-0 pb-3">
            <LoadingOnSubmitButton
              type="button"
              className={"btn btn-primary fw-100"}
              customonclick={this._sendDeletionFeedback}
              enableOnCompletion={true}
              disabled={!this.state.deletionFeedback}
            >
              {"Submit"}
            </LoadingOnSubmitButton>
          </div>
        )
      };
    } else if (viewMode === "CLOSE_ACCOUNT") {
      return {
        content: (
          <>
            <h4 className="fw-bolder text-center mb-5">{"Are you sure you want to close your account?"}</h4>
            <p className="text-muted">
              This action is irreversible. If you close your account, any assets you hold will be sold and the
              proceedings, along with any cash held in your Wealthyhood account, will be withdrawn to your bank
              account.
              <OverlayTrigger
                placement="bottom"
                overlay={
                  <Popover id="popover-explanation">
                    <Popover.Content>
                      The markets fluctuate, so the value of your assets to be sold may increase or decrease while
                      the trade is executed.
                    </Popover.Content>
                  </Popover>
                }
              >
                <span className={"ms-1 cursor-pointer material-symbols-outlined"} style={{ fontSize: "16px" }}>
                  info
                </span>
              </OverlayTrigger>
            </p>
            <p className="text-muted">
              Any rewards you hold for less than the minimum holding period will be lost.
            </p>
            <p className="text-muted">
              Read more in our
              <a
                className="ms-1 text-decoration-none text-primary fw-bold"
                href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
                target="_blank"
                rel="noopener noreferrer"
              >
                Terms & Conditions.
              </a>
            </p>
          </>
        ),
        buttons: (
          <div className="row m-0 w-100 pb-3 px-1">
            <div className="col p-0 me-2">
              <button
                className="btn btn-primary text-nowrap fw-100"
                onClick={() => this.setState({ viewMode: "DELETION_FEEDBACK" })}
              >
                {"Go back"}
              </button>
            </div>
            <div className="col p-0">
              <LoadingOnSubmitButton
                type="button"
                className={"btn btn-secondary text-nowrap fw-100"}
                customonclick={this._closeAccount}
                enableOnCompletion={true}
              >
                {"Close account"}
              </LoadingOnSubmitButton>
            </div>
          </div>
        )
      };
    }
  }

  render(): JSX.Element {
    const { show } = this.props;

    const { buttons, content } = this._getModalConfig();

    return (
      <Modal show={show} onHide={this._handleClose} dialogClassName="p-md-5 max-w-600px" size={"lg"}>
        <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton />
        <Modal.Body className="px-3 pt-0">{content}</Modal.Body>
        <Modal.Footer className="justify-content-center border-top-0">{buttons}</Modal.Footer>
      </Modal>
    );
  }
}

export default CloseAccountModal;
