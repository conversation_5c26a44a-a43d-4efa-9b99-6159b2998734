import React from "react";
import { Modal } from "react-bootstrap";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";

type PropsType = {
  title: string;
  description?: string;
  handleConfirmation: () => Promise<void>;
  show: boolean;
  handleClose: () => void;
  yesButtonLabel?: string;
  onlyYesButton?: boolean;
  noButtonLabel?: string;
  size?: "sm" | "lg" | "xl";
  revertButtons?: boolean;
  dialogClassName?: string;
};

type StateType = {
  canClose: boolean;
};

class ConfirmationModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      canClose: true
    };
  }

  private _handleClose = () => {
    const { handleClose } = this.props;
    const { canClose } = this.state;

    if (canClose) {
      handleClose();
    } else {
      return;
    }
  };

  render(): JSX.Element {
    const {
      title,
      description,
      handleConfirmation,
      show,
      children,
      yesButtonLabel,
      noButtonLabel,
      size,
      revertButtons,
      onlyYesButton,
      dialogClassName
    } = this.props;

    return (
      <Modal show={show} onHide={this._handleClose} dialogClassName={`p-md-5 ${dialogClassName}`} size={size}>
        <Modal.Header className="justify-content-end border-bottom-0" closeButton />
        <Modal.Body className="px-md-5 px-3">
          {/* Action Title */}
          <h5 className="fw-bolder text-start mb-4">{title}</h5>
          {/* End Action Title */}
          {description && (
            <div className="row pb-5">
              <div className="col-12 text-start">
                <p className="text-muted">{description}</p>
              </div>
            </div>
          )}
          {children}
        </Modal.Body>
        <Modal.Footer className="justify-content-center border-top-0 pt-3 pb-5">
          <div className="row m-0">
            {onlyYesButton ? (
              <div className="col p-0">
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary text-nowrap me-md-4 me-2"
                  customonclick={handleConfirmation}
                >
                  {yesButtonLabel ?? "Yes"}
                </LoadingOnSubmitButton>
              </div>
            ) : revertButtons ? (
              <>
                <div className="col p-0">
                  <button className="btn btn-primary text-nowrap me-md-4 me-2" onClick={this._handleClose}>
                    {noButtonLabel ?? "No"}
                  </button>
                </div>
                <div className="col p-0">
                  <LoadingOnSubmitButton
                    type="button"
                    className="btn btn-secondary text-nowrap"
                    customonclick={handleConfirmation}
                  >
                    {yesButtonLabel ?? "Yes"}
                  </LoadingOnSubmitButton>
                </div>
              </>
            ) : (
              <>
                <div className="col p-0">
                  <LoadingOnSubmitButton
                    type="button"
                    className="btn btn-primary text-nowrap me-md-4 me-2"
                    customonclick={handleConfirmation}
                  >
                    {yesButtonLabel ?? "Yes"}
                  </LoadingOnSubmitButton>
                </div>
                <div className="col p-0">
                  <button className="btn btn-secondary text-nowrap" onClick={this._handleClose}>
                    {noButtonLabel ?? "No"}
                  </button>
                </div>
              </>
            )}
          </div>
        </Modal.Footer>
      </Modal>
    );
  }
}

export default ConfirmationModal;
