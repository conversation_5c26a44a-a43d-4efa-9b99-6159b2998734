import React from "react";
import { Dropdown, DropdownButton } from "react-bootstrap";
import { getDateOrdinal, getMonthlyOnTheXthDateString, getOnTheXthDateString } from "../../utils/dateUtil";

type PropsType = {
  handleOnDayOfMonthChange: (dayOfMonth: number) => void;
  selectedDayOfMonth: number;
  includeFrequencyKeyword: boolean;
};

class DayOfMonthSelectionDropdown extends React.Component<PropsType> {
  render(): JSX.Element {
    const { selectedDayOfMonth, handleOnDayOfMonthChange, includeFrequencyKeyword } = this.props;

    return (
      <DropdownButton
        id="day-of-month-dropdown"
        title={
          includeFrequencyKeyword
            ? getMonthlyOnTheXthDateString({ capitalFirst: true }, selectedDayOfMonth)
            : getOnTheXthDateString({ capitalFirst: true }, selectedDayOfMonth)
        }
        className={"wh-card-amount text-primary ms-2 fw-light t-875"}
        bsPrefix={"dropdown-button"}
      >
        {[...Array(28).keys()]
          .map((n: number) => getDateOrdinal(n + 1))
          .concat("Last day")
          .map((item: string) => (
            <Dropdown.Item
              key={`dropdown-item-${item}`}
              id={item}
              onClick={() =>
                handleOnDayOfMonthChange(item === "Last day" ? -1 : Number.parseInt(item.replace(/\D/g, "")))
              }
            >
              {item}
            </Dropdown.Item>
          ))}
      </DropdownButton>
    );
  }
}

export default DayOfMonthSelectionDropdown;
