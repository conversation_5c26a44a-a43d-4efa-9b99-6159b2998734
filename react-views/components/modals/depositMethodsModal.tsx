import React from "react";
import { Modal } from "react-bootstrap";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { banksConfig } from "@wealthyhood/shared-configs";
import BankTransferInfoRow from "../bankTransferInfoRow";
import BankTransferDepositModalInfoItem from "../bankTransferDepositModalInfoItem";
import BankAccountRow from "../bankAccountRow";
import { BankProviderType, LinkedBankAccount } from "../../types/bank";
import { eventEmitter, EVENTS } from "../../utils/eventService";
import BanksUtil from "../../utils/banksUtil";
import DepositMethodRow from "../../components/depositMethodRow";
import { isAllowedOneStepInvest } from "../../utils/userUtil";
import { DepositMethodsType } from "../modalsWrapper";

const { EU_BANK_DETAILS } = banksConfig;

type ViewModeType = "OVERVIEW" | "EASY_BANK_SELECTION" | "REGULAR_BANK_TRANSFER" | "MORE_DETAILS";

type PropsType = {
  handleClose: () => void;
  show: boolean;
  walletIban: string;
  bankAccounts: LinkedBankAccount[];
  banks: BankProviderType[];
  type: DepositMethodsType;
};

type StateType = {
  viewMode: ViewModeType;
};

class DepositMethodsModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "OVERVIEW"
    };
  }

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _getTitle = () => {
    switch (this.props.type) {
      case "REGULAR_TOPUP":
        return "How to add money";
      case "NO_CASH_TOPUP":
        return "Please top up your account";
      case "EXTRA_CASH_TOPUP":
        return "How do you want to top up?";
    }
  };

  render(): JSX.Element {
    const { user } = this.context as GlobalContextType;
    const { handleClose, show, walletIban, bankAccounts, banks, type } = this.props;
    const { viewMode } = this.state;

    const easyTransferBankAccounts = bankAccounts.filter((bankAccount) => bankAccount.supportsEasyTransfer);

    return (
      <Modal
        show={show}
        onHide={() => {
          this._setViewMode("OVERVIEW");
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        {/* Overview */}
        {viewMode === "OVERVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  <h5 className="fw-bolder text-start mb-5 mt-3">{this._getTitle()}</h5>
                  <div className={"d-flex flex-column mb-4"}>
                    {isAllowedOneStepInvest(user) && easyTransferBankAccounts.length > 0 && (
                      <div>
                        <h6 className={"fw-bolder mb-3"}>Recent</h6>
                        {easyTransferBankAccounts.map((bankAccount, index) => (
                          <div
                            key={index}
                            className="d-flex justify-content-between cursor-pointer mb-3 deposit-options-item"
                          >
                            <div className="d-flex align-items-center">
                              <BankAccountRow
                                key={index}
                                bankAccount={bankAccount}
                                handleClick={() => {
                                  handleClose();
                                  eventEmitter.emit(
                                    EVENTS.depositModal,
                                    { bankAccountId: bankAccount.id },
                                    { showDepositMethodsOnDepositModalClose: true }
                                  );
                                }}
                                includeEasyTags={true}
                                showMoreButton={false}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    <div>
                      {type == "REGULAR_TOPUP" && <h6 className={"fw-bolder mb-3 mt-3"}>Recommended</h6>}
                      <DepositMethodRow
                        title="Regular bank transfer"
                        description="View account details to make a transfer from your bank"
                        iconSrc="/images/icons/topup-clock.svg"
                        iconAlt="topup-clock"
                        onClick={() => this._setViewMode("REGULAR_BANK_TRANSFER")}
                      />
                    </div>
                    <div>
                      {type == "REGULAR_TOPUP" && <h6 className={"fw-bolder mb-3 mt-3"}>Other</h6>}
                      <DepositMethodRow
                        title="Easy bank transfer"
                        description="Use a new bank account"
                        iconSrc="/images/icons/topup-bolt.svg"
                        iconAlt="topup-bolt"
                        onClick={() => this._setViewMode("EASY_BANK_SELECTION")}
                        disabled={!isAllowedOneStepInvest(user)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Overview */}

        {/* Regular Bank Transfer */}
        {viewMode === "REGULAR_BANK_TRANSFER" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Back Button */}
                  <div className="row p-0 m-0 mb-md-5 mb-3">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center"
                        onClick={() => this._setViewMode("OVERVIEW")}
                        style={{
                          fontSize: "24px"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                  {/* End Back Button*/}

                  <h5 className="fw-bolder text-start mb-3 mt-3">Regular bank transfer</h5>
                  <p className="text-muted mb-4">
                    Make a SEPA transfer to your Wealthyhood account. Use your bank’s mobile app or website.
                  </p>
                  <BankTransferInfoRow label={"Account name"} value={EU_BANK_DETAILS.accountName} />
                  <BankTransferInfoRow label={"IBAN"} value={walletIban} />
                  <BankTransferInfoRow label={"BIC/SWIFT"} value={EU_BANK_DETAILS.bank.bic} />
                  <button
                    className="btn btn-link text-center p-0 w-100 mt-4 mb-4"
                    onClick={() => this._setViewMode("MORE_DETAILS")}
                  >
                    View more account details
                  </button>
                  <div
                    className={"p-3 d-flex flex-column mt-2 mb-5"}
                    style={{ backgroundColor: "#F1F3FD", borderRadius: "18px" }}
                  >
                    <BankTransferDepositModalInfoItem
                      iconName={"location_away"}
                      content={
                        "This IBAN is unique to your account. It is only used for funding your account and is not the custody account where your cash is safeguarded."
                      }
                    />
                    <BankTransferDepositModalInfoItem
                      iconName={"person_check"}
                      content={
                        "Deposits are only accepted from bank accounts in the name of the Wealthyhood account holder."
                      }
                    />
                    <BankTransferDepositModalInfoItem
                      iconName={"assured_workload"}
                      content={"Only SEPA transfers from banks in the EU, EEA and the UK are accepted."}
                    />
                    <BankTransferDepositModalInfoItem
                      iconName={"event_available"}
                      content={
                        "Funds typically arrive instantly if your bank supports SEPA instant. Otherwise the funds will arrive within 1-2 business days."
                      }
                    />
                    <BankTransferDepositModalInfoItem
                      iconName={"universal_currency_alt"}
                      content={
                        "You may incur additional charges from your bank when sending a transfer. Wealthyhood does not charge for deposits or withdrawals."
                      }
                    />
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Regular Bank Transfer */}

        {/* Regular Bank Transfer */}
        {viewMode === "EASY_BANK_SELECTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Back Button */}
                  <div className="row p-0 m-0 mb-md-5 mb-3">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center"
                        onClick={() => this._setViewMode("OVERVIEW")}
                        style={{
                          fontSize: "24px"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                  {/* End Back Button*/}

                  <h5 className="fw-bolder text-start mb-3 mt-3">Choose your bank</h5>
                  <div className={"pb-4"}>
                    {banks.map((bank) => (
                      <div
                        key={bank.name}
                        className={"row m-0 wh-select-account-card-option mb-3"}
                        onClick={() => {
                          handleClose();
                          eventEmitter.emit(
                            EVENTS.depositModal,
                            { bankId: bank.id },
                            { showDepositMethodsOnDepositModalClose: true }
                          );
                        }}
                      >
                        <div className="col-2 p-0 d-flex justify-content-center align-self-center">
                          <img
                            className="h-100 align-self-center rounded"
                            style={{ maxHeight: "44px" }}
                            src={bank.logo}
                            alt={bank.name}
                          />
                        </div>
                        <div className="col-8 align-self-center">
                          <div className="d-flex flex-column">
                            <span className=" fw-bold">{bank.name}</span>
                          </div>
                        </div>
                        <div className="col-2 align-self-center">
                          <div className="d-flex flex-column">
                            <i
                              className="material-symbols-outlined align-self-center w-100 text-end fw-bolder pe-2"
                              style={{ fontSize: "22px", color: "#536AE3" }}
                            >
                              arrow_forward_ios
                            </i>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Regular Bank Transfer */}

        {/* More Details */}
        {viewMode === "MORE_DETAILS" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center mb-5">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {/* Back Button */}
                  <div className="row p-0 m-0 mb-md-5 mb-3">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center"
                        onClick={() => this._setViewMode("REGULAR_BANK_TRANSFER")}
                        style={{
                          fontSize: "24px"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                  {/* End Back Button*/}
                  <h5 className="fw-bolder text-start mb-5 mt-3">Account details</h5>
                  <BankTransferInfoRow label={"Account name"} value={EU_BANK_DETAILS.accountName} />
                  <BankTransferInfoRow
                    label={"Beneficiary address"}
                    value={EU_BANK_DETAILS.beneficiaryFullAddress}
                  />
                  <BankTransferInfoRow label={"IBAN"} value={walletIban} />
                  <BankTransferInfoRow label={"BIC/SWIFT"} value={EU_BANK_DETAILS.bank.bic} />
                  <BankTransferInfoRow label={"Bank name"} value={EU_BANK_DETAILS.bank.name} />
                  <BankTransferInfoRow
                    label={"Bank address"}
                    value={BanksUtil.getEuropeanBankFullAddress()}
                    includeBottomSeparator={false}
                  />
                  {/* End More Details Content */}
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End More Details */}
      </Modal>
    );
  }
}

DepositMethodsModal.contextType = GlobalContext;

export default DepositMethodsModal;
