import axios from "axios";
import React from "react";
import { Modal } from "react-bootstrap";
import { currenciesConfig, depositsConfig, localeConfig } from "@wealthyhood/shared-configs";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import OrderInput from "../orderInput";
import { BankProviderType, LinkedBankAccount } from "../../types/bank";
import { ProviderType } from "../../../services/truelayerService";
import { ViewModeType } from "../../types/modal";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { formatCurrency } from "../../utils/currencyUtil";
import { OtherModalType } from "../modalsWrapper";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import AddBankAccountButton from "../addBankAccountButton";

const { CURRENCY_SYMBOLS } = currenciesConfig;

type PaymentStatusType = "allowed" | "noPayment" | "forbiddenAmount";
type ModalStatusType = PaymentStatusType | "emptyBank";
type PropsType = {
  handleClose: () => void;
  show: boolean;
  linkedBankAccounts: LinkedBankAccount[];
  initialSelectedLinkedBankAccount: LinkedBankAccount;
  initialSelectedBank: BankProviderType;
};
type StateType = {
  depositAmount: string;
  bankDetails: {
    accountHolderName: string;
    bank: ProviderType;
    accountNumber: string;
    sortCode: string;
  };
  selectedLinkedBankAccount: LinkedBankAccount;
  viewMode: ViewModeType;
  errorMsg: string;
  actionButtonClicked: boolean;
};

const ACCOUNT_NUMBER_LENGTH = 8;
const SORT_CODE_LENGTH = 6;

const WARNING_MESSAGES: Record<
  PaymentStatusType,
  (userCurrency: currenciesConfig.MainCurrencyType, locale: localeConfig.LocaleType) => string
> = {
  allowed: () => "",
  noPayment: () => "",
  forbiddenAmount: (userCurrency, locale) =>
    `You can't deposit less than ${formatCurrency(
      depositsConfig.MIN_ALLOWED_DEPOSIT,
      userCurrency,
      locale,
      0,
      0
    )}.`
};

const PRE_DEFINED_AMOUNTS = [100, 250, 500, 1000];

class DepositModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      depositAmount: "",
      bankDetails: {
        accountHolderName: "",
        bank: null,
        accountNumber: "",
        sortCode: ""
      },
      selectedLinkedBankAccount: this._getInitialSelectedLinkedBankAccount(),
      viewMode: "ACTION",
      errorMsg: "",
      actionButtonClicked: false
    };
  }

  private static _currencyStrToNum(currencyStr: string): number {
    return Number(currencyStr.replace(/[^0-9.-]+/g, ""));
  }

  private _getInitialSelectedLinkedBankAccount(): LinkedBankAccount {
    const { initialSelectedBank, initialSelectedLinkedBankAccount, linkedBankAccounts } = this.props;

    if (initialSelectedLinkedBankAccount) {
      return initialSelectedLinkedBankAccount;
    } else if (!initialSelectedBank) {
      return linkedBankAccounts?.[0];
    } else return null;
  }

  private _onAmountChange = (depositAmount: string): void => {
    this.setState({ depositAmount });
  };

  private _getModalStatus(): ModalStatusType {
    const { initialSelectedBank } = this.props;
    const { bankDetails, selectedLinkedBankAccount } = this.state;

    if (selectedLinkedBankAccount || initialSelectedBank) {
      return this._getPaymentStatus();
    } else {
      return bankDetails.bank &&
        this._filledWithValidLength(bankDetails.sortCode, SORT_CODE_LENGTH) &&
        this._filledWithValidLength(bankDetails.accountNumber, ACCOUNT_NUMBER_LENGTH) &&
        bankDetails.accountHolderName
        ? this._getPaymentStatus()
        : "emptyBank";
    }
  }

  private _getPaymentStatus(): PaymentStatusType {
    const { depositAmount } = this.state;
    const amount = DepositModal._currencyStrToNum(depositAmount);
    if (isNaN(amount) || amount === 0) {
      return "noPayment";
    } else if (amount >= depositsConfig.MIN_ALLOWED_DEPOSIT) {
      return "allowed";
    } else {
      return "forbiddenAmount";
    }
  }

  private _submitPayment = async () => {
    const { initialSelectedBank } = this.props;
    const { depositAmount, bankDetails, selectedLinkedBankAccount } = this.state;

    if (!this._isPaymentAllowed()) {
      return;
    }

    this.setState({ errorMsg: "" });
    eventEmitter.emit(EVENTS.loadingSplashMask, "Your deposit is being processed");

    let body;
    if (selectedLinkedBankAccount) {
      body = {
        paymentAmount: DepositModal._currencyStrToNum(depositAmount).toString(),
        bankAccountId: selectedLinkedBankAccount.id
      };
    } else if (initialSelectedBank) {
      body = {
        paymentAmount: DepositModal._currencyStrToNum(depositAmount).toString(),
        bankId: initialSelectedBank.id
      };
    } else {
      body = {
        paymentAmount: DepositModal._currencyStrToNum(depositAmount).toString(),
        bankAccount: {
          name: bankDetails.accountHolderName,
          truelayerProviderId: bankDetails.bank.provider_id,
          number: bankDetails.accountNumber,
          sortCode: this._addDashesToSortCode(bankDetails.sortCode)
        },
        bankLinkedFrom: "modal"
      };
    }

    try {
      const res = await axios.post("/investor/payments", body);
      window.location.replace(res.data.redirectUri);
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      this.setState({ errorMsg: err.response.data.message });

      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _isPaymentAllowed(): boolean {
    return this._getModalStatus() === "allowed";
  }

  private _filledWithValidLength = (input: string, length: number): boolean => {
    return input && input.length == length;
  };

  private _addDashesToSortCode = (sortCode: string): string => {
    if (sortCode.length !== SORT_CODE_LENGTH) {
      throw new Error(`Sort code should have length ${SORT_CODE_LENGTH}`);
    }

    return sortCode.match(/.{1,2}/g).join("-");
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setSelectedLinkedBankAccount = (selectedLinkedBankAccount: LinkedBankAccount) => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _getActionViewModeButton(): JSX.Element {
    const { initialSelectedBank } = this.props;
    const { selectedLinkedBankAccount, depositAmount } = this.state;

    if ((selectedLinkedBankAccount || initialSelectedBank) && depositAmount) {
      return (
        <button
          type="button"
          className="btn btn-primary fw-100"
          onClick={() => {
            this._setActionButtonClicked(true);

            if (this._isPaymentAllowed()) {
              this._setViewMode("REVIEW");
            }
          }}
        >
          Review
        </button>
      );
    }

    if ((selectedLinkedBankAccount || initialSelectedBank) && !depositAmount) {
      return (
        <button type="button" className="btn btn-primary fw-100" disabled={true}>
          Review
        </button>
      );
    }

    return (
      <button
        type="submit"
        className="btn btn-primary fw-100"
        onClick={() =>
          eventEmitter.emit(EVENTS.linkBankAccountModal, {
            originModal: "deposit" as OtherModalType
          })
        }
      >
        Proceed
      </button>
    );
  }

  componentDidUpdate(prevProps: Readonly<PropsType>) {
    if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(this.props.initialSelectedLinkedBankAccount);
    } else if (prevProps.linkedBankAccounts?.length != this.props.linkedBankAccounts?.length) {
      this._setSelectedLinkedBankAccount(this.props.linkedBankAccounts?.[0]);
    } else if (prevProps.initialSelectedBank !== this.props.initialSelectedBank) {
      this._setSelectedLinkedBankAccount(null);
    }
  }

  render(): JSX.Element {
    const { handleClose, show, linkedBankAccounts, initialSelectedBank } = this.props;
    const { depositAmount, selectedLinkedBankAccount, viewMode, errorMsg, actionButtonClicked } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    return (
      <Modal
        show={show}
        onHide={() => {
          this._onAmountChange("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0">
            <TransactionModalBackButton
              onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
              title={"Add money"}
            />
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode == "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-3">
                    <OrderInput
                      amount={depositAmount}
                      placeholderAmount={0}
                      onAmountChange={(amount) => {
                        const { depositAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != depositAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._onAmountChange(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                    />
                  </div>
                  <div className="d-flex justify-content-center mb-5">
                    {PRE_DEFINED_AMOUNTS.map((amount) => (
                      <div
                        key={`amount-${amount}`}
                        className="wh-card-amount text-primary me-2"
                        onClick={() => this._onAmountChange(amount.toString())}
                      >
                        {formatCurrency(amount, user.currency, locale, 0, 0)}
                      </div>
                    ))}
                  </div>
                  {actionButtonClicked && (
                    <p className="text-center text-danger mb-4">
                      {WARNING_MESSAGES[this._getPaymentStatus()](user.currency, locale)}
                    </p>
                  )}
                  {/* End Order Input */}

                  {initialSelectedBank ? (
                    <SelectedPaymentMethod
                      selectedBank={initialSelectedBank}
                      paymentMethod={"EASY_BANK_TOP_UP"}
                      availableCash={null}
                      frequency={"ONE_TIME"}
                    />
                  ) : selectedLinkedBankAccount ? (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      availableCash={null}
                      frequency={"ONE_TIME"}
                    />
                  ) : (
                    <></>
                  )}

                  {errorMsg && <div className="text-danger px-5">{errorMsg}</div>}
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._getActionViewModeButton()}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Action Modal Content */}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  <div className="row pb-3 mb-3 border-bottom">
                    <div className="col text-start">Amount</div>
                    <div className="col text-end">
                      <span className="fw-bolder">
                        {formatCurrency(Number(depositAmount ?? 0), user.currency, locale, 2, 2)}
                      </span>
                    </div>
                  </div>
                  {/* FIXME: This row should be updated with dynamic values*/}
                  <div className="row pb-3 mb-3">
                    <div className="col text-start">Arriving</div>
                    <div className="col text-end">
                      <span className="fw-bolder">Instantly</span>
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton
                  className="btn btn-primary fw-100"
                  enableOnCompletion={true}
                  customonclick={this._submitPayment}
                >
                  Confirm deposit
                </LoadingOnSubmitButton>
                <p className="text-muted mt-4 mb-0">
                  By continuing you are permitting TrueLayer to initiate a payment from your bank account. You also
                  agree to{" "}
                  <a
                    className="text-decoration-none"
                    href="https://truelayer.com/legal/enduser_tos"
                    target="_blank"
                    rel="noreferrer"
                  >
                    End User Terms of Service
                  </a>{" "}
                  and{" "}
                  <a
                    className="text-decoration-none"
                    href="https://truelayer.com/legal/privacy"
                    target="_blank"
                    rel="noreferrer"
                  >
                    Privacy Policy
                  </a>
                  .
                </p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    frequency={"ONE_TIME"}
                    paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) =>
                      this._setSelectedLinkedBankAccount(selectedLinkedBankAccount)
                    }
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <AddBankAccountButton originModal="deposit" />
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* EndBank Select Modal Content */}
      </Modal>
    );
  }
}

DepositModal.contextType = GlobalContext;

export default DepositModal;
