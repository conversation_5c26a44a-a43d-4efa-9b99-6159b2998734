import React from "react";
import { Modal } from "react-bootstrap";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";

type PropsType = {
  handleConfirmation: () => void;
  handleClose: () => void;
  show: boolean;
  isWithinBuilderFlow: boolean;
};

class DiscardChangesModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { handleConfirmation, handleClose, show, isWithinBuilderFlow } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5">
        <Modal.Body className="px-4">
          <div className="row mb-5">
            <div className="d-flex align-self-center">
              <i className="material-symbols-outlined align-self-center wh-card me-2 text-danger bg-light-danger">
                warning
              </i>
              <span className="fw-bolder align-self-center">Your progress will be lost</span>
              <div className="d-flex align-self-center justify-content-center ms-auto">
                <span
                  className="material-symbols-outlined align-self-center me-2 cursor-pointer"
                  onClick={handleClose}
                >
                  close
                </span>
              </div>
            </div>
          </div>
          <div className="row mb-4">
            <div className="col-12 text-start">
              {isWithinBuilderFlow ? (
                <p>
                  By going back to personalisation preferences, any assets you’ve added or removed will be lost.
                  Are you sure you want to discard changes and go back?
                </p>
              ) : (
                <p>
                  By going, any assets you’ve added or removed will be lost. Are you sure you want to discard
                  changes and go back?
                </p>
              )}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="px-4 justify-content-center border-top-0">
          <div className="d-flex align-self-center">
            <button className="btn btn-secondary me-4" onClick={handleClose}>
              Cancel
            </button>
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-danger text-truncate"
              customonclick={async () => {
                handleConfirmation();
                handleClose();
              }}
            >
              Discard changes
            </LoadingOnSubmitButton>
          </div>
        </Modal.Footer>
      </Modal>
    );
  }
}

export default DiscardChangesModal;
