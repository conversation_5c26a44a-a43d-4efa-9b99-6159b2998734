import React, { Component } from "react";
import { Modal } from "react-bootstrap";
import OnboardingEditableUniverse from "../onboardingEditableUniverse";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  show: boolean;
  handleClose: () => void;

  // OnboardingEditableUniverse props:
  selectedUniverse: Set<investmentUniverseConfig.AssetType>;
  onAssetClick: (assetKey: investmentUniverseConfig.AssetType) => void;
  onAssetSelection: (assetKey: investmentUniverseConfig.AssetType) => void;
};

export default class DiscoverAssetsModal extends Component<PropsType> {
  render(): JSX.Element {
    const { selectedUniverse, onAssetClick, onAssetSelection, show, handleClose } = this.props;
    return (
      <Modal show={show} onHide={() => handleClose()} size="lg" className="white-background-modal">
        <Modal.Header className="p-md-5 p-3 pb-md-0 pb-0 border-bottom-0 ">
          <div className="row justify-content-end text-end w-100">
            <div className="col p-0">
              <i className="fas fa-times cursor-pointer" onClick={() => handleClose()} />
            </div>
          </div>
        </Modal.Header>
        <Modal.Body className="p-0 px-md-3 px-3 fade-in">
          <div className="row m-0 align-self-center justify-content-center">
            <div className="col p-0">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "450px" }}>
                  <OnboardingEditableUniverse
                    selectedUniverse={selectedUniverse}
                    onAssetClick={onAssetClick}
                    onAssetSelection={onAssetSelection}
                  />
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    );
  }
}
