import { formatDateToDDMONYY, formatDateToHHMMSS } from "../../utils/dateUtil";
import { formatCurrency } from "../../utils/currencyUtil";
import React from "react";
import { DividendTransactionDocument } from "../../../models/Transaction";
import { Modal } from "react-bootstrap";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AssetIcon from "../assetIcon";
import { getAssetIconUrl } from "../../utils/universeUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { getReportingFirm } from "../../utils/userUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  transaction: DividendTransactionDocument;
  show: boolean;
  handleClose: () => void;
};

class DividendReceiptModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { transaction, show, handleClose } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const { asset, displayAmount, createdAt } = transaction;
    const assetConfig = ASSET_CONFIG[asset];
    const { simpleName, category, tickerWithCurrency, isin } = assetConfig;

    const transactionName = `${tickerWithCurrency} · ${simpleName}`;

    return (
      <Modal show={show} onHide={handleClose}>
        <div className="modal-content bg-dividend-receipt">
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          <Modal.Body className="p-0 px-3 mb-5 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="w-100" style={{ maxWidth: "420px" }}>
                {/* Action Title */}
                <div className="d-flex w-100 justify-content-start mb-5">
                  <AssetIcon category={category} iconUrl={getAssetIconUrl(asset)} size="md" />
                  <div className="d-flex flex-column">
                    <h6 className="text-primary success fw-bolder mt-1 ms-3">Dividend</h6>
                    <h5 className="fw-bolder text-center ms-3">{transactionName}</h5>
                  </div>
                </div>
                {/* End Action Title */}
                <div className="row m-0 mb-2">
                  <div className="col-6 p-0 text-start fw-bold">{formatDateToDDMONYY(new Date(createdAt))}</div>
                  <div className="col-6 p-0 text-muted text-end">{formatDateToHHMMSS(new Date(createdAt))}</div>
                </div>
                <div className="row m-0 mb-4 wh-receipt-card">
                  <div className="col p-0 align-self-center">
                    <div className="d-flex flex-column text-center">
                      <div className="fw-bold">
                        {formatCurrency(displayAmount / 100, transaction.consideration.currency, locale, 2, 2)}
                      </div>
                      <div className="text-muted fw-bold">Amount</div>
                    </div>
                  </div>
                </div>
                <div className="row m-0 mb-4">
                  <div className="col p-0">
                    <div className="receipt-separator-primary" />
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">Client</div>
                  <div className="col text-end">
                    <span className="fw-bolder">
                      {user.firstName} {user.lastName}
                    </span>
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">ISIN</div>
                  <div className="col text-end">
                    <span className="fw-bolder">{isin}</span>
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">Reporting firm</div>
                  <div className="col text-end">
                    <span className="fw-bolder">{getReportingFirm(user)}</span>
                  </div>
                </div>
                <div className="d-flex justify-content-center">
                  <img
                    className="wh-header-logo"
                    src="/images/icons/wealthyhood-logo-dark.svg"
                    style={{ height: "28px" }}
                  />
                </div>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

DividendReceiptModal.contextType = GlobalContext;

export default DividendReceiptModal;
