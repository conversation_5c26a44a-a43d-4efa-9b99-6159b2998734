import { TransactionDocument } from "../../../models/Transaction";
import React from "react";
import { Modal, ModalBody } from "react-bootstrap";
import InvestorTransactionRow from "../investorTransactionRow";
import { ProviderType } from "../../../services/truelayerService";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
type PropsType = {
  title: string;
  transactions: TransactionDocument[];
  show: boolean;
  handleClose: () => void;
  truelayerProviders: ProviderType[];
  investmentProducts: InvestmentProductDocument[];
  onTransactionSelected: (transaction: TransactionDocument) => void;
};

class IncomingCashFlowModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const {
      show,
      handleClose,
      title,
      transactions,
      truelayerProviders,
      investmentProducts,
      onTransactionSelected
    } = this.props;
    return (
      <Modal onHide={handleClose} dialogClassName="p-md-5" size={"lg"} show={show}>
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        {/* Action Title */}
        <h5 className="px-md-5 px-3 font-weight-bolder">{title}</h5>
        <ModalBody className={"mb-5"}>
          <div className="row m-0 px-md-5 px-2">
            <div className="col p-0">
              {transactions.map((transaction, index) => {
                return (
                  <InvestorTransactionRow
                    onClick={() => onTransactionSelected(transaction)}
                    transaction={transaction}
                    truelayerProviders={truelayerProviders}
                    investmentProducts={investmentProducts}
                    key={`cashflow_transaction_${index}`}
                  />
                );
              })}
            </div>
          </div>
        </ModalBody>
      </Modal>
    );
  }
}

export default IncomingCashFlowModal;
