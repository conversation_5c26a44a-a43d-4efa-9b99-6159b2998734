import React from "react";
import { Modal } from "react-bootstrap";

type PropsType = {
  title: string;
  show: boolean;
  handleClose: () => void;
  dialogClassName?: string;
};

class InfoModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { show, title, children, handleClose, dialogClassName } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName={`p-md-5 ${dialogClassName}`} size={"lg"}>
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        {/* Action Title */}
        <h5 className="px-md-5 px-3 font-weight-bolder">{title}</h5>
        {/* End Action Title */}
        <Modal.Body className="px-md-5 px-3">
          <div className="row pb-5">{children}</div>
        </Modal.Body>
        {/*<Modal.Footer className="justify-content-center border-top-0"></Modal.Footer>*/}
      </Modal>
    );
  }
}

export default InfoModal;
