import React from "react";
import { Modal } from "react-bootstrap";

type PropsType = {
  handleClose: () => void;
  allocation: number;
  show: boolean;
};

class InvalidAllocationModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { handleClose, allocation, show } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5">
        <Modal.Header className="px-4 border-bottom-0 " closeButton>
          <Modal.Title />
        </Modal.Header>
        <Modal.Body className="px-4">
          <div className="row mb-5">
            <div className="d-flex align-self-center">
              <i className="material-symbols-outlined align-self-center wh-card me-2 text-danger bg-light-danger">
                running_with_errors
              </i>
              <span className="fw-bolder align-self-center">Your total allocation is {allocation}%</span>
            </div>
          </div>
          <div className="row mb-4">
            <div className="col-12 text-start">
              <p>
                Your portfolio allocation should be exactly 100% to proceed. Please adjust your portfolio weights.
              </p>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="px-4 justify-content-center border-top-0" />
      </Modal>
    );
  }
}

export default InvalidAllocationModal;
