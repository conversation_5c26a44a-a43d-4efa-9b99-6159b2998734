import React from "react";
import { Modal } from "react-bootstrap";
import { AssetTransactionDocument } from "../../../models/Transaction";
import config from "../../configs/transactionsTableConfig";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { emitToast } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
import { UserDocument } from "../../../models/User";
import { OrderSideType } from "../../../services/wealthkernelService";
import InvestorOrderRow from "../investorOrderRow";
import OrderReceipt from "../orderReceipt";
import { OrderDocument } from "../../../models/Order";
import { AssetPriceInfo } from "../../types/price";

type PropsType = {
  user: UserDocument;
  transaction: AssetTransactionDocument;
  show: boolean;
  investmentProducts: InvestmentProductDocument[];
  handleClose: () => void;
};

type StateType = {
  viewMode: "TRANSACTION" | "ORDER";
  selectedOrder: OrderDocument;
};

const { ASSET_CONFIG } = investmentUniverseConfig;

class InvestmentReceiptModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);

    const assetId = this._getSingleOrderAssetId();
    const isSingleOrder = !!assetId;

    if (isSingleOrder) {
      const order = this.props.transaction.orders[0];
      this.state = {
        viewMode: "ORDER",
        selectedOrder: order
      };
    } else
      this.state = {
        viewMode: "TRANSACTION",
        selectedOrder: null
      };
  }

  private _getTransactionName = (): string => {
    const { transaction } = this.props;
    const { category } = transaction;
    const transactionConfig = config[category];
    let transactionName = transactionConfig.nameDisplay;
    const { portfolioTransactionCategory, orders } = transaction;

    if (orders.length == 1 && portfolioTransactionCategory == "update") {
      const order = orders[0];
      transactionName = Object.values(ASSET_CONFIG).find((config) => config.isin == order.isin).simpleName;
      if (transaction.displayStatus != "Settled") {
        transactionName = `${order.side} ${transactionName}`;
      }
    } else {
      transactionName = `${transactionName.toUpperCase().substr(0, 1)}${transactionName
        .toLowerCase()
        .substr(1)} ${portfolioTransactionCategory}`;
    }

    return transactionName;
  };

  /**
   * Returns asset id of single order of an asset transaction, if transaction
   * is an asset transaction with a single order. Otherwise, it returns null.
   *
   * @private
   */
  private _getSingleOrderAssetId(): investmentUniverseConfig.AssetType | null {
    const { transaction } = this.props;

    if (transaction.category == "AssetTransaction") {
      const assetTransaction = transaction as AssetTransactionDocument;
      const isSingleOrder =
        assetTransaction.portfolioTransactionCategory == "update" && assetTransaction.orders.length == 1;
      if (isSingleOrder) {
        const isin = assetTransaction.orders[0].isin;
        return Object.keys(ASSET_CONFIG).find(
          (key: investmentUniverseConfig.AssetType) => ASSET_CONFIG[key].isin == isin
        ) as investmentUniverseConfig.AssetType;
      }
    }

    return null;
  }

  private _getAssetPriceInfoForIsin(targetIsin: string): AssetPriceInfo {
    const { investmentProducts } = this.props;
    const investmentProduct = investmentProducts.find(({ commonId }) => ASSET_CONFIG[commonId].isin == targetIsin);
    return {
      tradedPrice: investmentProduct?.tradedPrice,
      tradedCurrency: investmentProduct?.tradedCurrency,
      currentTickerPrice: investmentProduct?.currentTicker?.price
    };
  }

  private _cancelTransaction = async () => {
    const { transaction } = this.props;

    try {
      const res = await axios.post(`/transactions/${transaction.id}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  private _cancelOrder = async () => {
    const { selectedOrder } = this.state;

    try {
      const res = await axios.post(`/orders/${selectedOrder.id}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  private static _getBackgroundClass(
    assetId: investmentUniverseConfig.AssetType | null,
    side: OrderSideType
  ): string {
    if (assetId && side == "Buy") {
      return "bg-order-buy-receipt";
    } else if (assetId && side == "Sell") {
      return "bg-order-sell-receipt";
    } else {
      return "";
    }
  }

  private _setViewMode(viewMode: "TRANSACTION" | "ORDER") {
    this.setState({ viewMode });
  }

  private _setSelectedOrder(selectedOrder: OrderDocument) {
    this.setState({ selectedOrder });
  }

  render(): JSX.Element {
    const { transaction, show, handleClose } = this.props;
    const { viewMode, selectedOrder } = this.state;

    const { displayStatus, orders, portfolioTransactionCategory } = transaction;

    const isSettled = displayStatus == "Settled";
    const isCancellable = transaction.isCancellable;

    const assetId = this._getSingleOrderAssetId();
    const isSingleOrder = !!assetId;

    // this field will be filled and used only
    // for single etf orders
    let side: OrderSideType;

    // if is a single etf order
    if (isSingleOrder) {
      const order = transaction.orders[0];
      side = order.side;
    } else if (portfolioTransactionCategory == "buy") {
      side = "Buy";
    } else if (portfolioTransactionCategory == "sell") {
      side = "Sell";
    }

    return (
      <Modal show={show} onHide={handleClose}>
        <div
          className={`modal-content ${
            isSettled
              ? InvestmentReceiptModal._getBackgroundClass(
                  selectedOrder?.commonId as investmentUniverseConfig.AssetType,
                  selectedOrder?.side
                )
              : ""
          }`}
        >
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          <Modal.Body className="p-0 px-3 mb-5">
            {viewMode == "TRANSACTION" && (
              <div className="d-flex align-self-center justify-content-center fade-in">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {/* Action Title */}
                  <h5 className="fw-bolder text-center mb-5">{this._getTransactionName()}</h5>
                  {/* End Action Title */}
                  {orders.map((order, index) => (
                    <InvestorOrderRow
                      onClick={() => {
                        this._setSelectedOrder(order);
                        this._setViewMode("ORDER");
                      }}
                      currentPriceInfo={this._getAssetPriceInfoForIsin(order.isin)}
                      parentTransaction={transaction}
                      order={order}
                      key={`order_${index}`}
                    />
                  ))}
                </div>
              </div>
            )}
            {viewMode == "ORDER" && selectedOrder && (
              <div className="d-flex align-self-center justify-content-center fade-in">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {/* Back Button */}
                  {!isSingleOrder && (
                    <div className="row p-0 m-0 mb-3">
                      <div className="col p-0">
                        <span
                          className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                          onClick={() => this._setViewMode("TRANSACTION")}
                          style={{
                            fontSize: "24px"
                          }}
                        >
                          arrow_back
                        </span>
                      </div>
                    </div>
                  )}
                  {/* End Back Button*/}
                  <OrderReceipt
                    order={selectedOrder}
                    price={
                      selectedOrder?.isMatched
                        ? selectedOrder?.displayUnitPrice
                        : this._getAssetPriceInfoForIsin(selectedOrder.isin)
                    }
                    executionWindow={
                      selectedOrder.assetCategory === "etf"
                        ? transaction.executionWindow?.etfs
                        : transaction.executionWindow?.stocks
                    }
                    isPartOfPortfolioTransaction={true}
                  />
                </div>
              </div>
            )}
          </Modal.Body>
          {isCancellable && viewMode === "TRANSACTION" && (
            <Modal.Footer className="pt-0 pb-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-danger fw-100" customonclick={this._cancelTransaction}>
                  Cancel open orders
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          )}
          {selectedOrder && selectedOrder.isCancellable && viewMode === "ORDER" && (
            <Modal.Footer className="pt-0 pb-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-danger fw-100" customonclick={this._cancelOrder}>
                  Cancel Order
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          )}
        </div>
      </Modal>
    );
  }
}

export default InvestmentReceiptModal;
