import React from "react";
import { Modal } from "react-bootstrap";
import axios from "axios";
import { OtherModalType } from "../../modalsWrapper";
import { investmentUniverseConfig, plansConfig } from "@wealthyhood/shared-configs";
import { toQueryParams } from "../../../utils/stringUtil";
import { BankProviderType } from "../../../types/bank";
import { emitToast } from "../../../utils/eventService";
import { ToastTypeEnum } from "../../../configs/toastConfig";

type PropsType = {
  handleClose: () => void;
  originModal?: OtherModalType;
  displayedAsset?: investmentUniverseConfig.AssetType;
  show: boolean;
  activePage: string;
  selectedPrice?: plansConfig.PriceType;
  banks: BankProviderType[];
};

type StateType = {
  loading: boolean;
};

class LinkEuBankAccountModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      loading: false
    };
  }

  private _redirectToGocardless = async (provider: BankProviderType): Promise<void> => {
    const { originModal, displayedAsset, activePage, selectedPrice } = this.props;

    const queryParams = toQueryParams({
      providerId: provider.id,
      originPage: activePage,
      originModal,
      displayedAsset: displayedAsset,
      selectedPrice: selectedPrice,
      bankId: provider.id
    });

    try {
      const res = await axios.get(`/investor/gocardless-auth-url?${queryParams}`);

      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    } finally {
      this.setState({ loading: false });
    }
  };

  render(): JSX.Element {
    const { handleClose, show, originModal, banks } = this.props;

    return (
      <Modal show={show} onHide={handleClose} size="xl" dialogClassName="p-md-5 max-w-600px">
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        <div className="fade-in">
          <Modal.Body className="p-0 px-3">
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ width: "400px" }}>
                {/* Back Button */}
                {originModal && (
                  <div className="row p-0 m-0 mb-md-5 mb-3">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                        onClick={handleClose}
                        style={{
                          fontSize: "24px"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                )}
                {/* End Back Button*/}
                <h4 className="fw-bolder mb-3">Choose your bank</h4>
                <div className="text-muted mb-5">You can only add new EUR bank accounts with EU IBANs.</div>
                <>
                  <div className={"pb-4"}>
                    {banks.map((provider) => (
                      <div
                        key={provider.name}
                        className={`row m-0 wh-select-account-card-option mb-3 ${
                          this.state.loading ? "opacity-50" : ""
                        }`}
                        onClick={() => {
                          if (!this.state.loading) {
                            this.setState({ loading: true });
                            this._redirectToGocardless(provider);
                          }
                        }}
                      >
                        <div className="col-2 p-0 d-flex justify-content-center align-self-center">
                          <img
                            className="h-100 align-self-center rounded"
                            style={{ maxHeight: "44px" }}
                            src={provider.logo}
                            alt={provider.name}
                          />
                        </div>
                        <div className="col-8 align-self-center">
                          <div className="d-flex flex-column">
                            <span className=" fw-bold">{provider.name}</span>
                          </div>
                        </div>
                        <div className="col-2 align-self-center">
                          <div className="d-flex flex-column">
                            <i
                              className="material-symbols-outlined align-self-center w-100 text-end fw-bolder pe-2"
                              style={{ fontSize: "22px", color: "#536AE3" }}
                            >
                              arrow_forward_ios
                            </i>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

export default LinkEuBankAccountModal;
