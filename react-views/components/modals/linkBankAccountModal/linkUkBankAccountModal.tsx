import React from "react";
import { Modal } from "react-bootstrap";
import axios from "axios";
import LoadingOnSubmitButton from "../../buttons/loadingOnSubmitButton";
import { OtherModalType } from "../../modalsWrapper";
import { investmentUniverseConfig, plansConfig } from "@wealthyhood/shared-configs";
import { toQueryParams } from "../../../utils/stringUtil";
import { BankProviderType } from "../../../types/bank";

type PropsType = {
  handleClose: () => void;
  originModal?: OtherModalType;
  displayedAsset?: investmentUniverseConfig.AssetType;
  show: boolean;
  activePage: string;
  selectedPrice?: plansConfig.PriceType;
  banks: BankProviderType[];
};
type StateType = {
  viewMode: ViewModeType;
  selectedBank: BankProviderType;
};
type ViewModeType = "BANK_SELECT" | "LINK_ACCOUNT";

class LinkUkBankAccountModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "BANK_SELECT",
      selectedBank: null
    };
  }

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setSelectedBank = (provider: BankProviderType): void => {
    this.setState({ selectedBank: provider });
  };

  private _redirectToTruelayer = async (): Promise<void> => {
    const { originModal, displayedAsset, activePage, selectedPrice } = this.props;
    const { selectedBank } = this.state;

    const queryParams = toQueryParams({
      providerId: selectedBank.providerId,
      originPage: activePage,
      originModal,
      displayedAsset: displayedAsset,
      selectedPrice: selectedPrice
    });

    const res = await axios.get(`/investor/truelayer-auth-url?${queryParams}`);

    if (res.data.redirectUri) {
      window.location.replace(res.data.redirectUri);
    }
  };

  render(): JSX.Element {
    const { handleClose, show, originModal, banks } = this.props;
    const { viewMode, selectedBank } = this.state;

    return (
      <Modal show={show} onHide={handleClose} size="xl" dialogClassName="p-md-5 max-w-600px">
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        <div className="fade-in">
          {viewMode === "BANK_SELECT" ? (
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ width: "400px" }}>
                  {/* Back Button */}
                  {originModal && (
                    <div className="row p-0 m-0 mb-md-5 mb-3">
                      <div className="col p-0">
                        <span
                          className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                          onClick={handleClose}
                          style={{
                            fontSize: "24px"
                          }}
                        >
                          arrow_back
                        </span>
                      </div>
                    </div>
                  )}
                  {/* End Back Button*/}
                  <h5 className="fw-bolder text-center mb-5">Choose your bank</h5>
                  <>
                    <div className={"pb-4"}>
                      {banks.map((provider) => (
                        <div
                          key={provider.name}
                          className={"row m-0 wh-select-account-card-option mb-3"}
                          onClick={() => {
                            this._setSelectedBank(provider);
                            this._setViewMode("LINK_ACCOUNT");
                          }}
                        >
                          <div className="col-2 p-0 d-flex justify-content-center align-self-center">
                            <img
                              className="h-100 align-self-center rounded"
                              style={{ maxHeight: "44px" }}
                              src={provider.logo}
                              alt={provider.name}
                            />
                          </div>
                          <div className="col-8 align-self-center">
                            <div className="d-flex flex-column">
                              <span className=" fw-bold">{provider.name}</span>
                            </div>
                          </div>
                          <div className="col-2 align-self-center">
                            <div className="d-flex flex-column">
                              <i
                                className="material-symbols-outlined align-self-center w-100 text-end fw-bolder pe-2"
                                style={{ fontSize: "22px", color: "#536AE3" }}
                              >
                                arrow_forward_ios
                              </i>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                </div>
              </div>
            </Modal.Body>
          ) : (
            <>
              <Modal.Body className="p-0 px-3">
                <div className="d-flex align-self-center justify-content-center">
                  <div style={{ width: "400px" }}>
                    {/* Back Button */}
                    <div className="row p-0 m-0 mb-md-4 mb-3">
                      <div className="col p-0">
                        <span
                          className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                          onClick={() => this._setViewMode("BANK_SELECT")}
                          style={{
                            fontSize: "24px"
                          }}
                        >
                          arrow_back
                        </span>
                      </div>
                    </div>
                    {/* End Back Button*/}
                    <img
                      className="d-flex h-100 mx-auto align-self-center rounded"
                      style={{ maxHeight: "65px" }}
                      src={selectedBank.logo}
                      alt={selectedBank.name}
                    />
                    <h5 className="fw-bolder text-center mt-3 mb-3">Link your {selectedBank.name} account</h5>
                    <p className={"text-muted mb-5"}>
                      We need to make sure this account is valid and belongs to you. Wealthyhood’s partner,
                      Truelayer, would like permission for one-off access to the following details.
                    </p>
                    <div className={"row mb-3 mt-3"}>
                      <div className="col-2 align-self-center text-center">
                        <i
                          className="material-symbols-outlined icon-primary align-self-center"
                          style={{
                            fontSize: "20px",
                            backgroundColor: "#F1F3FD",
                            borderRadius: "6px",
                            padding: "12px"
                          }}
                        >
                          account_circle
                        </i>
                      </div>
                      <div className="col-10 align-self-center">
                        <div className="d-flex flex-column">
                          <span className="fw-bold text-truncate">Full name</span>
                          <span className="text-muted t-875">Name and surname</span>
                        </div>
                      </div>
                    </div>
                    <div className={"row"}>
                      <div className="col-2 align-self-center text-center">
                        <i
                          className="material-symbols-outlined icon-primary align-self-center"
                          style={{
                            fontSize: "20px",
                            backgroundColor: "#F1F3FD",
                            borderRadius: "6px",
                            padding: "12px"
                          }}
                        >
                          account_balance
                        </i>
                      </div>
                      <div className="col-10 align-self-center">
                        <div className="d-flex flex-column">
                          <span className="fw-bold text-truncate">Bank account details</span>
                          <span className="text-muted t-875">Account number and sort code</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
                <div
                  className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                  style={{ maxWidth: "400px" }}
                >
                  <p className="text-muted mb-4 t-875">
                    *TrueLayer are FCA-regulated, and won’t share or use your personal data for anything else.
                    TrueLayer uses bank-grade encryption to keep your info secure.
                  </p>
                  <LoadingOnSubmitButton
                    customonclick={async () => this._redirectToTruelayer()}
                    type="button"
                    className="btn btn-primary fw-100 mt-3"
                  >
                    Allow
                  </LoadingOnSubmitButton>
                  <p className="text-muted mt-4 mb-0 t-875">
                    By choosing ‘Allow’, you agree to TrueLayer’s{" "}
                    <a
                      className="text-decoration-none"
                      href="https://truelayer.com/legal/enduser_tos"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a
                      className="text-decoration-none"
                      href="https://truelayer.com/legal/privacy"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Privacy Policy
                    </a>
                    .
                  </p>
                </div>
              </Modal.Footer>
            </>
          )}
        </div>
      </Modal>
    );
  }
}

export default LinkUkBankAccountModal;
