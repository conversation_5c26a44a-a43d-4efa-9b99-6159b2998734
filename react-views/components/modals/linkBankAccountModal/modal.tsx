import React from "react";
import { Modal } from "react-bootstrap";
import { OtherModalType } from "../../modalsWrapper";
import { entitiesConfig, investmentUniverseConfig, plansConfig } from "@wealthyhood/shared-configs";
import { BankProviderType } from "../../../types/bank";
import LoadingSpinner from "../../loadingSpinner";
import LinkUkBankAccountModal from "./linkUkBankAccountModal";
import LinkEuBankAccountModal from "./linkEuBankAccountModal";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  originModal?: OtherModalType;
  displayedAsset?: investmentUniverseConfig.AssetType;
  activePage: string;
  selectedPrice?: plansConfig.PriceType;
  userCompanyEntity: entitiesConfig.CompanyEntityEnum;
  banks: BankProviderType[];
};

class LinkBankAccountModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { handleClose, show, originModal, activePage, selectedPrice, userCompanyEntity, banks } = this.props;

    if (!show) return <></>;

    if (show && !banks?.length) {
      return (
        <Modal show={true} onHide={handleClose} size="xl" dialogClassName="p-md-5 max-w-600px">
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>

          <LoadingSpinner />
        </Modal>
      );
    }

    return (
      <>
        {userCompanyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK && (
          <LinkUkBankAccountModal
            show={true}
            activePage={activePage}
            originModal={originModal}
            selectedPrice={selectedPrice}
            banks={banks}
            handleClose={handleClose}
          />
        )}
        {userCompanyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE && (
          <LinkEuBankAccountModal
            show={true}
            activePage={activePage}
            originModal={originModal}
            selectedPrice={selectedPrice}
            banks={banks}
            handleClose={handleClose}
          />
        )}
      </>
    );
  }
}

export default LinkBankAccountModal;
