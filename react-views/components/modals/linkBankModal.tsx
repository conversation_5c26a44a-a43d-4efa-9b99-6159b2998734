import React from "react";
import { Modal } from "react-bootstrap";

type PropsType = {
  show: boolean;
  handleClose: () => void;
};

class LinkBankModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { show, handleClose } = this.props;

    return (
      <Modal
        show={show}
        onHide={(): void => {
          handleClose();
        }}
      >
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title>
            <h4>Link Bank Account</h4>
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <p>
            Clicking the button to start the process will take you to a new page where you{"'"}ll login to your
            bank account.
          </p>
          <p>
            The process is handled through Truelayer and any security information you submit is visible only
            between you and your bank.
          </p>
        </Modal.Body>
        <Modal.Footer className="border-top-0">
          <a className="btn btn-primary font-weight-bold" href="/investor/truelayer-auth-dialog">
            Start process
          </a>
        </Modal.Footer>
      </Modal>
    );
  }
}

export default LinkBankModal;
