import React from "react";
import { Modal } from "react-bootstrap";
import { AssetTransactionDocument, FeesType, FeeType } from "../../../models/Transaction";
import { formatCurrency } from "../../utils/currencyUtil";
import config, { STATUS_CONFIG } from "../../configs/transactionsTableConfig";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { emitToast } from "../../utils/eventService";
import InvestorOrderRow from "../investorOrderRow";
import { ViewModeType } from "../../types/modal";
import { OrderDocument } from "../../../models/Order";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
import { AssetPriceInfo } from "../../types/price";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import OrderReceipt from "../orderReceipt";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import ExecutionWindowPreviewRows from "../executionWindowPreviewRows";
import HoverableInfoIcon from "../hoverableInfoIcon";
import FxRatePreviewRow from "../fxRatePreviewRow";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { getTotalCommission } from "../../utils/feesUtil";
import CommissionPreviewRow from "../commissionPreviewRow";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  transaction: AssetTransactionDocument;
  investmentProducts: InvestmentProductDocument[];
  onSuccessButtonCallback: (transactionTotalAmount: number) => void;
};

type StateType = {
  viewMode: ViewModeType;
  selectedOrder: OrderDocument;
};

const { ASSET_CONFIG } = investmentUniverseConfig;

class OrderSuccessModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "ACTION",
      selectedOrder: null
    };
  }
  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private _cancelTransaction = async () => {
    const { transaction } = this.props;

    try {
      const res = await axios.post(`/transactions/${transaction.id}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  private _getAssetPriceInfoForIsin(targetIsin: string): AssetPriceInfo {
    const { investmentProducts } = this.props;
    const investmentProduct = investmentProducts.find(({ commonId }) => ASSET_CONFIG[commonId].isin == targetIsin);

    return {
      tradedPrice: investmentProduct?.tradedPrice,
      tradedCurrency: investmentProduct?.tradedCurrency,
      currentTickerPrice: investmentProduct?.currentTicker?.price
    };
  }

  private _getTransactionName = (): string => {
    const { transaction } = this.props;
    const { category } = transaction;
    const transactionConfig = config[category];
    let transactionName = transactionConfig.nameDisplay;
    const { portfolioTransactionCategory, orders } = transaction;

    if (orders.length == 1 && portfolioTransactionCategory == "update") {
      const order = orders[0];
      transactionName = Object.values(ASSET_CONFIG).find((config) => config.isin == order.isin).simpleName;
      if (transaction.displayStatus != "Settled") {
        transactionName = `${order.side} ${transactionName}`;
      }
    } else {
      transactionName = `${transactionName.toUpperCase().substr(0, 1)}${transactionName
        .toLowerCase()
        .substr(1)} ${portfolioTransactionCategory}`;
    }

    return transactionName;
  };

  private _setSelectedOrder(selectedOrder: OrderDocument) {
    this.setState({ selectedOrder });
  }

  render(): JSX.Element {
    const { show, handleClose, transaction, onSuccessButtonCallback } = this.props;
    const { viewMode, selectedOrder } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    let price;
    if (selectedOrder?.isMatched) {
      price = selectedOrder?.displayUnitPrice;
    } else if (selectedOrder) {
      price = this._getAssetPriceInfoForIsin(selectedOrder.isin);
    }

    return (
      <Modal
        show={show}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
        onHide={(): void => {
          handleClose();
        }}
      >
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        <Modal.Body className="p-0 px-3">
          {viewMode == "ACTION" && (
            <div className="d-flex flex-column align-self-center justify-content-center">
              <div className="w-100 align-self-center" style={{ maxWidth: "400px" }}>
                {/* Action Title */}
                <div className="d-flex w-100 justify-content-center mb-3">
                  <i
                    className="material-symbols-outlined align-self-center icon-primary wh-card"
                    style={{ fontSize: "32px" }}
                  >
                    check_circle
                  </i>
                </div>
                <h4 className="fw-bolder text-center mb-5">Order Placed!</h4>
                <h5 className="fw-bolder text-center mb-5">Buy Portfolio</h5>
                {/* End Action Title */}
                <div className="row pb-3 m-0 mb-3 border-bottom">
                  <div className="col p-0 text-start">Total amount</div>
                  <div className="col p-0 text-end">
                    <span className="fw-bolder">
                      {formatCurrency(
                        Decimal.div(transaction?.displayAmount ?? 0, 100).toNumber(),
                        transaction?.consideration.currency ?? user.currency,
                        locale,
                        2,
                        2
                      )}
                    </span>
                  </div>
                </div>
                <CommissionPreviewRow
                  isRepeatingInvestment={!!transaction?.linkedAutomation}
                  hasETFOrders={transaction?.orders?.some((order) => order.assetCategory === "etf")}
                  fees={{ smart: transaction?.fees }}
                />
                <FxRatePreviewRow
                  showBottomBorder={true}
                  foreignCurrencyRates={transaction?.foreignCurrencyRates}
                />
                {!transaction?.linkedAutomation ? (
                  <ExecutionWindowPreviewRows
                    executionWindow={{ smart: transaction?.executionWindow }}
                    showBottomBorder={true}
                  />
                ) : (
                  <></>
                )}
                <div className="row pb-3 mb-3 border-bottom">
                  <div className="col text-start">Status</div>
                  <div className="col text-end">
                    <span
                      className={`fw-bolder text-nowrap text-${STATUS_CONFIG[transaction?.displayStatus]?.color}`}
                    >
                      {STATUS_CONFIG[transaction?.displayStatus]?.label}
                    </span>
                  </div>
                </div>
                <div className="row mb-3">
                  <div className="col text-center">
                    <div className="text-primary cursor-pointer" onClick={() => this._setViewMode("DETAILS")}>
                      View all orders
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {viewMode == "DETAILS" && (
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                {/* Back Button */}
                <div className="row p-0 m-0 mb-3">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                      onClick={() => this._setViewMode("ACTION")}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}
                {/* Action Title */}
                <h5 className="fw-bolder text-center mb-5">{this._getTransactionName()}</h5>
                {/* End Action Title */}
                {transaction?.orders.map((order, index) => (
                  <InvestorOrderRow
                    onClick={() => {
                      this._setSelectedOrder(order);
                      this._setViewMode("RECEIPT");
                    }}
                    currentPriceInfo={this._getAssetPriceInfoForIsin(order.isin)}
                    parentTransaction={transaction}
                    order={order}
                    key={`order_${index}`}
                  />
                ))}
              </div>
            </div>
          )}
          {viewMode == "RECEIPT" && selectedOrder && (
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                {/* Back Button */}
                <div className="row p-0 m-0 mb-3">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                      onClick={() => this._setViewMode("DETAILS")}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}
                <OrderReceipt
                  order={selectedOrder}
                  price={price}
                  executionWindow={
                    selectedOrder.assetCategory === "etf"
                      ? transaction.executionWindow?.etfs
                      : transaction.executionWindow?.stocks
                  }
                  isPartOfPortfolioTransaction={true}
                />
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
          <div
            className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
            style={{ maxWidth: "400px" }}
          >
            <LoadingOnSubmitButton
              className="btn btn-primary fw-100 mb-3"
              customonclick={async () => onSuccessButtonCallback(transaction.displayAmount)}
            >
              Done
            </LoadingOnSubmitButton>
            {transaction?.isCancellable && (
              <LoadingOnSubmitButton className="btn btn-danger fw-100" customonclick={this._cancelTransaction}>
                Cancel order
              </LoadingOnSubmitButton>
            )}
            {/* here there is a missing cancellation button */}
          </div>
        </Modal.Footer>
      </Modal>
    );
  }
}

OrderSuccessModal.contextType = GlobalContext;

export default OrderSuccessModal;
