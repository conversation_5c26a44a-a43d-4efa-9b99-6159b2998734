import Decimal from "decimal.js";
import React from "react";
import { Modal } from "react-bootstrap";
import {
  cashbacksConfig,
  countriesConfig,
  currenciesConfig,
  giftsConfig,
  investmentsConfig,
  localeConfig,
  plansConfig
} from "@wealthyhood/shared-configs";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { LinkedBankAccount } from "../../types/bank";
import { FrequencySetting, PaymentMethod, ViewModeType } from "../../types/modal";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { convertIntoRecurrenceDate, getEveryMonthOnTheXthDateString } from "../../utils/dateUtil";
import { TransactionPreview } from "../../types/transactionPreview";
import OrdersPreview from "../ordersPreview";
import { OtherModalType } from "../modalsWrapper";
import FrequencySelect from "../frequencySelect";
import { MandateDocument } from "../../../models/Mandate";
import { BankAccountDocument } from "../../../models/BankAccount";
import { TopUpAutomationDocument } from "../../../models/Automation";
import FxRatePreviewRow from "../fxRatePreviewRow";
import { formatPercentage } from "../../utils/formatterUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import ConfigUtil from "../../../utils/configUtil";
import ExecutionWindowPreviewRows from "../executionWindowPreviewRows";
import CommissionPreviewRow from "../commissionPreviewRow";
import TransactionModalTargetAllocationSelector, {
  AllocationMethodEnum
} from "../transactionModalTargetAllocationSelector";
import CashbackPreviewRow from "../cashbackPreviewRow";
import { GiftDocument } from "../../../models/Gift";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import AllocationMethodSelect from "../allocationMethodSelect";
import TransactionModalFrequencySelector from "../transactionModalFrequencySelector";
import { ExecutionModeType } from "../../types/executionMode";
import ExecutionModeToggle from "../executionModeToggle";
import { UserDocument } from "../../../models/User";
import { isAllowedOneStepInvest } from "../../utils/userUtil";
import AddBankAccountButton from "../addBankAccountButton";
import DirectDebitSetup from "../directDebitSetup";

const {
  MIN_ALLOWED_INVESTMENT,
  MIN_ALLOWED_ASSET_INVESTMENT,
  MIN_ALLOWED_RECURRING_TOP_UP,
  MAX_ALLOWED_RECURRING_TOP_UP,
  MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT
} = investmentsConfig;
const { RESTRICTED_HOLDING_PERIOD_DAYS } = giftsConfig;
const { CASHBACK_RATES, MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

type TransactionStatusType =
  | "allowedCashBuy"
  | "allowedBankTopUpBuy"
  | "allowedGiftBuy"
  | "forbiddenAmount"
  | "smallerThanMinimumRecurringAmount"
  | "largerThanMinimumRecurringAmount"
  | "insufficientCash"
  | "insufficientCashTopupPrompt"
  | "noInvestmentCashBuy"
  | "noInvestmentBankBuy"
  | "noInvestment"
  | "zeroCash";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  gift?: GiftDocument;
  linkedBankAccounts: LinkedBankAccount[];
  activeOrPendingMandates: MandateDocument[];
  portfolioId: string;
  isPortfolioAllocationSetup: boolean;
  initialSelectedLinkedBankAccount?: LinkedBankAccount;
  availableCash: number;
  hasHoldings: boolean;
  existingTopUpAutomation?: TopUpAutomationDocument;
  isFirstInvestment?: boolean;
};

type StateType = {
  orderAmount: string;
  frequency: FrequencySetting;
  paymentMethod: PaymentMethod;
  selectedLinkedBankAccount: LinkedBankAccount;
  selectedDayOfMonth: number;
  viewMode: ViewModeType;
  transactionPreview: TransactionPreview;
  errorMsg: string;
  actionButtonClicked: boolean;
  viewModeBeforeCashback?: ViewModeType;
  buyTargetAllocation: boolean;
  executionMode: ExecutionModeType;
};

class PortfolioBuyModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();
    this.state = {
      orderAmount: this._getInitialOrderAmount(),
      frequency: "ONE_TIME",
      paymentMethod: this._getDefaultPaymentMethod(),
      errorMsg: "",
      selectedLinkedBankAccount:
        this.props.initialSelectedLinkedBankAccount ?? availableLinkedBankAccounts?.[0] ?? null,
      selectedDayOfMonth:
        props.existingTopUpAutomation?.dayOfMonth ?? convertIntoRecurrenceDate(new Date(Date.now())),
      viewMode: "ACTION",
      transactionPreview: null,
      actionButtonClicked: false,
      buyTargetAllocation: this._getBuyTargetAllocationDefaultValue(),
      executionMode: null
    };
  }

  private _getAvailableLinkedBankAccounts = (): LinkedBankAccount[] => {
    const { linkedBankAccounts } = this.props;

    return linkedBankAccounts.filter((bankAccount) => bankAccount.isAvailableForDirectDebit);
  };

  componentDidUpdate(prevProps: PropsType) {
    if (this._shouldAllowBankAccountTopUp()) {
      if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", this.props.initialSelectedLinkedBankAccount);
      } else if (this.props.linkedBankAccounts !== prevProps.linkedBankAccounts) {
        const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", availableLinkedBankAccounts[0]);
      }
    }

    if (this.props.existingTopUpAutomation !== prevProps.existingTopUpAutomation) {
      this._setSelectedDayOfMonth(this.props.existingTopUpAutomation?.dayOfMonth);
    }

    if (this.props.gift !== prevProps.gift && this.props.gift) {
      this._setPaymentMethodAndAccount("GIFT", null);
      this._setOrderAmount(new Decimal(this.props.gift.consideration.amount).div(100).toNumber().toString());
    }
  }

  private _shouldAllowBankAccountTopUp = (): boolean => {
    const { user } = this.context as GlobalContextType;

    return isAllowedOneStepInvest(user) || this.state.frequency !== "ONE_TIME";
  };

  private _getBuyTargetAllocationDefaultValue = (): boolean => {
    const { isPortfolioAllocationSetup, hasHoldings } = this.props;

    if (!isPortfolioAllocationSetup) {
      return false;
    }

    return !hasHoldings;
  };

  private _getDefaultPaymentMethod = (): PaymentMethod => {
    const { gift, availableCash } = this.props;

    if (gift && !gift.used) {
      return "GIFT";
    } else if (availableCash && availableCash > MIN_ALLOWED_ASSET_INVESTMENT) {
      return "CASH";
    } else {
      return "BANK_ACCOUNT_TOP_UP";
    }
  };

  private _getInitialOrderAmount = (): string => {
    const { gift } = this.props;

    if (gift && !gift.used && this._getDefaultPaymentMethod() === "GIFT") {
      return new Decimal(gift.consideration.amount).div(100).toNumber().toString();
    } else {
      return "";
    }
  };

  private _getTransactionStatus = (): TransactionStatusType => {
    const { paymentMethod, orderAmount, frequency } = this.state;
    const { user } = this.context as GlobalContextType;
    const { availableCash } = this.props;

    const orderAmountNum = Number(orderAmount);

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      if (orderAmountNum === 0) {
        return "noInvestmentBankBuy";
      }

      if (frequency === "ONE_TIME") {
        if (orderAmountNum < MIN_ALLOWED_INVESTMENT) {
          return "forbiddenAmount";
        }
      } else {
        if (orderAmountNum < MIN_ALLOWED_RECURRING_TOP_UP) {
          return "smallerThanMinimumRecurringAmount";
        } else if (orderAmountNum > MAX_ALLOWED_RECURRING_TOP_UP) {
          return "largerThanMinimumRecurringAmount";
        }
      }

      return "allowedBankTopUpBuy";
    } else if (paymentMethod === "GIFT") {
      return "allowedGiftBuy";
    } else if (paymentMethod === "CASH" && availableCash !== undefined) {
      if (availableCash === 0) {
        return "zeroCash";
      } else if (availableCash > 0) {
        if (orderAmountNum === 0) {
          return "noInvestmentCashBuy";
        } else if (orderAmountNum < MIN_ALLOWED_INVESTMENT) {
          return "forbiddenAmount";
        } else if (orderAmountNum > availableCash) {
          return isAllowedOneStepInvest(user) ? "insufficientCash" : "insufficientCashTopupPrompt";
        }
      }
      return "allowedCashBuy";
    }

    return "noInvestmentBankBuy";
  };

  private _isTransactionAllowed = (): boolean => {
    const { paymentMethod } = this.state;
    const transactionStatus = this._getTransactionStatus();

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      return transactionStatus === "allowedBankTopUpBuy";
    } else if (paymentMethod === "GIFT") {
      return transactionStatus === "allowedGiftBuy";
    } else if (paymentMethod === "CASH") {
      return transactionStatus === "allowedCashBuy" || transactionStatus === "insufficientCashTopupPrompt";
    }

    return false;
  };

  private _getStatusMessage = (
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { availableCash } = this.props;
    const { orderAmount } = this.state;
    const transactionStatus = this._getTransactionStatus();

    const STATUS_MESSAGES: Record<string, string> = {
      allowedCashBuy: `${formatCurrency(
        Decimal.sub(availableCash, orderAmount || 0).toNumber(),
        userCurrency,
        locale
      )} remaining cash`,
      allowedBankTopUpBuy: `${formatCurrency(
        new Decimal(orderAmount || 0).toNumber(),
        userCurrency,
        locale
      )} paid with your bank account`,
      allowedGiftBuy: `${formatCurrency(
        new Decimal(orderAmount || 0).toNumber(),
        userCurrency,
        locale
      )} paid with your gift`,
      forbiddenAmount: `You can't invest less than ${formatCurrency(
        MIN_ALLOWED_INVESTMENT,
        userCurrency,
        locale
      )}.`,
      smallerThanMinimumRecurringAmount: `The minimum repeating investment is ${formatCurrency(
        MIN_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale,
        0,
        0
      )}.`,
      largerThanMinimumRecurringAmount: `The maximum repeating investment is ${formatCurrency(
        MAX_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale,
        0,
        0
      )}.`,
      insufficientCash: `You don't have enough cash.`,
      noInvestmentCashBuy: `${formatCurrency(availableCash, userCurrency, locale)} remaining cash`,
      noInvestmentBankBuy: "",
      zeroCash: `You have ${formatCurrency(0, userCurrency, locale, 0, 0)} cash available.`,
      insufficientCashTopupPrompt: `Your available balance is ${formatCurrency(
        availableCash,
        userCurrency,
        locale
      )}. Please top up your account to proceed.`
    };

    return STATUS_MESSAGES[transactionStatus] || "";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setFrequency = (frequency: FrequencySetting): void => {
    this.setState({ frequency });
  };

  private _setPaymentMethodAndAccount = (
    paymentMethod: PaymentMethod,
    selectedLinkedBankAccount: LinkedBankAccount
  ): void => {
    this.setState({ paymentMethod, selectedLinkedBankAccount });
  };

  private _setSelectedDayOfMonth = (selectedDayOfMonth: number): void => {
    this.setState({ selectedDayOfMonth });
  };

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setBuyTargetAllocation(buyTargetAllocation: boolean) {
    this.setState({ buyTargetAllocation });
  }

  private _getDisclaimer = (): string => {
    const { paymentMethod } = this.state;

    if (paymentMethod === "GIFT") {
      return `Any shares bought with a voucher must be held for at least ${RESTRICTED_HOLDING_PERIOD_DAYS} days. Capital at risk.`;
    } else return "Capital at risk.";
  };

  private _getBuyConfirmationCTACopy = (): string => {
    const { frequency } = this.state;

    if (frequency === "ONE_TIME") {
      return "Confirm buy";
    } else return "Confirm repeating investment";
  };

  /**
   * When selecting a recurring frequency, we select a bank account with a pending/active mandate. If there isn't any,
   * we select the first linked bank account in the list.
   */
  private _getBankAccountForRecurringTopUp = (): LinkedBankAccount => {
    const { activeOrPendingMandates, existingTopUpAutomation } = this.props;

    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    // First check if there's an existing automation with a mandate
    if (existingTopUpAutomation) {
      const mandateIdUsedForExistingAutomation = (existingTopUpAutomation.mandate as MandateDocument)?.id;
      if (mandateIdUsedForExistingAutomation) {
        const bankAccountUsedForExistingAutomation = activeOrPendingMandates.find(
          (mandate) => mandate.id === mandateIdUsedForExistingAutomation
        )?.bankAccount as BankAccountDocument;

        if (bankAccountUsedForExistingAutomation) {
          const linkedBankAccount = availableLinkedBankAccounts.find(
            (bankAccount) => bankAccount.id === bankAccountUsedForExistingAutomation._id.toString()
          );
          if (linkedBankAccount) return linkedBankAccount;
        }
      }
    }

    // Otherwise look for a bank account with an active/pending mandate
    const bankAccountsWithPendingOrActiveMandate = activeOrPendingMandates
      .filter((mandate) => mandate.status !== "Inactive")
      .map((mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString());

    return (
      availableLinkedBankAccounts.find((bankAccount) =>
        bankAccountsWithPendingOrActiveMandate.includes(bankAccount.id)
      ) || availableLinkedBankAccounts[0]
    );
  };

  private _reviewPayment = async () => {
    const { existingTopUpAutomation } = this.props;
    const { frequency, paymentMethod } = this.state;

    if (!this._isTransactionAllowed()) {
      return;
    }

    const transactionStatus = this._getTransactionStatus();
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    // When user clicks on review, we move to the 'REVIEW' view mode unless they're updating an existing automation.
    if (availableLinkedBankAccounts.length === 0 && paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      eventEmitter.emit(EVENTS.linkBankAccountModal, {
        originModal: "portfolioBuy" as OtherModalType
      });
      return;
    } else if (frequency === "MONTHLY" && existingTopUpAutomation) {
      this._setViewMode("CONFIRM_UPDATE_TOP_UP");
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      eventEmitter.emit(EVENTS.depositMethodsModal, {
        originModal: "portfolioBuy" as OtherModalType
      });
    } else {
      await this._getTransactionPreview(() => this._setViewMode("REVIEW"));
    }
  };

  private _shouldCreateMandateForSelectedBankAccount(): boolean {
    const { activeOrPendingMandates } = this.props;
    const { frequency, selectedLinkedBankAccount } = this.state;

    // If the user has chosen to make their subscription fee-based OR they already hold a pending/active mandate with the bank
    // account they've chosen, we don't want to show them 'Set up payment method'
    return (
      frequency !== "ONE_TIME" &&
      (activeOrPendingMandates.length === 0 ||
        activeOrPendingMandates.every(
          (mandate) =>
            (mandate.bankAccount as BankAccountDocument)._id.toString() !== selectedLinkedBankAccount?.id
        ))
    );
  }

  private async _setupAutomation() {
    const { activeOrPendingMandates } = this.props;
    const { frequency, selectedLinkedBankAccount, orderAmount, buyTargetAllocation, selectedDayOfMonth } =
      this.state;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      if (this._shouldCreateMandateForSelectedBankAccount()) {
        await axios.post("/investor/setup-recurring-topup", {
          bankAccount: selectedLinkedBankAccount,
          dayOfMonth: selectedDayOfMonth,
          orderAmount,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          frequency
        });

        window.location.href = `/investor/new-mandate-success?dayOfMonth=${selectedDayOfMonth}`;
      } else {
        await axios.post("/investor/setup-recurring-topup", {
          orderAmount,
          frequency,
          dayOfMonth: selectedDayOfMonth,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          mandate: activeOrPendingMandates.find(
            (mandate) =>
              (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
          ).id
        });

        window.location.href = "/investor/new-recurring-top-up-success";
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _confirmBuy = async () => {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount, frequency } = this.state;

    if (
      frequency !== "ONE_TIME" &&
      !activeOrPendingMandates.some(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
      )
    ) {
      this._setViewMode("SETUP_DIRECT_DEBIT");
    } else if (frequency !== "ONE_TIME") {
      await this._setupAutomation();
    } else {
      await this._submitOrder();
    }
  };

  private _submitOrder = async () => {
    const { portfolioId, gift } = this.props;
    const { orderAmount, selectedLinkedBankAccount, paymentMethod, buyTargetAllocation, executionMode } =
      this.state;

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your order is being sent... ");

    this.setState({ errorMsg: "" });

    try {
      let url;
      let body;

      const allocationMethod = buyTargetAllocation ? "targetAllocation" : "holdings";
      const executeEtfOrdersInRealtime = executionMode ? executionMode === "EXPRESS" : undefined;

      if (paymentMethod === "CASH") {
        url = `/portfolios/${portfolioId}/buy?allocationMethod=${allocationMethod}${
          executeEtfOrdersInRealtime !== undefined
            ? `&executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
        body = { orderAmount };
      } else if (paymentMethod === "GIFT" && gift) {
        url = `/portfolios/${portfolioId}/buy?paymentMethod=gift&allocationMethod=${allocationMethod}${
          executeEtfOrdersInRealtime !== undefined
            ? `&executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
        body = { orderAmount, gift: gift.id };
      } else if (paymentMethod === "BANK_ACCOUNT_TOP_UP") {
        url = `/investor/deposit-and-invest?allocationMethod=${allocationMethod}${
          executeEtfOrdersInRealtime !== undefined
            ? `&executeEtfOrdersInRealtime=${executeEtfOrdersInRealtime}`
            : ""
        }`;
        body = { orderAmount: orderAmount, bankAccountId: selectedLinkedBankAccount.id };
      }

      const res = await axios.post(url, body);
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      this.setState({ errorMsg: err.response.data.message });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private async _getTransactionPreview(callback: () => void) {
    const { orderAmount, buyTargetAllocation, frequency } = this.state;
    const { user } = this.context as GlobalContextType;

    try {
      const allocationMethod = buyTargetAllocation ? "targetAllocation" : "holdings";

      const res = await axios.get(
        `/transactions/portfolio/preview?portfolioTransactionType=buy&orderAmount=${orderAmount}&allocationMethod=${allocationMethod}`
      );
      this.setState(
        {
          transactionPreview: res.data,
          executionMode: this._shouldShowExecutionToggle(user, res.data, frequency) ? "SMART" : undefined
        },
        () => callback()
      );
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      this.setState({ errorMsg: err.response.data.message });

      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  }

  private _getActionViewCTACopy(): string {
    const { paymentMethod, frequency } = this.state;

    const transactionStatus = this._getTransactionStatus();
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    if (
      paymentMethod === "BANK_ACCOUNT_TOP_UP" &&
      (!availableLinkedBankAccounts || availableLinkedBankAccounts?.length === 0)
    ) {
      return "Proceed";
    } else if (
      (!availableLinkedBankAccounts || availableLinkedBankAccounts?.length === 0) &&
      frequency !== "ONE_TIME"
    ) {
      return "Proceed";
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      return "Top up";
    } else {
      return "Review";
    }
  }

  private _renderActionButton(): JSX.Element {
    const { orderAmount } = this.state;

    const allowedTransaction = this._isTransactionAllowed();
    const ctaCopy = this._getActionViewCTACopy();

    if (!orderAmount || !allowedTransaction) {
      return (
        <button type="button" className="btn btn-primary fw-100" disabled>
          {ctaCopy}
        </button>
      );
    }

    return (
      <LoadingOnSubmitButton
        className="btn btn-primary fw-100"
        customonclick={async () => {
          this._setActionButtonClicked(true);
          await this._reviewPayment();
        }}
        enableOnCompletion={true}
      >
        {ctaCopy}
      </LoadingOnSubmitButton>
    );
  }

  private _getCurrentPlan(): plansConfig.PlanType {
    const { user } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    return PRICE_CONFIG[user?.subscription?.price]?.plan;
  }

  private _shouldShowExecutionToggle(
    user: UserDocument,
    transactionPreview: TransactionPreview,
    frequency: FrequencySetting
  ): boolean {
    return user.isRealtimeETFExecutionEnabled && transactionPreview?.hasETFOrders && frequency === "ONE_TIME";
  }

  render(): JSX.Element {
    const {
      show,
      handleClose,
      linkedBankAccounts,
      gift,
      existingTopUpAutomation,
      activeOrPendingMandates,
      isPortfolioAllocationSetup,
      availableCash,
      hasHoldings
    } = this.props;

    const {
      orderAmount,
      frequency,
      paymentMethod,
      selectedLinkedBankAccount,
      selectedDayOfMonth,
      viewMode,
      transactionPreview,
      actionButtonClicked,
      buyTargetAllocation,
      errorMsg,
      executionMode
    } = this.state;

    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();
    const shouldShowExecutionModeToggle = this._shouldShowExecutionToggle(user, transactionPreview, frequency);

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);
    const plan = this._getCurrentPlan();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          this._setBuyTargetAllocation(this._getBuyTargetAllocationDefaultValue());
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0 w-100 d-flex">
            <div className={"me-auto"}>
              <TransactionModalBackButton
                onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
                title={"Buy"}
                subtitle={"Portfolio"}
              />
            </div>
            {viewMode === "ACTION" && (
              <div className={"ms-auto"}>
                <TransactionModalFrequencySelector
                  frequency={frequency}
                  paymentMethod={paymentMethod}
                  selectedDayOfMonth={selectedDayOfMonth}
                  handleClick={() => this._setViewMode("SET_FREQUENCY")}
                  handleSelectDayOfMonth={(selectedDayOfMonth) => this._setSelectedDayOfMonth(selectedDayOfMonth)}
                />
              </div>
            )}
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode == "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className={"d-flex flex-column align-items-center"} style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-3">
                    {paymentMethod === "GIFT" ? (
                      <p className="border-0 p-0 order-input">
                        {gift &&
                          formatCurrency(
                            new Decimal(gift.consideration.amount).div(100).toNumber(),
                            gift.consideration.currency,
                            locale,
                            0,
                            0
                          )}
                      </p>
                    ) : (
                      <OrderInput
                        amount={orderAmount}
                        placeholderAmount={0}
                        onAmountChange={(amount) => {
                          const { orderAmount } = this.state;
                          // if order amount is empty or reduced
                          // we clear the error message
                          if (!amount || amount != orderAmount) {
                            this._setActionButtonClicked(false);
                          }

                          this._setOrderAmount(amount);
                        }}
                        prefix={CURRENCY_SYMBOLS[user.currency]}
                        decimalLimit={2}
                      />
                    )}
                  </div>
                  {/* End Order Input*/}

                  {/* Error message when transaction not allowed */}
                  {!["allowedCashBuy", "allowedBankTopUpBuy", "noInvestmentCashBuy", "allowedGiftBuy"].includes(
                    transactionStatus
                  ) &&
                    orderAmount && (
                      <p className="text-center text-danger mb-4">
                        {this._getStatusMessage(user.currency, locale)}
                      </p>
                    )}

                  {/* Selected Payment Method Field */}
                  {paymentMethod === "BANK_ACCOUNT_TOP_UP" && !selectedLinkedBankAccount ? (
                    <></>
                  ) : (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={paymentMethod}
                      availableCash={availableCash || null}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      gift={gift}
                      frequency={frequency}
                    />
                  )}
                  {/* Selected Payment Method Field */}

                  {errorMsg && <div className="text-danger px-5 pb-2">{errorMsg}</div>}

                  {/* Buy target allocation switch */}
                  <TransactionModalTargetAllocationSelector
                    onClick={() => this._setViewMode("ALLOCATION_METHOD_SELECTION")}
                    allocationMethod={
                      buyTargetAllocation ? AllocationMethodEnum.TARGET_ALLOCATION : AllocationMethodEnum.HOLDINGS
                    }
                  />
                  {/* End Buy target allocation switch */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._renderActionButton()}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Action Modal Content */}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {transactionPreview && (
                    <>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Total amount</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(Number(orderAmount ?? 0), user.currency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      {frequency === "MONTHLY" ? (
                        <div className="row pb-3 mb-3 border-bottom">
                          <div className="col text-start text-nowrap">Repeating</div>
                          <div className="col text-end">
                            <span className="fw-bolder text-nowrap">
                              {getEveryMonthOnTheXthDateString({ capitalFirst: true }, selectedDayOfMonth)}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <></>
                      )}
                      <CashbackPreviewRow
                        transactionPreview={transactionPreview}
                        onInfoClick={() =>
                          this.setState((prevState) => {
                            return {
                              ...prevState,
                              viewModeBeforeCashback: prevState.viewMode,
                              viewMode: "EARN_CASHBACK_INFORMATION"
                            };
                          })
                        }
                      />
                      <CommissionPreviewRow
                        fees={transactionPreview.fees}
                        executionMode={executionMode}
                        isRepeatingInvestment={frequency !== "ONE_TIME"}
                        hasETFOrders={transactionPreview.hasETFOrders}
                      />
                      <FxRatePreviewRow
                        foreignCurrencyRates={transactionPreview.foreignCurrencyRates}
                        showBottomBorder={frequency === "ONE_TIME"}
                      />
                      {frequency === "ONE_TIME" ? (
                        <ExecutionWindowPreviewRows
                          executionWindow={transactionPreview.executionWindow}
                          executionMode={executionMode}
                          showBottomBorder={shouldShowExecutionModeToggle}
                        />
                      ) : (
                        <></>
                      )}
                      {shouldShowExecutionModeToggle && (
                        <ExecutionModeToggle
                          executionMode={executionMode}
                          onChange={() =>
                            this.setState({ executionMode: executionMode === "EXPRESS" ? "SMART" : "EXPRESS" })
                          }
                        />
                      )}
                      <div className="row mb-3">
                        <div className="col text-center">
                          <div
                            className="text-primary cursor-pointer"
                            onClick={() => this._setViewMode("DETAILS")}
                          >
                            View all orders
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  {/* Warning component */}
                  {transactionPreview?.willSkipOrders && (
                    <span className="d-block text-center text-warning">
                      The minimum buy order for individual assets is{" "}
                      {formatCurrency(MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT, user.currency, locale, 0, 0)}. Orders for
                      less than {formatCurrency(MIN_ALLOWED_PORTFOLIO_ORDER_AMOUNT, user.currency, locale, 0, 0)}{" "}
                      in value will be ignored.
                    </span>
                  )}
                  {/* End Warning component */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-primary fw-100" customonclick={this._confirmBuy}>
                  {this._getBuyConfirmationCTACopy()}
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">{this._getDisclaimer()}</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Details Modal Content */}
        {viewMode == "DETAILS" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {/* Orders Preview */}
                  <OrdersPreview
                    transactionPreview={transactionPreview}
                    mode="buy"
                    executionMode={executionMode}
                  />
                  {/* End Orders Preview */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-primary fw-100" customonclick={this._confirmBuy}>
                  {this._getBuyConfirmationCTACopy()}
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">Capital at risk.</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Details Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={paymentMethod}
                    frequency={frequency}
                    gift={gift}
                    availableCash={availableCash || null}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) => {
                      this._setPaymentMethodAndAccount(paymentMethod, selectedLinkedBankAccount);
                      if (paymentMethod === "GIFT" && gift) {
                        this._setOrderAmount(
                          new Decimal(gift.consideration.amount).div(100).toNumber().toString()
                        );
                      } else if (paymentMethod !== "GIFT") {
                        // Reset order amount when not gift (for both cash and bank)
                        this._setOrderAmount("");
                      }
                    }}
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._shouldAllowBankAccountTopUp() && <AddBankAccountButton originModal="portfolioBuy" />}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Bank Select Modal Content */}

        {/* Select Frequency Modal Content */}
        {viewMode == "SET_FREQUENCY" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <FrequencySelect
                    initialFrequencySelected={frequency}
                    linkedBankAccounts={linkedBankAccounts}
                    activeOrPendingMandates={activeOrPendingMandates}
                    existingTopUpAutomation={existingTopUpAutomation}
                    onFrequencyChange={(frequency) => {
                      this._setFrequency(frequency);
                      if (frequency !== "ONE_TIME") {
                        if (existingTopUpAutomation) {
                          this._setOrderAmount(
                            Decimal.div(existingTopUpAutomation.consideration.amount, 100).toNumber().toString()
                          );
                        }
                        this._setPaymentMethodAndAccount(
                          "BANK_ACCOUNT_TOP_UP",
                          this._getBankAccountForRecurringTopUp()
                        );
                      } else if (!selectedLinkedBankAccount && availableCash) {
                        // We are switching back to ONE_TIME, so if there is no selected linked bank account
                        // and we're not in new user mode with cash available, we want to switch
                        // payment method to the available balance.
                        this._setPaymentMethodAndAccount("CASH", null);
                      }

                      this._setViewMode("ACTION");
                    }}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }} />
          </div>
        )}
        {/* End Select Frequency Modal Content */}

        {/* Select Frequency Modal Content */}
        {viewMode == "ALLOCATION_METHOD_SELECTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <AllocationMethodSelect
                    allocationMethodSelected={
                      buyTargetAllocation ? AllocationMethodEnum.TARGET_ALLOCATION : AllocationMethodEnum.HOLDINGS
                    }
                    onAllocationMethodChange={(allocationMethod) =>
                      this._setBuyTargetAllocation(allocationMethod === AllocationMethodEnum.TARGET_ALLOCATION)
                    }
                    hasHoldings={hasHoldings}
                    isPortfolioAllocationSetup={isPortfolioAllocationSetup}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }} />
          </div>
        )}
        {/* End Select Frequency Modal Content */}

        {/* Update Existing Top-Up */}
        {viewMode == "CONFIRM_UPDATE_TOP_UP" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">Update existing recurring investment?</h5>
                    <p className={"text-muted"}>
                      A recurring investment is already set up. If you proceed, the existing recurring investment
                      will be updated.
                    </p>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="pb-4 pt-2 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-between align-self-center mt-2 w-100"
                style={{ maxWidth: "400px" }}
              >
                <button
                  className="btn btn-secondary"
                  style={{ width: "46%" }}
                  onClick={() => this._setViewMode("ACTION")}
                >
                  Cancel
                </button>
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary"
                  style={{ width: "46%" }}
                  customonclick={async () => await this._getTransactionPreview(() => this._setViewMode("REVIEW"))}
                >
                  Next
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Update Existing Top-Up */}

        {/* Direct Debit Information */}
        {viewMode == "DIRECT_DEBIT_INFORMATION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">The Direct Debit Guarantee</h5>
                    <ul className={"ps-4 pe-4 text-muted"}>
                      <li className={"pt-3"}>
                        This Guarantee is offered by all banks and building societies that accept instructions to
                        pay Direct Debits.
                      </li>
                      <li className={"pt-3"}>
                        If there are any changes to the amount, date or interval of your Direct Debit GC Re
                        Wealthkernel will notify you 3 working days in advance of your account being debited or as
                        otherwise agreed. If you request GC Re Wealthkernel to collect a payment, confirmation of
                        the amount and date will be given to you at the time of the request.{" "}
                      </li>
                      <li className={"pt-3"}>
                        If an error is made in the payment of your Direct Debit, by GC Re Wealthkernel or your bank
                        or building society, you are entitled to a full and immediate refund of the amount paid
                        from your bank or building society - If you receive a refund you are not entitled to, you
                        must pay it back when GC Re Wealthkernel asks you to.
                      </li>
                      <li className={"pt-3"}>
                        You can cancel a Direct Debit at any time by simply contacting your bank or building
                        society. Written confirmation may be required. Please also notify us.
                      </li>
                    </ul>
                    <div className="d-flex p-0 justify-content-center">
                      <img
                        src={"/images/icons/direct-debit.png"}
                        style={{ height: "48px", width: "130.29px" }}
                        className="p-2"
                        alt="The Direct Debit Guarantee"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Direct Debit Information */}

        {/* Setup Direct Debit Content */}
        {viewMode == "SETUP_DIRECT_DEBIT" && selectedLinkedBankAccount && (
          <DirectDebitSetup
            selectedLinkedBankAccount={selectedLinkedBankAccount}
            orderAmount={orderAmount}
            onSetupAutomation={async () => this._setupAutomation()}
            onViewDirectDebitInfo={() => this._setViewMode("DIRECT_DEBIT_INFORMATION")}
            description="Set up a direct debit for your repeating investment."
            investmentType="Repeating investment"
          />
        )}
        {/* End Setup Direct Debit Content */}

        {/* Earn Cashback Info */}
        {viewMode == "EARN_CASHBACK_INFORMATION" && (
          <Modal.Body className="p-0 px-md-3 px-3 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                {plan == "free" ? (
                  <div className="d-flex flex-column align-items-center p-0 m-0 mb-4">
                    <span
                      className="material-symbols-outlined icon-primary align-self-center align-self-center"
                      style={{
                        fontSize: "60px"
                      }}
                    >
                      payments
                    </span>
                    <h5 className="fw-bolder mt-4 mb-4">{`Earn cashback with ${PLAN_CONFIG.paid_low.name}!`}</h5>
                    <p className="text-muted text-center mt-2 mb-5">
                      {`Upgrade to Wealthyhood ${PLAN_CONFIG.paid_low.name} and earn ${formatPercentage(
                        CASHBACK_RATES["paid_low"],
                        locale
                      )} cash back every time you invest ${formatCurrency(
                        MINIMUM_AMOUNT_FOR_CASHBACK,
                        user.currency,
                        locale
                      )} or more! Start growing your portfolio faster today.`}
                    </p>
                    <button
                      className="btn btn-primary fw-100 my-4"
                      onClick={() => (window.location.href = "/investor/change-plan?select=paid_low_monthly")}
                    >
                      Upgrade to {PLAN_CONFIG.paid_low.name}
                    </button>
                  </div>
                ) : (
                  <div className="d-flex flex-column align-items-center p-0 m-0 mb-4">
                    <span
                      className="material-symbols-outlined icon-primary align-self-center align-self-center"
                      style={{
                        fontSize: "60px"
                      }}
                    >
                      payments
                    </span>
                    <h5 className="fw-bolder mt-4 mb-4">{`Earn cashback with ${PLAN_CONFIG[plan].name}!`}</h5>
                    <p className="text-muted text-center mt-2 mb-5">
                      {`Earn ${formatPercentage(
                        CASHBACK_RATES[plan],
                        locale
                      )} cash back every time you invest ${formatCurrency(
                        MINIMUM_AMOUNT_FOR_CASHBACK,
                        user.currency,
                        locale
                      )} or more with your Wealthyhood ${
                        PLAN_CONFIG[plan].name
                      } subscription! Make every investment a bit more rewarding!`}
                    </p>
                    <button
                      className="btn btn-primary fw-100 my-4"
                      onClick={() => this._setViewMode(this.state.viewModeBeforeCashback)}
                    >
                      Got it!
                    </button>
                  </div>
                )}
              </div>
            </div>
          </Modal.Body>
        )}
        {/* End Earn Cashback Info */}
      </Modal>
    );
  }
}

PortfolioBuyModal.contextType = GlobalContext;

export default PortfolioBuyModal;
