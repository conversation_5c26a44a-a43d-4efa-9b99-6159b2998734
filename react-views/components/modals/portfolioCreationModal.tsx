import React, { Component } from "react";
import { Modal } from "react-bootstrap";
import PortfolioCreationOption from "../portfolioCreationOption";
import Cookies from "universal-cookie";

export type PortfolioCreationModalPropsType = {
  show: boolean;
  handleClose: () => void;
  isRoboAdvisorEnabled: boolean;
};

export default class PortfolioCreationModal extends Component<PortfolioCreationModalPropsType> {
  private _cookiesManager: Cookies;

  componentDidMount() {
    this._cookiesManager = new Cookies();
    this._cookiesManager.set(
      "overridePortfolioCreationSuccess",
      "/portfolios/target?triggerTargetAllocationSuccessModal=true"
    );
  }

  private _onPortfolioPersonalisationClick() {
    window.location.href = "/portfolios/creation-template-info?useSavedPersonalisationPreferences=false";
  }

  private _onRoboAdvisorClick() {
    window.location.href = "/portfolios/creation-robo-advisor";
  }

  render() {
    const { show, handleClose, isRoboAdvisorEnabled } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="max-w-600px">
        <Modal.Header className="border-bottom-0 pb-0 justify-content-end" closeButton></Modal.Header>
        <Modal.Body className="pt-0 pb-5 px-5">
          <div className="d-flex flex-column m-0 p-0 pt-3">
            <h3 className="fw-bold mb-3">Get your new portfolio</h3>
            <p className="p-0 text-muted mb-5">
              Create your target portfolio from the ground up using our portfolio builder or model portfolios. Your
              new target portfolio will overwrite your current one.
            </p>
            <div className={"d-flex flex-column p-0 gap-4"}>
              <PortfolioCreationOption
                title="Use our portfolio builder"
                description="Set your preferences, get your template and customise it!"
                onSelectionCb={this._onPortfolioPersonalisationClick}
                icon="/images/icons/pie-chart-3.png"
              />
              {isRoboAdvisorEnabled && (
                <PortfolioCreationOption
                  title="Ready-made portfolio"
                  description="Diversified portfolio based on your risk appetite, created and actively managed by Vanguard!"
                  onSelectionCb={this._onRoboAdvisorClick}
                  icon="/images/icons/robo-advisor.png"
                />
              )}
            </div>
          </div>
        </Modal.Body>
      </Modal>
    );
  }
}
