import React from "react";
import { Modal } from "react-bootstrap";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { compareAssets, getAssetIconUrl } from "../../utils/universeUtil";
import ConfigUtil from "../../../utils/configUtil";
import PortfolioReviewAssetRow from "../portfolioReviewAssetRow";
import InfoModal from "./infoModal";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { GlobalContext } from "../../contexts/globalContext";

export type AllocationActionType = "toAdd" | "toRemove";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  portfolioDifferences: {
    assetsInCurrentPortfolioButNotInTargetPortfolio: investmentUniverseConfig.AssetType[];
    assetsInTargetPortfolioButNotInCurrentPortfolio: investmentUniverseConfig.AssetType[];
  };
  onButtonClick: (pendingAllocation: {
    toAdd: investmentUniverseConfig.AssetType[];
    toRemove: investmentUniverseConfig.AssetType[];
  }) => void;
};

type StateType = {
  showInfoLearnWhyModal: boolean;
  // Tracks assets to be added or removed from the target allocation during review.
  pendingAllocation: {
    toAdd: investmentUniverseConfig.AssetType[];
    toRemove: investmentUniverseConfig.AssetType[];
  };
};

class PortfolioReviewModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showInfoLearnWhyModal: false,
      pendingAllocation: { toAdd: [], toRemove: [] }
    };
  }

  private _showInfoLearnWhyModal() {
    this.setState({ showInfoLearnWhyModal: true });
  }

  private _hideInfoLearnWhyModal() {
    this.setState({ showInfoLearnWhyModal: false });
  }

  private _resetPendingAllocation() {
    this.setState({ pendingAllocation: { toAdd: [], toRemove: [] } });
  }

  private _closeAndDiscardReviewModal() {
    const { handleClose } = this.props;
    this.setState({ pendingAllocation: { toAdd: [], toRemove: [] } }, () => {
      handleClose();
    });
  }

  private _closeAndSubmitPendingAllocation(pendingAllocation: {
    toAdd: investmentUniverseConfig.AssetType[];
    toRemove: investmentUniverseConfig.AssetType[];
  }) {
    const { onButtonClick } = this.props;
    this.setState({ pendingAllocation: { toAdd: [], toRemove: [] } }, () => {
      onButtonClick(pendingAllocation);
    });
  }

  /*
   * This function adds/removes assets from pending Allocation
   */
  private _toggleAssetFromPendingAllocation = (
    assetCommonId: investmentUniverseConfig.AssetType,
    allocationType: AllocationActionType
  ): void => {
    this.setState((prevState) => {
      // Determine if the asset is already included in the specific action list
      const isAssetIncluded = prevState.pendingAllocation[allocationType].includes(assetCommonId);

      // Update the list based on whether the asset is already included
      const updatedActionList = isAssetIncluded
        ? prevState.pendingAllocation[allocationType].filter((id) => id !== assetCommonId) // Remove asset if included
        : [...prevState.pendingAllocation[allocationType], assetCommonId]; // Add asset if not included

      // Construct the new pendingAllocation object with the updated action list
      const updatedPendingAllocation = {
        ...prevState.pendingAllocation,
        [allocationType]: updatedActionList
      };

      return {
        ...prevState,
        pendingAllocation: updatedPendingAllocation
      };
    });
  };

  private _getPendingUniverse = (): Set<investmentUniverseConfig.AssetType> => {
    const pendingAllocation = this.state.pendingAllocation;
    const pendingAssetAllocation = [...pendingAllocation.toAdd, ...pendingAllocation.toRemove];
    return new Set([...(pendingAssetAllocation as investmentUniverseConfig.AssetType[])]);
  };

  private _renderPendingAssetsList(
    pendingAssetAllocation: investmentUniverseConfig.AssetType[],
    action: AllocationActionType
  ) {
    const selectedUniverse = this._getPendingUniverse();
    return (
      <div className="row my-4">
        {pendingAssetAllocation
          .sort(compareAssets)
          .map((asset) =>
            this._createPortfolioSetupRow(
              asset,
              selectedUniverse.has(asset),
              this._toggleAssetFromPendingAllocation,
              action
            )
          )}
      </div>
    );
  }

  private _createPortfolioSetupRow(
    asset: investmentUniverseConfig.AssetType,
    isSelected: boolean,
    onAssetClick: (assetKey: investmentUniverseConfig.AssetType, action: AllocationActionType) => void,
    action: AllocationActionType
  ) {
    const ASSET_CONFIG = ConfigUtil.getInvestmentUniverseAssets(this.context.user.companyEntity);
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[asset];
    return (
      <PortfolioReviewAssetRow
        key={asset}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(asset)}
        isSelected={isSelected}
        onAssetClick={(): void => onAssetClick(asset, action)}
        category={category}
        action={action}
      />
    );
  }

  render(): JSX.Element {
    const { show, portfolioDifferences } = this.props;
    const { showInfoLearnWhyModal, pendingAllocation } = this.state;
    const numberOfChangesToReview =
      portfolioDifferences.assetsInCurrentPortfolioButNotInTargetPortfolio.length +
      portfolioDifferences.assetsInTargetPortfolioButNotInCurrentPortfolio.length;
    const isPendingAllocationEdited = Boolean(pendingAllocation.toAdd.length || pendingAllocation.toRemove.length);

    return (
      <>
        <Modal
          show={show}
          onHide={() => this._closeAndDiscardReviewModal()}
          size="lg"
          dialogClassName="p-md-5 max-w-700px"
        >
          <Modal.Header className="p-md-5 p-3 pb-md-0 pb-0 border-bottom-0 ">
            <div className="row justify-content-end text-end w-100">
              <div className="col p-0">
                <i className="fas fa-times cursor-pointer" onClick={() => this._closeAndDiscardReviewModal()} />
              </div>
            </div>
          </Modal.Header>
          <Modal.Body>
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ maxWidth: "600px" }}>
                {/* Action Title */}
                <h4 className="fw-bolder mb-5 mt-3">{numberOfChangesToReview} changes to review</h4>
                {/* End Action Title */}
                {/*/!* About *!/*/}
                <div className="row p-0 m-0 mb-4">
                  <div className="col-11 align-self-center p-0 pe-1">
                    {/* Description */}
                    <p className="text-muted">
                      Your holdings don&apos;t match your target portfolio allocation. Review the changes below.{" "}
                      <a
                        style={{ color: "#536AE3", cursor: "pointer" }}
                        onClick={() => this._showInfoLearnWhyModal()}
                      >
                        Learn why
                      </a>
                    </p>
                    {/* End Description */}
                  </div>
                </div>
                {portfolioDifferences.assetsInCurrentPortfolioButNotInTargetPortfolio.length > 0 ? (
                  <div>
                    <h6 className="fw-bolder">Add new assets to your target</h6>
                    {this._renderPendingAssetsList(
                      portfolioDifferences.assetsInCurrentPortfolioButNotInTargetPortfolio,
                      "toAdd"
                    )}
                  </div>
                ) : null}
                {portfolioDifferences.assetsInTargetPortfolioButNotInCurrentPortfolio.length > 0 ? (
                  <div>
                    <h6 className="fw-bolder">Remove assets from your target</h6>
                    {this._renderPendingAssetsList(
                      portfolioDifferences.assetsInTargetPortfolioButNotInCurrentPortfolio,
                      "toRemove"
                    )}
                  </div>
                ) : null}
                <div className="d-flex justify-content-center">
                  <LoadingOnSubmitButton
                    className="btn btn-primary update-and-review-btn "
                    customonclick={async () => this._closeAndSubmitPendingAllocation(pendingAllocation)}
                    disabled={!isPendingAllocationEdited}
                  >
                    Update & review your allocation
                  </LoadingOnSubmitButton>
                </div>
                <div className="row m-0 mt-md-2 p-0">
                  {isPendingAllocationEdited ? (
                    <button
                      type="button"
                      className="btn btn-link text-decoration-none"
                      style={{ marginBottom: "1rem" }}
                      onClick={() => this._resetPendingAllocation()}
                    >
                      <span>{"Discard changes"}</span>
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="btn btn-link text-decoration-none"
                      style={{ marginBottom: "1rem" }}
                      onClick={() => this._closeAndDiscardReviewModal()}
                    >
                      <span>{"Maybe later"}</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </Modal.Body>
        </Modal>

        <InfoModal
          title={"Why review the changes?"}
          show={showInfoLearnWhyModal}
          handleClose={() => this._hideInfoLearnWhyModal()}
          dialogClassName={"max-w-600px"}
        >
          <p className={"text-muted"}>
            Your current holdings are different from your target portfolio. This is because you’ve:
          </p>
          <p className={"text-muted"}>
            <b style={{ color: "black", fontWeight: 500 }}>Bought some assets</b> that are currently not included
            in your target portfolio and/or
          </p>
          <p className={"text-muted"}>
            <b style={{ color: "black", fontWeight: 500 }}>Entirely sold some assets</b> that are currently
            included in your target portfolio.
          </p>
          <p className={"text-muted"}>
            <b style={{ color: "black", fontWeight: 500 }}>Updated your target portfolio</b> without rebalancing
            your holdings.
          </p>
          <p className={"text-muted"}>
            This will not impact your investments at this point. However, if you decide to set up automated
            rebalancing, your holdings will be adjusted to match your target portfolio.
          </p>
          <p className={"text-muted"}>
            This is why it is important to regularly update your target portfolio to reflect your desired
            allocation, especially if you’re considering using automated rebalancing.
          </p>
          <h6 className={"mb-3 mt-4"}>What is your target portfolio?</h6>
          <p className={"text-muted"}>
            Remember, your target portfolio is like a recipe for your investments, saying how much of each type you
            want.
          </p>
          <p className={"text-muted"}>
            Your actual portfolio (holdings) is what you really have and you can see this in your Invest tab.
          </p>
        </InfoModal>
      </>
    );
  }
}

PortfolioReviewModal.contextType = GlobalContext;

export default PortfolioReviewModal;
