import React from "react";
import { Modal } from "react-bootstrap";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import axios from "axios";
import SuccessAnimatedIcon from "../icons/successAnimatedIcon";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";

export type ViewModeType = "REVIEW" | "SUCCESS";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  handleAutomatedRebalanceSetup: () => void;
};

type StateType = {
  viewMode: ViewModeType;
};

class PortfolioTargetAllocationAutomatedRebalanceModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "REVIEW"
    };
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private async _setupAutomatedRebalancing() {
    const { handleAutomatedRebalanceSetup } = this.props;

    try {
      await axios.post("/investor/setup-automated-rebalancing", {});
      this._setViewMode("SUCCESS");
      handleAutomatedRebalanceSetup();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
      throw err;
    }
  }

  render(): JSX.Element {
    const { show, handleClose } = this.props;
    const { viewMode } = this.state;

    return (
      <Modal
        show={show}
        onHide={(): void => {
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        <Modal.Body className="p-0 px-md-3 px-3">
          {viewMode == "REVIEW" && (
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ maxWidth: "400px" }}>
                <div className="d-flex justify-content-center">
                  <span
                    className="material-symbols-outlined text-primary align-self-center"
                    style={{ fontSize: "74px" }}
                  >
                    sync
                  </span>
                </div>
                {/* Action Title */}
                <h5 className="fw-bolder text-center mt-5 mb-4">Automated rebalancing</h5>
                {/* End Action Title */}
                {/* Desc */}
                <p className="text-muted fw-light">
                  By setting up monthly automated rebalancing, you help maintain the asset allocation you defined
                  for your target portfolio.
                </p>
                <p className="text-muted fw-light">
                  If activated, automated rebalancing will take place on the first Monday of the month, placing buy
                  and sell orders to get your holdings back to your target portfolio.
                </p>
                <p className="text-muted fw-light mb-5">
                  You can always manage your automation settings through Autopilot.
                </p>
                {/* End Desc */}

                {/* Buttons */}
                <div className="row m-0 mb-4 w-100 text-center">
                  <LoadingOnSubmitButton
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    customonclick={() => this._setupAutomatedRebalancing()}
                  >
                    Set automated rebalancing
                  </LoadingOnSubmitButton>
                </div>
                <div className="row m-0 w-100 text-center">
                  <div
                    className="text-primary cursor-pointer mb-5 "
                    style={{ maxWidth: "100% !important" }}
                    onClick={handleClose}
                  >
                    Maybe later
                  </div>
                </div>
                {/* End Buttons*/}
              </div>
            </div>
          )}
          {viewMode == "SUCCESS" && (
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ maxWidth: "400px" }}>
                <div className="d-flex justify-content-center">
                  <SuccessAnimatedIcon />
                </div>
                <h5 className="fw-bolder text-center mt-5 mb-4">
                  Your automated rebalancing was set up successfully!
                </h5>
                <p className="text-center fw-light text-muted mb-5">
                  You can always manage your automation settings through Autopilot.
                </p>

                {/* Buttons */}
                <div className="row m-0 mb-4 w-100 text-center">
                  <button
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    onClick={handleClose}
                  >
                    Done
                  </button>
                </div>
                {/* End Buttons*/}
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    );
  }
}

export default PortfolioTargetAllocationAutomatedRebalanceModal;
