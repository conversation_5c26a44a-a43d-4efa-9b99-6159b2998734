import React from "react";
import { Modal } from "react-bootstrap";
import SuccessAnimatedIcon from "../icons/successAnimatedIcon";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import axios from "axios";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";

export type ViewModeType = "REVIEW" | "SUCCESS";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  userIsUninvested: boolean;
  hasActiveRebalanceAutomation: boolean;
  handleAutomatedRebalanceSetup: () => void;
};

type StateType = {
  viewMode: ViewModeType;
};

class PortfolioTargetAllocationUpdateSuccessModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "REVIEW"
    };
  }

  private _hideModal() {
    const { handleClose } = this.props;

    handleClose();
    this._setViewMode("REVIEW");
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private async _setupAutomatedRebalancing() {
    const { handleAutomatedRebalanceSetup } = this.props;

    try {
      await axios.post("/investor/setup-automated-rebalancing", {});
      this._setViewMode("SUCCESS");
      handleAutomatedRebalanceSetup();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
      throw err;
    }
  }

  private _getCTAConfig() {
    const { hasActiveRebalanceAutomation, userIsUninvested } = this.props;

    if (userIsUninvested) {
      return {
        subtitle: "Hooray! Do you want to make your first investment in your new portfolio?",
        cta: "Make your first investment",
        action: () => eventEmitter.emit(EVENTS.portfolioBuyModal),
        shouldShowMaybeLater: true
      };
    } else if (!hasActiveRebalanceAutomation) {
      return {
        subtitle:
          "Do you want to set automated rebalancing to maintain your target allocation every month? You can always manage your automation settings through Autopilot.",
        cta: "Set automated rebalancing",
        action: () => this._setupAutomatedRebalancing(),
        shouldShowMaybeLater: true
      };
    } else {
      return {
        subtitle:
          "When your next Automated Rebalancing takes place, your investments will be adjusted to your target allocation. You can always manage your automation settings through Autopilot.",
        cta: "Done",
        action: () => this._hideModal(),
        shouldShowMaybeLater: false
      };
    }
  }

  render(): JSX.Element {
    const { show } = this.props;
    const { viewMode } = this.state;

    const config = this._getCTAConfig();
    if (!config) {
      this._hideModal();
      return <></>;
    }

    return (
      <Modal show={show} onHide={(): void => this._hideModal()} size="xl" dialogClassName="p-md-5 max-w-600px">
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>
        <Modal.Body className="p-0 px-md-3 px-3">
          {viewMode === "REVIEW" && (
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ maxWidth: "400px" }}>
                <div className="d-flex justify-content-center">
                  <SuccessAnimatedIcon />
                </div>
                {/* Action Title */}
                <h5 className="fw-bolder text-center mb-5 mt-3">Your target portfolio was updated!</h5>
                {/* End Action Title */}
                {/* Desc */}
                <p className="text-muted mb-5 text-center">{config.subtitle}</p>
                {/* End Desc */}

                {/* Buttons */}
                <div className="row m-0 mb-4 w-100 text-center">
                  <LoadingOnSubmitButton
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    customonclick={async () => {
                      await config.action();
                    }}
                  >
                    {config.cta}
                  </LoadingOnSubmitButton>
                </div>
                {config.shouldShowMaybeLater ? (
                  <div className="row m-0 w-100 text-center">
                    <div
                      className="text-primary cursor-pointer mb-5 "
                      style={{ maxWidth: "100% !important" }}
                      onClick={() => {
                        this._hideModal();
                      }}
                    >
                      Maybe later
                    </div>
                  </div>
                ) : (
                  <></>
                )}
                {/* End Buttons*/}
              </div>
            </div>
          )}
          {viewMode == "SUCCESS" && (
            <div className="d-flex align-self-center justify-content-center">
              <div style={{ maxWidth: "400px" }}>
                <div className="d-flex justify-content-center">
                  <SuccessAnimatedIcon />
                </div>
                <h5 className="fw-bolder text-center mt-5 mb-4">
                  Your automated rebalancing was set up successfully!
                </h5>
                <p className="text-center fw-light text-muted mb-5">
                  You can always manage your automation settings through Autopilot.
                </p>

                {/* Buttons */}
                <div className="row m-0 mb-4 w-100 text-center">
                  <button
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    onClick={() => {
                      this._hideModal();
                    }}
                  >
                    Done
                  </button>
                </div>
                {/* End Buttons*/}
              </div>
            </div>
          )}
        </Modal.Body>
      </Modal>
    );
  }
}

export default PortfolioTargetAllocationUpdateSuccessModal;
