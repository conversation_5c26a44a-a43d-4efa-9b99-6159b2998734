import React from "react";
import { Modal } from "react-bootstrap";
import { currenciesConfig, investmentsConfig, localeConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { LinkedBankAccount } from "../../types/bank";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { convertIntoRecurrenceDate } from "../../utils/dateUtil";
import { OtherModalType } from "../modalsWrapper";
import { MandateDocument } from "../../../models/Mandate";
import { BankAccountDocument } from "../../../models/BankAccount";
import TransactionModalTargetAllocationSelector, {
  AllocationMethodEnum
} from "../transactionModalTargetAllocationSelector";
import { HoldingsType } from "../../../models/Portfolio";
import SuccessAnimatedIcon from "../icons/successAnimatedIcon";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import DayOfMonthSelectionDropdown from "./dayOfMonthSelectionDropdown";
import AllocationMethodSelect from "../allocationMethodSelect";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import AddBankAccountButton from "../addBankAccountButton";

const { MIN_ALLOWED_RECURRING_TOP_UP, MAX_ALLOWED_RECURRING_TOP_UP } = investmentsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

type ViewModeType =
  | "ACTION"
  | "BANK_SELECT"
  | "REVIEW"
  | "MONTHLY_INVESTMENT_INFO"
  | "SUCCESS"
  | "ALLOCATION_METHOD_SELECTION";

type TransactionStatusType =
  | "allowedBankTopUpBuy"
  | "smallerThanMinimumRecurringAmount"
  | "largerThanMinimumRecurringAmount"
  | "noInvestmentBankBuy";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  linkedBankAccounts: LinkedBankAccount[];
  activeOrPendingMandates: MandateDocument[];
  initialOrderAmount: number;
  initialSelectedLinkedBankAccount: LinkedBankAccount;
  holdings: HoldingsType[];
  isPortfolioAllocationSetup: boolean;
  postponeActivation: boolean;
};
type StateType = {
  orderAmount: string;
  selectedLinkedBankAccount: LinkedBankAccount;
  selectedDayOfMonth: number;
  viewMode: ViewModeType;
  actionButtonClicked: boolean;
  buyTargetAllocation: boolean;
};

class PromptToRepeatingInvestmentModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      orderAmount: props.initialOrderAmount.toString(),
      selectedLinkedBankAccount: props.initialSelectedLinkedBankAccount ?? this._getBankAccountForRecurringTopUp(),
      selectedDayOfMonth: convertIntoRecurrenceDate(new Date(Date.now())),
      viewMode: "ACTION",
      actionButtonClicked: false,
      buyTargetAllocation: this._getBuyTargetAllocationDefaultValue(
        props.isPortfolioAllocationSetup,
        props.holdings
      )
    };
  }

  componentDidUpdate(prevProps: PropsType) {
    if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(this.props.initialSelectedLinkedBankAccount);
    } else if (this.props.linkedBankAccounts !== prevProps.linkedBankAccounts) {
      this._setSelectedLinkedBankAccount(this.props.linkedBankAccounts[0]);
    }

    if (this.props.initialOrderAmount !== prevProps.initialOrderAmount) {
      this._setOrderAmount(this.props.initialOrderAmount.toString());
    }
  }

  private _getBuyTargetAllocationDefaultValue = (
    isPortfolioAllocationSetup: boolean,
    holdings: HoldingsType[]
  ): boolean => {
    if (!isPortfolioAllocationSetup) {
      return false;
    }

    if (holdings.length === 0) {
      return true;
    }

    // Default value
    return false;
  };

  private _getStatusMessage = (
    status: TransactionStatusType,
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { actionButtonClicked } = this.state;

    if (!actionButtonClicked) {
      return;
    }

    const STATUS_MESSAGES: Record<TransactionStatusType, string> = {
      allowedBankTopUpBuy: "",
      smallerThanMinimumRecurringAmount: `The minimum repeating investment is ${formatCurrency(
        MIN_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale,
        0,
        0
      )}.`,
      largerThanMinimumRecurringAmount: `The maximum repeating investment is ${formatCurrency(
        MAX_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale,
        0,
        0
      )}.`,
      noInvestmentBankBuy: ""
    };

    return STATUS_MESSAGES[status];
  };

  private _getTransactionStatus = (): TransactionStatusType => {
    const { orderAmount } = this.state;

    const orderAmountNum = Number(orderAmount);

    if (orderAmountNum === 0) {
      return "noInvestmentBankBuy";
    }
    if (orderAmountNum < MIN_ALLOWED_RECURRING_TOP_UP) {
      return "smallerThanMinimumRecurringAmount";
    }
    if (orderAmountNum > MAX_ALLOWED_RECURRING_TOP_UP) {
      return "largerThanMinimumRecurringAmount";
    }

    return "allowedBankTopUpBuy";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _setSelectedDayOfMonth = (selectedDayOfMonth: number): void => {
    this.setState({ selectedDayOfMonth });
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setBankAccount = (selectedLinkedBankAccount: LinkedBankAccount): void => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _setBuyTargetAllocation(buyTargetAllocation: boolean) {
    this.setState({ buyTargetAllocation });
  }

  private _reviewPayment = async () => {
    if (!this._isTransactionAllowed()) {
      return;
    }

    const { linkedBankAccounts } = this.props;

    // When user clicks on review, we move to the 'REVIEW' view mode unless they're updating an existing automation.
    if (linkedBankAccounts.length === 0) {
      eventEmitter.emit(EVENTS.linkBankAccountModal, {
        originModal: "promptToRepeatingInvestmentModal" as OtherModalType
      });
    } else {
      await this._setupAutomation();
    }
  };

  /**
   * When selecting a recurring frequency, we select a bank account with a pending/active mandate. If there isn't any,
   * we select the first linked bank account in the list.
   */
  private _getBankAccountForRecurringTopUp = (): LinkedBankAccount => {
    const { linkedBankAccounts, activeOrPendingMandates } = this.props;

    const bankAccountsWithPendingOrActiveMandate = activeOrPendingMandates
      .filter((mandate) => mandate.status !== "Inactive")
      .map((mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString());

    return (
      linkedBankAccounts.find((bankAccount) => bankAccountsWithPendingOrActiveMandate.includes(bankAccount.id)) ||
      linkedBankAccounts[0]
    );
  };

  private _setSelectedLinkedBankAccount = (selectedLinkedBankAccount: LinkedBankAccount): void => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _shouldCreateMandateForSelectedBankAccount(): boolean {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount } = this.state;

    return (
      activeOrPendingMandates.length === 0 ||
      activeOrPendingMandates.every(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() !== selectedLinkedBankAccount?.id
      )
    );
  }

  private async _setupAutomation() {
    const { activeOrPendingMandates, postponeActivation } = this.props;
    const { selectedLinkedBankAccount, orderAmount, buyTargetAllocation, selectedDayOfMonth } = this.state;

    try {
      if (this._shouldCreateMandateForSelectedBankAccount()) {
        await axios.post("investor/setup-recurring-topup", {
          postponeActivation,
          bankAccount: selectedLinkedBankAccount,
          orderAmount,
          dayOfMonth: selectedDayOfMonth,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          frequency: "monthly"
        });

        this.setState({ viewMode: "SUCCESS" });
      } else {
        await axios.post("investor/setup-recurring-topup", {
          postponeActivation,
          orderAmount,
          frequency: "monthly",
          dayOfMonth: selectedDayOfMonth,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          mandate: activeOrPendingMandates.find(
            (mandate) =>
              (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
          ).id
        });

        this.setState({ viewMode: "SUCCESS" });
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _getActionViewCTACopy(): string {
    const { linkedBankAccounts } = this.props;

    if (!linkedBankAccounts || linkedBankAccounts?.length === 0) {
      return "Proceed";
    } else {
      return "Set up monthly investments";
    }
  }

  private _isTransactionAllowed(): boolean {
    return this._getTransactionStatus() == "allowedBankTopUpBuy";
  }

  render(): JSX.Element {
    const { show, handleClose, linkedBankAccounts, initialOrderAmount, holdings, isPortfolioAllocationSetup } =
      this.props;
    const {
      orderAmount,
      selectedLinkedBankAccount,
      selectedDayOfMonth,
      viewMode,
      actionButtonClicked,
      buyTargetAllocation
    } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount(initialOrderAmount.toString());
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          this.state.viewMode == "SUCCESS";
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0">
            <TransactionModalBackButton
              onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
              title={"Repeating"}
              subtitle={"Investment"}
            />
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode == "ACTION" && (
          <>
            <Modal.Body className="p-0 px-md-3 px-3 fade-in" style={{ maxHeight: "700px" }}>
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Action Title */}
                  <div className="mb-3 autopilot-prompt-center">
                    <img
                      className="mb-3"
                      style={{ width: "140px", height: "120px" }}
                      alt="Autopilot!"
                      src={"/images/icons/autopilot.png"}
                    />
                    {/* Action Title */}
                    <h4 className="fw-bolder text-center mb-5 mt-3">Set up a monthly investment!</h4>
                    {/* End Action Title */}
                    <span>
                      Monthly investments (aka Dollar-Cost Averaging) help you stay on track with your long-term
                      goals!
                    </span>
                  </div>
                  {/* End Action Title */}

                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-3">
                    <OrderInput
                      amount={orderAmount}
                      placeholderAmount={150}
                      key={"key-buy"}
                      onAmountChange={(amount) => {
                        const { orderAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != orderAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._setOrderAmount(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                      maxNumberLimit={100000}
                    />
                  </div>
                  {/* End Order Input */}

                  <div className="d-flex justify-content-center mb-5">
                    <DayOfMonthSelectionDropdown
                      handleOnDayOfMonthChange={this._setSelectedDayOfMonth}
                      selectedDayOfMonth={selectedDayOfMonth}
                      includeFrequencyKeyword={true}
                    />
                  </div>

                  <p
                    className={`text-center ${
                      this._isTransactionAllowed() || !actionButtonClicked ? "text-muted" : "text-danger"
                    } mb-4`}
                  >
                    {this._getStatusMessage(transactionStatus, user.currency, locale)}
                  </p>

                  {/* Selected Payment Method Field */}
                  {selectedLinkedBankAccount && (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                      availableCash={0}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      frequency="MONTHLY"
                    />
                  )}
                  {/* Selected Payment Method Field */}
                  {/* Buy target allocation switch */}
                  <div className="mt-2">
                    <TransactionModalTargetAllocationSelector
                      onClick={() => this._setViewMode("ALLOCATION_METHOD_SELECTION")}
                      allocationMethod={
                        buyTargetAllocation
                          ? AllocationMethodEnum.TARGET_ALLOCATION
                          : AllocationMethodEnum.HOLDINGS
                      }
                    />
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-3 justify-content-center fade-in mb-3" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {/* End Buy target allocation switch */}
                {orderAmount ? (
                  <LoadingOnSubmitButton
                    className="btn btn-primary fw-100"
                    enableOnCompletion={true}
                    customonclick={async () => {
                      this._setActionButtonClicked(true);
                      await this._reviewPayment();
                    }}
                  >
                    {this._getActionViewCTACopy()}
                  </LoadingOnSubmitButton>
                ) : (
                  <button type="button" className="btn btn-primary fw-100" disabled={true}>
                    {this._getActionViewCTACopy()}
                  </button>
                )}

                <button
                  className="btn d-flex justify-content-center me-2 mt-3"
                  onClick={() => this._setViewMode("MONTHLY_INVESTMENT_INFO")}
                >
                  <span
                    className="material-symbols-outlined cursor-pointer align-self-center text-primary me-1"
                    style={{
                      fontSize: "16px"
                    }}
                  >
                    info
                  </span>
                  <span className="fw-bolder" style={{ color: "#536AE3" }}>
                    Why set up a monthly investment?
                  </span>
                </button>
              </div>
            </Modal.Footer>
          </>
        )}
        {/* End Action Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                    frequency={"MONTHLY"}
                    availableCash={0}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) =>
                      this._setBankAccount(selectedLinkedBankAccount)
                    }
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <AddBankAccountButton originModal={"promptToRepeatingInvestment" as OtherModalType} />
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Bank Select Modal Content */}

        {/* Monthly Investment Info Content */}
        {viewMode == "MONTHLY_INVESTMENT_INFO" && (
          <Modal.Body className="p-0 px-md-3 px-3 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                {/* Back Button */}
                <div className="row p-0 m-0 mb-md-4">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                      onClick={() => this._setViewMode("ACTION")}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}
                <div className="row p-0 m-0 mb-4">
                  <h5 className="mb-4">Why set up a monthly investment?</h5>
                  <p className="text-muted">
                    Investing can be challenging. Even professional investors who try to time the market to buy at
                    the most opportune moments can come up short. This is why setting up repeating investments aka
                    Dollar-Cost Averaging can be the way to go for both beginners and long-term investors!
                  </p>
                  <h5 className="mb-4">What Is Dollar-Cost Averaging (DCA)?</h5>

                  <p className="text-muted">
                    Dollar-cost averaging means investing the same amount of money in your target portfolio at
                    regular (usually monthly) intervals, regardless of price.
                  </p>
                  <p className="text-muted">
                    It means automating your investments, which also supports an investor&apos;s effort to invest
                    regularly.
                  </p>
                  <h5 className="mb-4">Why does it work?</h5>

                  <p className="text-muted">
                    By setting up monthly investments (DCA), investors may lower their average cost per share and
                    reduce the impact of volatility on their portfolios.
                  </p>
                  <p className="text-muted">
                    Simply put, by buying regularly in up and down periods, investors buy more shares at lower
                    prices and fewer shares at higher prices.
                  </p>
                  <p className="text-muted">
                    This strategy helps navigate the ups and downs of uncertain markets and eliminates the effort
                    required to time the market to buy at the best prices.
                  </p>
                  <h5 className="mb-4">Getting started</h5>
                  <p className="text-muted">
                    Here, you can decide the amount you want to invest and set up repeating monthly investments
                    instantly. You can always pause or manage your repeating investments anytime from your
                    Autopilot section.
                  </p>
                </div>
              </div>
            </div>
          </Modal.Body>
        )}
        {/* End Monthly Investment Info Content */}

        {/* Select Frequency Modal Content */}
        {viewMode == "ALLOCATION_METHOD_SELECTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <AllocationMethodSelect
                    allocationMethodSelected={
                      buyTargetAllocation ? AllocationMethodEnum.TARGET_ALLOCATION : AllocationMethodEnum.HOLDINGS
                    }
                    onAllocationMethodChange={(allocationMethod) =>
                      this._setBuyTargetAllocation(allocationMethod === AllocationMethodEnum.TARGET_ALLOCATION)
                    }
                    hasHoldings={holdings.length > 0}
                    isPortfolioAllocationSetup={isPortfolioAllocationSetup}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }} />
          </div>
        )}
        {/* End Select Frequency Modal Content */}

        {/* Success Modal Content */}
        {viewMode == "SUCCESS" && (
          <Modal.Body className="p-0 px-md-3 px-3 fade-in">
            <div className="d-flex align-self-center justify-content-center">
              <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                <div className="autopilot-prompt-center">
                  <SuccessAnimatedIcon />
                  <h5 className="fw-bolder text-center mt-5 mb-4">
                    Your monthly investment was set up successfully.
                  </h5>
                  <p className="text-center mb-5">
                    A Direct Debit has been set up. You’ll receive a separate confirmation email within 3 days. You
                    may cancel it at any time by contacting us or your bank.
                  </p>
                </div>
              </div>
            </div>
          </Modal.Body>
        )}
        {/* End Success Modal Content */}
      </Modal>
    );
  }
}

PromptToRepeatingInvestmentModal.contextType = GlobalContext;

export default PromptToRepeatingInvestmentModal;
