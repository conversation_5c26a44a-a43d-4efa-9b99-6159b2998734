import React from "react";
import { Modal } from "react-bootstrap";
import { RebalanceTransactionDocument } from "../../../models/Transaction";
import { formatCurrency } from "../../utils/currencyUtil";
import { formatDateToDDMONYY } from "../../utils/dateUtil";
import config, { STATUS_CONFIG } from "../../configs/transactionsTableConfig";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { emitToast } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { ViewModeType } from "../../types/modal";
import { OrderDocument } from "../../../models/Order";
import InvestorOrderRow from "../investorOrderRow";
import OrderReceipt from "../orderReceipt";
import { InvestmentProductDocument } from "../../../models/InvestmentProduct";
import { AssetPriceInfo } from "../../types/price";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import HoverableInfoIcon from "../hoverableInfoIcon";
import { getLocalExecutionWindowStart } from "../../utils/executionWindowUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  transaction: RebalanceTransactionDocument;
  show: boolean;
  investmentProducts: InvestmentProductDocument[];
  handleClose: () => void;
};

type StateType = {
  viewMode: ViewModeType;
  selectedOrder: OrderDocument;
};

class RebalanceReceiptModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      viewMode: "REVIEW",
      selectedOrder: null
    };
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private _setSelectedOrder(selectedOrder: OrderDocument) {
    this.setState({ selectedOrder });
  }

  private _cancelTransaction = async () => {
    const { transaction } = this.props;

    try {
      const res = await axios.post(`/transactions/${transaction.id}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  private _getAssetPriceInfoForIsin(targetIsin: string): AssetPriceInfo {
    const { investmentProducts } = this.props;
    const investmentProduct = investmentProducts.find(({ commonId }) => ASSET_CONFIG[commonId].isin == targetIsin);
    return {
      tradedPrice: investmentProduct?.tradedPrice,
      tradedCurrency: investmentProduct?.tradedCurrency,
      currentTickerPrice: investmentProduct?.currentTicker?.price
    };
  }

  render(): JSX.Element {
    const { transaction, show, handleClose } = this.props;
    const { viewMode, selectedOrder } = this.state;
    const { locale, user } = this.context as GlobalContextType;

    const { displayStatus, fees, buyExecutionWindow, sellExecutionWindow, createdAt, settledAt, orders } =
      transaction;
    const { icon } = config[transaction.category];
    const commission = fees?.commission?.amount ?? 0;

    const isCancelled = displayStatus == "Cancelled";
    let price;
    if (selectedOrder?.isMatched) {
      price = selectedOrder?.displayUnitPrice;
    } else if (selectedOrder) {
      price = this._getAssetPriceInfoForIsin(selectedOrder.isin);
    }

    return (
      <Modal show={show} onHide={handleClose}>
        <Modal.Header className="border-bottom-0" closeButton>
          <Modal.Title />
        </Modal.Header>

        <Modal.Body className="p-0 px-3 mb-5">
          {viewMode == "REVIEW" && (
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                {/* Action Title */}
                <div className="d-flex w-100 justify-content-center mb-3">
                  <i
                    className="material-symbols-outlined align-self-center icon-primary wh-card"
                    style={{ fontSize: "32px" }}
                  >
                    {icon}
                  </i>
                </div>
                <h5 className="fw-bolder text-center mb-5">My portfolio rebalance</h5>
                {/* End Action Title */}
                {transaction.linkedAutomation && transaction.displayStatus === "Pending" && (
                  <>
                    <div className="row pb-3 m-0 mb-3 border-bottom">
                      <div className="col-4 p-0 text-start">Repeating</div>
                      <div className="col-8 p-0 text-end">
                        <span className="fw-bolder">Every month</span>
                      </div>
                    </div>
                  </>
                )}
                {isCancelled ? (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start text-nowrap">Date</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder text-nowrap">{formatDateToDDMONYY(new Date(createdAt))}</span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="row pb-3 m-0 mb-3 border-bottom">
                      <div className="col p-0 text-start">
                        <div className="d-flex w-100">
                          <p className="m-0 align-self-center text-nowrap me-2">Commission</p>
                          <HoverableInfoIcon
                            hoverText={
                              "Wealthyhood is commission-free, which means we charge ZERO COMMISSIONS for all stocks and ETFs."
                            }
                            colorHex={"#536AE3"}
                          />
                        </div>
                      </div>
                      <div className="col p-0 text-end">
                        <span className="fw-bolder">
                          {formatCurrency(commission, fees?.commission?.currency ?? user.currency, locale, 2, 2)}
                        </span>
                      </div>
                    </div>
                  </>
                )}
                {settledAt && (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start text-nowrap">Date</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder text-nowrap">{formatDateToDDMONYY(new Date(settledAt))}</span>
                    </div>
                  </div>
                )}
                {!settledAt && !isCancelled && (
                  <>
                    <div className="row pb-3 m-0 mb-3 border-bottom">
                      <div className="col p-0 text-start text-nowrap">
                        <div className="d-flex w-100">
                          <p className="m-0 align-self-center text-nowrap me-2">Sell trading window</p>
                          <HoverableInfoIcon
                            hoverText={`Rebalancing takes place over two consecutive trading sessions. First, overweight assets are sold, and then, the proceedings are used to buy underweight assets. Both phases are executed within a dedicated trading window at ${getLocalExecutionWindowStart()} of the relevant session.`}
                            colorHex={"#536AE3"}
                          />
                        </div>
                      </div>
                      <div className="col p-0 text-end">
                        <span className="fw-bolder text-nowrap">
                          {transaction.hasSellExecutionStarted
                            ? "In progress"
                            : formatDateToDDMONYY(new Date(sellExecutionWindow.start))}
                        </span>
                      </div>
                    </div>
                    <div className="row pb-3 m-0 mb-3 border-bottom">
                      <div className="col p-0 text-start text-nowrap">
                        <div className="d-flex w-100">
                          <p className="m-0 align-self-center text-nowrap me-2">Buy trading window</p>
                          <HoverableInfoIcon
                            hoverText={`Rebalancing takes place over two consecutive trading sessions. First, overweight assets are sold, and then, the proceedings are used to buy underweight assets. Both phases are executed within a dedicated trading window at ${getLocalExecutionWindowStart()} of the relevant session.`}
                            colorHex={"#536AE3"}
                          />
                        </div>
                      </div>
                      <div className="col p-0 text-end">
                        <span className="fw-bolder text-nowrap">
                          {transaction.hasBuyExecutionStarted
                            ? "In progress"
                            : formatDateToDDMONYY(new Date(buyExecutionWindow.start))}
                        </span>
                      </div>
                    </div>
                  </>
                )}
                <div className="row pb-3 m-0 mb-3">
                  <div className="col p-0 text-start text-nowrap">Status</div>
                  <div className="col p-0 text-end">
                    <span className={`fw-bolder text-nowrap text-${STATUS_CONFIG[displayStatus].color}`}>
                      {displayStatus}
                    </span>
                  </div>
                </div>
                {orders?.length > 0 && (
                  <div className="row m-0">
                    <div className="col p-0 text-center">
                      <div
                        className="text-decoration-none text-primary cursor-pointer"
                        onClick={() => this._setViewMode("DETAILS")}
                      >
                        View all orders
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          {viewMode == "DETAILS" && (
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                {/* Back Button */}
                <div className="row p-0 m-0 mb-3">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                      onClick={() => this._setViewMode("REVIEW")}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}
                {/* Action Title */}
                <h5 className="fw-bolder text-center mb-5">My portfolio rebalance</h5>
                {/* End Action Title */}
                {orders.map((order, index) => (
                  <InvestorOrderRow
                    onClick={() => {
                      this._setSelectedOrder(order);
                      this._setViewMode("RECEIPT");
                    }}
                    currentPriceInfo={this._getAssetPriceInfoForIsin(order.isin)}
                    parentTransaction={transaction}
                    order={order}
                    key={`order_${index}`}
                  />
                ))}
              </div>
            </div>
          )}
          {viewMode == "RECEIPT" && selectedOrder && (
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                {/* Back Button */}
                <div className="row p-0 m-0 mb-3">
                  <div className="col p-0">
                    <span
                      className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                      onClick={() => this._setViewMode("DETAILS")}
                      style={{
                        fontSize: "24px"
                      }}
                    >
                      arrow_back
                    </span>
                  </div>
                </div>
                {/* End Back Button*/}
                <OrderReceipt
                  order={selectedOrder}
                  price={price}
                  executionWindow={transaction.buyExecutionWindow}
                  isPartOfPortfolioTransaction={true}
                />
              </div>
            </div>
          )}
        </Modal.Body>
        {transaction.isCancellable && (
          <Modal.Footer className="pt-0 pb-5 justify-content-center" style={{ borderTop: "none" }}>
            <div
              className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
              style={{ maxWidth: "400px" }}
            >
              <LoadingOnSubmitButton className="btn btn-danger fw-100" customonclick={this._cancelTransaction}>
                Cancel rebalance
              </LoadingOnSubmitButton>
            </div>
          </Modal.Footer>
        )}
      </Modal>
    );
  }
}

RebalanceReceiptModal.contextType = GlobalContext;

export default RebalanceReceiptModal;
