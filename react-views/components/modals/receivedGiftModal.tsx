import React from "react";
import { Modal } from "react-bootstrap";
import { GiftDocument } from "../../../models/Gift";
import { UserDocument } from "../../../models/User";
import { eventEmitter, EVENTS } from "../../utils/eventService";
import axios from "axios";
import { captureException } from "@sentry/react";
import { getUserInitials, isVerified, isVerifying } from "../../utils/userUtil";
import { formatCurrency } from "../../utils/currencyUtil";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";

type PropsType = {
  handleClose: () => void;
  show: boolean;
  gift: GiftDocument;
};

class ReceivedGiftModal extends React.Component<PropsType> {
  async componentDidMount() {
    const { gift } = this.props;

    try {
      await axios.post(`/gifts/${gift.id}`, { hasViewedAppModal: true });
    } catch (err) {
      captureException(err);
    }
  }

  private _getAction() {
    const { handleClose, gift } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    if (user.hasConvertedPortfolio) {
      return (
        <button
          type="button"
          className={"btn btn-primary w-100"}
          style={{ maxWidth: "unset" }}
          onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
        >
          Invest my{" "}
          {formatCurrency(
            Decimal.div(gift.consideration.amount, 100).toNumber(),
            gift.consideration.currency,
            locale,
            0,
            0
          )}{" "}
          gift
        </button>
      );
    } else if (isVerified(user)) {
      return (
        <button
          type="button"
          className={"btn btn-primary w-100"}
          style={{ maxWidth: "unset" }}
          onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
        >
          Invest my{" "}
          {formatCurrency(
            Decimal.div(gift.consideration.amount, 100).toNumber(),
            gift.consideration.currency,
            locale,
            0,
            0
          )}{" "}
          gift
        </button>
      );
    } else if (isVerifying(user)) {
      return (
        <button
          type="button"
          className={"btn btn-primary w-100"}
          style={{ maxWidth: "unset" }}
          onClick={handleClose}
        >
          Got it
        </button>
      );
    } else {
      return (
        <a
          className="btn btn-primary w-100"
          style={{ maxWidth: "unset" }}
          href="/investor/collect-personal-details"
        >
          Verify your account
        </a>
      );
    }
  }

  private _getWealthyhoodMessage() {
    const { gift } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    if (isVerified(user)) {
      return `Your ${formatCurrency(
        Decimal.div(gift.consideration.amount, 100).toNumber(),
        gift.consideration.currency,
        locale,
        0,
        0
      )} gift is now available for you to invest!`;
    } else if (isVerifying(user)) {
      return `You will unlock your ${formatCurrency(
        Decimal.div(gift.consideration.amount, 100).toNumber(),
        gift.consideration.currency,
        locale,
        0,
        0
      )} gift once your account has been verified!`;
    } else {
      return `You need to verify your account to unlock your ${formatCurrency(
        Decimal.div(gift.consideration.amount, 100).toNumber(),
        gift.consideration.currency,
        locale,
        0,
        0
      )} gift!`;
    }
  }

  render(): JSX.Element {
    const { handleClose, show, gift } = this.props;
    const { locale } = this.context as GlobalContextType;

    const gifter = gift.gifter as UserDocument;

    return (
      <Modal show={show} onHide={handleClose} size="lg" dialogClassName="p-md-5 max-w-600px border-radius-24px">
        <div className="fade-in">
          <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton />
          <Modal.Body className="px-4 py-5 d-flex justify-content-center">
            <div className="col-md-10 d-flex flex-column align-self-center justify-content-center">
              {gifter.firstName && gifter.lastName ? (
                <>
                  <div className={"d-flex align-items-center justify-content-center"} style={{ gap: "2rem" }}>
                    <div className="wh-avatar-img-lg me-2 text-white">{getUserInitials(gifter)}</div>
                    <i className="fas fa-arrow-right fa-lg text-primary" />
                    <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift.png"} />
                  </div>
                  <div className={"d-flex flex-column text-center mt-4 pt-2"}>
                    <p className={"mb-0 fw-bolder"}>
                      {gifter.firstName} {gifter.lastName}
                    </p>
                    <p>
                      has sent you a{" "}
                      <span className={"text-primary fw-bold"}>
                        {formatCurrency(
                          Decimal.div(gift.consideration.amount, 100).toNumber(),
                          gift.consideration.currency,
                          locale,
                          0,
                          0
                        )}{" "}
                        gift!
                      </span>
                    </p>
                  </div>
                  <div className={"d-flex align-items-start mt-4 pt-1"}>
                    <div className={"d-flex align-items-center"}>
                      <div className="wh-avatar-img-sm me-2 text-white">{getUserInitials(gifter)}</div>
                      <img alt="" src={"/images/gifts/gift-message-blob-corner.svg"} />
                    </div>
                    <p
                      className={"p-3 mb-0 t-875 text-muted"}
                      style={{
                        background: "#F1F3FD",
                        borderRadius: "12px",
                        marginLeft: "-2px",
                        maxWidth: "86%"
                      }}
                    >
                      {gift.message}
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <div className={"d-flex align-items-center justify-content-center"} style={{ gap: "2rem" }}>
                    <img
                      className="me-2"
                      style={{ width: "104px", height: "104px" }}
                      alt=""
                      src={"/images/avatars/astronaut-avatar.svg"}
                    />
                    <i className="fas fa-arrow-right fa-lg text-primary" />
                    <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift.png"} />
                  </div>
                  <div className={"d-flex flex-column text-center mt-4 pt-2"}>
                    <p className={"mb-0 fw-bolder"}>{gifter.email}</p>
                    <p>
                      has sent you a{" "}
                      <span className={"text-primary fw-bold"}>
                        {formatCurrency(
                          Decimal.div(gift.consideration.amount, 100).toNumber(),
                          gift.consideration.currency,
                          locale,
                          0,
                          0
                        )}{" "}
                        gift!
                      </span>
                    </p>
                  </div>
                  <div className={"d-flex align-items-start mt-4 pt-1"}>
                    <div className={"d-flex align-items-center"}>
                      <img
                        className="me-2"
                        style={{ width: "30px", height: "30px" }}
                        alt=""
                        src={"/images/avatars/astronaut-avatar.svg"}
                      />
                      <img alt="" src={"/images/gifts/gift-message-blob-corner.svg"} />
                    </div>
                    <p
                      className={"p-3 mb-0 t-875 text-muted"}
                      style={{
                        background: "#F1F3FD",
                        borderRadius: "12px",
                        marginLeft: "-2px",
                        maxWidth: "86%"
                      }}
                    >
                      {gift.message}
                    </p>
                  </div>
                </>
              )}
              <div className={"d-flex align-items-start pt-1"}>
                <div className={"d-flex align-items-center"}>
                  <div className="wh-avatar-img-sm me-2">
                    <img alt="" src={"/images/icons/wh-mini-logo-white.svg"} />
                  </div>
                  <img alt="" src={"/images/gifts/gift-message-blob-corner.svg"} />
                </div>
                <p
                  className={"p-3 mb-0 t-875 text-muted"}
                  style={{
                    background: "#F1F3FD",
                    borderRadius: "12px",
                    marginLeft: "-2px",
                    maxWidth: "86%"
                  }}
                >
                  {this._getWealthyhoodMessage()}
                </p>
              </div>
              <div className={"d-flex justify-content-center pt-5"}>{this._getAction()}</div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

ReceivedGiftModal.contextType = GlobalContext;

export default ReceivedGiftModal;
