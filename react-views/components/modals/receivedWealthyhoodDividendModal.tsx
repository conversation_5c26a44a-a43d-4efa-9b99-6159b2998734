import React, { Component } from "react";
import { Modal } from "react-bootstrap";
import { WealthyhoodDividendTransactionDocument } from "../../../models/Transaction";
import axios from "axios";
import { formatCurrency } from "../../utils/currencyUtil";
import { captureException } from "@sentry/react";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";

type PropsType = {
  whDividends: WealthyhoodDividendTransactionDocument[];
  show: boolean;
  handleClose: () => void;
};

export default class ReceivedWealthyhoodDividendModal extends Component<PropsType> {
  async componentDidMount() {
    const { whDividends } = this.props;

    try {
      await Promise.all([
        whDividends.map((whDividend) =>
          axios.post(`/transactions/wealthyhood-dividends/${whDividend.id}`, { hasViewedAppModal: true })
        )
      ]);
    } catch (err) {
      captureException(err);
    }
  }

  render() {
    const { whDividends, show, handleClose } = this.props;
    const { locale } = this.context as GlobalContextType;

    const multipleWhDividends = whDividends.length > 1;
    const totalDividendAmountInCents = whDividends.reduce(
      (sum, whDividend) => (sum += whDividend.displayAmount),
      0
    );

    // We assume user can not receive WH dividends in multiple currencies.
    const currency = whDividends[0].consideration.currency;

    return (
      <Modal show={show} dialogClassName="w-fit-content" onHide={handleClose}>
        <div className="fade-in">
          <Modal.Header className="justify-content-end border-bottom-0 pb-0 pt-2" closeButton></Modal.Header>
          <Modal.Body className="p-0 pb-4 px-5 d-flex flex-column align-items-center justify-content-center  bg-received-wh-dividend border-radius-24px">
            <h5 className="fw-bolder">{multipleWhDividends ? "You’ve received" : "You just received a"}</h5>
            <h5 className="fw-bolder mb-3 text-primary">
              {multipleWhDividends
                ? `${formatCurrency(
                    Decimal.div(totalDividendAmountInCents, 100).toNumber(),
                    currency,
                    locale
                  )} in bonus dividends!`
                : `${formatCurrency(
                    Decimal.div(totalDividendAmountInCents, 100).toNumber(),
                    currency,
                    locale
                  )} bonus dividend!`}
            </h5>

            <img src={"/images/icons/wh-dividend-lg.png"} style={{ height: "148px", width: "148px" }} />
            <button
              className="btn btn-primary fw-100 mt-3"
              onClick={() => (window.location.href = "/investor/cash")}
            >
              View more!
            </button>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

ReceivedWealthyhoodDividendModal.contextType = GlobalContext;
