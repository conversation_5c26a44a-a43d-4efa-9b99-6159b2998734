import React from "react";
import { Modal } from "react-bootstrap";
import { DepositCashTransactionDocument, SavingsTopupTransactionDocument } from "../../../models/Transaction";
import { SavingsTopUpAutomationDocument } from "../../../models/Automation";
import DirectDebitSchedule from "../directDebitSchedule";

type PropsType = {
  transaction: SavingsTopupTransactionDocument;
  automation: SavingsTopUpAutomationDocument;
  show: boolean;
  handleClose: () => void;
};

class RepeatingSavingsReceiptModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { transaction, show, handleClose, automation } = this.props;

    const deposit = transaction.pendingDeposit as DepositCashTransactionDocument;

    return (
      <Modal show={show} onHide={handleClose}>
        <div className="modal-content">
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          <Modal.Body className="p-0 px-3 mb-5">
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "400px" }}>
                <DirectDebitSchedule automation={automation} deposit={deposit} />
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

export default RepeatingSavingsReceiptModal;
