import { formatDateToDDMONYY, formatDateToHHMMSS } from "../../utils/dateUtil";
import { formatCurrency } from "../../utils/currencyUtil";
import React from "react";
import { Modal } from "react-bootstrap";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { RewardDocument } from "../../../models/Reward";
import AssetIcon from "../assetIcon";
import { getAssetIconUrl } from "../../utils/universeUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { getReportingFirm } from "../../utils/userUtil";
import { getTotalCommission } from "../../utils/feesUtil";
import FxRatePreviewRow from "../fxRatePreviewRow";
import axios from "axios";
import { captureException } from "@sentry/react";
import { emitToast } from "./../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  reward: RewardDocument;
  show: boolean;
  handleClose: () => void;
};

type StateType = {
  loadingTradeConfirmation: boolean;
};

class RewardReceiptModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      loadingTradeConfirmation: false
    };
  }
  private _shouldShowTradeConfirmation(): boolean {
    const { user } = this.context as GlobalContextType;

    return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE;
  }

  private _downloadTradeConfirmation = async (): Promise<void> => {
    const { reward } = this.props;
    this.setState({ loadingTradeConfirmation: true });

    try {
      let uri = `/rewards/${reward.id}/trade-confirmations/generate`;
      const response = await axios.post(uri);

      window.open(response.data.fileUri, "_blank");
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    } finally {
      this.setState({ loadingTradeConfirmation: false });
    }
  };

  render(): JSX.Element {
    const { reward, show, handleClose } = this.props;
    const { asset, consideration, quantity, updatedAt, displayUnitPrice, isin, displayUserFriendlyId } = reward;
    const { user, locale } = this.context as GlobalContextType;

    const [assetCommonId, assetConfig] = Object.entries(ASSET_CONFIG).find(([, config]) => config.isin == isin);
    const { simpleName, category, tickerWithCurrency } = assetConfig;
    const amount = (consideration.amount ?? 0) / 100;

    const transactionName = `${tickerWithCurrency} · ${simpleName}`;

    return (
      <Modal show={show} onHide={handleClose}>
        <div className="modal-content bg-reward-receipt">
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          <Modal.Body className="p-0 px-3 mb-5" style={{ background: "url(/reward-background.jpg)" }}>
            <div className="d-flex align-self-center justify-content-center fade-in">
              <div className="w-100" style={{ maxWidth: "420px" }}>
                {/* Action Title */}
                <div className="d-flex w-100 justify-content-start mb-5">
                  <AssetIcon category={category} iconUrl={getAssetIconUrl(asset)} size="md" />
                  <div className="d-flex flex-column">
                    <h6 className="text-primary success fw-bolder mt-1 ms-3">Reward</h6>
                    <h5 className="fw-bolder text-center ms-3">{transactionName}</h5>
                  </div>
                </div>
                {/* End Action Title */}
                <div className="row m-0 mb-2">
                  <div className="col-6 p-0 text-start fw-bold">{formatDateToDDMONYY(new Date(updatedAt))}</div>
                  <div className="col-6 p-0 text-muted text-end">{formatDateToHHMMSS(new Date(updatedAt))}</div>
                </div>
                <div className="row m-0 mb-4 wh-receipt-card">
                  <div className="col-4 p-0 text-center">
                    <div className="d-flex flex-column">
                      <div className="fw-bold">{formatCurrency(amount, consideration.currency, locale, 2, 2)}</div>
                      <div className="text-muted fw-bold">Amount</div>
                    </div>
                  </div>
                  <div className="col-4 p-0 text-center border-start border-end">
                    <div className="d-flex flex-column">
                      <div className="fw-bold">
                        {quantity.toLocaleString(locale, { minimumFractionDigits: 4, maximumFractionDigits: 4 })}
                      </div>
                      <div className="text-muted fw-bold">Shares</div>
                    </div>
                  </div>
                  <div className="col-4 p-0 text-center">
                    <div className="d-flex flex-column">
                      <div className="fw-bold">
                        {displayUnitPrice?.amount && displayUnitPrice?.currency
                          ? formatCurrency(displayUnitPrice.amount, displayUnitPrice.currency, locale, 2, 2)
                          : "0 "}
                      </div>
                      <div className="text-muted fw-bold">Per share</div>
                    </div>
                  </div>
                </div>
                <div className="row m-0 mb-4">
                  <div className="col p-0">
                    <div className="d-flex flex-column justify-content-center text-center">
                      <p className="fw-bold">Market Order</p>
                      <div className="d-flex justify-content-center">
                        <div className="wh-receipt-bar bg-primary" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="row m-0 mb-4">
                  <div className="col p-0">
                    <div className="receipt-separator-primary" />
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">Client</div>
                  <div className="col text-end">
                    <span className="fw-bolder">
                      {user.firstName} {user.lastName}
                    </span>
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">Order ID</div>
                  <div className="col text-end">
                    <span className="fw-bolder">{displayUserFriendlyId}</span>
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">ISIN</div>
                  <div className="col text-end">
                    <span className="fw-bolder">{isin}</span>
                  </div>
                </div>
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">Reporting firm</div>
                  <div className="col text-end">
                    <span className="fw-bolder">{getReportingFirm(user)}</span>
                  </div>
                </div>
                {this._shouldShowTradeConfirmation() && (
                  <div
                    className={
                      "d-flex align-items-center my-5 " +
                      (this.state.loadingTradeConfirmation ? "opacity-50 pointer-events-none" : "cursor-pointer")
                    }
                    onClick={() => this._downloadTradeConfirmation()}
                  >
                    <img
                      src="/images/icons/trade-confirmation.svg"
                      alt="Trade confirmation icon"
                      width="44"
                      height="45"
                    />
                    <span>Trade Confirmation</span>
                    <img
                      src="/images/icons/arrow-forward.svg"
                      className="ms-auto"
                      alt="Arrow forward icon"
                      width="16"
                      height="16"
                    />
                  </div>
                )}
                <div className="d-flex justify-content-center">
                  <img
                    className="wh-header-logo"
                    src="/images/icons/wealthyhood-logo-dark.svg"
                    style={{ height: "28px" }}
                  />
                </div>
              </div>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

RewardReceiptModal.contextType = GlobalContext;

export default RewardReceiptModal;
