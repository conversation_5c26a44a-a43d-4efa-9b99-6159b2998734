import React from "react";
import { Modal } from "react-bootstrap";
import { RewardDocument } from "../../../models/Reward";
import axios from "axios";
import { captureException } from "@sentry/react";
import { formatCurrency } from "../../utils/currencyUtil";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { getAssetIconUrl } from "../../utils/universeUtil";
import { emitToast } from "../../utils/eventService";
import { ToastTypeEnum } from "../../configs/toastConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  handleClose: () => void;
  show: boolean;
  reward: RewardDocument;
};

type StateType = {
  showRewardDetails: boolean;
};

class RewardSettledModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showRewardDetails: false
    };
  }

  private _handleRevealReward = () => {
    this.setState({ showRewardDetails: true });
  };

  private _handleDone = async () => {
    const { handleClose, reward } = this.props;
    try {
      await axios.post(`/rewards/${reward.id}`, { hasViewedAppModal: true });
      handleClose();
    } catch (err) {
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
      captureException(err);
    }
  };

  private _renderInitialView(): JSX.Element {
    return (
      <div className="d-flex flex-column justify-content-center text-center w-100">
        {/* Gift Icon */}
        <div className="d-flex justify-content-center mb-5">
          <img width={100} height={100} alt="Reward!" src="/images/icons/gift-box-1.png" />
        </div>

        {/* Title */}
        <div className="mb-5">
          <h5 className="mb-2">You've unlocked a</h5>
          <h5 className="text-primary mb-0">free share!</h5>
        </div>

        {/* Action Button */}
        <button className="btn btn-primary fw-100" onClick={this._handleRevealReward}>
          Reveal my reward!
        </button>
      </div>
    );
  }

  private _renderRewardDetails(): JSX.Element {
    const { reward } = this.props;
    const { locale } = this.context as GlobalContextType;

    const assetConfig = ASSET_CONFIG[reward.asset];
    const assetIconUrl = getAssetIconUrl(reward.asset);
    const rewardAmount = Decimal.div(reward.consideration.amount, 100).toNumber();

    return (
      <div className="d-flex flex-column justify-content-center text-center w-100">
        {/* Asset Icon */}
        <div className="d-flex justify-content-center mb-5">
          <img width={100} height={100} alt={assetConfig.simpleName} src={assetIconUrl} />
        </div>

        {/* Reward Amount */}
        <h5 className="text-primary mb-3">
          {formatCurrency(rewardAmount, reward.consideration.currency, locale, 0, 2)}
        </h5>

        {/* Asset Name */}
        <h5 className="mb-5">{assetConfig.simpleName}</h5>

        {/* Done Button */}
        <button className="btn btn-primary fw-100" onClick={this._handleDone}>
          Done!
        </button>
      </div>
    );
  }

  render(): JSX.Element {
    const { handleClose, show } = this.props;
    const { showRewardDetails } = this.state;

    return (
      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        dialogClassName="p-md-5 max-w-400px border-radius-24px reward-settled-modal"
        backdrop="static"
        keyboard={false}
      >
        <div className="fade-in">
          <Modal.Header className="justify-content-end border-bottom-0 pb-0" />
          <Modal.Body className="px-4 pt-4 pb-4 d-flex justify-content-center bg-starry">
            {showRewardDetails ? this._renderRewardDetails() : this._renderInitialView()}
          </Modal.Body>
        </div>
      </Modal>
    );
  }
}

RewardSettledModal.contextType = GlobalContext;

export default RewardSettledModal;
