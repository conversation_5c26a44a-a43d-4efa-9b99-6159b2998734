import React from "react";
import { Modal } from "react-bootstrap";

type PropsType = {
  show: boolean;
  handleClose: () => void;
};

class RiskExplanationModal extends React.Component<PropsType> {
  render(): JSX.Element {
    const { show, handleClose } = this.props;

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="px-lg-20" size="lg">
        <Modal.Header className="px-10 px-lg-20 pt-15 border-bottom-0" closeButton>
          <Modal.Title>
            <h2 className="font-weight-bolder mb-0">Portfolio Risk</h2>
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="px-10 px-lg-20 min-w-400px">
          {/* How we calculate risk */}
          <div className="row mb-6">
            <div className="col-12">
              <h4 className="text-dark-75 mb-4">{"👩‍🏫"} How do we calculate risk?</h4>
              <p>We calculate risk as the total expected volatility of your portfolio.</p>
              <p>
                Volatility is a statistical measure that represents how much the value of your portfolio swings
                around its mean price!
              </p>
              <p>
                More volatile portfolios are considered to be riskier than less volatile ones, because the value
                movements are expected to be steeper and therefore, less predictable.
              </p>
              <p>
                We calculate the expected volatility of your portfolio, based on how the individual assets
                performed over the past years.
              </p>
            </div>
          </div>
          {/* End How we calculate risk */}

          {/* About different risks */}
          <div className="row mb-6">
            <div className="col-12">
              <h4 className="text-dark-75 mb-4">
                {"🤔"} How this translates into Conservative, Balanced, Adventurous portfolios?
              </h4>
              <ul className="pl-6">
                <li className="pb-3">
                  <span className="font-weight-bold font-italic">Conservative</span> portfolios have usually very
                  low risk (volatility {"< 5%"}), but also lower growth potential and typically consist of just
                  government and corporate bonds. The closer you get to the conservative portfolio, your risk
                  approaches 5% or less.
                </li>
                <li className="pb-3">
                  <span className="font-weight-bold font-italic">Adventurous</span> portfolios have usually higher
                  risk ({"volatility > 15%"}), but also the potential for higher returns and growth of your
                  investment over the long run. They consist mainly of stocks. The closer you get to the
                  adventurous portfolio, your risk approaches 15% or more.
                </li>
                <li className="pb-3">
                  <span className="font-weight-bold font-italic">Balanced</span> portfolios stand between
                  conservative and adventurous portfolios ({"volatility between 5% & 15%"}). They are usually
                  well-diversified portfolios with balanced risk-reward potential.
                </li>
              </ul>
            </div>
          </div>
          {/* End About different risks */}
        </Modal.Body>
      </Modal>
    );
  }
}

export default RiskExplanationModal;
