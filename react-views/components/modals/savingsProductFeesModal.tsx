import React, { Component } from "react";
import { Modal } from "react-bootstrap";
import { SavingsProductFeeDetailsType } from "../../types/savings";
import { plansConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import ConfigUtil from "../../../utils/configUtil";

export enum SavingsProductFeesModalViewMode {
  ONE_DAY_YIELD = "ONE_DAY_YIELD",
  PLANS = "PLANS",
  EARN_INTEREST = "EARN_INTEREST"
}

export type SavingsProductFeesModalPropsType = {
  handleClose: () => void;
  savingsProductFeeDetails: SavingsProductFeeDetailsType;
  mode: SavingsProductFeesModalViewMode;
  selectedPlan?: plansConfig.PlanType;
};

export default class SavingsProductFeesModal extends Component<SavingsProductFeesModalPropsType> {
  private _getPricingTable() {
    const { savingsProductFeeDetails } = this.props;
    const { user } = this.context as GlobalContextType;
    const {
      netInterestRateColumnLabel,
      planColumnLabel,
      fundManagerFeeColumnLabel,
      wealthyhoodFeeColumnLabel,
      feeDetails
    } = savingsProductFeeDetails;

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    return (
      <div className="mb-5">
        <div className="row">
          <div className="col-3 align-self-center">{planColumnLabel}</div>
          <div className="col-3 align-self-center">{fundManagerFeeColumnLabel}</div>
          <div className="col-3 align-self-center">{wealthyhoodFeeColumnLabel}</div>
          <div className="col-3 align-self-center">{netInterestRateColumnLabel}</div>
        </div>
        {feeDetails.map((feeDetail, index) => (
          <div className="p-0 m-0" key={index}>
            <hr className="mb-2" />
            <div className="row">
              <div className="col-3 text-muted">{PLAN_CONFIG[feeDetail.plan].name}</div>
              <div className="col-3 text-muted">{feeDetail.fundManagerAnnualFeePercentage}</div>
              <div className="col-3 text-muted">{feeDetail.wealthyhoodAnnualFeePercentage}</div>
              <div className="col-3 text-success">{feeDetail.netInterestRate}</div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  private _getEarnInterestViewModalBody() {
    const { savingsProductFeeDetails } = this.props;
    const { grossInterestRate, netInterestRateOfCurrentPlan } = savingsProductFeeDetails;

    return (
      <div className="d-flex flex-column w-100">
        {/* Action Title */}
        <h4 className="fw-bolder text-start mb-4" style={{ lineHeight: "120%" }}>
          {`Earn ${netInterestRateOfCurrentPlan} interest`}
        </h4>
        {/* End Action Title */}

        <p className="text-muted">
          Money market funds are an alternative to traditional savings accounts, typically offering higher interest
          rates with less risk than stocks or long-term investments.
        </p>
        <p className="text-muted mb-5">{`The ${netInterestRateOfCurrentPlan} interest is calculated as the current 1-day yield, net of all fees and depends on your Wealthyhood plan.`}</p>

        {this._getPricingTable()}

        <p className="text-muted">
          {`Interest rates shown are illustrative and are using the current 1-day yield of ${grossInterestRate} p.a. gross.`}
        </p>
      </div>
    );
  }

  private _getOneDayYieldModalBody() {
    const { savingsProductFeeDetails } = this.props;
    const { grossInterestRate } = savingsProductFeeDetails;

    return (
      <div className="d-flex flex-column w-100">
        <h4 className="fw-bolder text-start mb-3" style={{ lineHeight: "120%" }}>
          1-day yield (net)
        </h4>
        <p className="text-muted mb-4">
          {`The 1-day yield is the average rate of return of this fund over the prior day, if held for one year. It's
          calculated by multiplying the daily interest rate paid out by 365. The net 1-day yield takes service fees
          into account.`}
        </p>

        <h5 className="fw-bolder text-start mb-3" style={{ lineHeight: "120%" }}>
          Pricing tiers
        </h5>
        <p className="text-muted">
          Service fees include our fee and the management fee which is paid to the fund manager. Fees calculated
          daily and are automatically deducted from the daily interest you receive.
        </p>
        <p className="text-muted mb-3">Our fees vary depending on your Wealthyhood plan:</p>

        {this._getPricingTable()}

        <p className="text-muted">
          {`Interest rates shown are illustrative and are using the current 1-day yield of ${grossInterestRate} p.a. gross.`}
        </p>
      </div>
    );
  }

  private _getPlansViewModalBody() {
    const { savingsProductFeeDetails, selectedPlan } = this.props;
    const { locale } = this.context as GlobalContextType;

    const { grossInterestRate, feeDetails } = savingsProductFeeDetails;

    const netInterestForSelectedPlan = feeDetails.find((fee) => fee.plan === selectedPlan)?.netInterestRateValue;

    return (
      <div className="d-flex flex-column w-100">
        <h4 className="fw-bolder text-start mb-3" style={{ lineHeight: "120%" }}>
          {`Earn ${netInterestForSelectedPlan.toLocaleString(locale, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}% interest`}
        </h4>

        <p className="text-muted">
          Money market funds are an alternative to traditional savings accounts, typically offering higher interest
          rates with less risk than stocks or long-term investments.
        </p>
        <p className="text-muted mb-3">The interest you earn depends on your Wealthyhood plan, as follows.</p>

        {this._getPricingTable()}

        <p className="text-muted">
          {`Interest rates shown are illustrative and are using the current 1-day yield of ${grossInterestRate} p.a. gross.`}
        </p>
      </div>
    );
  }

  render() {
    const { handleClose, mode } = this.props;

    return (
      <Modal show={true} onHide={handleClose} dialogClassName="max-w-600px">
        <Modal.Header className="border-bottom-0 pb-0 justify-content-end" closeButton></Modal.Header>
        <Modal.Body className="pt-0 pb-5 px-5">
          {mode === SavingsProductFeesModalViewMode.EARN_INTEREST && this._getEarnInterestViewModalBody()}

          {mode === SavingsProductFeesModalViewMode.ONE_DAY_YIELD && this._getOneDayYieldModalBody()}

          {mode === SavingsProductFeesModalViewMode.PLANS && this._getPlansViewModalBody()}
        </Modal.Body>
      </Modal>
    );
  }
}

SavingsProductFeesModal.contextType = GlobalContext;
