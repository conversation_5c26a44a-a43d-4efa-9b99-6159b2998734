import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";

// Cloudflare bucket Key Information Documents (KIDs)
const KID_DOMAIN = "https://kids.wealthyhood.dev/";

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
};

class SavingsProductSideModalDocuments extends Component<PropsType> {
  render() {
    const kidUrl = KID_DOMAIN + this.props.savingsProductId + "_kid.pdf";

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Documents</h5>
            <a href={kidUrl} target="_blank" rel="noreferrer" className="nostyle">
              <div className="d-flex flex-row justify-content-between cursor-pointer">
                <div className="d-flex align-items-center">
                  <span className="material-symbols-outlined align-self-center p-2 me-2 kid-document-icon">
                    description
                  </span>
                  <h6 className="m-0">Key Information Document</h6>
                </div>

                <span className="material-symbols-outlined align-self-center" style={{ fontSize: "20px" }}>
                  arrow_forward_ios
                </span>
              </div>
            </a>
          </div>
        </div>
      </>
    );
  }
}

export default SavingsProductSideModalDocuments;
