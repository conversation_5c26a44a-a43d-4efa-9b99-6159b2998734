import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { SavingsProductFundQualitySectionType } from "../../../types/savings";
import { formatPercentage } from "../../../utils/formatterUtil";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  quality: SavingsProductFundQualitySectionType;
  onInfoButtonClick: (infoConfig: savingsUniverseConfig.SavingsProductInfoSectionType) => void;
};

export default class SavingsProductSideModalFundQualitySection extends Component<PropsType> {
  private _getCreditRatingRow(key: string, label: string, value: string): JSX.Element {
    return (
      <div key={key} className="row p-0 m-0 mb-3">
        <div className="col-6">
          <p className="text-muted m-0">{label}</p>
        </div>
        <div className="col-6 text-end">
          <p className="fw-bolder text-success m-0">{value}</p>
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { savingsProductId, quality, onInfoButtonClick } = this.props;

    const { infoConfig } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Fund Quality</h5>

            <div className="row p-3 m-0 mb-5 asset-modal-card">
              {/* Fund Rating - START */}
              <div className="mb-2 d-flex justify-content-center align-items-center">
                <h6 className="text-primary m-0">{quality.rating}</h6>
              </div>
              <div className="mb-2 d-flex justify-content-center align-items-center">
                <h6 className="m-0">Rating</h6>
                <span
                  className="material-symbols-outlined cursor-pointer align-self-center ms-1"
                  style={{
                    fontSize: "16px"
                  }}
                  onClick={() => onInfoButtonClick(infoConfig.rating)}
                >
                  info
                </span>
              </div>

              {/* Fund Rating - END */}

              <div className="d-flex justify-content-center mb-3">
                <div className="horizontal-border"></div>
              </div>

              {/* Credit rating - START */}
              {quality.creditRatings.map((rating, index) => {
                return this._getCreditRatingRow(`credit-rating-${index}`, rating.label, rating.rating);
              })}
              <p className="text-muted small">{quality.ratingSubtitle}</p>
              {/* Credit rating - END */}

              <div className="d-flex justify-content-center mb-3">
                <div className="horizontal-border"></div>
              </div>

              {/* RISK - START */}
              <div className="row p-0 m-0 mb-3">
                <div className="col-6 d-flex align-items-center">
                  <p className="text-muted m-0">Risk indicator</p>
                  <span
                    className="material-symbols-outlined cursor-pointer align-self-center ms-1"
                    style={{
                      fontSize: "16px"
                    }}
                    onClick={() => onInfoButtonClick(infoConfig.riskIndicator)}
                  >
                    info
                  </span>
                </div>
                <div className="col-6 d-flex align-items-center justify-content-between text-end">
                  <div className="progress-bar-left" style={{ width: "80%" }}>
                    <div
                      className="progress-left"
                      style={{
                        width: formatPercentage(quality.risk.score / quality.risk.scale, "en"),
                        backgroundColor: "#536AE3"
                      }}
                    ></div>
                  </div>
                  <p className="fw-bolder text-primary m-0">{`${quality.risk.score}/${quality.risk.scale}`}</p>
                </div>
              </div>
              <p className="text-muted small m-0">{quality.risk.subtitle}</p>

              {/* RISK - END */}
            </div>
          </div>
        </div>
      </>
    );
  }
}
