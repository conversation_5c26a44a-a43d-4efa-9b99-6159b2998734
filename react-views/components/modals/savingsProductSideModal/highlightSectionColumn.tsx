import React, { Component } from "react";

type PropsType = {
  label: string;
  onClick: () => void;
  enableMargin: boolean;
  value?: string;
};

export default class HighlightSectionColumn extends Component<PropsType> {
  render() {
    const { label, onClick, enableMargin, value } = this.props;

    return (
      <div key={label} className={`col-6 p-0 ${enableMargin ? "mb-5" : ""}`}>
        <div className="d-flex flex-column align-items-center">
          <h6 className="fw-bolder text-primary">{value}</h6>
          <div className="d-flex flex-row">
            <p className="text-muted m-0">{label}</p>
            <span
              className="material-symbols-outlined cursor-pointer align-self-center ms-1"
              style={{
                fontSize: "16px"
              }}
              onClick={onClick}
            >
              info
            </span>
          </div>
        </div>
      </div>
    );
  }
}
