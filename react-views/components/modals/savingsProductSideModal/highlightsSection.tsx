import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { SavingsProductHighlightsSectionType } from "../../../types/savings";
import { EVENTS, eventEmitter } from "../../../utils/eventService";
import { SavingsProductFeesModalViewMode } from "../savingsProductFeesModal";
import HighlightSectionColumn from "./highlightSectionColumn";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  highlights: SavingsProductHighlightsSectionType;
  onInfoButtonClick: (infoConfig: savingsUniverseConfig.SavingsProductInfoSectionType) => void;
};

export default class SavingsProductSideModalHighlightSection extends Component<PropsType> {
  render(): JSX.Element {
    const { savingsProductId, highlights, onInfoButtonClick } = this.props;
    const { infoConfig } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5 mt-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Highlights</h5>
            <div className="row m-0 py-4 asset-modal-card">
              <HighlightSectionColumn
                label={highlights.oneDayYieldGross.label}
                onClick={() => onInfoButtonClick(infoConfig.oneDayYieldGross)}
                enableMargin={true}
                value={highlights.oneDayYieldGross.value}
              />
              <HighlightSectionColumn
                label={highlights.oneDayYieldNet.label}
                onClick={() =>
                  eventEmitter.emit(
                    EVENTS.savingsProductFeesModal,
                    savingsProductId,
                    SavingsProductFeesModalViewMode.ONE_DAY_YIELD
                  )
                }
                enableMargin={true}
                value={highlights.oneDayYieldNet.value}
              />
              <HighlightSectionColumn
                label={highlights.earnedLastMonth.label}
                onClick={() => onInfoButtonClick(infoConfig.earnLastMonth)}
                enableMargin={false}
                value={highlights.earnedLastMonth.value}
              />
              <HighlightSectionColumn
                label={highlights.lifetimeEarnings.label}
                onClick={() => onInfoButtonClick(infoConfig.lifetimeSavings)}
                enableMargin={false}
                value={highlights.lifetimeEarnings.value}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}
