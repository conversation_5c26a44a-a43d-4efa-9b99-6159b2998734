import React, { Component } from "react";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import SavingsProductSideModalLayout from "./layout";
import SavingsProductSideModalHighlightSection from "./highlightsSection";
import SavingsProductSideModalInformationSection from "./informationSection";
import SavingsProductSideModalFundQualitySection from "./fundQualitySection";
import SavingsProductSideModalDocuments from "./documents";
import { SavingsProductDataType } from "../../../types/savings";
import InfoModal from "../infoModal";
import { eventEmitter, EVENTS } from "../../../utils/eventService";
import { SavingsProductFeesModalViewMode } from "../savingsProductFeesModal";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  handleClose: () => void;
  savingsProductData: SavingsProductDataType;
  netInterestRate: string;
  displaySavingsAmount: string;
};

type StateType = {
  modalSlideAnimation: "in" | "out";
  showInfoDialog: boolean;
  selectedInfoConfig: savingsUniverseConfig.SavingsProductInfoSectionType;
};

class SavingsProductSideModal extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      modalSlideAnimation: "in",
      showInfoDialog: false,
      selectedInfoConfig: null
    };
    this._setSelectedInfo = this._setSelectedInfo.bind(this);
  }

  private _handleClose() {
    this.setState({ modalSlideAnimation: "out" }, () => {
      setTimeout(() => this.props.handleClose(), 500);
    });
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  private _setSelectedInfo(infoConfig: savingsUniverseConfig.SavingsProductInfoSectionType) {
    this.setState({ selectedInfoConfig: infoConfig, showInfoDialog: true });
  }

  render(): JSX.Element {
    const { savingsProductId, savingsProductData, netInterestRate, displaySavingsAmount } = this.props;
    const { modalSlideAnimation, showInfoDialog, selectedInfoConfig } = this.state;

    return (
      <>
        <SavingsProductSideModalLayout
          handleClose={() => this._handleClose()}
          modalSlideAnimation={modalSlideAnimation}
        >
          <h3>{displaySavingsAmount}</h3>
          <div className="d-flex flex-row text-primary mb-3">
            <h4 className="m-0">{`Earn ${netInterestRate} interest`}</h4>
            <span
              className="material-symbols-outlined cursor-pointer align-self-center ms-1"
              style={{
                fontSize: "20px"
              }}
              onClick={() =>
                eventEmitter.emit(
                  EVENTS.savingsProductFeesModal,
                  savingsProductId,
                  SavingsProductFeesModalViewMode.EARN_INTEREST
                )
              }
            >
              info
            </span>
          </div>
          <p className="text-muted">
            Money market funds are an alternative to traditional savings accounts, typically offering higher
            interest rates with less risk than stocks or long-term investments. Capital at risk.
          </p>

          <SavingsProductSideModalHighlightSection
            savingsProductId={savingsProductId}
            highlights={savingsProductData.highlightsSection}
            onInfoButtonClick={this._setSelectedInfo}
          />

          <SavingsProductSideModalInformationSection
            savingsProductId={savingsProductId}
            information={savingsProductData.informationSection}
            onInfoButtonClick={this._setSelectedInfo}
          />

          <SavingsProductSideModalFundQualitySection
            savingsProductId={savingsProductId}
            quality={savingsProductData.fundQualitySection}
            onInfoButtonClick={this._setSelectedInfo}
          />

          <SavingsProductSideModalDocuments savingsProductId={savingsProductId} />

          <p className="text-muted p-0">
            {SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId].infoConfig.disclaimerText}
          </p>
        </SavingsProductSideModalLayout>

        <InfoModal
          title={selectedInfoConfig?.title}
          show={showInfoDialog}
          handleClose={() => this._setShowInfoDialog(false)}
        >
          {selectedInfoConfig?.paragraphs &&
            selectedInfoConfig.paragraphs.map((paragraph, index) => {
              return (
                <p className="text-muted" key={index}>
                  {paragraph}
                </p>
              );
            })}
        </InfoModal>
      </>
    );
  }
}

export default SavingsProductSideModal;
