import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import { SavingsProductInformationSectionType } from "../../../types/savings";
import InformationSectionRow from "./informationSectionColumn";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

type PropsType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  information: SavingsProductInformationSectionType;
  onInfoButtonClick: (infoConfig: savingsUniverseConfig.SavingsProductInfoSectionType) => void;
};

export default class SavingsProductSideModalInformationSection extends Component<PropsType> {
  render(): JSX.Element {
    const { savingsProductId, information } = this.props;
    const { infoConfig } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];

    return (
      <>
        <div className="row m-0 p-0 m-0 mb-5">
          <div className="col p-0">
            <h5 className=" fw-bolder mb-4">Information</h5>

            <div className="row p-3 m-0 mb-5 asset-modal-card">
              <InformationSectionRow
                label={"Fund"}
                value={information.fundName}
                infoConfig={infoConfig.about}
                onInfoButtonClick={this.props.onInfoButtonClick}
              />
              <InformationSectionRow
                label={"Fund manager"}
                value={information.fundManager}
                infoConfig={infoConfig.fundManager}
                onInfoButtonClick={this.props.onInfoButtonClick}
              />
              <InformationSectionRow label={"ISIN"} value={information.isin} />
              <InformationSectionRow
                label={"Benchmark"}
                value={information.benchmark}
                infoConfig={infoConfig.benchmark}
                onInfoButtonClick={this.props.onInfoButtonClick}
              />
              <div className="d-flex justify-content-center mb-3">
                <div className="horizontal-border"></div>
              </div>
              <InformationSectionRow
                label={"Base Currency"}
                value={information.baseCurrency}
                infoConfig={infoConfig.baseCurrency}
                onInfoButtonClick={this.props.onInfoButtonClick}
              />
              <InformationSectionRow
                label={"Income"}
                value={information.income}
                infoConfig={infoConfig.income}
                onInfoButtonClick={this.props.onInfoButtonClick}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}
