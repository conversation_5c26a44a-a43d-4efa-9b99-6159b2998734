import React, { Component } from "react";

type PropsType = {
  label: string;
  value: string;
  infoConfig?: any;
  onInfoButtonClick?: (infoConfig: any) => void;
};

export default class InformationSectionColumn extends Component<PropsType> {
  render() {
    const { label, value, infoConfig, onInfoButtonClick } = this.props;

    return (
      <div className="row p-0 m-0 mb-3">
        <div className="col-6 d-flex align-items-center">
          <p className="text-muted m-0">{label}</p>
          {infoConfig && (
            <span
              className="material-symbols-outlined cursor-pointer align-self-center ms-1"
              style={{
                fontSize: "16px"
              }}
              onClick={() => onInfoButtonClick(infoConfig)}
            >
              info
            </span>
          )}
        </div>
        <div className="col-6 text-end">
          <p className="fw-bolder m-0">{value}</p>
        </div>
      </div>
    );
  }
}
