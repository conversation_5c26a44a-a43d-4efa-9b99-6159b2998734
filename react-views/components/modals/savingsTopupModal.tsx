import Decimal from "decimal.js";
import React from "react";
import { Modal } from "react-bootstrap";
import {
  countriesConfig,
  currenciesConfig,
  investmentsConfig,
  localeConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { LinkedBankAccount } from "../../types/bank";
import { FrequencySetting, PaymentMethod, ViewModeType } from "../../types/modal";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { OtherModalType } from "../modalsWrapper";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import FrequencySelect from "../frequencySelect";
import { SavingsTopUpAutomationDocument } from "../../../models/Automation";
import { MandateDocument } from "../../../models/Mandate";
import { convertIntoRecurrenceDate, getEveryMonthOnTheXthDateString } from "../../utils/dateUtil";
import { BankAccountDocument } from "../../../models/BankAccount";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import TransactionModalFrequencySelector from "../transactionModalFrequencySelector";
import AddBankAccountButton from "../addBankAccountButton";
import { isAllowedOneStepInvest } from "../../utils/userUtil";
import DirectDebitSetup from "../directDebitSetup";

const { MIN_ALLOWED_SAVINGS_INVESTMENT, MIN_ALLOWED_RECURRING_TOP_UP, MAX_ALLOWED_RECURRING_TOP_UP } =
  investmentsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

type TransactionStatusType =
  | "allowedCashBuy"
  | "allowedBankTopUpBuy"
  | "forbiddenAmount"
  | "insufficientCash"
  | "insufficientCashTopupPrompt"
  | "noInvestmentCashBuy"
  | "noInvestmentBankBuy"
  | "zeroCash"
  | "smallerThanMinimumRecurringAmount"
  | "largerThanMinimumRecurringAmount";

type PropsType = {
  availableCash: number;
  portfolioId: string;
  show: boolean;
  handleClose: () => void;
  linkedBankAccounts: LinkedBankAccount[];
  initialSelectedLinkedBankAccount: LinkedBankAccount;
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  netInterestRate: string;
  existingSavingsTopUpAutomation: SavingsTopUpAutomationDocument;
  activeOrPendingMandates: MandateDocument[];
};
type StateType = {
  orderAmount: string;
  paymentMethod: Exclude<PaymentMethod, "GIFT">;
  selectedLinkedBankAccount: LinkedBankAccount;
  viewMode: ViewModeType;
  errorMsg: string;
  actionButtonClicked: boolean;
  frequency: FrequencySetting;
  selectedDayOfMonth: number;
};

class SavingsTopupOrderModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();
    this.state = {
      paymentMethod: this._getInitialPaymentMethod(),
      orderAmount: "",
      selectedLinkedBankAccount:
        this.props.initialSelectedLinkedBankAccount ?? availableLinkedBankAccounts?.[0] ?? null,
      viewMode: "ACTION",
      errorMsg: "",
      actionButtonClicked: false,
      frequency: "ONE_TIME",
      selectedDayOfMonth:
        props.existingSavingsTopUpAutomation?.dayOfMonth ?? convertIntoRecurrenceDate(new Date(Date.now()))
    };
  }

  private _getAvailableLinkedBankAccounts = (): LinkedBankAccount[] => {
    const { linkedBankAccounts } = this.props;

    return linkedBankAccounts.filter((bankAccount) => bankAccount.isAvailableForDirectDebit);
  };

  private _shouldAllowBankAccountTopUp = (): boolean => {
    const { user } = this.context as GlobalContextType;

    return isAllowedOneStepInvest(user) || this.state.frequency !== "ONE_TIME";
  };

  private _getStatusMessage = (
    status: TransactionStatusType,
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { availableCash } = this.props;
    const { orderAmount } = this.state;

    const STATUS_MESSAGES: Record<TransactionStatusType, string> = {
      allowedCashBuy: `${formatCurrency(
        Decimal.sub(availableCash, orderAmount || 0).toNumber(),
        userCurrency,
        locale
      )} remaining cash`,
      allowedBankTopUpBuy: `${formatCurrency(
        new Decimal(orderAmount || 0).toNumber(),
        userCurrency,
        locale
      )} paid with your bank account`,
      forbiddenAmount: `You can't invest less than ${formatCurrency(
        MIN_ALLOWED_SAVINGS_INVESTMENT,
        userCurrency,
        locale
      )}.`,
      insufficientCash: "You don't have enough cash.",
      noInvestmentCashBuy: `${formatCurrency(availableCash, userCurrency, locale)} remaining cash`,
      noInvestmentBankBuy: "",
      zeroCash: `You have ${formatCurrency(0, userCurrency, locale, 0, 0)} cash available.`,
      smallerThanMinimumRecurringAmount: `The minimum repeating investment is ${formatCurrency(
        MIN_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale
      )}.`,
      largerThanMinimumRecurringAmount: `The maximum repeating investment is ${formatCurrency(
        MAX_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale
      )}.`,
      insufficientCashTopupPrompt: `Your available balance is ${formatCurrency(
        availableCash,
        userCurrency,
        locale
      )}. Please top up your account to proceed.`
    };

    return STATUS_MESSAGES[status];
  };

  private _getTransactionStatus = (): TransactionStatusType => {
    const { user } = this.context as GlobalContextType;
    const { paymentMethod, orderAmount, frequency } = this.state;
    const { availableCash } = this.props;

    const orderAmountNum = Number(orderAmount);

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      if (orderAmountNum === 0) {
        return "noInvestmentBankBuy";
      }

      if (frequency === "ONE_TIME") {
        if (orderAmountNum < MIN_ALLOWED_SAVINGS_INVESTMENT) {
          return "forbiddenAmount";
        }
      } else {
        if (orderAmountNum < MIN_ALLOWED_RECURRING_TOP_UP) {
          return "smallerThanMinimumRecurringAmount";
        } else if (orderAmountNum > MAX_ALLOWED_RECURRING_TOP_UP) {
          return "largerThanMinimumRecurringAmount";
        }
      }

      return "allowedBankTopUpBuy";
    } else {
      if (availableCash === 0) {
        return "zeroCash";
      } else if (availableCash > 0) {
        if (orderAmountNum === 0) {
          return "noInvestmentCashBuy";
        } else if (orderAmountNum < MIN_ALLOWED_SAVINGS_INVESTMENT) {
          return "forbiddenAmount";
        } else if (orderAmountNum > availableCash) {
          return isAllowedOneStepInvest(user) ? "insufficientCash" : "insufficientCashTopupPrompt";
        }
      }
      return "allowedCashBuy";
    }
  };

  private _isTransactionAllowed = (): boolean => {
    const { paymentMethod } = this.state;

    const transactionStatus = this._getTransactionStatus();

    if (paymentMethod === "BANK_ACCOUNT_TOP_UP") {
      return transactionStatus === "allowedBankTopUpBuy";
    } else {
      return transactionStatus === "allowedCashBuy" || transactionStatus === "insufficientCashTopupPrompt";
    }
  };

  private _getDisclaimer = (): string => {
    return "Capital at risk.";
  };

  private _getBuyConfirmationCTACopy = (): string => {
    return "Confirm";
  };

  private _getInitialPaymentMethod = (): Exclude<PaymentMethod, "GIFT"> => {
    const { availableCash } = this.props;

    if (availableCash > MIN_ALLOWED_SAVINGS_INVESTMENT) {
      return "CASH";
    } else return "BANK_ACCOUNT_TOP_UP";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setSelectedDayOfMonth = (selectedDayOfMonth: number): void => {
    this.setState({ selectedDayOfMonth });
  };

  private _setFrequency = (frequency: FrequencySetting): void => {
    this.setState({ frequency });
  };

  private _setPaymentMethodAndAccount = (
    paymentMethod: Exclude<PaymentMethod, "GIFT">,
    selectedLinkedBankAccount: LinkedBankAccount
  ): void => {
    this.setState({ paymentMethod, selectedLinkedBankAccount });
  };

  private _submitPayment = async () => {
    const { portfolioId, savingsProductId, handleClose } = this.props;
    const { orderAmount, paymentMethod, selectedLinkedBankAccount } = this.state;

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your deposit is being processed...");

    let url: string;
    let body: {
      orderAmount: string;
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      bankAccountId?: string;
    };
    if (paymentMethod === "CASH") {
      url = `/portfolios/${portfolioId}/topup-savings`;
      body = { orderAmount, savingsProductId };
    } else {
      url = `/portfolios/${portfolioId}/topup-savings-pending-deposit`;
      body = { orderAmount, savingsProductId, bankAccountId: selectedLinkedBankAccount.id };
    }

    try {
      const res = await axios.post(url, body);
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      handleClose();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _confirmTopup = async () => {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount, frequency } = this.state;

    if (
      frequency !== "ONE_TIME" &&
      !activeOrPendingMandates.some(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
      )
    ) {
      this._setViewMode("SETUP_DIRECT_DEBIT");
    } else if (frequency !== "ONE_TIME") {
      await this._setupAutomation();
    } else {
      await this._submitPayment();
    }
  };

  private _reviewPayment = async () => {
    const { existingSavingsTopUpAutomation } = this.props;
    const { frequency } = this.state;

    if (!this._isTransactionAllowed()) {
      return;
    }

    const transactionStatus = this._getTransactionStatus();
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    if (frequency === "MONTHLY" && availableLinkedBankAccounts.length === 0) {
      eventEmitter.emit(EVENTS.linkBankAccountModal, {
        originModal: "topupSavings" as OtherModalType
      });
    } else if (frequency === "MONTHLY" && existingSavingsTopUpAutomation) {
      this._setViewMode("CONFIRM_UPDATE_TOP_UP");
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      eventEmitter.emit(EVENTS.depositMethodsModal, {
        originModal: "topupSavings" as OtherModalType
      });
    } else this._setViewMode("REVIEW");
  };

  componentDidUpdate(prevProps: PropsType) {
    if (this._shouldAllowBankAccountTopUp()) {
      if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", this.props.initialSelectedLinkedBankAccount);
      } else if (this.props.linkedBankAccounts !== prevProps.linkedBankAccounts) {
        const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();
        this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", availableLinkedBankAccounts[0]);
      }
    }

    if (this.props.existingSavingsTopUpAutomation !== prevProps.existingSavingsTopUpAutomation) {
      this._setSelectedDayOfMonth(this.props.existingSavingsTopUpAutomation?.dayOfMonth);
    }
  }

  private _getActionViewCTACopy(): string {
    const { frequency } = this.state;

    const transactionStatus = this._getTransactionStatus();
    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    if (!availableLinkedBankAccounts?.length && frequency !== "ONE_TIME") {
      return "Proceed";
    } else if (transactionStatus === "insufficientCashTopupPrompt") {
      return "Top up";
    } else {
      return "Review";
    }
  }

  private _renderActionButton(): JSX.Element {
    const { orderAmount } = this.state;

    const allowedTransaction = this._isTransactionAllowed();
    const ctaCopy = this._getActionViewCTACopy();

    if (!orderAmount || !allowedTransaction) {
      return (
        <button type="button" className="btn btn-primary fw-100" disabled>
          {ctaCopy}
        </button>
      );
    }

    return (
      <LoadingOnSubmitButton
        className="btn btn-primary fw-100"
        enableOnCompletion={true}
        customonclick={async () => {
          this._setActionButtonClicked(true);
          await this._reviewPayment();
        }}
      >
        {ctaCopy}
      </LoadingOnSubmitButton>
    );
  }

  private _shouldCreateMandateForSelectedBankAccount(): boolean {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount } = this.state;

    return (
      activeOrPendingMandates.length === 0 ||
      activeOrPendingMandates.every(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() !== selectedLinkedBankAccount?.id
      )
    );
  }

  private async _setupAutomation() {
    const { activeOrPendingMandates, savingsProductId } = this.props;
    const { selectedLinkedBankAccount, orderAmount, selectedDayOfMonth } = this.state;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      if (this._shouldCreateMandateForSelectedBankAccount()) {
        await axios.post("/investor/setup-recurring-savings-topup", {
          bankAccount: selectedLinkedBankAccount,
          dayOfMonth: selectedDayOfMonth,
          orderAmount,
          frequency: "monthly",
          savingsProduct: savingsProductId
        });

        window.location.href = `/investor/new-mandate-success?target=myaccount&dayOfMonth=${selectedDayOfMonth}`;
      } else {
        await axios.post("/investor/setup-recurring-savings-topup", {
          orderAmount,
          frequency: "monthly",
          dayOfMonth: selectedDayOfMonth,
          savingsProduct: savingsProductId,
          mandate: activeOrPendingMandates.find(
            (mandate) =>
              (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
          ).id
        });

        window.location.href = "/investor/new-recurring-top-up-success?target=myaccount";
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  /**
   * When selecting a recurring frequency, we prioritize the bank account used in the existing automation.
   *
   * Otherwise we select a bank account with a pending/active mandate. If there isn't any,
   * we select the first linked bank account in the list.
   */
  private _getBankAccountForRecurringTopUp = (): LinkedBankAccount => {
    const { activeOrPendingMandates, existingSavingsTopUpAutomation } = this.props;

    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    const mandateIdUsedForExistingAutomation = (existingSavingsTopUpAutomation?.mandate as MandateDocument)?.id;
    if (mandateIdUsedForExistingAutomation) {
      const bankAccountUsedForExistingAutomation = activeOrPendingMandates.find(
        (mandate) => mandate.id === mandateIdUsedForExistingAutomation
      ).bankAccount as BankAccountDocument;

      return (
        availableLinkedBankAccounts.find(
          (linkedBankAccount) => linkedBankAccount.id === bankAccountUsedForExistingAutomation.id
        ) || availableLinkedBankAccounts[0]
      );
    }

    const bankAccountsWithPendingOrActiveMandate = activeOrPendingMandates
      .filter((mandate) => mandate.status !== "Inactive")
      .map((mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString());

    return (
      availableLinkedBankAccounts.find((bankAccount) =>
        bankAccountsWithPendingOrActiveMandate.includes(bankAccount.id)
      ) || availableLinkedBankAccounts[0]
    );
  };

  render(): JSX.Element {
    const {
      show,
      handleClose,
      linkedBankAccounts,
      availableCash,
      netInterestRate,
      activeOrPendingMandates,
      existingSavingsTopUpAutomation
    } = this.props;
    const {
      orderAmount,
      paymentMethod,
      selectedLinkedBankAccount,
      viewMode,
      actionButtonClicked,
      frequency,
      selectedDayOfMonth
    } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0 w-100 d-flex">
            <div className={"me-auto"}>
              <TransactionModalBackButton
                onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
                title={"Add money"}
              />
            </div>
            {viewMode === "ACTION" && (
              <div className={"ms-auto"}>
                <TransactionModalFrequencySelector
                  frequency={frequency}
                  paymentMethod={paymentMethod}
                  selectedDayOfMonth={selectedDayOfMonth}
                  handleClick={() => this._setViewMode("SET_FREQUENCY")}
                  handleSelectDayOfMonth={(selectedDayOfMonth) => this._setSelectedDayOfMonth(selectedDayOfMonth)}
                />
              </div>
            )}
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode === "ACTION" && (
          <>
            <Modal.Body className="p-0 px-md-3 px-3 fade-in" style={{ maxHeight: "700px" }}>
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-2">
                    <OrderInput
                      amount={orderAmount}
                      placeholderAmount={0}
                      key={"key-buy"}
                      onAmountChange={(amount) => {
                        const { orderAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != orderAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._setOrderAmount(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                      maxNumberLimit={100000}
                    />
                  </div>
                  {/* End Order Input */}

                  {/* Interest rate */}
                  <p className="text-muted text-center mb-3">{`Earn ${netInterestRate} interest`}</p>
                  {/* End Interest rate */}

                  {!["allowedCashBuy", "allowedBankTopUpBuy", "noInvestmentCashBuy"].includes(transactionStatus) &&
                    orderAmount && (
                      <p className="text-center text-danger mb-4">
                        {this._getStatusMessage(transactionStatus, user.currency, locale)}
                      </p>
                    )}

                  {/* Selected Payment Method Field */}
                  {paymentMethod === "BANK_ACCOUNT_TOP_UP" && !selectedLinkedBankAccount ? (
                    <></>
                  ) : (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={paymentMethod}
                      availableCash={availableCash}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      frequency={frequency}
                    />
                  )}
                  {/* Selected Payment Method Field */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center fade-in" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._renderActionButton()}
              </div>
            </Modal.Footer>
          </>
        )}
        {/* End Action Modal Content */}

        {/* Review Modal Content */}
        {viewMode === "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  <div className="row pb-3 mb-3 border-bottom">
                    <div className="col text-start">Total amount</div>
                    <div className="col text-end">
                      <span className="fw-bolder">
                        {formatCurrency(Number(orderAmount ?? 0), user.currency, locale, 2, 2)}
                      </span>
                    </div>
                  </div>
                  {frequency === "ONE_TIME" ? (
                    <div className="row pb-3 mb-3">
                      <div className="col text-start">Arriving</div>
                      <div className="col text-end">
                        <span className="fw-bolder">Instantly</span>
                      </div>
                    </div>
                  ) : (
                    <div className="row pb-3 mb-3 border-bottom">
                      <div className="col text-start text-nowrap">Repeating</div>
                      <div className="col text-end">
                        <span className="fw-bolder text-nowrap">
                          {getEveryMonthOnTheXthDateString({ capitalFirst: true }, selectedDayOfMonth)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="pb-3 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-primary fw-100" customonclick={this._confirmTopup}>
                  {this._getBuyConfirmationCTACopy()}
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">{this._getDisclaimer()}</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode === "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={paymentMethod}
                    frequency={frequency}
                    availableCash={availableCash}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) => {
                      this._setPaymentMethodAndAccount(
                        paymentMethod as Exclude<PaymentMethod, "GIFT">,
                        selectedLinkedBankAccount
                      );
                    }}
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._shouldAllowBankAccountTopUp() && <AddBankAccountButton originModal="topupSavings" />}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Bank Select Modal Content */}

        {/* Select Frequency Modal Content */}
        {viewMode == "SET_FREQUENCY" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <FrequencySelect
                    initialFrequencySelected={frequency}
                    linkedBankAccounts={linkedBankAccounts}
                    activeOrPendingMandates={activeOrPendingMandates}
                    existingTopUpAutomation={existingSavingsTopUpAutomation}
                    onFrequencyChange={(frequency) => {
                      this._setFrequency(frequency);
                      if (frequency !== "ONE_TIME") {
                        if (existingSavingsTopUpAutomation) {
                          this._setOrderAmount(
                            Decimal.div(existingSavingsTopUpAutomation.consideration.amount, 100)
                              .toNumber()
                              .toString()
                          );
                        }
                        this._setPaymentMethodAndAccount(
                          "BANK_ACCOUNT_TOP_UP",
                          this._getBankAccountForRecurringTopUp()
                        );
                      } else if (!selectedLinkedBankAccount) {
                        // We are switching back to ONE_TIME, so if there is no selected linked bank account, we want to switch
                        // payment method to the available balance.
                        this._setPaymentMethodAndAccount("CASH", null);
                      }

                      this._setViewMode("ACTION");
                    }}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }} />
          </div>
        )}
        {/* End Select Frequency Modal Content */}

        {/* Update Existing Top-Up */}
        {viewMode == "CONFIRM_UPDATE_TOP_UP" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">Update existing montly deposit?</h5>
                    <p className={"text-muted"}>
                      A monthly deposit is already set up. If you proceed, the existing monthly deposit will be
                      updated.
                    </p>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="pb-4 pt-2 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-between align-self-center mt-2 w-100"
                style={{ maxWidth: "400px" }}
              >
                <button
                  className="btn btn-secondary"
                  style={{ width: "46%" }}
                  onClick={() => this._setViewMode("ACTION")}
                >
                  Cancel
                </button>
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary"
                  style={{ width: "46%" }}
                  customonclick={async () => {
                    this._setViewMode("REVIEW");
                  }}
                >
                  Update
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Update Existing Top-Up */}

        {/* Direct Debit Information */}
        {viewMode == "DIRECT_DEBIT_INFORMATION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">The Direct Debit Guarantee</h5>
                    <ul className={"ps-4 pe-4 text-muted"}>
                      <li className={"pt-3"}>
                        This Guarantee is offered by all banks and building societies that accept instructions to
                        pay Direct Debits.
                      </li>
                      <li className={"pt-3"}>
                        If there are any changes to the amount, date or interval of your Direct Debit GC Re
                        Wealthkernel will notify you 3 working days in advance of your account being debited or as
                        otherwise agreed. If you request GC Re Wealthkernel to collect a payment, confirmation of
                        the amount and date will be given to you at the time of the request.{" "}
                      </li>
                      <li className={"pt-3"}>
                        If an error is made in the payment of your Direct Debit, by GC Re Wealthkernel or your bank
                        or building society, you are entitled to a full and immediate refund of the amount paid
                        from your bank or building society - If you receive a refund you are not entitled to, you
                        must pay it back when GC Re Wealthkernel asks you to.
                      </li>
                      <li className={"pt-3"}>
                        You can cancel a Direct Debit at any time by simply contacting your bank or building
                        society. Written confirmation may be required. Please also notify us.
                      </li>
                    </ul>
                    <div className="d-flex p-0 justify-content-center">
                      <img
                        src={"/images/icons/direct-debit.png"}
                        style={{ height: "48px", width: "130.29px" }}
                        className="p-2"
                        alt="The Direct Debit Guarantee"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Direct Debit Information */}

        {/* Setup Direct Debit Content */}
        {viewMode == "SETUP_DIRECT_DEBIT" && (
          <DirectDebitSetup
            selectedLinkedBankAccount={selectedLinkedBankAccount}
            orderAmount={orderAmount}
            onSetupAutomation={async () => this._setupAutomation()}
            onViewDirectDebitInfo={() => this._setViewMode("DIRECT_DEBIT_INFORMATION")}
            description="Set up a direct debit for your repeating savings."
            investmentType="Repeating savings"
          />
        )}
        {/* End Setup Direct Debit Content */}
      </Modal>
    );
  }
}

SavingsTopupOrderModal.contextType = GlobalContext;

export default SavingsTopupOrderModal;
