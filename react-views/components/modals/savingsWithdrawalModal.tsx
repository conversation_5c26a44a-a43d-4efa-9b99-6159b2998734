import {
  currenciesConfig,
  investmentsConfig,
  localeConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import React from "react";
import { Modal } from "react-bootstrap";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { ViewModeType } from "../../types/modal";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";

const { MIN_ALLOWED_SAVINGS_WITHDRAWAL } = investmentsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

type TransactionStatusType = "allowedWithdrawal" | "insufficientCash" | "noWithdrawal" | "notAllowedWithdrawal";

type PropsType = {
  availableCash: number;
  displayAvailableSavingsAmount: string;
  availableSavingsAmount: number;
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  portfolioId: string;
  show: boolean;
  handleClose: () => void;
};
type StateType = {
  withdrawalAmount: string;
  viewMode: ViewModeType;
  actionButtonClicked: boolean;
};

class SavingsWithdrawalModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      withdrawalAmount: "",
      viewMode: "ACTION",
      actionButtonClicked: false
    };
  }

  private _getStatusMessage = (
    status: TransactionStatusType,
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { displayAvailableSavingsAmount } = this.props;

    const STATUS_MESSAGES: Record<TransactionStatusType, string> = {
      allowedWithdrawal: `${displayAvailableSavingsAmount} available`,
      insufficientCash: `You can't withdraw more than ${displayAvailableSavingsAmount}.`,
      noWithdrawal: `${displayAvailableSavingsAmount} available`,
      notAllowedWithdrawal: `You can't withdraw less than ${formatCurrency(
        MIN_ALLOWED_SAVINGS_WITHDRAWAL,
        userCurrency,
        locale
      )}.`
    };

    return STATUS_MESSAGES[status];
  };

  private _setWithdrawalAmount = (withdrawal: string): void => {
    this.setState({ withdrawalAmount: withdrawal });
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _getTransactionStatus = (): TransactionStatusType => {
    const { availableSavingsAmount } = this.props;
    const withdrawalAmountNum = Number(this.state.withdrawalAmount);

    if (withdrawalAmountNum > availableSavingsAmount) {
      return "insufficientCash";
    } else if (withdrawalAmountNum <= 0) {
      return "noWithdrawal";
    } else if (withdrawalAmountNum < MIN_ALLOWED_SAVINGS_WITHDRAWAL) {
      return "notAllowedWithdrawal";
    } else {
      return "allowedWithdrawal";
    }
  };

  private _submitWithdrawalRequest = async (): Promise<void> => {
    const { portfolioId, savingsProductId } = this.props;
    const { withdrawalAmount } = this.state;

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your withdrawal is being processed");

    const orderAmount = Number(withdrawalAmount);
    try {
      const response = await axios.post(`/portfolios/${portfolioId}/withdraw-savings`, {
        orderAmount,
        savingsProductId
      });
      if (response.status == 204) {
        window.location.href = "/investor/savings-withdrawal-success?redirect=/";
      } else {
        emitToast({
          content: "We couldn't complete your request. Please try later.",
          toastType: ToastTypeEnum.error
        });
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: "We couldn't complete your request. Please try later.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _isTransactionAllowed = (): boolean => {
    const { withdrawalAmount } = this.state;
    return !!withdrawalAmount && this._getTransactionStatus() === "allowedWithdrawal";
  };

  private _getActionViewModeButton(): JSX.Element {
    return (
      <LoadingOnSubmitButton
        className="btn btn-primary fw-100"
        enableOnCompletion={true}
        customonclick={async () => {
          this._setActionButtonClicked(true);
          if (this._isTransactionAllowed()) {
            await this._submitWithdrawalRequest();
          }
        }}
      >
        Withdraw
      </LoadingOnSubmitButton>
    );
  }

  private static _shouldShowStatusMessage(transactionStatus: TransactionStatusType) {
    return !["allowedWithdrawal", "noWithdrawal"].includes(transactionStatus);
  }

  render(): JSX.Element {
    const { show, handleClose, displayAvailableSavingsAmount, availableCash } = this.props;
    const { viewMode, withdrawalAmount, actionButtonClicked } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setWithdrawalAmount("");
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0">
            <TransactionModalBackButton onClick={handleClose} title={"Withdraw"} />
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode === "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-2">
                    <OrderInput
                      amount={withdrawalAmount}
                      placeholderAmount={0}
                      onAmountChange={(amount) => {
                        const { withdrawalAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != withdrawalAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._setWithdrawalAmount(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                    />
                  </div>
                  {/* End Order Input */}

                  {/* Available Savings amount */}
                  <p className="text-muted text-center mb-4">{`${displayAvailableSavingsAmount} available`}</p>
                  {/* End Available Savings amount */}

                  {/* Cash balance */}
                  <div className="py-2 d-flex m-0 payment-method-select outline-hover cursor-pointer mx-auto">
                    <div className="ps-2 align-self-center text-center">
                      <img className="h-100" src={"/images/icons/logo-circular.svg"} alt={""} />
                    </div>
                    <div className="px-3 fw-light align-self-center">
                      <div className="d-flex flex-column">
                        <span className="text-truncate t-875">
                          Cash balance · {formatCurrency(availableCash, user.currency, locale)}
                        </span>
                      </div>
                    </div>
                  </div>
                  {/* End Cash balance */}

                  {/* Status message */}
                  {actionButtonClicked && SavingsWithdrawalModal._shouldShowStatusMessage(transactionStatus) && (
                    <p className="text-center text-danger mt-3 mb-0">
                      {this._getStatusMessage(transactionStatus, user.currency, locale)}
                    </p>
                  )}
                  {/* End Status Message */}
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._getActionViewModeButton()}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Action Modal Content */}
      </Modal>
    );
  }
}

SavingsWithdrawalModal.contextType = GlobalContext;

export default SavingsWithdrawalModal;
