import React from "react";
import { Modal } from "react-bootstrap";
import { currenciesConfig, investmentsConfig, localeConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { LinkedBankAccount } from "../../types/bank";
import PaymentMethodSelect from "../paymentMethodSelect";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { convertIntoRecurrenceDate, getEveryMonthOnTheXthDateString } from "../../utils/dateUtil";
import OrdersPreview from "../ordersPreview";
import { OtherModalType } from "../modalsWrapper";
import { MandateDocument } from "../../../models/Mandate";
import { BankAccountDocument } from "../../../models/BankAccount";
import { TopUpAutomationDocument } from "../../../models/Automation";
import { TransactionPreview } from "../../types/transactionPreview";
import TransactionModalTargetAllocationSelector, {
  AllocationMethodEnum
} from "../transactionModalTargetAllocationSelector";
import { HoldingsType } from "../../../models/Portfolio";
import FxRatePreviewRow from "../fxRatePreviewRow";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import DayOfMonthSelectionDropdown from "./dayOfMonthSelectionDropdown";
import AllocationMethodSelect from "../allocationMethodSelect";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import CommissionPreviewRow from "../commissionPreviewRow";
import AddBankAccountButton from "../addBankAccountButton";
import DirectDebitSetup from "../directDebitSetup";

const { MIN_ALLOWED_RECURRING_TOP_UP, MAX_ALLOWED_RECURRING_TOP_UP } = investmentsConfig;
const { CURRENCY_SYMBOLS } = currenciesConfig;

export type ViewModeType =
  | "ACTION"
  | "BANK_SELECT"
  | "SETUP_DIRECT_DEBIT"
  | "DIRECT_DEBIT_INFORMATION"
  | "DETAILS"
  | "REVIEW"
  | "CONFIRM_UPDATE_TOP_UP"
  | "ALLOCATION_METHOD_SELECTION";

type TransactionStatusType =
  | "allowedBankTopUpBuy"
  | "smallerThanMinimumRecurringAmount"
  | "largerThanMinimumRecurringAmount"
  | "noInvestmentBankBuy";

type PropsType = {
  show: boolean;
  handleClose: () => void;
  linkedBankAccounts: LinkedBankAccount[];
  activeOrPendingMandates: MandateDocument[];
  existingTopUpAutomation: TopUpAutomationDocument;
  initialOrderAmount: number;
  initialSelectedLinkedBankAccount: LinkedBankAccount;
  holdings: HoldingsType[];
  isPortfolioAllocationSetup: boolean;
};
type StateType = {
  orderAmount: string;
  selectedLinkedBankAccount: LinkedBankAccount;
  selectedDayOfMonth: number;
  viewMode: ViewModeType;
  transactionPreview: TransactionPreview;
  actionButtonClicked: boolean;
  buyTargetAllocation: boolean;
};

class SetupRepeatingInvestmentModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      orderAmount: props.initialOrderAmount.toString(),
      selectedDayOfMonth:
        props.existingTopUpAutomation?.dayOfMonth ?? convertIntoRecurrenceDate(new Date(Date.now())),
      selectedLinkedBankAccount: props.initialSelectedLinkedBankAccount ?? this._getBankAccountForRecurringTopUp(),
      viewMode: "ACTION",
      transactionPreview: null,
      actionButtonClicked: false,
      buyTargetAllocation: this._getBuyTargetAllocationDefaultValue(
        props.isPortfolioAllocationSetup,
        props.holdings
      )
    };
  }

  componentDidUpdate(prevProps: PropsType) {
    if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(this.props.initialSelectedLinkedBankAccount);
    } else if (this.props.linkedBankAccounts !== prevProps.linkedBankAccounts) {
      const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();
      this._setSelectedLinkedBankAccount(availableLinkedBankAccounts[0]);
    }

    if (this.props.existingTopUpAutomation !== prevProps.existingTopUpAutomation) {
      this._setSelectedDayOfMonth(this.props.existingTopUpAutomation?.dayOfMonth);
    }

    if (this.props.initialOrderAmount !== prevProps.initialOrderAmount) {
      this._setOrderAmount(this.props.initialOrderAmount.toString());
    }
  }

  private _getAvailableLinkedBankAccounts = (): LinkedBankAccount[] => {
    const { linkedBankAccounts } = this.props;

    return linkedBankAccounts.filter((bankAccount) => bankAccount.isAvailableForDirectDebit);
  };

  private _getBuyTargetAllocationDefaultValue = (
    isPortfolioAllocationSetup: boolean,
    holdings: HoldingsType[]
  ): boolean => {
    if (!isPortfolioAllocationSetup) {
      return false;
    }

    if (holdings.length === 0) {
      return true;
    }

    // Default value
    return false;
  };

  private _getStatusMessage = (
    status: TransactionStatusType,
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): string => {
    const { actionButtonClicked } = this.state;

    if (!actionButtonClicked) {
      return;
    }

    const STATUS_MESSAGES: Record<TransactionStatusType, string> = {
      allowedBankTopUpBuy: "",
      smallerThanMinimumRecurringAmount: `The minimum repeating investment is ${formatCurrency(
        MIN_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale
      )}.`,
      largerThanMinimumRecurringAmount: `The maximum repeating investment is ${formatCurrency(
        MAX_ALLOWED_RECURRING_TOP_UP,
        userCurrency,
        locale
      )}.`,
      noInvestmentBankBuy: ""
    };

    return STATUS_MESSAGES[status];
  };

  private _getTransactionStatus = (): TransactionStatusType => {
    const { orderAmount } = this.state;

    const orderAmountNum = Number(orderAmount);

    if (orderAmountNum === 0) {
      return "noInvestmentBankBuy";
    }
    if (orderAmountNum < MIN_ALLOWED_RECURRING_TOP_UP) {
      return "smallerThanMinimumRecurringAmount";
    }
    if (orderAmountNum > MAX_ALLOWED_RECURRING_TOP_UP) {
      return "largerThanMinimumRecurringAmount";
    }

    return "allowedBankTopUpBuy";
  };

  private _setActionButtonClicked(actionButtonClicked: boolean) {
    this.setState({ actionButtonClicked });
  }

  private _setOrderAmount = (orderAmount: string): void => {
    this.setState({ orderAmount });
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _setBankAccount = (selectedLinkedBankAccount: LinkedBankAccount): void => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _setBuyTargetAllocation(buyTargetAllocation: boolean) {
    this.setState({ buyTargetAllocation });
  }

  private _reviewPayment = async () => {
    if (!this._isTransactionAllowed()) {
      return;
    }

    const { existingTopUpAutomation } = this.props;

    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    // When user clicks on review, we move to the 'REVIEW' view mode unless they're updating an existing automation.
    if (availableLinkedBankAccounts.length === 0) {
      eventEmitter.emit(EVENTS.linkBankAccountModal, {
        originModal: "setupRepeatingInvestment" as OtherModalType
      });
    } else if (existingTopUpAutomation) {
      this._setViewMode("CONFIRM_UPDATE_TOP_UP");
    } else await this._getTransactionPreview(() => this._setViewMode("REVIEW"));
  };

  private async _getTransactionPreview(callback: () => void) {
    const { orderAmount, buyTargetAllocation } = this.state;
    try {
      const allocationMethod = buyTargetAllocation ? "targetAllocation" : "holdings";
      const res = await axios.get(
        `/transactions/portfolio/preview?portfolioTransactionType=buy&orderAmount=${orderAmount}&allocationMethod=${allocationMethod}`
      );
      this.setState({ transactionPreview: res.data }, () => callback());
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });

      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  }

  /**
   * When selecting a recurring frequency, we prioritize the bank account used in the existing automation.
   *
   * Otherwise we select a bank account with a pending/active mandate. If there isn't any,
   * we select the first linked bank account in the list.
   */
  private _getBankAccountForRecurringTopUp = (): LinkedBankAccount => {
    const { activeOrPendingMandates, existingTopUpAutomation } = this.props;

    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    const mandateIdUsedForExistingAutomation = (existingTopUpAutomation?.mandate as MandateDocument)?.id;
    if (mandateIdUsedForExistingAutomation) {
      const bankAccountUsedForExistingAutomation = activeOrPendingMandates.find(
        (mandate) => mandate.id === mandateIdUsedForExistingAutomation
      ).bankAccount as BankAccountDocument;

      return (
        availableLinkedBankAccounts.find(
          (linkedBankAccount) => linkedBankAccount.id === bankAccountUsedForExistingAutomation.id
        ) || availableLinkedBankAccounts[0]
      );
    }

    const bankAccountsWithPendingOrActiveMandate = activeOrPendingMandates
      .filter((mandate) => mandate.status !== "Inactive")
      .map((mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString());

    return (
      availableLinkedBankAccounts.find((bankAccount) =>
        bankAccountsWithPendingOrActiveMandate.includes(bankAccount.id)
      ) || availableLinkedBankAccounts[0]
    );
  };

  private _setSelectedLinkedBankAccount = (selectedLinkedBankAccount: LinkedBankAccount): void => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _setSelectedDayOfMonth = (selectedDayOfMonth: number): void => {
    this.setState({ selectedDayOfMonth });
  };

  private _confirmBuy = async () => {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount } = this.state;

    if (
      !activeOrPendingMandates.some(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
      )
    ) {
      this._setViewMode("SETUP_DIRECT_DEBIT");
    } else {
      await this._setupAutomation();
    }
  };

  private _shouldCreateMandateForSelectedBankAccount(): boolean {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount } = this.state;

    return (
      activeOrPendingMandates.length === 0 ||
      activeOrPendingMandates.every(
        (mandate) => (mandate.bankAccount as BankAccountDocument)._id.toString() !== selectedLinkedBankAccount?.id
      )
    );
  }

  private async _setupAutomation() {
    const { activeOrPendingMandates } = this.props;
    const { selectedLinkedBankAccount, orderAmount, buyTargetAllocation, selectedDayOfMonth } = this.state;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      if (this._shouldCreateMandateForSelectedBankAccount()) {
        await axios.post("/investor/setup-recurring-topup", {
          bankAccount: selectedLinkedBankAccount,
          dayOfMonth: selectedDayOfMonth,
          orderAmount,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          frequency: "monthly"
        });

        window.location.href = `/investor/new-mandate-success?target=autopilot&dayOfMonth=${selectedDayOfMonth}`;
      } else {
        await axios.post("/investor/setup-recurring-topup", {
          orderAmount,
          frequency: "monthly",
          dayOfMonth: selectedDayOfMonth,
          allocationMethod: buyTargetAllocation ? "targetAllocation" : "holdings",
          mandate: activeOrPendingMandates.find(
            (mandate) =>
              (mandate.bankAccount as BankAccountDocument)._id.toString() === selectedLinkedBankAccount?.id
          ).id
        });

        window.location.href = "/investor/new-recurring-top-up-success?target=autopilot";
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _getActionViewCTACopy(): string {
    const { existingTopUpAutomation } = this.props;

    const availableLinkedBankAccounts = this._getAvailableLinkedBankAccounts();

    if (!availableLinkedBankAccounts || availableLinkedBankAccounts?.length === 0) {
      return "Proceed";
    } else if (!existingTopUpAutomation) {
      return "Set up repeating investment";
    } else {
      return "Update repeating investment";
    }
  }

  private _isTransactionAllowed(): boolean {
    return this._getTransactionStatus() == "allowedBankTopUpBuy";
  }

  render(): JSX.Element {
    const { show, handleClose, linkedBankAccounts, initialOrderAmount, isPortfolioAllocationSetup, holdings } =
      this.props;
    const {
      orderAmount,
      selectedLinkedBankAccount,
      viewMode,
      transactionPreview,
      actionButtonClicked,
      buyTargetAllocation,
      selectedDayOfMonth
    } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setOrderAmount(initialOrderAmount.toString());
          this._setViewMode("ACTION");
          this._setActionButtonClicked(false);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0">
            <TransactionModalBackButton
              onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
              title={"Repeating"}
              subtitle={"Investment"}
            />
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode == "ACTION" && (
          <>
            <Modal.Body className="p-0 px-md-3 px-3 fade-in" style={{ maxHeight: "700px" }}>
              <div className="d-flex align-self-center justify-content-center">
                <div className={"d-flex flex-column align-items-center"} style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-3">
                    <OrderInput
                      amount={orderAmount}
                      placeholderAmount={150}
                      key={"key-buy"}
                      onAmountChange={(amount) => {
                        const { orderAmount } = this.state;
                        // if order amount is empty or reduced
                        // we clear the error message
                        if (!amount || amount != orderAmount) {
                          this._setActionButtonClicked(false);
                        }

                        this._setOrderAmount(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                      maxNumberLimit={100000}
                    />
                  </div>
                  {/* End Order Input */}

                  <div className="d-flex justify-content-center mb-2">
                    <DayOfMonthSelectionDropdown
                      handleOnDayOfMonthChange={this._setSelectedDayOfMonth}
                      selectedDayOfMonth={selectedDayOfMonth}
                      includeFrequencyKeyword={true}
                    />
                  </div>

                  <p
                    className={`text-center ${
                      this._isTransactionAllowed() || !actionButtonClicked ? "text-muted" : "text-danger"
                    } mb-4`}
                  >
                    {this._getStatusMessage(transactionStatus, user.currency, locale)}
                  </p>

                  {/* Selected Payment Method Field */}
                  {selectedLinkedBankAccount && (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                      availableCash={0}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      frequency="MONTHLY"
                    />
                  )}
                  {/* Selected Payment Method Field */}

                  {/* Buy target allocation switch */}
                  <TransactionModalTargetAllocationSelector
                    onClick={() => this._setViewMode("ALLOCATION_METHOD_SELECTION")}
                    allocationMethod={
                      buyTargetAllocation ? AllocationMethodEnum.TARGET_ALLOCATION : AllocationMethodEnum.HOLDINGS
                    }
                  />
                  {/* End Buy target allocation switch */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center fade-in" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {orderAmount ? (
                  <LoadingOnSubmitButton
                    className="btn btn-primary fw-100"
                    enableOnCompletion={true}
                    customonclick={async () => {
                      this._setActionButtonClicked(true);
                      await this._reviewPayment();
                    }}
                  >
                    {this._getActionViewCTACopy()}
                  </LoadingOnSubmitButton>
                ) : (
                  <button type="button" className="btn btn-primary fw-100" disabled={true}>
                    {this._getActionViewCTACopy()}
                  </button>
                )}
              </div>
            </Modal.Footer>
          </>
        )}
        {/* End Action Modal Content */}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {transactionPreview && (
                    <>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start">Total amount</div>
                        <div className="col text-end">
                          <span className="fw-bolder">
                            {formatCurrency(Number(orderAmount ?? 0), user.currency, locale, 2, 2)}
                          </span>
                        </div>
                      </div>
                      <div className="row pb-3 mb-3 border-bottom">
                        <div className="col text-start text-nowrap">Repeating</div>
                        <div className="col text-end">
                          <span className="fw-bolder text-nowrap">
                            {getEveryMonthOnTheXthDateString({ capitalFirst: true }, selectedDayOfMonth)}
                          </span>
                        </div>
                      </div>
                      <CommissionPreviewRow
                        fees={transactionPreview.fees}
                        isRepeatingInvestment={true}
                        hasETFOrders={transactionPreview.hasETFOrders}
                      />
                      <FxRatePreviewRow
                        foreignCurrencyRates={transactionPreview.foreignCurrencyRates}
                        showBottomBorder={false}
                      />
                      <div className="row mb-5">
                        <div className="col text-center">
                          <div
                            className="text-primary cursor-pointer"
                            onClick={() => this._setViewMode("DETAILS")}
                          >
                            View all orders
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-primary fw-100" customonclick={this._confirmBuy}>
                  Confirm repeating investment
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">Capital at risk.</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Details Modal Content */}
        {viewMode == "DETAILS" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  {/* Orders Preview */}
                  <OrdersPreview transactionPreview={transactionPreview} mode="buy" />
                  {/* End Orders Preview */}
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton className="btn btn-primary fw-100" customonclick={this._confirmBuy}>
                  Confirm repeating investment
                </LoadingOnSubmitButton>
                <p className="mt-2 text-center text-secondary">Capital at risk.</p>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Details Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                    frequency={"MONTHLY"}
                    availableCash={0}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) =>
                      this._setBankAccount(selectedLinkedBankAccount)
                    }
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <AddBankAccountButton originModal="setupRepeatingInvestment" />
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Bank Select Modal Content */}

        {/* Update Existing Top-Up */}
        {viewMode == "CONFIRM_UPDATE_TOP_UP" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">Update existing recurring investment?</h5>
                    <p className={"text-muted"}>
                      A recurring investment is already set up. If you proceed, the existing recurring investment
                      will be updated.
                    </p>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="pb-4 pt-2 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-between align-self-center mt-2 w-100"
                style={{ maxWidth: "400px" }}
              >
                <button
                  className="btn btn-secondary"
                  style={{ width: "46%" }}
                  onClick={() => this._setViewMode("ACTION")}
                >
                  Cancel
                </button>
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary"
                  style={{ width: "46%" }}
                  customonclick={async () => await this._getTransactionPreview(() => this._setViewMode("REVIEW"))}
                >
                  Update
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Update Existing Top-Up */}

        {/* Direct Debit Information */}
        {viewMode == "DIRECT_DEBIT_INFORMATION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="p-0 fade-in" style={{ maxWidth: "400px" }}>
                  <div className="row m-0 mb-4">
                    <h5 className="mb-4 fw-bolder">The Direct Debit Guarantee</h5>
                    <ul className={"ps-4 pe-4 text-muted"}>
                      <li className={"pt-3"}>
                        This Guarantee is offered by all banks and building societies that accept instructions to
                        pay Direct Debits.
                      </li>
                      <li className={"pt-3"}>
                        If there are any changes to the amount, date or interval of your Direct Debit GC Re
                        Wealthkernel will notify you 3 working days in advance of your account being debited or as
                        otherwise agreed. If you request GC Re Wealthkernel to collect a payment, confirmation of
                        the amount and date will be given to you at the time of the request.{" "}
                      </li>
                      <li className={"pt-3"}>
                        If an error is made in the payment of your Direct Debit, by GC Re Wealthkernel or your bank
                        or building society, you are entitled to a full and immediate refund of the amount paid
                        from your bank or building society - If you receive a refund you are not entitled to, you
                        must pay it back when GC Re Wealthkernel asks you to.
                      </li>
                      <li className={"pt-3"}>
                        You can cancel a Direct Debit at any time by simply contacting your bank or building
                        society. Written confirmation may be required. Please also notify us.
                      </li>
                    </ul>
                    <div className="d-flex p-0 justify-content-center">
                      <img
                        src={"/images/icons/direct-debit.png"}
                        style={{ height: "48px", width: "130.29px" }}
                        className="p-2"
                        alt="The Direct Debit Guarantee"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </div>
        )}
        {/* End Direct Debit Information */}

        {/* Setup Direct Debit Content */}
        {viewMode == "SETUP_DIRECT_DEBIT" && (
          <DirectDebitSetup
            selectedLinkedBankAccount={selectedLinkedBankAccount}
            orderAmount={orderAmount}
            onSetupAutomation={async () => this._setupAutomation()}
            onViewDirectDebitInfo={() => this._setViewMode("DIRECT_DEBIT_INFORMATION")}
            description="Set up a direct debit for your repeating investment."
            investmentType="Repeating investment"
          />
        )}
        {/* End Setup Direct Debit Content */}

        {/* Select Allocation Method Modal Content */}
        {viewMode == "ALLOCATION_METHOD_SELECTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <AllocationMethodSelect
                    allocationMethodSelected={
                      buyTargetAllocation ? AllocationMethodEnum.TARGET_ALLOCATION : AllocationMethodEnum.HOLDINGS
                    }
                    onAllocationMethodChange={(allocationMethod) =>
                      this._setBuyTargetAllocation(allocationMethod === AllocationMethodEnum.TARGET_ALLOCATION)
                    }
                    hasHoldings={holdings.length > 0}
                    isPortfolioAllocationSetup={isPortfolioAllocationSetup}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }} />
          </div>
        )}
        {/* End Select Allocation Method Modal Content */}
      </Modal>
    );
  }
}

SetupRepeatingInvestmentModal.contextType = GlobalContext;

export default SetupRepeatingInvestmentModal;
