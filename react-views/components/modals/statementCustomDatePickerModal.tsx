import React from "react";
import { Modal } from "react-bootstrap";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import { dateFriendlyFormatToISO, dateIsValid } from "../../utils/dateUtil";
import DateInput from "../dateInput";

export type PropsType = {
  show: boolean;
  handleClose: () => void;
  handleDownload: (startDate: Date, endDate: Date) => Promise<void>;
};

type StateType = {
  startDate: string;
  endDate: string;
};

class StatementCustomDatePickerModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      startDate: null,
      endDate: null
    };
  }

  private _setStartDate = (startDate: string): void => {
    this.setState({ startDate });
  };

  private _setEndDate = (endDate: string): void => {
    this.setState({ endDate });
  };

  render(): JSX.Element {
    const { show, handleClose, handleDownload } = this.props;
    const { startDate, endDate } = this.state;

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setStartDate(null);
          this._setEndDate(null);
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header closeButton style={{ borderBottom: "none" }}>
          <Modal.Title />
        </Modal.Header>
        <Modal.Body className="p-0 px-3">
          <div className="d-flex align-self-center justify-content-center">
            <div style={{ maxWidth: "400px" }}>
              <h5 className="fw-bolder text-center mb-2 mt-3 mb-2">Choose specific dates</h5>
              <p className="text-center text-muted mx-0 mb-5">
                Choose which days you would like your account statement to include.
              </p>
              <h6>Starting date</h6>
              <DateInput
                date={startDate}
                handleChange={(event: any) => this._setStartDate(event.target.value)}
                inputName={"startDate"}
                placeholder={"dd/mm/yyyy"}
              />
              <h6 className={"mt-4"}>Ending date</h6>
              <DateInput
                date={endDate}
                handleChange={(event: any) => this._setEndDate(event.target.value)}
                inputName={"endDate"}
                placeholder={"dd/mm/yyyy"}
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
          <div
            className="d-flex justify-content-center align-self-center mt-4 w-100"
            style={{ maxWidth: "400px" }}
          >
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-primary fw-100"
              enableOnCompletion={true}
              customonclick={async () =>
                handleDownload(
                  new Date(dateFriendlyFormatToISO(startDate)),
                  new Date(dateFriendlyFormatToISO(endDate))
                )
              }
              disabled={
                !startDate ||
                !dateIsValid(new Date(dateFriendlyFormatToISO(startDate))) ||
                !endDate ||
                !dateIsValid(new Date(dateFriendlyFormatToISO(endDate)))
              }
            >
              Download
            </LoadingOnSubmitButton>
          </div>
        </Modal.Footer>
      </Modal>
    );
  }
}

export default StatementCustomDatePickerModal;
