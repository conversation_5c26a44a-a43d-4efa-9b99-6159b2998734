import React, { Component } from "react";
import { dividendsConfig } from "@wealthyhood/shared-configs";
import { formatPercentage } from "../../utils/formatterUtil";
import { Modal } from "react-bootstrap";
import { WealthyhoodDividendTransactionDocument } from "../../../models/Transaction";
import { formatCurrency } from "../../utils/currencyUtil";
import ConfigUtil from "../../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";

const { WEALTHYHOOD_DIVIDEND_RATES, MAXIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND } = dividendsConfig;

type PropsType = {
  whDividend: WealthyhoodDividendTransactionDocument;
  show: boolean;
  handleClose: () => void;
};

export default class WealthyhoodDividendReceiptModal extends Component<PropsType> {
  render() {
    const { whDividend, show, handleClose } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const plan = PRICE_CONFIG[whDividend.price].plan;
    const { name, keyName } = PLAN_CONFIG[plan];

    const dividendRateStr = formatPercentage(WEALTHYHOOD_DIVIDEND_RATES[keyName], locale, 0, 2);

    return (
      <Modal show={show} onHide={handleClose} dialogClassName="p-md-5 max-w-520px" size={"lg"}>
        <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton></Modal.Header>
        <Modal.Body className="d-flex flex-column align-items-center px-5 pt-0">
          <span
            className={"ms-1 cursor-pointer material-symbols-outlined text-primary p-3"}
            style={{ fontSize: "30px", borderRadius: "8px", backgroundColor: "#1013270D" }}
          >
            payments
          </span>
          <h4 className="fw-bolder mt-3 mb-3">{`Wealthyhood dividend ${dividendRateStr}`}</h4>
          <p className="text-muted mt-4">
            {`Thanks to your Wealthyhood ${name} subscription, you earn an extra ${dividendRateStr} bonus dividend from us at the end of each month, on top of any other dividend or income your investments pay out over time!`}
          </p>
          <p className="text-muted mt-2">
            {`*It's calculated annually and paid monthly. Applies to the first ${formatCurrency(
              MAXIMUM_PORTFOLIO_VALUE_FOR_DIVIDEND,
              whDividend.consideration.currency,
              locale,
              0,
              0
            )} of your portfolio.`}
          </p>
        </Modal.Body>
      </Modal>
    );
  }
}

WealthyhoodDividendReceiptModal.contextType = GlobalContext;
