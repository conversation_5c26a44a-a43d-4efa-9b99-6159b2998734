import { currenciesConfig, investmentsConfig, localeConfig } from "@wealthyhood/shared-configs";
import LoadingOnSubmitButton from "../buttons/loadingOnSubmitButton";
import React from "react";
import { Modal } from "react-bootstrap";
import { formatCurrency } from "../../utils/currencyUtil";
import OrderInput from "../orderInput";
import axios from "axios";
import { ToastTypeEnum } from "../../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../../utils/eventService";
import { LinkedBankAccount } from "../../types/bank";
import SelectedPaymentMethod from "../selectedPaymentMethod";
import { ViewModeType } from "../../types/modal";
import PaymentMethodSelect from "../paymentMethodSelect";
import { OtherModalType } from "../modalsWrapper";
import { GlobalContext, GlobalContextType } from "../../contexts/globalContext";
import HoverableInfoIcon from "../hoverableInfoIcon";
import TransactionModalBackButton from "../buttons/transactionModalBackButton";
import AddBankAccountButton from "../addBankAccountButton";

const { CURRENCY_SYMBOLS } = currenciesConfig;

type TransactionStatusType = "allowedWithdrawal" | "insufficientCash" | "noWithdrawal" | "notAllowedWithdrawal";

type PropsType = {
  amountAvailableToWithdraw: number;
  unsettledCash: number;
  portfolioId: string;
  show: boolean;
  linkedBankAccounts: LinkedBankAccount[];
  handleClose: () => void;
  initialSelectedLinkedBankAccount: LinkedBankAccount;
};
type StateType = {
  withdrawalAmount: string;
  selectedLinkedBankAccount: LinkedBankAccount;
  viewMode: ViewModeType;
};

class WithdrawalModal extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      withdrawalAmount: "",
      selectedLinkedBankAccount:
        this.props.initialSelectedLinkedBankAccount ?? this.props.linkedBankAccounts?.[0] ?? null,
      viewMode: "ACTION"
    };
  }

  private _getWithdrawalStatus = (
    status: TransactionStatusType,
    userCurrency: currenciesConfig.MainCurrencyType,
    locale: localeConfig.LocaleType
  ): JSX.Element => {
    const { amountAvailableToWithdraw, unsettledCash } = this.props;

    const STATUS_MESSAGES: Record<TransactionStatusType, JSX.Element> = {
      allowedWithdrawal: (
        <div className={"mb-4"}>
          <div className={"d-flex justify-content-center"}>
            <p className={"text-center text-muted mb-0 me-2"}>
              Available to withdraw {formatCurrency(amountAvailableToWithdraw, userCurrency, locale)}
            </p>
            <HoverableInfoIcon
              colorHex={"#6c757d"}
              hoverText={
                "This amount shows the money you can withdraw to your bank account right now. When you sell investments, you'll need to wait 2 business days before that money becomes available to withdraw. Until then, you can still use this money to buy other investments, but you can't withdraw it to your bank account."
              }
            />
          </div>
          {unsettledCash ? (
            <p className={"text-center mt-2 text-muted"}>
              (Unsettled cash {formatCurrency(unsettledCash, userCurrency, locale)})
            </p>
          ) : (
            <></>
          )}
        </div>
      ),
      insufficientCash: (
        <>
          <p className={"text-center mb-4 text-danger"}>You can't withdraw more than your available balance.</p>
        </>
      ),
      noWithdrawal: (
        <div className={"mb-4"}>
          <div className={"d-flex justify-content-center align-items-center "}>
            <p className={"text-center text-muted mb-0 me-2"}>
              Available to withdraw {formatCurrency(amountAvailableToWithdraw, userCurrency, locale)}
            </p>
            <HoverableInfoIcon
              colorHex={"#6c757d"}
              hoverText={
                "This amount shows the money you can withdraw to your bank account right now. When you sell investments, you'll need to wait 2 business days before that money becomes available to withdraw. Until then, you can still use this money to buy other investments, but you can't withdraw it to your bank account."
              }
            />
          </div>
          {unsettledCash ? (
            <p className={"text-center mt-2 text-muted"}>
              (Unsettled cash {formatCurrency(unsettledCash, userCurrency, locale)})
            </p>
          ) : (
            <></>
          )}
        </div>
      ),
      notAllowedWithdrawal: (
        <>
          <p className={"text-center mb-4 text-danger"}>
            You can only withdraw {formatCurrency(investmentsConfig.MIN_ALLOWED_WITHDRAWAL, userCurrency, locale)}{" "}
            or more.
          </p>
        </>
      )
    };

    return STATUS_MESSAGES[status];
  };

  private _setWithdrawalAmount = (withdrawal: string): void => {
    this.setState({ withdrawalAmount: withdrawal });
  };

  private _getTransactionStatus = (): TransactionStatusType => {
    const { amountAvailableToWithdraw } = this.props;
    const withdrawalAmountNum = Number(this.state.withdrawalAmount);
    if (withdrawalAmountNum > amountAvailableToWithdraw) {
      return "insufficientCash";
    } else if (withdrawalAmountNum <= 0) {
      return "noWithdrawal";
    } else if (withdrawalAmountNum < investmentsConfig.MIN_ALLOWED_WITHDRAWAL) {
      return "notAllowedWithdrawal";
    } else {
      return "allowedWithdrawal";
    }
  };

  private _isTransactionAllowed = (): boolean => {
    const transactionStatus = this._getTransactionStatus();
    return transactionStatus === "allowedWithdrawal";
  };

  private _submitWithdrawalRequest = async (): Promise<void> => {
    const { portfolioId } = this.props;
    const { withdrawalAmount, selectedLinkedBankAccount } = this.state;

    eventEmitter.emit(EVENTS.loadingSplashMask, "Your withdrawal is being processed");

    const amount = Number(withdrawalAmount);
    try {
      const response = await axios.post(`/portfolios/${portfolioId}/withdraw`, {
        amount,
        bankAccountId: selectedLinkedBankAccount.id
      });
      if (response.status == 204) {
        window.location.href = "/investor/withdrawal-success";
      } else {
        emitToast({
          content: "We couldn't complete your request. Please try later.",
          toastType: ToastTypeEnum.error
        });
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: "We couldn't complete your request. Please try later.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setViewMode = (viewMode: ViewModeType): void => {
    this.setState({ viewMode });
  };

  private _getActionViewModeButton(): JSX.Element {
    const { selectedLinkedBankAccount, withdrawalAmount } = this.state;

    if (selectedLinkedBankAccount && withdrawalAmount) {
      if (this._isTransactionAllowed()) {
        return (
          <button
            type="button"
            className="btn btn-primary fw-100"
            onClick={() => {
              this._setViewMode("REVIEW");
            }}
          >
            Review
          </button>
        );
      } else {
        return (
          <button type="button" className="btn btn-primary fw-100" disabled>
            Review
          </button>
        );
      }
    }

    if (selectedLinkedBankAccount && !withdrawalAmount) {
      return (
        <button type="submit" className="btn btn-primary fw-100" disabled>
          Review
        </button>
      );
    }

    if (withdrawalAmount) {
      return (
        <button
          type="submit"
          className="btn btn-primary fw-100"
          onClick={() =>
            eventEmitter.emit(EVENTS.linkBankAccountModal, {
              originModal: "withdraw" as OtherModalType
            })
          }
        >
          Proceed
        </button>
      );
    }

    return (
      <button type="submit" className="btn btn-primary fw-100" disabled>
        Proceed
      </button>
    );
  }

  private _setSelectedLinkedBankAccount = (selectedLinkedBankAccount: LinkedBankAccount) => {
    this.setState({ selectedLinkedBankAccount });
  };

  private static _shouldShowError(transactionStatus: TransactionStatusType) {
    return ["insufficientCash", "notAllowedWithdrawal"].includes(transactionStatus);
  }

  componentDidUpdate(prevProps: Readonly<PropsType>) {
    if (prevProps.initialSelectedLinkedBankAccount != this.props.initialSelectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(this.props.initialSelectedLinkedBankAccount);
    } else if (prevProps.linkedBankAccounts?.length != this.props.linkedBankAccounts?.length) {
      this._setSelectedLinkedBankAccount(this.props.linkedBankAccounts?.[0]);
    }
  }

  render(): JSX.Element {
    const { show, handleClose, linkedBankAccounts } = this.props;
    const { selectedLinkedBankAccount, viewMode, withdrawalAmount } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const transactionStatus = this._getTransactionStatus();

    return (
      <Modal
        show={show}
        onHide={(): void => {
          this._setWithdrawalAmount("");
          this._setViewMode("ACTION");
          handleClose();
        }}
        size="xl"
        dialogClassName="p-md-5 max-w-600px"
      >
        <Modal.Header className="pb-5" style={{ borderBottom: "none" }}>
          <div className="pt-4 pb-5 px-1 m-0">
            <TransactionModalBackButton
              onClick={() => (viewMode === "ACTION" ? handleClose() : this._setViewMode("ACTION"))}
              title={"Withdraw"}
            />
          </div>
          <Modal.Title />
        </Modal.Header>

        {/* Action Modal Content */}
        {viewMode == "ACTION" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div style={{ maxWidth: "400px" }}>
                  {/* Order Input */}
                  <div className="d-flex flex-row justify-content-center mb-4">
                    <OrderInput
                      amount={withdrawalAmount}
                      placeholderAmount={0}
                      onAmountChange={(amount) => {
                        this._setWithdrawalAmount(amount);
                      }}
                      prefix={CURRENCY_SYMBOLS[user.currency]}
                      decimalLimit={2}
                    />
                  </div>
                  {/* End Order Input */}
                  {/* Status message */}
                  {this._getWithdrawalStatus(transactionStatus, user.currency, locale)}
                  {/* End Status Message */}

                  {/* Selected Payment Method Field */}
                  {selectedLinkedBankAccount && (
                    <SelectedPaymentMethod
                      selectedLinkedBankAccount={selectedLinkedBankAccount}
                      paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                      onClick={() => this._setViewMode("BANK_SELECT")}
                      availableCash={null}
                      frequency={"ONE_TIME"}
                    />
                  )}
                  {/* Selected Payment Method Field */}
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                {this._getActionViewModeButton()}
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Action Modal Content */}

        {/* Review Modal Content */}
        {viewMode == "REVIEW" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-3">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100" style={{ maxWidth: "400px" }}>
                  <div className="row pb-3 mb-3 border-bottom">
                    <div className="col text-start">Amount</div>
                    <div className="col text-end">
                      <span className="fw-bolder">
                        {formatCurrency(Number(withdrawalAmount ?? 0), user.currency, locale, 2, 2)}
                      </span>
                    </div>
                  </div>
                  <div className="row pb-3 mb-3">
                    <div className="col text-start">Arriving</div>
                    <div className="col text-end">
                      <span className="fw-bolder">2-3 days</span>
                    </div>
                  </div>
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <LoadingOnSubmitButton
                  className="btn btn-primary fw-100"
                  customonclick={this._submitWithdrawalRequest}
                >
                  Confirm withdrawal
                </LoadingOnSubmitButton>
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* End Review Modal Content */}

        {/* Bank Select Modal Content */}
        {viewMode == "BANK_SELECT" && (
          <div className="fade-in">
            <Modal.Body className="p-0 px-md-3 px-3 fade-in">
              <div className="d-flex align-self-center justify-content-center">
                <div className="w-100 " style={{ maxWidth: "400px" }}>
                  <PaymentMethodSelect
                    selectedLinkedBankAccount={selectedLinkedBankAccount}
                    paymentMethod={"BANK_ACCOUNT_TOP_UP"}
                    frequency={"ONE_TIME"}
                    linkedBankAccounts={linkedBankAccounts}
                    onSelectedAccountChange={(paymentMethod, selectedLinkedBankAccount) =>
                      this._setSelectedLinkedBankAccount(selectedLinkedBankAccount)
                    }
                    onConfirmSelection={() => this._setViewMode("ACTION")}
                    userCurrency={user.currency}
                  />
                </div>
              </div>
            </Modal.Body>
            <Modal.Footer className="py-5 justify-content-center" style={{ borderTop: "none" }}>
              <div
                className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                style={{ maxWidth: "400px" }}
              >
                <AddBankAccountButton originModal="withdraw" />
              </div>
            </Modal.Footer>
          </div>
        )}
        {/* EndBank Select Modal Content */}
      </Modal>
    );
  }
}

WithdrawalModal.contextType = GlobalContext;

export default WithdrawalModal;
