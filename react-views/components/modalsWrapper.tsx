import axios from "axios";
import React from "react";
import PortfolioBuyModal from "./modals/portfolioBuyModal";
import { BankProviderType, LinkedBankAccount } from "../types/bank";
import DepositModal from "./modals/depositModal";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import { isAllowedOneStepInvest, isVerified } from "../utils/userUtil";
import { captureException } from "@sentry/react";
import SessionRecordingService from "../external-services/sessionRecordingService";
import {
  banksConfig,
  investmentUniverseConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import { assetsArrayToDict, assetsDictToArray } from "../utils/portfolioUtil";
import { HoldingsType } from "../../models/Portfolio";
import {
  AssetTransactionDocument,
  TransactionDocument,
  WealthyhoodDividendTransactionDocument
} from "../../models/Transaction";
import AssetBuyOrderModal from "./modals/assetBuyOrderModal";
import AssetSellOrderModal from "./modals/assetSellOrderModal";
import { RewardDocument } from "../../models/Reward";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { PartialRecord } from "../types/utils";
import CancelOrderModal from "./modals/cancelOrderModal";
import WithdrawalModal from "./modals/withdrawalModal";
import LinkBankAccountModal from "../components/modals/linkBankAccountModal/modal";
import { UserDocument } from "../../models/User";
import { GiftDocument } from "../../models/Gift";
import ReceivedGiftModal from "./modals/receivedGiftModal";
import RewardSettledModal from "./modals/rewardSettledModal";
import { MandateDocument } from "../../models/Mandate";
import {
  AutomationDocument,
  SavingsTopUpAutomationDocument,
  TopUpAutomationDocument
} from "../../models/Automation";
import SetupRepeatingInvestmentModal from "./modals/setupRepeatingInvestmentModal";
import Decimal from "decimal.js";
import { BankAccountDocument } from "../../models/BankAccount";
import ReceivedWealthyhoodDividendModal from "./modals/receivedWealthyhoodDividendModal";
import AssetSideModal from "./modals/assetSideModal/assetSideModal";
import ConfigUtil from "../../utils/configUtil";
import { AssetDiscoveryListViewModeType } from "./assetDiscoveryList";
import { AssetCollectionsType } from "../types/assetDiscovery";
import AssetListModal from "./modals/assetListModal";
import OrderSuccessModal from "./modals/orderSuccessModal";
import PromptToRepeatingInvestmentModal from "./modals/promptToRepeatingInvestmentModal";
import { SavingsProductDataType, SavingsProductFeeDetailsType } from "../types/savings";
import SavingsProductSideModal from "./modals/savingsProductSideModal";
import SavingsTopupModal from "./modals/savingsTopupModal";
import SavingsWithdrawalModal from "./modals/savingsWithdrawalModal";
import SavingsProductFeesModal, { SavingsProductFeesModalViewMode } from "./modals/savingsProductFeesModal";
import BankAccountListModal from "./modals/bankAccountListModal";
import DepositMethodsModal from "./modals/depositMethodsModal";
import { MarketInfoType } from "./modals/assetSideModal/assetSideModal.types";
import SetupRepeatingSavingsModal from "./modals/setupRepeatingSavingsModal";
import { WalletDocument } from "../../models/Wallet";
import { ModalPromptType, RewardModalPromptDataType } from "../types/modalPrompt";

export type OtherModalType =
  | ""
  | "portfolioBuy"
  | "newPortfolioBuy"
  | "assetBuy"
  | "deposit"
  | "withdraw"
  | "setupRepeatingInvestment"
  | "setupRepeatingSavings"
  | "promptToRepeatingInvestmentModal"
  | "topupSavings"
  | "bankAccountListModal";

export type DepositMethodsType = "REGULAR_TOPUP" | "NO_CASH_TOPUP" | "EXTRA_CASH_TOPUP";

const DEFAULT_REPEATING_INVESTMENT_AMOUNT = 150;
const RECEIVED_GIFT_MODAL_DELAY = 2000; // We delay showing the received gift modal to ensure the page is fully  loaded
const RECEIVED_WH_DIVIDEND_MODAL_DELAY = 2000; // We delay showing the received dividend modal to ensure the page is fully  loaded
const SETTLED_REWARD_MODAL_DELAY = 2000; // We delay showing the settled reward modal to ensure the page is fully  loaded
const DEFAULT_LOADING_SPLASH_COLOR_CLASS = "primary";

type BankDataType = {
  bankId?: banksConfig.BankType;
  bankAccountId?: string;
};

type StateType = {
  showReceivedWhDividendModal: boolean;
  showDepositModal: boolean;
  showDepositMethodsModal: boolean;
  showWithdrawalModal: boolean;
  showPortfolioBuyModal: boolean;
  showSetupRepeatingInvestmentModal: boolean;
  showSetupRepeatingSavingsModal: boolean;
  showPromptToRepeatingInvestment: boolean;
  showReceivedGiftModal: boolean;
  showRewardSettledModal: boolean;
  showCancelOrderModal: boolean;
  showSuccessOrderModal: boolean;
  showInvestmentProductModal: boolean;
  showBuyAssetModal: boolean;
  showSellAssetModal: boolean;
  showLinkBankAccountModal: boolean;
  showDepositMethodsOnDepositModalClose: boolean;
  loadingSplashColorClass: string;
  loadingSplashMessage: string;
  triggeredByInvestmentProductModal: boolean;
  triggeredByOtherModal: OtherModalType;
  linkedBankAccounts: LinkedBankAccount[];
  holdingsWithReturns: (HoldingsType & { sinceByReturns: number })[];
  availableHoldings: HoldingsType[];
  gift: GiftDocument;
  reward: RewardDocument;
  banks: BankProviderType[];
  activeOrPendingMandates: MandateDocument[];
  activeTopUpAutomation: TopUpAutomationDocument;
  activeSavingsTopUpAutomations: SavingsTopUpAutomationDocument[];
  investmentProductsDict: PartialRecord<investmentUniverseConfig.AssetType, InvestmentProductDocument>;
  displayedAsset: investmentUniverseConfig.AssetType;
  transactionToCancel: TransactionDocument;
  selectedLinkedBankAccount: LinkedBankAccount;
  selectedBankProvider: BankProviderType;
  whDividends: WealthyhoodDividendTransactionDocument[];
  hasRestrictedAssetQuantity: boolean;
  hasRestrictedHoldingsQuantity: boolean;
  assetListViewMode?: AssetDiscoveryListViewModeType;
  assetCollections?: AssetCollectionsType;
  successOrderDocument: AssetTransactionDocument;
  hasNoActiveRepeatingInvestments: boolean;
  postponeTopUpActivation: boolean;
  repeatingInvestmentAmount: number;
  savingsProductDataDict: PartialRecord<savingsUniverseConfig.SavingsProductType, SavingsProductDataType>;
  savingsProductFeeDetailsDict: PartialRecord<
    savingsUniverseConfig.SavingsProductType,
    SavingsProductFeeDetailsType
  >;
  showSavingsProductModal: boolean;
  selectedSavingsProduct: {
    id: savingsUniverseConfig.SavingsProductType;
    displaySavingsAmount?: string;
    netInterestRate?: string;
    savingsAmount?: number;
  };
  showSavingsTopupModal: boolean;
  showSavingsWithdrawalModal: boolean;
  showSavingsProductFeesModal: boolean;
  savingsProductFeeModalSettings: {
    mode: SavingsProductFeesModalViewMode;
    selectedPlan?: plansConfig.PlanType;
  };
  showBankAccountListModal: boolean;
  marketInfo?: MarketInfoType;
  depositMethodsType: DepositMethodsType;
};

type PropsType = {
  user: UserDocument;
  activePage?: string;
  lazyLoadData?: boolean;
};

class ModalsWrapper extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showDepositModal: false,
      showDepositMethodsModal: false,
      showWithdrawalModal: false,
      linkedBankAccounts: [],
      banks: [],
      holdingsWithReturns: [],
      activeOrPendingMandates: [],
      availableHoldings: [],
      gift: null,
      reward: null,
      activeTopUpAutomation: null,
      activeSavingsTopUpAutomations: [],
      investmentProductsDict: {},
      displayedAsset: null,
      transactionToCancel: null,
      showPortfolioBuyModal: false,
      showDepositMethodsOnDepositModalClose: false,
      showBuyAssetModal: false,
      showSellAssetModal: false,
      showSetupRepeatingInvestmentModal: false,
      showSetupRepeatingSavingsModal: false,
      showPromptToRepeatingInvestment: false,
      showInvestmentProductModal: false,
      showCancelOrderModal: false,
      showSuccessOrderModal: false,
      showLinkBankAccountModal: false,
      showReceivedGiftModal: false,
      showRewardSettledModal: false,
      triggeredByInvestmentProductModal: true,
      loadingSplashMessage: "",
      loadingSplashColorClass: "primary",
      triggeredByOtherModal: "",
      selectedLinkedBankAccount: null,
      selectedBankProvider: null,
      showReceivedWhDividendModal: false,
      whDividends: null,
      hasRestrictedAssetQuantity: false,
      hasRestrictedHoldingsQuantity: false,
      successOrderDocument: null,
      hasNoActiveRepeatingInvestments: false,
      repeatingInvestmentAmount: DEFAULT_REPEATING_INVESTMENT_AMOUNT * 100,
      showSavingsProductModal: false,
      savingsProductDataDict: {},
      selectedSavingsProduct: null,
      showSavingsTopupModal: false,
      showSavingsWithdrawalModal: false,
      showSavingsProductFeesModal: false,
      savingsProductFeeDetailsDict: {},
      savingsProductFeeModalSettings: null,
      showBankAccountListModal: false,
      postponeTopUpActivation: false,
      depositMethodsType: "REGULAR_TOPUP"
    };

    const { user } = props;
    SessionRecordingService.record(user);
  }

  private _setSelectedLinkedBankAccount = (selectedLinkedBankAccount: LinkedBankAccount): void => {
    this.setState({ selectedLinkedBankAccount });
  };

  private _setSelectedBankProvider = (selectedBankProvider: BankProviderType): void => {
    this.setState({ selectedBankProvider });
  };

  private _setShowDepositMethodsOnDepositModalClose = (showDepositMethodsOnDepositModalClose: boolean): void => {
    this.setState({ showDepositMethodsOnDepositModalClose });
  };

  private async _handleDepositEvent(
    bankData?: BankDataType,
    options?: {
      showDepositMethodsOnDepositModalClose?: boolean;
    }
  ): Promise<void> {
    this._setShowDepositMethodsOnDepositModalClose(options?.showDepositMethodsOnDepositModalClose ?? false);

    if (bankData?.bankId) {
      await this._fetchBankProviders(banksConfig.BankProviderScopeEnum.PAY);

      const { banks } = this.state;

      this._setSelectedBankProvider(banks.find((bank) => bank.id === bankData?.bankId));
      this._setShowDepositModal(true);
    } else {
      await this._fetchLinkedBankAccounts(
        () => {
          const selectedLinkedBankAccount = this._findBankAccount(bankData);
          if (selectedLinkedBankAccount) {
            this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
          }

          this._setShowDepositModal(true);
        },
        () => {
          emitToast({
            content: "We couldn't initiate your deposit. Please try again later.",
            toastType: ToastTypeEnum.error
          });
        }
      );
    }
  }

  private async _handleDepositMethodsEvent(config?: { originModal: OtherModalType }): Promise<void> {
    await this._fetchBankProviders(banksConfig.BankProviderScopeEnum.PAY);
    const originModal: OtherModalType = config?.originModal ?? "";

    let depositMethodsType: DepositMethodsType = "REGULAR_TOPUP";
    switch (originModal) {
      case "portfolioBuy":
        this._setShowPortfolioBuyModal(false);
        depositMethodsType = "EXTRA_CASH_TOPUP";
        break;
      case "assetBuy":
        this._hideAssetOrderModals();
        depositMethodsType = "EXTRA_CASH_TOPUP";
        break;
      case "topupSavings":
        this._setShowSavingsTopupModal(false);
        depositMethodsType = "EXTRA_CASH_TOPUP";
        break;
    }
    this._setShowDepositMethodsModal(true, depositMethodsType);
  }

  private async _handleWithdrawalEvent(bankData?: BankDataType): Promise<void> {
    await this._fetchLinkedBankAccounts(
      () => {
        const selectedLinkedBankAccount = this._findBankAccount(bankData);
        if (selectedLinkedBankAccount) {
          this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
        }

        this._setShowWithdrawalModal(true);
      },
      () => {
        emitToast({
          content: "We couldn't initiate your withdrawal. Please try again later.",
          toastType: ToastTypeEnum.error
        });
      }
    );
  }

  private async _handlePortfolioBuyEvent(bankData?: BankDataType): Promise<void> {
    await Promise.all([
      this._fetchGifts(),
      this._fetchActiveOrPendingMandates(),
      this._fetchTopUpAutomation(),
      this._fetchSavingsTopUpAutomation()
    ]);
    await this._fetchLinkedBankAccounts(
      async () => {
        const selectedLinkedBankAccount = this._findBankAccount(bankData);
        if (selectedLinkedBankAccount) {
          this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
        }

        this._setShowPortfolioBuyModal(true);
      },
      () => {
        emitToast({
          content: "We couldn't initiate your investment. Please try again later.",
          toastType: ToastTypeEnum.error
        });
      }
    );
  }

  private async _handleSetupRepeatingInvestmentEvent(bankData?: BankDataType): Promise<void> {
    await Promise.all([
      this._fetchActiveOrPendingMandates(),
      this._fetchTopUpAutomation(),
      this._fetchLinkedBankAccounts(),
      this._fetchPortfolioWithReturns()
    ]);

    const selectedLinkedBankAccount = this._findBankAccount(bankData);
    if (selectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
    }

    this._setShowSetupRepeatingInvestmentModal(true);
  }

  private async _handleSetupRepeatingSavingsEvent(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    netInterestRate: string,
    bankData?: BankDataType
  ): Promise<void> {
    await Promise.all([
      this._fetchActiveOrPendingMandates(),
      this._fetchSavingsTopUpAutomation(),
      this._fetchLinkedBankAccounts(),
      this._fetchPortfolioWithReturns()
    ]);

    const selectedLinkedBankAccount = this._findBankAccount(bankData);
    if (selectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
    }

    this._setShowSetupRepeatingSavingsModal(true, {
      id: savingsProductId,
      netInterestRate
    });
  }

  private async _handleSuccessOrderEvent(assetTransaction: AssetTransactionDocument) {
    await Promise.all([this._fetchInvestmentProducts(), this._fetchTopUpAutomation()]);
    const { activeTopUpAutomation } = this.state;
    const hasNoActiveRepeatingInvestments = !activeTopUpAutomation;
    this.setState({ successOrderDocument: assetTransaction, hasNoActiveRepeatingInvestments });
    this._setShowSuccessOrderModal(true);
  }

  private async _handlePromptToRepeatingInvestmentEvent({
    repeatingInvestmentAmount,
    postponeActivation,
    bankData
  }: {
    repeatingInvestmentAmount?: number;
    postponeActivation?: boolean;
    bankData?: BankDataType;
  }): Promise<void> {
    await Promise.all([this._fetchActiveOrPendingMandates(), this._fetchLinkedBankAccounts()]);

    const selectedLinkedBankAccount = this._findBankAccount(bankData);
    if (selectedLinkedBankAccount) {
      this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
    }
    const amount = repeatingInvestmentAmount ?? DEFAULT_REPEATING_INVESTMENT_AMOUNT * 100;
    this.setState({ repeatingInvestmentAmount: amount, postponeTopUpActivation: postponeActivation ?? false });
    this._setShowPromptToRepeatingInvestment(true);
  }

  private async _handleLinkBankAccountEvent(config?: { originModal: OtherModalType }): Promise<void> {
    const originModal: OtherModalType = config?.originModal ?? "";

    await this._fetchBankProviders(banksConfig.BankProviderScopeEnum.DATA);

    this.setState({
      showBuyAssetModal: false,
      showPortfolioBuyModal: false,
      showDepositModal: false,
      showWithdrawalModal: false,
      showSetupRepeatingInvestmentModal: false,
      showSetupRepeatingSavingsModal: false,
      showSavingsTopupModal: false,
      showPromptToRepeatingInvestment: false,
      showLinkBankAccountModal: true,
      triggeredByOtherModal: originModal
    });
  }

  private async _handleInvestmentProductEvent(assetId: investmentUniverseConfig.AssetType): Promise<void> {
    this.setState({ displayedAsset: assetId, showInvestmentProductModal: true });
  }

  private async _handleCancelOrderEvent(transaction: TransactionDocument): Promise<void> {
    this.setState({ transactionToCancel: transaction }, async () => {
      this._setShowCancelOrderModal(true);
    });
  }

  private async _handleAssetBuyEvent(
    assetId: investmentUniverseConfig.AssetType,
    options?: {
      bankData?: BankDataType;
      marketInfo?: MarketInfoType;
    }
  ): Promise<void> {
    this.setState({ displayedAsset: assetId }, async () => {
      try {
        await Promise.all([
          this._fetchPortfolioWithReturns(),
          this._fetchLinkedBankAccounts(),
          this._fetchInvestmentProducts(),
          this._fetchGifts()
        ]);

        const selectedLinkedBankAccount = this._findBankAccount(options?.bankData);
        if (selectedLinkedBankAccount) {
          this._setSelectedLinkedBankAccount(selectedLinkedBankAccount);
        }

        if (this._userHasNoFunds()) {
          this._setShowDepositMethodsModal(true, "NO_CASH_TOPUP");
          return;
        }

        this.setState((prevState) => {
          return {
            showInvestmentProductModal: false,
            showBuyAssetModal: true,
            triggeredByInvestmentProductModal: prevState.showInvestmentProductModal,
            marketInfo: options?.marketInfo
          };
        });
      } catch (err) {
        captureException(err);
        emitToast({
          content: "We couldn't initiate your investment. Please try again.",
          toastType: ToastTypeEnum.error
        });
      }
    });
  }

  private async _handleAssetSellEvent(
    assetId: investmentUniverseConfig.AssetType,
    options?: {
      marketInfo?: MarketInfoType;
    }
  ): Promise<void> {
    this.setState({ displayedAsset: assetId }, async () => {
      try {
        await Promise.all([
          this._fetchPortfolioWithReturns(),
          this._fetchAvailableHoldings(),
          this._fetchInvestmentProducts(),
          this._hasRestrictedAssetQuantity(assetId)
        ]);

        this.setState((prevState) => {
          return {
            showInvestmentProductModal: false,
            showSellAssetModal: true,
            triggeredByInvestmentProductModal: prevState.showInvestmentProductModal,
            marketInfo: options?.marketInfo
          };
        });
      } catch (err) {
        captureException(err);
        emitToast({
          content: "We couldn't initiate your investment. Please try again.",
          toastType: ToastTypeEnum.error
        });
      }
    });
  }

  private _handleLoadingSplashEvent(loadingSplashMessage: string, loadingSplashColor?: string) {
    this.setState({ loadingSplashMessage, loadingSplashColorClass: loadingSplashColor ?? "primary" });
  }

  private async _handlePendingGiftEvent() {
    const { gift } = this.state;
    if (!gift) {
      await this._fetchGifts();
    }
    this._setShowReceivedGiftModal(true);
  }

  private _hideLoadingSplashMask() {
    this.setState({
      loadingSplashColorClass: DEFAULT_LOADING_SPLASH_COLOR_CLASS,
      loadingSplashMessage: ""
    });
  }

  private _setShowDepositModal = (showDepositModal: boolean): void => {
    this.setState({ showDepositModal });
  };

  private _setShowDepositMethodsModal = (
    showDepositMethodsModal: boolean,
    depositMethodsType: DepositMethodsType = "REGULAR_TOPUP"
  ): void => {
    this.setState({ showDepositMethodsModal, depositMethodsType: depositMethodsType });
  };

  private _setShowWithdrawalModal = (showWithdrawalModal: boolean): void => {
    this.setState({ showWithdrawalModal });
  };

  private _setShowPortfolioBuyModal = (showPortfolioBuyModal: boolean): void => {
    if (showPortfolioBuyModal) {
      if (this._userHasNoFunds()) {
        this._setShowDepositMethodsModal(true, "NO_CASH_TOPUP");
        return;
      }
    }

    this.setState({ showPortfolioBuyModal: showPortfolioBuyModal });
  };

  private _userHasNoFunds(options: { excludeGifts: boolean } = { excludeGifts: false }): boolean {
    const { user } = this.props;
    const { gift } = this.state;
    const portfolio = user.portfolios.length > 0 ? user.portfolios[0] : null;
    const noCash = !portfolio?.cash?.[user.currency]?.available;

    if (options?.excludeGifts) {
      return noCash && !isAllowedOneStepInvest(user);
    } else {
      return noCash && !gift && !isAllowedOneStepInvest(user);
    }
  }

  private _setShowReceivedGiftModal = (showReceivedGiftModal: boolean): void => {
    this.setState({ showReceivedGiftModal: showReceivedGiftModal });
  };

  private _setShowRewardSettledModal = (showRewardSettledModal: boolean): void => {
    this.setState({ showRewardSettledModal: showRewardSettledModal });
  };

  private _setShowSetupRepeatingInvestmentModal = (showSetupRepeatingInvestmentModal: boolean): void => {
    this.setState({ showSetupRepeatingInvestmentModal });
  };

  private _setShowSetupRepeatingSavingsModal(
    showSetupRepeatingSavingsModal: boolean,
    selectedSavingsProduct: {
      id: savingsUniverseConfig.SavingsProductType;
      netInterestRate: string;
    } = null
  ) {
    this.setState({ showSetupRepeatingSavingsModal, selectedSavingsProduct });
  }

  private _setShowSuccessOrderModal = (showSuccessOrderModal: boolean): void => {
    this.setState({ showSuccessOrderModal });
  };

  private _setShowPromptToRepeatingInvestment = (showPromptToRepeatingInvestment: boolean): void => {
    this.setState({ showPromptToRepeatingInvestment });
  };

  private _setShowCancelOrderModal = (showCancelOrderModal: boolean): void => {
    this.setState({ showCancelOrderModal });
  };

  private _closeLinkBankAccountModal = ({ originModal = "" }: { originModal: OtherModalType }): void => {
    const MODAL_CONFIG: Record<OtherModalType, keyof StateType> = {
      assetBuy: "showBuyAssetModal",
      portfolioBuy: "showPortfolioBuyModal",
      newPortfolioBuy: "showPortfolioBuyModal",
      deposit: "showDepositModal",
      withdraw: "showWithdrawalModal",
      setupRepeatingInvestment: "showSetupRepeatingInvestmentModal",
      setupRepeatingSavings: "showSetupRepeatingSavingsModal",
      promptToRepeatingInvestmentModal: "showPromptToRepeatingInvestment",
      bankAccountListModal: "showBankAccountListModal",
      topupSavings: "showSavingsTopupModal",
      "": undefined
    };

    const modalToShow = MODAL_CONFIG[originModal];
    this.setState({
      [modalToShow]: true,
      showLinkBankAccountModal: false,
      triggeredByOtherModal: originModal
    } as any);
  };

  private async _fetchGifts(): Promise<void> {
    try {
      const gifts = (await axios.get<GiftDocument[]>("/gifts")).data;

      if (gifts.length > 0) {
        this.setState({ gift: gifts[0] }, () => {
          if (!gifts[0].hasViewedAppModal) {
            setTimeout(() => {
              this._setShowReceivedGiftModal(true);
            }, RECEIVED_GIFT_MODAL_DELAY);
          }
        });
      }
    } catch (err) {
      captureException(err);
    }
  }

  private async _fetchModals(): Promise<void> {
    try {
      const response = await axios.get<{ modalPrompts: ModalPromptType[] }>("/investor/modal-prompts");
      const modalPrompts = response.data.modalPrompts;
      const rewardSettledModal = modalPrompts.find((modalPrompt) => modalPrompt.modalType === "RewardSettled");

      if (rewardSettledModal) {
        const rewardData = rewardSettledModal.data as RewardModalPromptDataType;
        const reward = rewardData.rewards?.[0];

        if (reward) {
          this.setState({ reward: reward }, () => {
            setTimeout(() => {
              this._setShowRewardSettledModal(true);
            }, SETTLED_REWARD_MODAL_DELAY);
          });
        }
      }
    } catch (err) {
      captureException(err);
    }
  }

  private async _fetchActiveOrPendingMandates(): Promise<void> {
    const { activeOrPendingMandates } = this.state;

    if (activeOrPendingMandates.length === 0) {
      try {
        const mandates = (await axios.get("/mandates", { params: { includeInactive: false, category: "Top-Up" } }))
          .data as MandateDocument[];

        if (mandates.length > 0) {
          this.setState({ activeOrPendingMandates: mandates });
        }
      } catch (err) {
        captureException(err);
      }
    }
  }

  private async _fetchTopUpAutomation(): Promise<void> {
    const { activeTopUpAutomation } = this.state;

    if (!activeTopUpAutomation) {
      try {
        const automations = (await axios.get("/automations")).data as AutomationDocument[];

        if (automations.length > 0) {
          this.setState({
            activeTopUpAutomation: automations.find(
              (automation) => automation.category === "TopUpAutomation" && automation.active
            ) as TopUpAutomationDocument
          });
        }
      } catch (err) {
        captureException(err);
      }
    }
  }

  private async _fetchSavingsTopUpAutomation(): Promise<void> {
    const { activeSavingsTopUpAutomations } = this.state;

    if (activeSavingsTopUpAutomations.length === 0) {
      try {
        const automations = (await axios.get("/automations")).data as AutomationDocument[];

        if (automations.length > 0) {
          this.setState({
            activeSavingsTopUpAutomations: automations.filter(
              (automation) => automation.category === "SavingsTopUpAutomation" && automation.active
            ) as SavingsTopUpAutomationDocument[]
          });
        }
      } catch (err) {
        captureException(err);
      }
    }
  }

  private async _fetchBankProviders(scope: banksConfig.BankProviderScopeEnum): Promise<void> {
    try {
      const response = await axios.get<BankProviderType[]>(`/investor/bank-providers?scope=${scope}`);

      this.setState({ banks: response.data });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load bank providers. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _fetchLinkedBankAccounts(
    callback?: () => void,
    errorCallback?: () => void,
    forceUpdate = false
  ): Promise<void> {
    const { linkedBankAccounts } = this.state;

    if (linkedBankAccounts.length == 0 || forceUpdate) {
      try {
        const fetchedLinkBankAccounts = (await axios.get("/investor/linked-bank-accounts")).data;

        if (fetchedLinkBankAccounts?.length > 0) {
          this.setState({ linkedBankAccounts: fetchedLinkBankAccounts }, () => {
            if (callback) {
              callback();
            }
          });
          return;
        }
      } catch (err) {
        captureException(err);
        if (errorCallback) {
          errorCallback();
        }
        return;
      }
    }

    if (callback) {
      callback();
    }
  }

  private async _fetchPortfolioWithReturns() {
    const { user } = this.props;
    try {
      const { holdingsWithReturns } = this.state;
      if (holdingsWithReturns.length == 0) {
        const res = await axios.get(`/portfolios/${user.portfolios?.[0]?.id}/with-returns-by-tenor`);
        const portfolio = res.data;
        if (portfolio?.holdings?.length > 0) {
          this.setState({ holdingsWithReturns: portfolio.holdings });
        }
      }
    } catch (err) {
      captureException(err);
    }
  }

  private async _fetchAvailableHoldings() {
    const { user } = this.props;
    try {
      const { availableHoldings } = this.state;
      if (Object.keys(availableHoldings).length == 0) {
        const res = await axios.get(`/portfolios/${user.portfolios?.[0]?.id}/available-holdings`);
        const holdings = res.data;
        if (holdings.length > 0) {
          this.setState({ availableHoldings: holdings });
        }
      }
    } catch (err) {
      captureException(err);
    }
  }

  private async _fetchInvestmentProducts() {
    try {
      const { investmentProductsDict } = this.state;
      if (Object.keys(investmentProductsDict).length == 0) {
        const res = await axios.get("/investment-products");
        const investmentProducts = res.data;
        const investmentProductsDict = assetsArrayToDict(investmentProducts);
        this.setState({ investmentProductsDict });
      }
    } catch (err) {
      captureException(err);
    }
  }

  private _hideAssetOrderModals = (): void => {
    this.setState({
      showBuyAssetModal: false,
      showSellAssetModal: false,
      marketInfo: null
    });
  };

  private _getLinkedBankAccountForActiveAutomation() {
    const { activeTopUpAutomation, activeOrPendingMandates, linkedBankAccounts } = this.state;

    if (!activeTopUpAutomation || !activeOrPendingMandates || activeOrPendingMandates.length === 0) {
      return null;
    }

    return linkedBankAccounts.find(
      (bankAccount) =>
        bankAccount.id ===
        (
          activeOrPendingMandates.find(
            (mandate) => mandate.id === (activeTopUpAutomation.mandate as MandateDocument).id
          ).bankAccount as BankAccountDocument
        )._id
    );
  }

  private _getLinkedBankAccountForActiveSavingsAutomation(savingsAutomation: SavingsTopUpAutomationDocument) {
    const { activeOrPendingMandates, linkedBankAccounts } = this.state;

    if (!savingsAutomation || !activeOrPendingMandates || activeOrPendingMandates.length === 0) {
      return null;
    }

    return linkedBankAccounts.find(
      (bankAccount) =>
        bankAccount.id ===
        (
          activeOrPendingMandates.find(
            (mandate) => mandate.id === (savingsAutomation.mandate as MandateDocument).id
          ).bankAccount as BankAccountDocument
        )._id
    );
  }

  async componentDidMount() {
    const { user, lazyLoadData } = this.props;
    const isUserVerified = isVerified(user);

    if (isUserVerified) {
      eventEmitter.on(EVENTS.depositModal, this._handleDepositEvent.bind(this));
      eventEmitter.on(EVENTS.depositMethodsModal, this._handleDepositMethodsEvent.bind(this));
      eventEmitter.on(EVENTS.withdrawalModal, this._handleWithdrawalEvent.bind(this));
      eventEmitter.on(EVENTS.portfolioBuyModal, this._handlePortfolioBuyEvent.bind(this));
      eventEmitter.on(EVENTS.cancelOrderModal, this._handleCancelOrderEvent.bind(this));
      eventEmitter.on(EVENTS.assetBuyModal, this._handleAssetBuyEvent.bind(this));
      eventEmitter.on(EVENTS.assetSellModal, this._handleAssetSellEvent.bind(this));
      eventEmitter.on(EVENTS.linkBankAccountModal, this._handleLinkBankAccountEvent.bind(this));
      eventEmitter.on(EVENTS.setupRepeatingInvestmentModal, this._handleSetupRepeatingInvestmentEvent.bind(this));
      eventEmitter.on(EVENTS.setupRepeatingSavingsModal, this._handleSetupRepeatingSavingsEvent.bind(this));
      eventEmitter.on(EVENTS.orderSuccessModal, this._handleSuccessOrderEvent.bind(this));
      eventEmitter.on(
        EVENTS.promptToRepeatingInvestmentModal,
        this._handlePromptToRepeatingInvestmentEvent.bind(this)
      );
      eventEmitter.on(EVENTS.pendingWhDividendModal, this._handlePendingWhDividendModal.bind(this));

      const params = new URLSearchParams(window.location.search);
      if (params.get("displayPortfolioBuyModal") === "true" && isUserVerified) {
        await this._handlePortfolioBuyEvent();
      }

      this._fetchPortfolioWithReturns();
      this._fetchAvailableHoldings();
    }
    eventEmitter.on(EVENTS.investmentProductModal, this._handleInvestmentProductEvent.bind(this));
    eventEmitter.on(EVENTS.loadingSplashMask, this._handleLoadingSplashEvent.bind(this));
    eventEmitter.on(EVENTS.pendingGiftModal, this._handlePendingGiftEvent.bind(this));
    eventEmitter.on(EVENTS.assetListModal, this._handleAssetListModal.bind(this));
    eventEmitter.on(EVENTS.savingsProductModal, this._handleSavingsProductModal.bind(this));
    eventEmitter.on(EVENTS.savingsTopupModal, this._handleSavingsTopupModal.bind(this));
    eventEmitter.on(EVENTS.savingsWithdrawalModal, this._handleSavingsWithdrawalModal.bind(this));
    eventEmitter.on(EVENTS.savingsProductFeesModal, this._handleSavingsProductFeesModal.bind(this));
    eventEmitter.on(EVENTS.bankAccountList, this._handleBankAccountListModal.bind(this));

    if (!lazyLoadData) {
      this._fetchActiveOrPendingMandates();
      this._fetchTopUpAutomation();
      this._fetchGifts();
      this._fetchModals();
      this._fetchLinkedBankAccounts();
      this._fetchInvestmentProducts();
    }

    const state = this._parseStateParam();
    if (state.modal) {
      this._displayStatePassedModal(
        state.modal as OtherModalType,
        state.asset as investmentUniverseConfig.AssetType,
        state.bankAccountId as string
      );
    }
  }

  private _parseStateParam = (): Record<string, string> => {
    const urlParams = new URLSearchParams(window.location.search);
    const state = urlParams.get("state");

    // If state is present, we've redirected to the current page from Truelayer auth.
    return state
      ? Object.fromEntries(
          state.split("__").map((param) => {
            // The key and the value are separated by an underscore. We only want to split the key and value by this
            // underscore but not split possible underscores inside the value.
            const [key, ...value] = param.split("_");
            return [key, value.join("_")];
          })
        )
      : {};
  };

  private _displayStatePassedModal = async (
    modal: OtherModalType,
    displayedAsset: investmentUniverseConfig.AssetType,
    bankAccountId: string
  ): Promise<void> => {
    switch (modal) {
      case "deposit":
        await this._handleDepositEvent({ bankAccountId });
        return;
      case "portfolioBuy":
        await this._handlePortfolioBuyEvent({ bankAccountId });
        return;
      case "promptToRepeatingInvestmentModal":
        await this._handlePromptToRepeatingInvestmentEvent({
          repeatingInvestmentAmount: this.state.repeatingInvestmentAmount,
          postponeActivation: false,
          bankData: {
            bankAccountId
          }
        });
        return;
      case "setupRepeatingInvestment":
        await this._handleSetupRepeatingInvestmentEvent({ bankAccountId });
        return;
      case "withdraw":
        await this._handleWithdrawalEvent({ bankAccountId });
        return;
      case "assetBuy":
        await this._handleAssetBuyEvent(displayedAsset, {
          bankData: { bankAccountId }
        });
        return;
      case "bankAccountListModal":
        await this._handleBankAccountListModal();
        return;
      default:
        return;
    }
  };

  private _findBankAccount = (bankData?: BankDataType) => {
    const { linkedBankAccounts } = this.state;

    return linkedBankAccounts.find((bankAccount) => bankAccount.id === bankData?.bankAccountId);
  };

  private _setShowReceivedWhDividendModal(showReceivedWhDividendModal: boolean) {
    this.setState({ showReceivedWhDividendModal });
  }

  private async _fetchWhDividends(): Promise<void> {
    try {
      const whDividends = (await axios.get("/transactions/wealthyhood-dividends"))
        .data as WealthyhoodDividendTransactionDocument[];

      if (whDividends.length > 0) {
        this.setState({ whDividends: whDividends }, () => {
          setTimeout(() => {
            this._setShowReceivedWhDividendModal(true);
          }, RECEIVED_WH_DIVIDEND_MODAL_DELAY);
        });
      }
    } catch (err) {
      captureException(err);
    }
  }

  private async _hasRestrictedHoldingsQuantity() {
    const { user } = this.props;
    try {
      const res = await axios.get(`/portfolios/${user.portfolios?.[0]?.id}/restricted-holdings`);
      this.setState({ hasRestrictedHoldingsQuantity: res.data.hasRestrictedQuantity });
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _hasRestrictedAssetQuantity(assetCommonId: string) {
    const { user } = this.props;
    try {
      const res = await axios.get(
        `/portfolios/${user.portfolios?.[0]?.id}/asset-restriction?assetId=${assetCommonId}`
      );
      this.setState({ hasRestrictedAssetQuantity: res.data.hasRestrictedQuantity });
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _handleOrderSuccessButton(transactionTotalAmount: number): Promise<void> {
    const repeatingInvestmentAmount =
      Decimal.div(transactionTotalAmount, 100).toNumber() < 50 ? 5000 : transactionTotalAmount;
    if (this.state.hasNoActiveRepeatingInvestments && !this.state.successOrderDocument.pendingGift) {
      eventEmitter.emit(EVENTS.promptToRepeatingInvestmentModal, {
        repeatingInvestmentAmount,
        postponeActivation: true
      });
    }
    this._setShowSuccessOrderModal(false);
  }

  private async _handlePendingWhDividendModal(): Promise<void> {
    const { whDividends } = this.state;
    if (!whDividends) {
      await this._fetchWhDividends();
    }
    this._setShowReceivedWhDividendModal(true);
  }

  private _getAssetPriceInUserCurrency(asssetCommonId: investmentUniverseConfig.AssetType): number {
    const userCurrency = this.props.user?.currency;
    const { investmentProductsDict } = this.state;
    return investmentProductsDict[asssetCommonId].currentTicker.pricePerCurrency[userCurrency];
  }

  /**
   * Answers the query:
   * Does user hold a certain asset?
   */
  private _isHoldingActive(asssetCommonId: investmentUniverseConfig.AssetType): boolean {
    const portfolio = this.props.user?.portfolios?.[0];
    if (!portfolio) return false;

    return portfolio.holdings.findIndex((holding) => holding.assetCommonId === asssetCommonId) > -1;
  }

  private _getUserAllowedAssets(): Record<
    investmentUniverseConfig.AssetType,
    investmentUniverseConfig.AssetConfigType
  > {
    const { companyEntity } = this.props.user;

    return ConfigUtil.getActiveOnlyInvestmentUniverseAssets(companyEntity);
  }

  private _handleAssetListModal(
    viewMode: AssetDiscoveryListViewModeType,
    assetCollections?: AssetCollectionsType
  ): void {
    this.setState({ assetListViewMode: viewMode, assetCollections });
  }

  private _closeAssetListModal(): void {
    this.setState({ assetListViewMode: undefined, assetCollections: undefined });
  }

  private _setSavingsProductModal(
    showSavingsProductModal: boolean,
    selectedSavingsProduct: {
      id: savingsUniverseConfig.SavingsProductType;
      displaySavingsAmount: string;
      netInterestRate: string;
    } = null
  ) {
    this.setState({ showSavingsProductModal, selectedSavingsProduct });
  }

  private _closeSavingsProductModal() {
    this._setSavingsProductModal(false);
  }

  private async _handleSavingsProductModal(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    displaySavingsAmount: string,
    netInterestRate: string
  ) {
    if (!this.state.savingsProductDataDict[savingsProductId]) {
      const savingsProductData: SavingsProductDataType = (
        await axios.get(`/savings-products/data?savingsProductId=${savingsProductId}`)
      ).data;
      this.setState((prevState) => ({
        showSavingsProductModal: true,
        selectedSavingsProduct: { id: savingsProductId, displaySavingsAmount, netInterestRate },
        savingsProductDataDict: {
          ...prevState.savingsProductDataDict,
          [savingsProductId]: savingsProductData
        }
      }));
    } else {
      this._setSavingsProductModal(true, {
        id: savingsProductId,
        displaySavingsAmount,
        netInterestRate
      });
    }
  }

  private _setShowSavingsTopupModal(
    showSavingsTopupModal: boolean,
    selectedSavingsProduct: {
      id: savingsUniverseConfig.SavingsProductType;
      netInterestRate: string;
    } = null
  ): void {
    this.setState({ showSavingsTopupModal, selectedSavingsProduct });
  }

  private async _handleSavingsTopupModal(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    netInterestRate: string
  ): Promise<void> {
    await Promise.all([
      this._fetchActiveOrPendingMandates(),
      this._fetchSavingsTopUpAutomation(),
      this._fetchLinkedBankAccounts()
    ]);

    if (this._userHasNoFunds({ excludeGifts: true })) {
      this._setShowDepositMethodsModal(true, "NO_CASH_TOPUP");
      return;
    }

    this._setShowSavingsTopupModal(true, {
      id: savingsProductId,
      netInterestRate
    });
  }

  private _setShowSavingsWithdrawalModal(
    showSavingsWithdrawalModal: boolean,
    selectedSavingsProduct: {
      id: savingsUniverseConfig.SavingsProductType;
      displaySavingsAmount: string;
      netInterestRate: string;
      savingsAmount: number;
    } = null
  ): void {
    this.setState({ showSavingsWithdrawalModal, selectedSavingsProduct });
  }

  private _closeSavingsWithdrawalModal() {
    this._setShowSavingsWithdrawalModal(false);
  }

  private _handleSavingsWithdrawalModal(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    displaySavingsAmount: string,
    netInterestRate: string,
    savingsAmount: number
  ): void {
    this._setShowSavingsWithdrawalModal(true, {
      id: savingsProductId,
      displaySavingsAmount,
      netInterestRate,
      savingsAmount
    });
  }

  private async _handleSavingsProductFeesModal(
    savingsProductId: savingsUniverseConfig.SavingsProductType,
    mode: SavingsProductFeesModalViewMode,
    selectedPlan?: plansConfig.PlanType
  ): Promise<void> {
    if (!this.state.savingsProductFeeDetailsDict[savingsProductId]) {
      const savingsProducFeeDetails: SavingsProductFeeDetailsType = (
        await axios.get(`/savings-products/fee-details?savingsProductId=${savingsProductId}`)
      ).data;
      this.setState((prevState) => ({
        showSavingsProductFeesModal: true,
        selectedSavingsProduct: { ...prevState.selectedSavingsProduct, id: savingsProductId },
        savingsProductFeeDetailsDict: {
          ...prevState.savingsProductFeeDetailsDict,
          [savingsProductId]: savingsProducFeeDetails
        },
        savingsProductFeeModalSettings: { mode, selectedPlan }
      }));
    } else {
      this.setState((prevState) => ({
        showSavingsProductFeesModal: true,
        selectedSavingsProduct: { ...prevState.selectedSavingsProduct, id: savingsProductId },
        savingsProductFeeModalSettings: { mode, selectedPlan }
      }));
    }
  }

  private _setShowBankAccountListModal(showBankAccountListModal: boolean) {
    this.setState({ showBankAccountListModal });
  }

  private async _handleBankAccountListModal() {
    await this._fetchLinkedBankAccounts(
      async () => {
        this._hideLoadingSplashMask();
        this._setShowBankAccountListModal(true);
      },
      () => {
        emitToast({
          content: "Please try again later.",
          toastType: ToastTypeEnum.error
        });
      },
      true
    );
  }

  render(): JSX.Element {
    const { children, user, activePage } = this.props;
    const {
      showReceivedWhDividendModal,
      showDepositModal,
      showDepositMethodsModal,
      showWithdrawalModal,
      showPortfolioBuyModal,
      showCancelOrderModal,
      showSuccessOrderModal,
      showBuyAssetModal,
      showSellAssetModal,
      showLinkBankAccountModal,
      showReceivedGiftModal,
      showRewardSettledModal,
      showSetupRepeatingInvestmentModal,
      showSetupRepeatingSavingsModal,
      showPromptToRepeatingInvestment,
      showSavingsProductModal,
      loadingSplashMessage,
      loadingSplashColorClass,
      linkedBankAccounts,
      holdingsWithReturns,
      availableHoldings,
      investmentProductsDict,
      displayedAsset,
      transactionToCancel,
      triggeredByOtherModal,
      gift,
      reward,
      activeOrPendingMandates,
      activeTopUpAutomation,
      activeSavingsTopUpAutomations,
      selectedLinkedBankAccount,
      selectedBankProvider,
      whDividends,
      hasRestrictedAssetQuantity,
      assetListViewMode,
      assetCollections,
      successOrderDocument,
      savingsProductDataDict,
      selectedSavingsProduct,
      showSavingsTopupModal,
      showSavingsWithdrawalModal,
      showSavingsProductFeesModal,
      savingsProductFeeDetailsDict,
      savingsProductFeeModalSettings,
      showBankAccountListModal,
      showDepositMethodsOnDepositModalClose,
      postponeTopUpActivation,
      marketInfo,
      banks,
      depositMethodsType: depositMethodsType
    } = this.state;
    const portfolio = user.portfolios.length > 0 ? user.portfolios[0] : null;
    const displayedHolding = holdingsWithReturns.find((holding) => holding.assetCommonId == displayedAsset);
    const availableHoldingsDict = Object.fromEntries(
      availableHoldings.map((holding: HoldingsType) => [holding.assetCommonId, holding])
    ) as Record<investmentUniverseConfig.AssetType, HoldingsType>;

    const isUserVerified = isVerified(user);

    const existingSavingsTopupAutomation = activeSavingsTopUpAutomations.find(
      (automation) => automation?.savingsProduct === selectedSavingsProduct?.id
    );

    return (
      <>
        {children}
        {isUserVerified && (
          <DepositModal
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={selectedLinkedBankAccount}
            initialSelectedBank={selectedBankProvider}
            show={showDepositModal}
            handleClose={() => {
              this._setSelectedBankProvider(null);

              if (showDepositMethodsOnDepositModalClose) {
                this._setShowDepositMethodsModal(true);
              }
              this._setShowDepositModal(false);
            }}
          />
        )}
        {isUserVerified && (
          <DepositMethodsModal
            show={showDepositMethodsModal}
            handleClose={() => this._setShowDepositMethodsModal(false)}
            walletIban={(user.wallet as WalletDocument)?.iban}
            bankAccounts={linkedBankAccounts}
            banks={banks}
            type={depositMethodsType}
          />
        )}
        {/* Withdrawal Modal */}
        {portfolio && isUserVerified && (
          <WithdrawalModal
            amountAvailableToWithdraw={portfolio.amountAvailableToWithdraw}
            unsettledCash={portfolio.unsettledCash}
            initialSelectedLinkedBankAccount={selectedLinkedBankAccount}
            show={showWithdrawalModal}
            portfolioId={portfolio._id}
            linkedBankAccounts={linkedBankAccounts}
            handleClose={() => this._setShowWithdrawalModal(false)}
          />
        )}
        {/* End Withdrawal Modal */}
        {portfolio && isUserVerified && (
          <PortfolioBuyModal
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={selectedLinkedBankAccount}
            activeOrPendingMandates={activeOrPendingMandates}
            existingTopUpAutomation={activeTopUpAutomation}
            gift={gift}
            availableCash={portfolio.cash?.[user.currency]?.available || 0}
            hasHoldings={portfolio.holdings?.length > 0}
            portfolioId={portfolio.id}
            isPortfolioAllocationSetup={portfolio.isTargetAllocationSetup}
            show={showPortfolioBuyModal}
            handleClose={(): void => this._setShowPortfolioBuyModal(false)}
            isFirstInvestment={user.portfolioConversionStatus === "notStarted"}
          />
        )}
        {portfolio && isUserVerified && selectedSavingsProduct && (
          <SavingsTopupModal
            savingsProductId={selectedSavingsProduct.id}
            netInterestRate={selectedSavingsProduct.netInterestRate}
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={selectedLinkedBankAccount}
            availableCash={portfolio.cash?.[user.currency]?.available || 0}
            portfolioId={portfolio.id}
            show={showSavingsTopupModal}
            handleClose={(): void => this._setShowSavingsTopupModal(false)}
            existingSavingsTopUpAutomation={existingSavingsTopupAutomation}
            activeOrPendingMandates={activeOrPendingMandates}
          />
        )}
        {portfolio && isUserVerified && selectedSavingsProduct && (
          <SavingsWithdrawalModal
            savingsProductId={selectedSavingsProduct.id}
            displayAvailableSavingsAmount={selectedSavingsProduct.displaySavingsAmount}
            availableSavingsAmount={selectedSavingsProduct.savingsAmount}
            availableCash={portfolio.cash?.[user.currency]?.available || 0}
            portfolioId={portfolio.id}
            show={showSavingsWithdrawalModal}
            handleClose={(): void => this._closeSavingsWithdrawalModal()}
          />
        )}
        {portfolio && isUserVerified && (
          <CancelOrderModal
            transaction={transactionToCancel}
            show={showCancelOrderModal}
            handleClose={(): void => this._setShowCancelOrderModal(false)}
          />
        )}
        {portfolio && isUserVerified && (
          <OrderSuccessModal
            show={showSuccessOrderModal}
            handleClose={() => this._setShowSuccessOrderModal(false)}
            transaction={successOrderDocument}
            investmentProducts={assetsDictToArray(
              investmentProductsDict as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>
            )}
            onSuccessButtonCallback={(transactionTotalAmount: number) =>
              this._handleOrderSuccessButton(transactionTotalAmount)
            }
          />
        )}
        {/* Investment Product Modal */}
        {displayedAsset &&
          Object.keys(investmentProductsDict).length > 0 &&
          (user.hasPassedKyc ? (
            <AssetSideModal
              userCurrency={user.currency}
              assetCommonId={displayedAsset}
              handleClose={() => {
                this.setState({ displayedAsset: null });
              }}
              includeInvestmentDetails={this._isHoldingActive(displayedAsset)}
              includeSellButton={this._isHoldingActive(displayedAsset)}
              includeRecentActivity={true}
              // optionals
              handleBuy={(marketInfo: MarketInfoType): void => {
                this._handleAssetBuyEvent(displayedAsset, { marketInfo });
              }}
              handleSell={(marketInfo: MarketInfoType): void => {
                this._handleAssetSellEvent(displayedAsset, { marketInfo });
              }}
              currentTickerPrice={investmentProductsDict[displayedAsset].currentTicker?.price}
              // end optionals
            />
          ) : (
            <AssetSideModal
              userCurrency={user.currency}
              assetCommonId={displayedAsset as investmentUniverseConfig.AssetType}
              handleClose={() => {
                this.setState({ displayedAsset: null });
              }}
              includeInvestmentDetails={false}
              includeSellButton={false}
              includeRecentActivity={false}
            />
          ))}
        {/* End Investment Product Modal */}
        {/* Buy Asset Modal */}
        {isUserVerified && portfolio && Object.keys(investmentProductsDict).length > 0 && displayedAsset && (
          <AssetBuyOrderModal
            portfolioId={portfolio.id}
            marketInfo={marketInfo}
            gift={gift}
            initialSelectedLinkedBankAccount={selectedLinkedBankAccount}
            assetCommonId={displayedAsset}
            assetPrice={investmentProductsDict[displayedAsset].tradedPrice}
            tradedCurrency={investmentProductsDict[displayedAsset].tradedCurrency}
            assetPriceInUserCurrency={this._getAssetPriceInUserCurrency(displayedAsset)}
            availableCash={portfolio.cash?.[user.currency]?.available}
            show={showBuyAssetModal}
            linkedBankAccounts={linkedBankAccounts}
            handleClose={(): void => this._hideAssetOrderModals()}
          />
        )}
        {/* End Buy Asset Modal */}
        {/* Sell Asset Modal */}
        {isUserVerified &&
          portfolio &&
          Object.keys(investmentProductsDict).length > 0 &&
          displayedAsset &&
          displayedHolding && (
            <AssetSellOrderModal
              assetCommonId={displayedAsset}
              marketInfo={marketInfo}
              portfolioId={portfolio.id}
              assetBoughtQuantity={
                availableHoldingsDict[displayedAsset] ? availableHoldingsDict[displayedAsset].quantity : 0
              }
              assetPrice={investmentProductsDict[displayedAsset].tradedPrice}
              tradedCurrency={investmentProductsDict[displayedAsset].tradedCurrency}
              assetPriceInUserCurrency={this._getAssetPriceInUserCurrency(displayedAsset)}
              show={showSellAssetModal}
              handleClose={(): void => this._hideAssetOrderModals()}
              hasRestrictedQuantity={hasRestrictedAssetQuantity}
              key={`asset-sell-modal_${displayedAsset}_${showSellAssetModal}`}
            />
          )}
        {/* End Sell Asset Modal */}
        {/* Link Bank Account Modal*/}
        <LinkBankAccountModal
          show={showLinkBankAccountModal}
          originModal={triggeredByOtherModal}
          displayedAsset={displayedAsset}
          activePage={activePage}
          userCompanyEntity={user.companyEntity}
          handleClose={() => {
            this._closeLinkBankAccountModal({ originModal: triggeredByOtherModal });
          }}
          banks={banks}
        />
        {/* End Link Bank Account Modal*/}
        {/* New Gift Modal*/}
        {gift && (
          <ReceivedGiftModal
            handleClose={() => this._setShowReceivedGiftModal(false)}
            show={showReceivedGiftModal}
            gift={gift}
          />
        )}
        {/* End New Gift Modal*/}
        {/* Reward Settled Modal*/}
        {reward && (
          <RewardSettledModal
            handleClose={() => this._setShowRewardSettledModal(false)}
            show={showRewardSettledModal}
            reward={reward}
          />
        )}
        {/* End Reward Settled Modal*/}
        {/* Setup Repeating Investment Modal */}
        {portfolio && isUserVerified && (
          <SetupRepeatingInvestmentModal
            initialOrderAmount={
              activeTopUpAutomation
                ? Decimal.div(activeTopUpAutomation.consideration.amount, 100).toNumber()
                : DEFAULT_REPEATING_INVESTMENT_AMOUNT
            }
            isPortfolioAllocationSetup={portfolio.isTargetAllocationSetup}
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={
              selectedLinkedBankAccount ?? this._getLinkedBankAccountForActiveAutomation()
            }
            activeOrPendingMandates={activeOrPendingMandates}
            existingTopUpAutomation={activeTopUpAutomation}
            holdings={portfolio?.holdings || []}
            show={showSetupRepeatingInvestmentModal}
            handleClose={(): void => this._setShowSetupRepeatingInvestmentModal(false)}
          />
        )}
        {/* End Setup Repeating Investment Modal */}
        {/* Setup Repeating Savings Modal */}
        {selectedSavingsProduct?.id && portfolio && isUserVerified && (
          <SetupRepeatingSavingsModal
            initialOrderAmount={
              existingSavingsTopupAutomation
                ? Decimal.div(existingSavingsTopupAutomation.consideration.amount, 100).toNumber()
                : DEFAULT_REPEATING_INVESTMENT_AMOUNT
            }
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={
              selectedLinkedBankAccount ??
              this._getLinkedBankAccountForActiveSavingsAutomation(existingSavingsTopupAutomation)
            }
            activeOrPendingMandates={activeOrPendingMandates}
            savingsProductId={selectedSavingsProduct.id}
            existingSavingsTopUpAutomation={existingSavingsTopupAutomation}
            show={showSetupRepeatingSavingsModal}
            handleClose={(): void => this._setShowSetupRepeatingSavingsModal(false)}
            netInterestRate={selectedSavingsProduct.netInterestRate}
          />
        )}
        {/* End Setup Repeating Savings Modal */}
        {portfolio && isUserVerified && (
          <PromptToRepeatingInvestmentModal
            postponeActivation={postponeTopUpActivation}
            isPortfolioAllocationSetup={portfolio.isTargetAllocationSetup}
            initialOrderAmount={
              Decimal.div(this.state.repeatingInvestmentAmount, 100).toNumber() ??
              DEFAULT_REPEATING_INVESTMENT_AMOUNT
            }
            linkedBankAccounts={linkedBankAccounts}
            initialSelectedLinkedBankAccount={
              selectedLinkedBankAccount ?? this._getLinkedBankAccountForActiveAutomation()
            }
            activeOrPendingMandates={activeOrPendingMandates}
            holdings={portfolio?.holdings || []}
            show={showPromptToRepeatingInvestment}
            handleClose={(): void => this._setShowPromptToRepeatingInvestment(false)}
          />
        )}
        {/* End Setup Repeating Investment Modal */}
        {/* Loading Splash Mask*/}
        {loadingSplashMessage && (
          <div className={`container-fluid vh-100 fixed-top bg-loading-${loadingSplashColorClass}`}>
            <div className="row justify-content-center m-0 h-100">
              <div className="d-flex flex-column align-self-center justify-content-center">
                <div className="row justify-content-center mb-4">
                  <img
                    className="p-0 animate-flicker"
                    alt=""
                    src="/images/icons/logo-white.svg"
                    style={{ height: "65px", width: "65px" }}
                  />
                </div>
                <p className="text-center text-light">{loadingSplashMessage}</p>
              </div>
            </div>
          </div>
        )}
        {/* End Loading Splash Mask*/}
        {whDividends && (
          <ReceivedWealthyhoodDividendModal
            show={showReceivedWhDividendModal}
            handleClose={() => this._setShowReceivedWhDividendModal(false)}
            whDividends={whDividends}
          />
        )}
        {assetListViewMode && Object.keys(investmentProductsDict).length > 0 && (
          <AssetListModal
            show={true}
            handleClose={() => this._closeAssetListModal()}
            viewMode={assetListViewMode}
            assetCollections={assetCollections}
            portfolio={portfolio}
            investmentProducts={assetsDictToArray(
              investmentProductsDict as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>
            )}
            universe={this._getUserAllowedAssets()}
          />
        )}
        {showSavingsProductModal && (
          <SavingsProductSideModal
            handleClose={() => this._closeSavingsProductModal()}
            savingsProductId={selectedSavingsProduct.id}
            savingsProductData={savingsProductDataDict[selectedSavingsProduct.id]}
            netInterestRate={selectedSavingsProduct.netInterestRate}
            displaySavingsAmount={selectedSavingsProduct.displaySavingsAmount}
          />
        )}
        {showSavingsProductFeesModal && (
          <SavingsProductFeesModal
            handleClose={() => this.setState({ showSavingsProductFeesModal: false })}
            savingsProductFeeDetails={savingsProductFeeDetailsDict[selectedSavingsProduct.id]}
            mode={savingsProductFeeModalSettings.mode}
            selectedPlan={savingsProductFeeModalSettings.selectedPlan}
          />
        )}

        {showBankAccountListModal && (
          <BankAccountListModal
            linkedBankAccounts={linkedBankAccounts}
            handleClose={() => this._setShowBankAccountListModal(false)}
          />
        )}
      </>
    );
  }
}

export default ModalsWrapper;
