import React, { Component } from "react";
import ToggleSwitch from "./toggleSwitch";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { EmailNotificationSettingEnum, Notification } from "../types/notifications";

const EMAIL_NOTIFICATION_ICON_MAP: Record<EmailNotificationSettingEnum, string> = {
  [EmailNotificationSettingEnum.TRANSACTIONAL]: "/images/notification-settings/swap_vert.svg",
  [EmailNotificationSettingEnum.PROMOTIONAL]: "/images/notification-settings/notifications_active.svg",
  [EmailNotificationSettingEnum.WEALTHYBITES]: "/images/notification-settings/mail.svg"
};

type PropsType = {
  notification: Notification<EmailNotificationSettingEnum>;
};

type StateType = {
  active: boolean;
  loading: boolean;
};

class NotificationItem extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      active: props.notification.active,
      loading: false
    };
  }

  private _handleToggleSwitchClick = async () => {
    if (this.state.loading) {
      return; // Prevent multiple clicks while loading
    }

    const newActiveState = !this.state.active;

    this.setState({ loading: true });

    try {
      await axios.post(`/investor/notification-settings/update`, {
        notificationId: this.props.notification.id,
        active: newActiveState
      });

      this.setState({ active: newActiveState });

      emitToast({
        content: `${this.props.notification.name} is ${newActiveState ? "enabled" : "disabled"}`,
        toastType: ToastTypeEnum.success
      });
    } catch (err: any) {
      emitToast({
        content: err.response?.data?.message || "Failed to update notification setting",
        toastType: ToastTypeEnum.error
      });
    } finally {
      this.setState({ loading: false });
    }
  };

  render(): JSX.Element {
    const { notification } = this.props;
    const { active, loading } = this.state;

    return (
      <div key={notification.id} className="row align-items-center">
        <div className="col-1 p-0 text-center">
          <img src={EMAIL_NOTIFICATION_ICON_MAP[notification.id]} alt={notification.name} width={16} height={16} />
        </div>
        <div className="col-9 p-0">
          <div className="fw-bold">{notification.name}</div>
          <div className="text-muted t-875 m-0">{notification.description}</div>
        </div>
        <div className="col-2 p-0 text-center">
          <ToggleSwitch checked={active} disabled={loading} customonclick={this._handleToggleSwitchClick} />
        </div>
      </div>
    );
  }
}

export default NotificationItem;
