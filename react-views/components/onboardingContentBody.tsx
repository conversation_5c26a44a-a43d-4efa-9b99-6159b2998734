import React from "react";

type PropsType = {
  title: string;
};

class OnboardingContentBody extends React.Component<PropsType> {
  render(): JSX.Element {
    const { title, children } = this.props;

    return (
      <div className="content d-flex flex-column flex-column-fluid">
        <div className="d-flex flex-column-fluid">
          <div className="container py-19">
            <div className="d-flex align-items-center flex-wrap mb-10">
              <div className="d-flex align-items-baseline flex-wrap mx-auto">
                <h1 className="text-center font-size-h1 h1-md text-dark font-weight-bolder my-1 mr-5">{title}</h1>
              </div>
            </div>
            {children}
          </div>
        </div>
      </div>
    );
  }
}

export default OnboardingContentBody;
