import React from "react";

class OnboardingContentFooter extends React.Component {
  render(): JSX.Element {
    return (
      <div className="footer py-10 d-flex flex-lg-column" id="kt_footer">
        <div className="container font-size-lg">
          <div className="row justify-content-between">
            <div className="col-3">
              <div className="text-dark">
                <span className="text-muted font-weight-bold mr-2">{new Date().getFullYear()}©</span>
                <a
                  className="text-dark-50 text-hover-primary"
                  href="https://wealthyhood.com"
                  target="_blank"
                  rel="noreferrer"
                >
                  Wealthyhood
                </a>
              </div>
            </div>
            <div className="col-3">
              <div className="nav nav-dark">
                <a
                  className="nav-link ml-auto py-2 text-dark-50"
                  href="https://wealthyhood.com"
                  target="_blank"
                  rel="noreferrer"
                >
                  About
                </a>
              </div>
            </div>
          </div>
          <hr className="pb-6 pt-0 mt-0" />
          <div className="row py-2">
            <div className="col-12">
              <span className="text-dark-50">Disclaimer: When investing, your capital is at risk.</span>
            </div>
          </div>
          <div className="row">
            <div className="col-12">
              <span className="text-dark-50">
                Wealthyhood (Wealthyhood Ltd, FCA Register: 933675) is an appointed representative of RiskSave
                Technologies Ltd, which is authorised and regulated by the Financial Conduct Authority (FRN
                775330).
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default OnboardingContentFooter;
