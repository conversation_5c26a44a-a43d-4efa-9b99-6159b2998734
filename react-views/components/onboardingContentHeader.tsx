import React from "react";

class OnboardingContentHeader extends React.Component {
  render(): JSX.Element {
    return (
      <div className="header" id="kt_header">
        <div className="container d-flex align-items-center justify-content-between">
          <div className="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">
            <div className="header-logo">
              <a href="/">
                <img className="h-30px" alt="Logo" src="https://wealthyhood.com/img/logo-full-dark.png" />
              </a>
            </div>
          </div>
          <div className="topbar">
            <div className="topbar-item mr-3">
              <a className="btn btn-block btn-sm btn-light-primary font-weight-bolder py-3 px-6" href="/logout">
                Log Out
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default OnboardingContentHeader;
