import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import PortfolioSetupAddableAssetRowNew from "./portfolioSetupAddableAssetRowNew";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil, { InvestmentUniverseAssets } from "../../utils/configUtil";
import {
  compareAssets,
  getAssetIconUrl,
  getAssetsFilteredOnSearchTerm,
  sortSearchAssets
} from "../utils/universeUtil";

const { InvestmentSectorArray } = investmentUniverseConfig;

type PropsType = {
  selectedUniverse: Set<investmentUniverseConfig.AssetType>;
  onAssetClick: (assetKey: investmentUniverseConfig.AssetType) => void;
  onAssetSelection: (assetKey: investmentUniverseConfig.AssetType) => void;
};

type StateType = {
  selectedAssetClass: investmentUniverseConfig.AssetClassType;
  searchTerm: string;
  assetsFilteredOnSearchTerm: investmentUniverseConfig.AssetType[];
};

class OnboardingEditableUniverse extends React.Component<PropsType, StateType> {
  private _universeAssets: InvestmentUniverseAssets;
  private _assetClassContainerRef: React.RefObject<HTMLDivElement>;
  private _assetClassRefs: Map<string, React.RefObject<HTMLDivElement>>;

  constructor(props: PropsType) {
    super(props);

    this.state = {
      selectedAssetClass: "equities",
      searchTerm: "",
      assetsFilteredOnSearchTerm: []
    };

    // Initialize refs
    this._assetClassContainerRef = React.createRef();
    this._assetClassRefs = new Map();

    // Create refs for each asset class
    investmentUniverseConfig.AssetClassArray.forEach((assetClass) => {
      this._assetClassRefs.set(assetClass, React.createRef());
    });
  }

  componentDidMount(): void {
    this._universeAssets = ConfigUtil.getActiveOnlyInvestmentUniverseAssets(
      (this.context as GlobalContextType).user.companyEntity
    );
  }

  private _handleSearchTermChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = event.target.value;
    const assetsFilteredOnSearchTerm = getAssetsFilteredOnSearchTerm(searchTerm, this._universeAssets);

    this.setState({ searchTerm, assetsFilteredOnSearchTerm });
  };

  private _isAssetClassSelected(assetClass: investmentUniverseConfig.AssetClassType): boolean {
    return assetClass == this.state.selectedAssetClass;
  }

  private _setSelectedAssetClass = (selectedAssetClass: investmentUniverseConfig.AssetClassType): void => {
    window.scrollTo(0, 0);
    this.setState({ selectedAssetClass });
    this._scrollToAssetClass(selectedAssetClass);
  };

  private _scrollToAssetClass = (assetClass: investmentUniverseConfig.AssetClassType): void => {
    const container = this._assetClassContainerRef.current;
    const targetElement = this._assetClassRefs.get(assetClass)?.current;

    if (container && targetElement) {
      const containerRect = container.getBoundingClientRect();
      const targetRect = targetElement.getBoundingClientRect();

      // Calculate the relative position of the target within the container
      const targetRelativeLeft = targetRect.left - containerRect.left;

      // Calculate scroll position to center the selected element
      const scrollLeft =
        container.scrollLeft + targetRelativeLeft - container.clientWidth / 2 + targetRect.width / 2;

      container.scrollTo({
        left: scrollLeft,
        behavior: "smooth"
      });
    }
  };

  private _renderAssetsList = (assetClass: investmentUniverseConfig.AssetClassType) => {
    const { selectedAssetClass, assetsFilteredOnSearchTerm, searchTerm } = this.state;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);
    const AssetsInSelectedAssetClass = ASSET_CLASS_CONFIG[selectedAssetClass].assets;
    let finalAssets = AssetsInSelectedAssetClass;

    if (searchTerm) {
      // if there is search terms filter on search term as well
      finalAssets = assetsFilteredOnSearchTerm
        .filter((asset) => AssetsInSelectedAssetClass.includes(asset))
        .sort((a, b) => sortSearchAssets(a, b, searchTerm));
    } else {
      finalAssets = finalAssets.sort(compareAssets);
    }

    if (!finalAssets.length) return <div className="text-center py-5">No results found</div>;

    const ASSET_CONFIG = ConfigUtil.getActiveOnlyInvestmentUniverseAssets(
      (this.context as GlobalContextType).user.companyEntity
    );
    const { selectedUniverse, onAssetClick, onAssetSelection } = this.props;

    // Equites with sectors
    if (assetClass === "equities" && !searchTerm) {
      return InvestmentSectorArray.map((sector) => {
        const sectorAssets = finalAssets.filter((asset) => ASSET_CONFIG[asset].sector === sector);

        if (sectorAssets.length === 0) {
          return null;
        }

        return (
          <div className="row my-4" key={`onboarding-editable-mobile-sector-${sector}`}>
            <div className="col h6 fw-bold">{SECTOR_CONFIG[sector].fieldName}</div>
            {sectorAssets.map((asset) =>
              this._createPortfolioSetupRow(asset, selectedUniverse.has(asset), onAssetClick, onAssetSelection)
            )}
          </div>
        );
      });
      // Rest of the classes without sectors
    } else {
      return (
        <div className="row my-4" key={`onboarding-editable-mobile-asset-class-${assetClass}`}>
          {!searchTerm && <div className="col h6 fw-bold">{ASSET_CLASS_CONFIG[assetClass].fieldName}</div>}
          {finalAssets.map((asset) =>
            this._createPortfolioSetupRow(asset, selectedUniverse.has(asset), onAssetClick, onAssetSelection)
          )}
        </div>
      );
    }
  };

  private _createPortfolioSetupRow(
    asset: investmentUniverseConfig.AssetType,
    isSelected: boolean,
    onAssetClick: (assetKey: investmentUniverseConfig.AssetType) => void,
    onAssetSelection: (assetKey: investmentUniverseConfig.AssetType) => void
  ) {
    const ASSET_CONFIG = ConfigUtil.getActiveOnlyInvestmentUniverseAssets(
      (this.context as GlobalContextType).user.companyEntity
    );
    const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[asset];

    return (
      <PortfolioSetupAddableAssetRowNew
        key={asset}
        simpleName={simpleName}
        description={tickerWithCurrency + " • " + shortDescription}
        logoUrl={getAssetIconUrl(asset)}
        isSelected={isSelected}
        onAssetClick={(): void => onAssetClick(asset)}
        onSelectionClick={(): void => {
          onAssetSelection(asset);
        }}
        category={category}
      />
    );
  }

  render(): JSX.Element {
    const { selectedAssetClass } = this.state;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);

    return (
      <>
        <div className="d-flex p-3 mb-4 assets-search-container">
          <span className="material-symbols-outlined me-2 icon-primary">search</span>
          <input
            className="fw-bold assets-search-input border-0"
            type="text"
            placeholder="Search by name or type, eg. ‘tech’, ‘ETF’"
            onChange={this._handleSearchTermChange}
          ></input>
        </div>
        <div
          className="d-flex flex-row flex-nowrap overflow-auto no-scroll-bar rounded-asset-classes"
          ref={this._assetClassContainerRef}
        >
          {investmentUniverseConfig.AssetClassArray.map((assetClass) => (
            <div
              key={assetClass}
              ref={this._assetClassRefs.get(assetClass)}
              className={
                "cursor-pointer col py-2 px-3 text-center align-self-center text-center text-nowrap " +
                (this._isAssetClassSelected(assetClass) ? "active-sector" : "")
              }
              style={
                this._isAssetClassSelected(assetClass)
                  ? { background: ASSET_CLASS_CONFIG[assetClass].colorClass }
                  : {}
              }
              onClick={() => this._setSelectedAssetClass(assetClass)}
            >
              {ASSET_CLASS_CONFIG[assetClass].fieldName}
            </div>
          ))}
        </div>
        {this._renderAssetsList(selectedAssetClass)}
      </>
    );
  }
}

OnboardingEditableUniverse.contextType = GlobalContext;

export default OnboardingEditableUniverse;
