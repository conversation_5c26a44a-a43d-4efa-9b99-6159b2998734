import React from "react";
import MaskedInput from "react-text-mask";
import createNumberMask from "text-mask-addons/dist/createNumberMask";

const defaultMaskOptions = {
  prefix: "£",
  suffix: "",
  includeThousandsSeparator: true,
  thousandsSeparatorSymbol: ",",
  allowDecimal: true,
  decimalSymbol: ".",
  decimalLimit: 2, // how many digits allowed after the decimal
  integerLimit: 6, // limit length of integer numbers
  allowNegative: false,
  allowLeadingZeroes: false
};

type PropsType = React.InputHTMLAttributes<HTMLInputElement> & {
  prefix?: string;
  suffix?: string;
  placeholderAmount: number;
  decimalLimit: number;
  maxNumberLimit?: number;
  amount?: string; // optional, use this to bind parent's state value
  onAmountChange: (newAmount: string) => void;
};

class OrderInput extends React.Component<PropsType> {
  static defaultProps = {
    prefix: "",
    suffix: "",
    maxNumberLimit: 999999
  };
  private _inputRef: React.RefObject<MaskedInput>;

  constructor(props: PropsType) {
    super(props);
    this._inputRef = React.createRef<MaskedInput>();
  }

  /**
   * @description Removes currency prefix and non numerical characters and returns a parsable number in string format
   * @param event
   * @returns
   */
  private _getNumericalValue(event: React.ChangeEvent<HTMLInputElement>): string {
    const eventValue = event.target.value;
    const { prefix, suffix, maxNumberLimit, amount } = this.props;
    const fixedValue = eventValue.replace(prefix, "").replace(suffix, "").split(",").join("");

    // when input starting with dot a string containing '_' is emitted
    // so by doing the following we disable typing a dot before number
    if (fixedValue.includes("_")) {
      return amount;
    }

    const fixedValueNum = parseFloat(fixedValue);
    if (fixedValueNum <= maxNumberLimit || fixedValue == "") {
      // value is lower than max limit or empty
      return fixedValue;
    } else {
      // previous value ,amount should be passed as prop for this to work
      return amount;
    }
  }

  componentDidMount = (): void => {
    // FIXME: Something better must be done for the following issue.For now this works.
    // The inner input element possibly is not ready during componentDinMount
    // and focus() does not work. So a timeout is added to give time to input element to be ready
    setTimeout(() => {
      this._inputRef.current.inputElement.focus();
    }, 500);
  };

  render(): JSX.Element {
    const {
      prefix,
      suffix,
      placeholderAmount,
      decimalLimit,
      amount,
      onAmountChange,
      maxNumberLimit,
      ...htmlProps
    } = this.props;
    const maskOptions = Object.assign(defaultMaskOptions, { prefix, suffix, decimalLimit });
    const currencyMask = createNumberMask(maskOptions);

    return (
      <span className="d-flex align-items-end justify-content-center mb-3">
        {/* we must rerender masked input after amount changes from null to value to show prefix this is a hack*/}
        <MaskedInput
          key={"masked-input-" + (!amount).toString()}
          ref={this._inputRef}
          autoFocus={true}
          className="border-0 p-0 bg-transparent order-input"
          placeholder={prefix + placeholderAmount + suffix}
          value={amount}
          onChange={(event): void => onAmountChange(this._getNumericalValue(event))}
          mask={currencyMask}
          {...htmlProps}
        />
      </span>
    );
  }
}

export default OrderInput;
