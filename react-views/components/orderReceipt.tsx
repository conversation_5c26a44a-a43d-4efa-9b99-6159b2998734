import {
  formatDateToDayDashHHMM,
  formatDateToDDMONYY,
  formatDateToDDMONYYHHMM,
  formatDateToHHMMSS
} from "../utils/dateUtil";
import { formatCurrency } from "../utils/currencyUtil";
import React from "react";
import { ExecutionWindowType, MarketHoursExecutionWindowType } from "../../models/Transaction";
import { OrderSideType } from "../../services/wealthkernelService";
import { entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import AssetIcon from "./assetIcon";
import { getAssetIconUrl } from "../utils/universeUtil";
import { OrderDocument, OrderStatusType, UnitPriceType } from "../../models/Order";
import { AssetPriceInfo } from "../types/price";
import HoverableInfoIcon from "./hoverableInfoIcon";
import FxRatePreviewRow from "./fxRatePreviewRow";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { getTotalCommission } from "../utils/feesUtil";
import { getReportingFirm } from "../utils/userUtil";
import { captureException } from "@sentry/react";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  order: OrderDocument;
  executionWindow: ExecutionWindowType;
  price: UnitPriceType | AssetPriceInfo;
  transactionId?: string;
  isTransactionCancellable?: boolean;
  isPartOfPortfolioTransaction?: boolean;
};

const STATUS_CONFIG: Record<OrderStatusType, { label: string; color: string }> = {
  Pending: { label: "Pending", color: "warning" },
  Cancelled: { label: "Cancelled", color: "danger" },
  Rejected: { label: "Rejected", color: "danger" },
  Matched: { label: "Settled", color: "success" },
  Settled: { label: "Settled", color: "success" }
};

type StateType = {
  loadingTradeConfirmation: boolean;
};

class OrderReceipt extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      loadingTradeConfirmation: false
    };
  }

  private static _getSeparatorClass(side: OrderSideType): string {
    return side == "Buy" ? "receipt-separator-success" : "receipt-separator-danger";
  }

  private _shouldShowTradeConfirmation(): boolean {
    const { user } = this.context as GlobalContextType;

    return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE;
  }

  private _downloadTradeConfirmation = async (): Promise<void> => {
    const { order } = this.props;
    this.setState({ loadingTradeConfirmation: true });

    try {
      let uri = `/orders/${order.id}/trade-confirmations/generate`;
      const response = await axios.post(uri);

      window.open(response.data.fileUri, "_blank");
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    } finally {
      this.setState({ loadingTradeConfirmation: false });
    }
  };

  private _cancelTransaction = async (transactionId: string) => {
    try {
      const res = await axios.post(`/transactions/${transactionId}/cancel`, {});
      if (res.data.redirectUri) {
        window.location.replace(res.data.redirectUri);
      }
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
      throw err;
    }
  };

  private _getCommissionHoverText(): string {
    const { user } = this.context as GlobalContextType;

    if (!user.isRealtimeETFExecutionEnabled) {
      return "Wealthyhood is commission-free, which means we charge ZERO COMMISSIONS for all stocks and ETFs.";
    }

    return "With Smart Execution your Portfolio Buy order is COMMISSION-FREE.\n\nIf you de-activate smart execution, you are charged €1 per ETF order. All orders for individual stocks remain commission-free.";
  }

  render(): JSX.Element {
    const {
      executionWindow,
      transactionId,
      isPartOfPortfolioTransaction,
      isTransactionCancellable,
      price,
      order
    } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const {
      side,
      isin,
      createdAt,
      updatedAt,
      displayAmount,
      displayQuantity,
      hasExecutionStarted,
      status,
      isMatched,
      displayExchangeRate,
      displayUserFriendlyId
    } = order;

    const [assetCommonId, assetConfig] = Object.entries(ASSET_CONFIG).find(([, config]) => config.isin == isin);
    const { simpleName, category, tickerWithCurrency } = assetConfig;

    const isCancellable = isTransactionCancellable && !isPartOfPortfolioTransaction;

    const transactionName = `${tickerWithCurrency} · ${simpleName}`;

    return (
      <div className="p-0 m-0 fade-in">
        {isMatched ? (
          <div className="d-flex align-self-center justify-content-center">
            <div className="w-100" style={{ maxWidth: "420px" }}>
              {/* Action Title */}
              <div className="d-flex w-100 justify-content-start mb-5">
                <AssetIcon
                  category={category}
                  iconUrl={getAssetIconUrl(assetCommonId as investmentUniverseConfig.AssetType)}
                  size="md"
                />
                <div className="d-flex flex-column">
                  <h6 className={`text-${side == "Buy" ? "success" : "danger"} success fw-bolder mt-1 ms-3`}>
                    {side}
                  </h6>
                  <h5 className="fw-bolder text-center ms-3">{transactionName}</h5>
                </div>
              </div>
              {/* End Action Title */}
              <div className="row m-0 mb-2">
                <div className="col-6 p-0 text-start fw-bold">
                  {formatDateToDDMONYY(new Date(updatedAt ?? createdAt))}
                </div>
                <div className="col-6 p-0 text-muted text-end">
                  {formatDateToHHMMSS(new Date(updatedAt ?? createdAt))}
                </div>
              </div>
              <div className="row m-0 wh-receipt-card mb-4 ">
                <div className="col-4 p-0 text-center">
                  {displayAmount && (
                    <div className="d-flex flex-column">
                      <div className="fw-bold">
                        {formatCurrency(
                          new Decimal(displayAmount).div(100).toDecimalPlaces(4).toNumber(),
                          order.consideration.currency,
                          locale,
                          2,
                          2
                        )}
                      </div>
                      <div className="text-muted fw-bold">Amount</div>
                    </div>
                  )}
                </div>
                {displayQuantity && (
                  <div className="col-4 p-0 text-center border-start border-end">
                    <div className="d-flex flex-column">
                      <div className="fw-bold">
                        {displayQuantity.toLocaleString(locale, {
                          minimumFractionDigits: 4,
                          maximumFractionDigits: 4
                        })}
                      </div>

                      <div className="text-muted fw-bold">Shares</div>
                    </div>
                  </div>
                )}
                <div className="col-4 p-0 text-center">
                  <div className="d-flex flex-column">
                    <div className="fw-bold">
                      {formatCurrency(
                        (price as UnitPriceType).amount,
                        (price as UnitPriceType).currency,
                        locale,
                        2,
                        2
                      )}
                    </div>
                    <div className="text-muted fw-bold">Per share</div>
                  </div>
                </div>
              </div>
              <div className="row m-0 mb-4">
                <div className="col p-0">
                  <div className="d-flex flex-column justify-content-center text-center">
                    <p className="fw-bold">Market Order</p>
                    <div className="d-flex justify-content-center">
                      <div className={`wh-receipt-bar bg-${side == "Buy" ? "success" : "danger"}`} />
                    </div>
                  </div>
                </div>
              </div>
              <div className="row m-0 mb-4">
                <div className="col p-0">
                  <div className={OrderReceipt._getSeparatorClass(side)} />
                </div>
              </div>
              <div className="row pb-3 mb-3 border-bottom align-items-center">
                <div className="col text-start text-nowrap text-muted">Client</div>
                <div className="col text-end">
                  <span className="fw-bolder">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
              </div>
              <div className="row pb-3 mb-3 border-bottom align-items-center">
                <div className="col text-start text-nowrap text-muted">Order ID</div>
                <div className="col text-end">
                  <span className="fw-bolder">{displayUserFriendlyId}</span>
                </div>
              </div>
              <div className="row pb-3 mb-3 border-bottom align-items-center">
                <div className="col text-start text-nowrap text-muted">ISIN</div>
                <div className="col text-end">
                  <span className="fw-bolder">{isin}</span>
                </div>
              </div>
              <div className="row pb-3 mb-3 border-bottom align-items-center">
                <div className="col text-start text-nowrap text-muted">Commission</div>
                <div className="col text-end">
                  <span className="fw-bolder">
                    {formatCurrency(
                      getTotalCommission(order.fees),
                      order.fees?.commission?.currency ?? order.fees?.realtimeExecution?.currency ?? user.currency,
                      locale,
                      2,
                      2
                    )}
                  </span>
                </div>
              </div>
              {order.displayExchangeRate && (
                <div className="row pb-3 mb-3 border-bottom align-items-center">
                  <div className="col text-start text-nowrap text-muted">FX rate</div>
                  <div className="col text-end">
                    <span className="fw-bolder">
                      {FxRatePreviewRow.getForeignCurrencyEquality(
                        FxRatePreviewRow.displayExchangeRateToDisplayExchangeRateType(displayExchangeRate),
                        user.currency,
                        locale
                      )}
                    </span>
                  </div>
                </div>
              )}
              <div className="row pb-3 mb-3 border-bottom align-items-center">
                <div className="col text-start text-nowrap text-muted">Reporting firm</div>
                <div className="col text-end">
                  <span className="fw-bolder">{getReportingFirm(user)}</span>
                </div>
              </div>
              {this._shouldShowTradeConfirmation() && (
                <div
                  className={
                    "d-flex align-items-center my-5 " +
                    (this.state.loadingTradeConfirmation ? "opacity-50 pointer-events-none" : "cursor-pointer")
                  }
                  onClick={() => this._downloadTradeConfirmation()}
                >
                  <img
                    src="/images/icons/trade-confirmation.svg"
                    alt="Trade confirmation icon"
                    width="44"
                    height="45"
                  />
                  <span>Trade Confirmation</span>
                  <img
                    src="/images/icons/arrow-forward.svg"
                    className="ms-auto"
                    alt="Arrow forward icon"
                    width="16"
                    height="16"
                  />
                </div>
              )}
              <div className="d-flex justify-content-center">
                <img
                  className="wh-header-logo"
                  src="/images/icons/wealthyhood-logo-dark.svg"
                  style={{ height: "28px" }}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="d-flex align-self-center justify-content-center">
            <div className="w-100" style={{ maxWidth: "400px" }}>
              {/* Action Title */}
              <div className="d-flex w-100 justify-content-center mb-3">
                <AssetIcon
                  category={category}
                  iconUrl={getAssetIconUrl(assetCommonId as investmentUniverseConfig.AssetType)}
                  size="md"
                />
              </div>
              <h5 className="fw-bolder text-center mb-5">{transactionName}</h5>
              {/* End Action Title */}
              <>
                {displayAmount && (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start">Amount {side === "Sell" ? " (est.)" : ""}</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder">
                        {formatCurrency(
                          new Decimal(displayAmount).div(100).toDecimalPlaces(4).toNumber(),
                          order.consideration.currency,
                          locale,
                          2,
                          2
                        )}
                      </span>
                    </div>
                  </div>
                )}
                {displayQuantity && (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start">No. of shares {side === "Buy" ? " (est.)" : ""}</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder">
                        {new Decimal(displayQuantity).toDecimalPlaces(4).toNumber()}
                      </span>
                    </div>
                  </div>
                )}
                {status !== "Cancelled" && (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start">Latest price</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder">
                        {formatCurrency(
                          (price as AssetPriceInfo).tradedPrice,
                          (price as AssetPriceInfo).tradedCurrency,
                          locale,
                          2,
                          2
                        )}
                      </span>
                    </div>
                  </div>
                )}
                {status === "Cancelled" ? (
                  <div className="row pb-3 m-0 mb-3 border-bottom">
                    <div className="col p-0 text-start text-nowrap">Date</div>
                    <div className="col p-0 text-end">
                      <span className="fw-bolder text-nowrap">{formatDateToDDMONYY(new Date(createdAt))}</span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="row pb-3 m-0 mb-3 border-bottom">
                      <div className="col p-0 text-start">
                        <div className="d-flex w-100">
                          <p className="m-0 align-self-center text-nowrap me-2">Commission</p>
                          <HoverableInfoIcon hoverText={this._getCommissionHoverText()} colorHex={"#536AE3"} />
                        </div>
                      </div>
                      <div className="col p-0 text-end">
                        <span className="fw-bolder">
                          {order.estimatedRealTimeCommission
                            ? formatCurrency(
                                order.estimatedRealTimeCommission,
                                order?.fees?.commission?.currency ?? user.currency,
                                locale,
                                2,
                                2
                              )
                            : formatCurrency(
                                getTotalCommission(order?.fees),
                                order?.fees?.commission?.currency ?? user.currency,
                                locale,
                                2,
                                2
                              )}
                        </span>
                      </div>
                    </div>
                    <FxRatePreviewRow
                      showBottomBorder={true}
                      foreignCurrencyRates={FxRatePreviewRow.displayExchangeRateToDisplayExchangeRateType(
                        displayExchangeRate
                      )}
                    />
                    {status !== "Pending" ? (
                      <div className="row pb-3 m-0 mb-3 border-bottom">
                        <div className="col p-0 text-start text-nowrap">Date</div>
                        <div className="col p-0 text-end">
                          <span className="fw-bolder text-nowrap">
                            {formatDateToDDMONYYHHMM(new Date(updatedAt))}
                          </span>
                        </div>
                      </div>
                    ) : (
                      // we use createdAt as fallback for old transactions
                      <div className="row pb-3 m-0 mb-3 border-bottom">
                        <div className="col p-0 text-start text-nowrap">
                          {executionWindow ? "Next trading window" : "Date"}
                        </div>
                        <div className="col p-0 text-end">
                          <span className="fw-bolder text-nowrap">
                            {hasExecutionStarted
                              ? "In progress"
                              : formatDateToDayDashHHMM(
                                  new Date((executionWindow as MarketHoursExecutionWindowType)?.start ?? createdAt)
                                )}
                          </span>
                        </div>
                      </div>
                    )}
                  </>
                )}
                <div className="row pb-3 m-0 mb-3">
                  <div className="col p-0 text-start text-nowrap">Status</div>
                  <div className="col p-0 text-end">
                    <span className={`fw-bolder text-nowrap text-${STATUS_CONFIG[status].color}`}>
                      {STATUS_CONFIG[status].label}
                    </span>
                  </div>
                </div>
                {isCancellable && (
                  <div className="p-0 mt-5 justify-content-center" style={{ borderTop: "none" }}>
                    <div
                      className="d-flex flex-column justify-content-center align-self-center mt-4 w-100"
                      style={{ maxWidth: "400px" }}
                    >
                      <LoadingOnSubmitButton
                        className="btn btn-danger fw-100"
                        customonclick={async () => this._cancelTransaction(transactionId)}
                      >
                        Cancel order
                      </LoadingOnSubmitButton>
                    </div>
                  </div>
                )}
              </>
            </div>
          </div>
        )}
      </div>
    );
  }
}

OrderReceipt.contextType = GlobalContext;

export default OrderReceipt;
