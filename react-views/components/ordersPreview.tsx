import React from "react";
import { PartialRecord } from "../types/utils";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import {
  DualExecutionValue,
  OrderPreviewType,
  OrdersPreviewType,
  TransactionPreview
} from "../types/transactionPreview";
import { formatCurrency } from "../utils/currencyUtil";
import Decimal from "decimal.js";
import { compareAssets } from "../utils/universeUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { ExecutionModeType } from "../types/executionMode";
import ConfigUtil from "../../utils/configUtil";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  transactionPreview: TransactionPreview;
  mode: "sell" | "buy" | "rebalance";
  executionMode?: ExecutionModeType;
};

class OrdersPreview extends React.Component<PropsType> {
  private _getDisplayedOrders(
    orders: DualExecutionValue<OrdersPreviewType>,
    executionMode?: ExecutionModeType
  ): OrdersPreviewType {
    if (orders.smart && !orders.express) {
      return orders.smart;
    } else if (orders.express && !orders.smart) {
      return orders.express;
    } else {
      if (executionMode === "SMART") {
        return orders.smart;
      } else return orders.smart;
    }
  }

  render(): JSX.Element {
    const { transactionPreview, mode, executionMode } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const orders = this._getDisplayedOrders(transactionPreview.orders, executionMode);

    if (!orders) {
      return <></>; // No orders available
    }

    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);

    const assetClassesDict: PartialRecord<
      investmentUniverseConfig.AssetClassType,
      (OrderPreviewType & {
        assetKey: investmentUniverseConfig.AssetType;
      })[]
    > = {};
    Object.entries(orders).map(([assetKey, order]: [investmentUniverseConfig.AssetType, OrderPreviewType]) => {
      const assetClassKey = ASSET_CONFIG[assetKey].assetClass;
      if (!assetClassesDict[assetClassKey]) {
        assetClassesDict[assetClassKey] = [];
      }

      assetClassesDict[assetClassKey].push({ ...order, assetKey });
    });

    return (
      <>
        {Object.entries(assetClassesDict)
          .sort(
            (a, b) =>
              ASSET_CLASS_CONFIG[a[0] as investmentUniverseConfig.AssetClassType].sorting -
              ASSET_CLASS_CONFIG[b[0] as investmentUniverseConfig.AssetClassType].sorting
          )
          .map(([assetClassKey, orders]) => (
            <div key={assetClassKey} className="mb-5">
              <div className="row m-0 mb-4">
                <div className="col p-0 text-start">
                  <span
                    className="material-icons me-2"
                    style={{
                      fontSize: "16px",
                      color:
                        ASSET_CLASS_CONFIG[assetClassKey as investmentUniverseConfig.AssetClassType].colorClass
                    }}
                  >
                    circle
                  </span>
                  <span className="fw-bolder">
                    {ASSET_CLASS_CONFIG[assetClassKey as investmentUniverseConfig.AssetClassType].fieldName}
                  </span>
                </div>
                {mode != "rebalance" && (
                  <div className="col p-0 text-end fw-bolder">
                    {mode == "buy"
                      ? formatCurrency(
                          orders
                            .map(({ money }) => new Decimal(money))
                            .reduce((sum, money) => sum.plus(money), new Decimal(0))
                            .toNumber(),
                          user.currency,
                          locale
                        )
                      : "Shares"}
                  </div>
                )}
              </div>
              {orders
                .sort((a, b) => compareAssets(a.assetKey, b.assetKey))
                .map((order) => (
                  <div key={order.side + order.assetKey} className="row mb-2">
                    <div className="col text-start text-nowrap">
                      <span className="fw-bolder me-2">{order.side == "buy" ? "Buy" : "Sell"}</span>
                      {ASSET_CONFIG[order.assetKey as investmentUniverseConfig.AssetType].simpleName}
                    </div>
                    <div className="col text-end">
                      {order.side == "buy" ? formatCurrency(order.money, user.currency, locale) : order.quantity}
                    </div>
                  </div>
                ))}
            </div>
          ))}
      </>
    );
  }
}

OrdersPreview.contextType = GlobalContext;

export default OrdersPreview;
