import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";
import { OrderDocument } from "../../models/Order";
import { ORDER_SIDE_COLOR, ORDER_STATUS_CONFIG } from "../configs/transactionsTableConfig";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

const { ASSET_CONFIG } = investmentUniverseConfig;

export type OrdersTableProps = {
  orders: OrderDocument[];
  columns?: OrderTableColumnsType[];
};

type StateType = {
  isMobile: boolean;
};

export type OrderTableColumnsType =
  | "WK Order ID"
  | "Name"
  | "Status"
  | "Reason"
  | "Action"
  | "Money"
  | "Quantity"
  | "Created"
  | "Created At"
  | "Submitted At";

type ColumnConfig = {
  css: string;
};

class OrdersTable extends React.Component<OrdersTableProps, StateType> {
  static defaultProps = {
    columns: ["WK Order ID", "Name", "Status", "Reason", "Action", "Money", "Quantity", "Created"]
  };
  private readonly _isinNameMapping: Record<string, string>;
  private _columnsConfig: {
    [key in OrderTableColumnsType]: ColumnConfig;
  } = {
    "WK Order ID": {
      css: ""
    },
    Name: {
      css: ""
    },
    Status: {
      css: ""
    },
    Reason: {
      css: ""
    },
    Action: {
      css: ""
    },
    Money: {
      css: ""
    },
    Quantity: {
      css: ""
    },
    Created: {
      css: ""
    },
    "Created At": {
      css: ""
    },
    "Submitted At": {
      css: ""
    }
  };
  constructor(props: OrdersTableProps) {
    super(props);
    this._isinNameMapping = Object.fromEntries(
      Object.entries<investmentUniverseConfig.AssetConfigType>(ASSET_CONFIG).map(
        ([assetId, { isin }]: [investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType]) => [
          isin,
          ASSET_CONFIG[assetId].simpleName
        ]
      )
    );
    this.state = {
      isMobile: false
    };
  }

  private _showPopOver(): boolean {
    return this.props.columns.includes("WK Order ID");
  }

  private _getWealthkernelOrderUrl(wealthkernelOrderId: string): string {
    if (wealthkernelOrderId) {
      return process.env.WEALTHKERNEL_DASHBOARD_URL + `/orders/${wealthkernelOrderId}`;
    }
  }

  componentDidMount(): void {
    window.addEventListener(
      "resize",
      () => {
        this.setState({
          isMobile: window.innerWidth < 1080
        });
      },
      false
    );
  }
  render(): JSX.Element {
    const { orders, columns } = this.props;
    const { locale } = this.context as GlobalContextType;

    return (
      <div className={"table-responsive"}>
        <table className={"table table-borderless"}>
          <thead className="thead-white" style={{ borderBottom: "1px solid #EBEDF3" }}>
            <tr>
              {columns.map((col, index) => (
                <th className={this._columnsConfig[col].css} key={`th-${index}`}>
                  {col}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => {
              return (
                <OverlayTrigger
                  placement="bottom"
                  overlay={
                    <Popover className="d-none d-md-block" id="popover-explanation">
                      {this._showPopOver() && <Popover.Content>Database ID: #{order._id}</Popover.Content>}
                    </Popover>
                  }
                  key={order._id}
                >
                  <tr className="bg-hover-light">
                    {columns.includes("WK Order ID") && (
                      <td className="pl-8   py-3">
                        {order.providers?.wealthkernel?.id ? (
                          <>
                            <span className="text-dark-50 font-weight-light d-flex align-items-center">
                              {order.providers?.wealthkernel?.id}
                              <a
                                className="material-symbols-outlined cursor-pointer text-muted px-2"
                                target="_blank"
                                rel="noreferrer"
                                href={this._getWealthkernelOrderUrl(order.providers?.wealthkernel?.id)}
                                style={{
                                  fontSize: "15px"
                                }}
                              >
                                open_in_new
                              </a>
                            </span>
                          </>
                        ) : (
                          "-"
                        )}
                      </td>
                    )}
                    {columns.includes("Name") && (
                      <td className="  py-3">
                        <span className="text-nowrap fw-bolder text-primary">
                          {this._isinNameMapping[order.isin]}
                        </span>
                      </td>
                    )}
                    {columns.includes("Status") && (
                      <td className="  py-3">
                        <span
                          className={`label label-inline text-${ORDER_STATUS_CONFIG[order.status]?.color} fw-bold`}
                        >
                          {ORDER_STATUS_CONFIG[order.status]?.label ?? order.status}
                        </span>
                      </td>
                    )}
                    {columns.includes("Reason") && (
                      <td className="  py-3">
                        {order.rejectionReason ? (
                          <span
                            className="d-block text-truncate w-100px"
                            data-toggle="popover"
                            data-content={order.rejectionReason}
                          >
                            {order.rejectionReason}
                          </span>
                        ) : (
                          <span className="font-size-h6">-</span>
                        )}
                      </td>
                    )}
                    {columns.includes("Action") && (
                      <td className={`fw-bold text-${ORDER_SIDE_COLOR[order.side]}   py-3`}>{order.side}</td>
                    )}
                    {columns.includes("Money") && (
                      <td className="  py-3">
                        {order.displayAmount > 0
                          ? formatCurrency(order.displayAmount / 100, order.consideration.currency, locale)
                          : "-"}
                      </td>
                    )}
                    {columns.includes("Quantity") && <td className="py-3">{order.quantity || "-"}</td>}
                    {columns.includes("Created") && (
                      <td className="text-nowrap py-3">
                        {new Date(order.createdAt).toLocaleString("en-GB", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                          second: "2-digit"
                        })}
                      </td>
                    )}
                    {columns.includes("Created At") && <td className="  py-3">{order.createdAt}</td>}
                    {columns.includes("Submitted At") && (
                      <td className="  py-3">{order.providers?.wealthkernel?.submittedAt}</td>
                    )}
                  </tr>
                </OverlayTrigger>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  }
}

OrdersTable.contextType = GlobalContext;

export default OrdersTable;
