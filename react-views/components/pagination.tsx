import React from "react";
import { Pagination as ReactPagination } from "react-bootstrap";

export type PaginationProps = {
  // The current page
  currentPage: number;
  // The total number of pages. If it's not provided, then resultsSize and pageSize are required.
  pages?: number;
  // The total number of results from all pages. If the "pages" prop is empty, then it is required.
  resultsSize?: number;
  // The number of elements to be rendered per page. If the "pages" prop is empty, then it is required.
  pageSize?: number;
};

/**
 * A component to render pagination elements. It renders links to other pages
 * through the use of a "page" URL search query parameter (e.g. www.example.com/home?page=3), which the controller
 * has to handle appropriately.
 */
class Pagination extends React.Component<PaginationProps> {
  render(): JSX.Element {
    const { currentPage, pages, resultsSize, pageSize } = this.props;
    const lastPage = pages ?? (resultsSize > 0 ? Math.ceil(resultsSize / pageSize) : 1);
    const previousPage = currentPage > 1 ? currentPage - 1 : currentPage;
    const nextPage = currentPage < lastPage ? currentPage + 1 : currentPage;

    return (
      <ReactPagination className="row justify-content-center mt-6">
        {currentPage != 1 ? <ReactPagination.Item href="?page=1">1</ReactPagination.Item> : ""}
        {currentPage > 3 ? <ReactPagination.Ellipsis /> : ""}
        {previousPage != 1 ? (
          <ReactPagination.Item href={`?page=${previousPage}`}>{previousPage}</ReactPagination.Item>
        ) : (
          ""
        )}
        <ReactPagination.Item active={true}>{currentPage}</ReactPagination.Item>
        {nextPage != lastPage ? (
          <ReactPagination.Item href={`?page=${nextPage}`}>{nextPage}</ReactPagination.Item>
        ) : (
          ""
        )}
        {currentPage < lastPage - 2 ? <ReactPagination.Ellipsis /> : ""}
        {currentPage != lastPage ? (
          <ReactPagination.Item href={`?page=${lastPage}`}>{lastPage}</ReactPagination.Item>
        ) : (
          ""
        )}
      </ReactPagination>
    );
  }
}

export default Pagination;
