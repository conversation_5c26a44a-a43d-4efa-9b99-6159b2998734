import React from "react";
import {
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  ChartOptions
} from "chart.js";
import { Line } from "react-chartjs-2";
import { Nav } from "react-bootstrap";
import { PartialRecord } from "../types/utils";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { DurationType } from "./portfolioAllocation";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const DURATION_CONFIG: Record<DurationType, string> = {
  "1m": "1 month",
  "6m": "6 months",
  "1y": "1 year",
  "2y": "2 years",
  "3y": "3 years",
  "5y": "5 years",
  "10y": "10 years",
  "15y": "15 years",
  "20y": "20 years",
  "30y": "30 years"
};

type PropsType = {
  data: PartialRecord<DurationType, { date: string; value: number }[]>;
  durations: DurationType[];
  activeDuration?: DurationType;
  onDurationChange?: (activeDuration: DurationType) => void;
  description?: (duration: string) => JSX.Element;
};

type StateType = {
  activeDuration: DurationType;
};

class PastPerformanceChart extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      activeDuration: this.props.activeDuration ?? props.durations[props.durations.length - 1]
    };
  }

  private _handleDurationSelect = (eventKey: DurationType): void => {
    this.setState({ activeDuration: eventKey });
    this.props.onDurationChange?.(eventKey);
  };

  shouldComponentUpdate(nextProps: PropsType, nextState: StateType) {
    return this.state.activeDuration !== nextState.activeDuration || this.props.data !== nextProps.data;
  }

  private _getChartOptions = (tradedCurrency: currenciesConfig.MainCurrencyType) => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        intersect: false,
        mode: "nearest",
        padding: { top: 10, bottom: 10, left: 10, right: 10 },
        caretPadding: 0,
        displayColors: false,
        backgroundColor: "#101327",
        titleColor: "#ffffff",
        cornerRadius: 4,
        callbacks: {
          label(context: any): string {
            return new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: tradedCurrency,
              minimumFractionDigits: 0
            }).format(context.raw as number);
          }
        }
      },
      datalabels: {
        display: false
      }
    },
    scales: {
      x: {
        grid: {
          tickLength: 30,
          color: "rgba(0, 0, 0, 0)",
          drawBorder: false
        },
        ticks: {
          display: false
        }
      },
      y: {
        grid: {
          color: "rgba(236, 240, 241, 1)",
          drawBorder: false,
          borderDash: [3, 4],
          lineWidth: 2
        },
        ticks: {
          display: true,
          color: "#000",
          font: { size: 16 },
          padding: 20,
          maxTicksLimit: 6,
          callback(value: number): string {
            let unit = "";
            let val = value as number;
            if (val >= 1000 && val < 1000000) {
              unit = "k";
              val = val / 1000;
            } else if (val >= 1000000) {
              unit = "M";
              val = val / 1000000;
            }

            return `${val.toLocaleString("en-US", {
              style: "currency",
              currency: tradedCurrency,
              minimumFractionDigits: 0,
              maximumFractionDigits: 2
            })}${unit}`;
          }
        }
      }
    },
    hover: {
      mode: "index"
    },
    elements: {
      line: {
        tension: 0.1
      },
      point: {
        radius: 0
      }
    },
    layout: {
      padding: {
        left: 0,
        right: 0,
        top: 20,
        bottom: 10
      }
    }
  });

  private _getChartData = () => {
    const { data } = this.props;
    const { activeDuration } = this.state;

    if (!data[activeDuration]?.length) {
      return {
        labels: [],
        datasets: []
      };
    }

    const labels = data[activeDuration].map(({ date }) =>
      new Date(date).toLocaleDateString("en-GB", { day: "2-digit", month: "short", year: "numeric" })
    );

    return {
      labels,
      datasets: [
        {
          data: data[activeDuration].map(({ value }) => value),
          borderColor: "#5d78ff",
          borderWidth: 2,
          pointBackgroundColor: "rgba(0, 0, 0, 0)",
          pointRadius: 0,
          pointHoverRadius: 4,
          fill: false,
          tension: 0.1
        }
      ]
    };
  };

  render(): JSX.Element {
    const { data, durations, description } = this.props;
    const { activeDuration } = this.state;
    const { user, locale } = this.context as GlobalContextType;

    const currentPortfolioValue = data[activeDuration]?.slice(-1)[0]?.value ?? 0;

    return (
      <>
        <div className="row px-3 m-0 mb-3">
          <div className="col p-0">
            <h5 className="fw-bolder text-primary mb-0">
              {formatCurrency(currentPortfolioValue, user.currency, locale)}
            </h5>
            {description && description(DURATION_CONFIG[activeDuration])}
          </div>
        </div>
        <div className="row m-0">
          <div className="col px-3 py-0" style={{ height: "210px" }}>
            <Line
              data={this._getChartData()}
              options={this._getChartOptions(user.currency) as ChartOptions<"line">}
            />
          </div>
        </div>
        <div className="row m-0 pb-3">
          <div className="col p-0">
            <Nav
              variant="pills"
              defaultActiveKey={activeDuration}
              className="justify-content-center"
              onSelect={this._handleDurationSelect}
            >
              {durations.map((tenor, index) => (
                <Nav.Item key={`tenor-nav-${index}`}>
                  <Nav.Link eventKey={tenor} className="p-2 mx-2 font-size-lg">
                    {tenor.toUpperCase()}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </div>
        </div>
      </>
    );
  }
}

PastPerformanceChart.contextType = GlobalContext;

export default PastPerformanceChart;
