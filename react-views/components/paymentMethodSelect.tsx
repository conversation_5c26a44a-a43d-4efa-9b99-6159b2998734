import { LinkedBankAccount } from "../types/bank";
import React from "react";
import InvestmentCashPaymentOptionListItem from "./investmentCashPaymentOptionListItem";
import { formatCurrency } from "../utils/currencyUtil";
import Decimal from "decimal.js";
import InvestmentBankPaymentOptionListItem from "./investmentBankPaymentOptionListItem";
import { FrequencySetting, PaymentMethod } from "../types/modal";
import { GiftDocument } from "../../models/Gift";
import InvestmentGiftPaymentOptionListItem from "./investmentGiftPaymentOptionListItem";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { isAllowedOneStepInvest } from "../utils/userUtil";

type PropsType = {
  selectedLinkedBankAccount: LinkedBankAccount;
  paymentMethod: PaymentMethod;
  frequency: FrequencySetting;
  availableCash?: number;
  linkedBankAccounts: LinkedBankAccount[];
  onSelectedAccountChange: (paymentMethod: PaymentMethod, selectedAccount: LinkedBankAccount) => void;
  onConfirmSelection: () => void;
  gift?: GiftDocument;
  hideBackButton?: boolean;
  className?: string;
  userCurrency: currenciesConfig.MainCurrencyType;
};

class PaymentMethodSelect extends React.Component<PropsType> {
  private _setPaymentMethodAndAccount = (
    paymentMethod: PaymentMethod,
    selectedLinkedBankAccount: LinkedBankAccount
  ): void => {
    this.setState({ paymentMethod, selectedLinkedBankAccount });
    this.props.onSelectedAccountChange(paymentMethod, selectedLinkedBankAccount);
  };

  render(): JSX.Element {
    const {
      paymentMethod,
      selectedLinkedBankAccount,
      availableCash,
      linkedBankAccounts,
      onConfirmSelection,
      gift,
      className,
      frequency
    } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    return (
      <div className={className}>
        {frequency !== "ONE_TIME" ? (
          <div
            onClick={() =>
              emitToast({
                toastType: ToastTypeEnum.error,
                content: "You can't use this option for repeating investments"
              })
            }
          >
            <div className={"disabled"} style={{ opacity: 0.5 }}>
              <InvestmentCashPaymentOptionListItem
                isSelected={paymentMethod == "CASH"}
                name="Cash balance"
                description={
                  formatCurrency(new Decimal(availableCash ?? 0).toNumber(), user.currency, locale) +
                  " available balance"
                }
                onChange={(): void => {
                  this._setPaymentMethodAndAccount("CASH", null);
                  onConfirmSelection();
                }}
              />
            </div>
          </div>
        ) : availableCash > 0 ? (
          <InvestmentCashPaymentOptionListItem
            isSelected={paymentMethod == "CASH"}
            name="Cash balance"
            description={
              formatCurrency(new Decimal(availableCash).toNumber(), user.currency, locale) + " available balance"
            }
            onChange={(): void => {
              this._setPaymentMethodAndAccount("CASH", null);
              onConfirmSelection();
            }}
          />
        ) : (
          <></>
        )}

        {gift && !gift.used ? (
          <InvestmentGiftPaymentOptionListItem
            isSelected={paymentMethod == "GIFT"}
            gift={gift}
            onChange={(): void => {
              this._setPaymentMethodAndAccount("GIFT", null);
              onConfirmSelection();
            }}
          />
        ) : (
          <></>
        )}

        {(isAllowedOneStepInvest(user) || frequency !== "ONE_TIME" || paymentMethod === "BANK_ACCOUNT_TOP_UP") &&
          linkedBankAccounts.map((linkedBankAccount) => (
            <InvestmentBankPaymentOptionListItem
              key={`bank-payment-${linkedBankAccount.id}`}
              linkedBankAccount={linkedBankAccount}
              isSelected={
                paymentMethod == "BANK_ACCOUNT_TOP_UP" && selectedLinkedBankAccount.id == linkedBankAccount.id
              }
              onChange={(): void => {
                this._setPaymentMethodAndAccount("BANK_ACCOUNT_TOP_UP", linkedBankAccount);
                onConfirmSelection();
              }}
              oneTimePayment={frequency === "ONE_TIME"}
            />
          ))}
      </div>
    );
  }
}

PaymentMethodSelect.contextType = GlobalContext;

export default PaymentMethodSelect;
