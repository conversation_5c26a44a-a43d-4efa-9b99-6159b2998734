import React from "react";

type PropsType = React.InputHTMLAttributes<HTMLInputElement>;

class PercentageInput extends React.Component<PropsType> {
  private _validate = (evt: any): any => {
    const theEvent = evt || window.event;
    const key = theEvent.keyCode || theEvent.which;
    const regex = /[0-9]|\./;
    if (!regex.test(String.fromCharCode(key))) {
      theEvent.returnValue = false;
      if (theEvent.preventDefault) theEvent.preventDefault();
    }
  };

  render(): JSX.Element {
    const { ...htmlProps } = this.props;

    return (
      <div className="percent">
        <input
          placeholder="0"
          type="number"
          {...htmlProps}
          onKeyPress={this._validate}
          onPaste={(e: any): boolean => {
            e.preventDefault();
            return false;
          }}
        />
      </div>
    );
  }
}

export default PercentageInput;
