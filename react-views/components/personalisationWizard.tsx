import React from "react";
import axios from "axios";
import StepW<PERSON>rd, { StepWizardProps } from "react-step-wizard";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AssetClassSelection from "./assetClassSelection";
import AssetGeographySelection from "./assetGeographySelection";
import InvestmentThemeSelection from "./investmentThemeSelection";
import ConfigurableWizardActions from "./configurableWizardActions";
import { animatedTransitions } from "../configs/portfolioPersonalisationWizardConfig";
import { captureException } from "@sentry/react";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

const { AssetClassArray, InvestmentSectorArray, InvestmentGeographyArray } = investmentUniverseConfig;

type PropsType = {
  portfolioId: string;
  savedPersonalisationPreferences: {
    assetClasses: investmentUniverseConfig.AssetClassType[];
    geography: investmentUniverseConfig.InvestmentGeographyType;
    risk: number;
    sectors: investmentUniverseConfig.InvestmentSectorType[];
  };
  onStepChange: (step: number) => void;
};
type StateType = {
  currentStep: number;
  personalisationPreferences: {
    assetClasses: investmentUniverseConfig.AssetClassType[];
    geography: investmentUniverseConfig.InvestmentGeographyType;
    risk: number;
    sectors: investmentUniverseConfig.InvestmentSectorType[];
  };
  wizardInstance: StepWizardProps & {
    nextStep: () => void;
    previousStep: () => void;
    goToStep: (step: number) => void;
  };
};

class PersonalisationWizard extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);

    this.state = {
      currentStep: 1,
      personalisationPreferences: this.props.savedPersonalisationPreferences,
      wizardInstance: null
    };
  }

  private _getWizardNavStepConfig = (currentStep: number): any => {
    const { personalisationPreferences, wizardInstance } = this.state;
    const { assetClasses } = personalisationPreferences;

    const STEP_NAV_CONFIG = [
      {
        nextStep: {
          disabled: assetClasses.length === 0,
          action: wizardInstance.nextStep,
          label: assetClasses.length > 0 ? "Next" : "No asset classes selected"
        }
      },
      {
        previousStep: {
          action: wizardInstance.previousStep,
          label: "Back"
        },
        nextStep: {
          action: this._isStocksAssetClassSelected()
            ? wizardInstance.nextStep
            : this._submitPersonalisationPreferences,
          label: "Next"
        }
      },
      {
        previousStep: {
          action: wizardInstance.previousStep,
          label: "Back"
        },
        completeWizard: {
          action: this._submitPersonalisationPreferences,
          label: "Next"
        }
      }
    ];

    return STEP_NAV_CONFIG[currentStep - 1];
  };

  private _submitPersonalisationPreferences = async (): Promise<void> => {
    const { personalisationPreferences } = this.state;
    const { assetClasses, geography, risk, sectors } = personalisationPreferences;

    // Validate data before submitting
    const activeAssetClasses = assetClasses.filter((assetClass) =>
      AssetClassArray.find((allowedClass) => allowedClass === assetClass)
    );
    const activeGeography = InvestmentGeographyArray.find((allowedGeo) => allowedGeo === geography)
      ? geography
      : "global";
    const activeSectors = sectors.filter((sector) =>
      InvestmentSectorArray.find((allowedSector) => allowedSector === sector)
    );

    // Submit personalisation preferences
    try {
      const { portfolioId } = this.props;
      await axios({
        method: "post",
        url: `/portfolios/${portfolioId}/personalisation-preferences`,
        data: {
          personalisationPreferences: {
            assetClasses: activeAssetClasses,
            geography: activeGeography,
            risk,
            sectors: activeSectors
          }
        }
      });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not load personalisation preferences. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
    window.location.href = "/portfolios/setup-as-step";
  };

  private _setPersonalisationPreference =
    (category: "assetClasses" | "geography" | "risk" | "sectors") =>
    (
      values: investmentUniverseConfig.AssetClassType[] &
        investmentUniverseConfig.InvestmentGeographyType &
        investmentUniverseConfig.InvestmentSectorType[] &
        number
    ): void => {
      this.setState((prevState) => {
        const newState = { ...prevState };
        newState.personalisationPreferences[category] = values;
        return newState;
      });
    };
  private _setCurrentStep = ({ activeStep }: { previousStep: number; activeStep: number }): void => {
    window.scrollTo(0, 0);
    this.setState({ currentStep: activeStep }, () => this.props.onStepChange(activeStep));
  };

  private _setWizardInstance = (wizardInstance: any): void => {
    this.setState({ wizardInstance });
  };

  private _isStocksAssetClassSelected = (): boolean => {
    const { personalisationPreferences } = this.state;
    return new Set(personalisationPreferences.assetClasses).has("equities");
  };

  render(): JSX.Element {
    const { currentStep, personalisationPreferences, wizardInstance } = this.state;
    const { assetClasses, geography, sectors } = personalisationPreferences;

    return (
      <>
        <style
          dangerouslySetInnerHTML={{
            __html: `
          .animated {
            animation-duration: .8192s;
            animation-fill-mode: backwards;
            transform-style: preserve-3d;
          }
          
          .rsw_2f{ display: none !important; } .rsw_3G{ display: block !important;} 
          /** intro */
          @keyframes intro {
            from {
              opacity: 0;
              transform: perspective(500px) translate3d(0, 0, -50px);
            }
          
            to {
              opacity: 1;
              transform: none;
            }
          }
          
          .intro {
            animation: intro 1s ease-out;
          }
          
          /** enterRight */
          @keyframes enterRight {
            from {
              opacity: 0;
              transform: perspective(500px) translate3d(20%, 0, 0);
            }
          
            to {
              opacity: 1;
              transform: none;
            }
          }
        `
          }}
        />

        <div className="w-100 wizard wizard-3">
          {/* NOTE: the wizard has a weird behavior where all the step components are displayed
            until the js has loaded and the wizard instance has been created. To fix that we
            check whether the wizard instance is available and then we enable the steps > 2 to
            be displayed */}
          <StepWizard
            isLazyMount={true}
            transitions={animatedTransitions}
            instance={this._setWizardInstance}
            onStepChange={this._setCurrentStep}
          >
            <AssetClassSelection
              selectedAssetClasses={assetClasses}
              onSelectionChange={this._setPersonalisationPreference("assetClasses")}
            />
            {wizardInstance && (
              <AssetGeographySelection
                selectedGeography={geography}
                onSelectionChange={this._setPersonalisationPreference("geography")}
              />
            )}
            {wizardInstance && (
              <InvestmentThemeSelection
                selectedSectors={sectors}
                onSelectionChange={this._setPersonalisationPreference("sectors")}
              />
            )}
          </StepWizard>
          {wizardInstance && (
            <ConfigurableWizardActions fixed actions={this._getWizardNavStepConfig(currentStep)} />
          )}
        </div>
      </>
    );
  }
}

export default PersonalisationWizard;
