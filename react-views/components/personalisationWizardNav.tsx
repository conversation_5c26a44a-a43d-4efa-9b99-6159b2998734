import React from "react";
import { stepsConfig } from "../configs/portfolioPersonalisationWizardConfig";

type PropsType = {
  currentStep?: number;
  goToStep?: any;
};

class PersonalisationWizardNav extends React.Component<PropsType> {
  render(): JSX.Element {
    const { currentStep } = this.props;

    return (
      <div className="d-flex flex-column align-items-center mb-12">
        {/* Progress */}
        <p className="bg-light-primary py-2 px-3 border-radius-sm">{currentStep}/7 Steps</p>
        {/* End Progress */}
        {/* Step Name */}
        <h5 className="text-dark-50 text-center my-auto">{stepsConfig[currentStep - 1].body}</h5>
        {/* End Step Name */}
      </div>
    );
  }
}

export default PersonalisationWizardNav;
