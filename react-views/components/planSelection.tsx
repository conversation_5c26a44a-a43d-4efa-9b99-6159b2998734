import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import PlanSelectionCarousel from "./planSelectionCarousel";
import PlanSelectionBullets from "./planSelectionBullets";
import PlanSelectionSubtitle from "./planSelectionSubtitle";
import PlanSelectionFees from "./planSelectionFees";
import PlanSelectionRecurrenceToggle from "./planSelectionRecurrenceToggle";

export type PlanSelectionPropsType = {
  handlePriceSelection: (plan: plansConfig.PriceType) => void;
  selectedPrice: plansConfig.PriceType;
  selectedRecurrence: plansConfig.PriceRecurrenceType;
  handleRecurrenceSelection: (recurrence: plansConfig.PriceRecurrenceType) => void;
  userCanGetFreeTrial: boolean;
};

class PlanSelection extends React.Component<PlanSelectionPropsType> {
  render() {
    const {
      handlePriceSelection,
      selectedPrice,
      userCanGetFreeTrial,
      selectedRecurrence,
      handleRecurrenceSelection
    } = this.props;

    return (
      <div className={"mb-5"}>
        <PlanSelectionRecurrenceToggle
          selectedRecurrence={selectedRecurrence}
          onSelection={handleRecurrenceSelection}
        />
        <PlanSelectionCarousel
          selectedPrice={selectedPrice}
          selectedRecurrence={selectedRecurrence}
          onSelection={handlePriceSelection}
          userCanGetFreeTrial={userCanGetFreeTrial}
        />
        <PlanSelectionSubtitle selectedPrice={selectedPrice} />
        <PlanSelectionBullets selectedPrice={selectedPrice} />
        <PlanSelectionFees selectedPrice={selectedPrice} />
        <div className="text-muted mt-3">Capital at risk. Terms apply.</div>
      </div>
    );
  }
}

export default PlanSelection;
