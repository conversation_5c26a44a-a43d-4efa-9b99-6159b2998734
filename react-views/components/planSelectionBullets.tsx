import React, { Component } from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import { OverlayTrigger, Popover } from "react-bootstrap";
import { PlansContext, PlansContextType } from "../contexts/plansContext";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { SavingsProductFeesModalViewMode } from "./modals/savingsProductFeesModal";
import ConfigUtil from "../../utils/configUtil";

const { getBulletConfig } = plansConfig;

type PropsType = {
  selectedPrice: plansConfig.PriceType;
};

class PlanSelectionBullets extends Component<PropsType> {
  /**
   * Converts a string to JSX element, by adding paragraphs on new lines
   */
  private static _convertToJSX(input: string): JSX.Element {
    const paragraphs = input.split("\n").filter((line) => line.trim() !== "");
    return (
      <>
        {paragraphs.map((paragraph, index) => (
          <p className={`text-muted  ${index === paragraphs.length - 1 ? " mb-0" : ""}`} key={index}>
            {paragraph}
          </p>
        ))}
      </>
    );
  }

  private _getWealthyhoodIconColor(): string {
    const { user } = this.context as PlansContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const { plan } = PRICE_CONFIG[this.props.selectedPrice];

    return PLAN_CONFIG[plan].color;
  }

  private _getPlanBulletRow(bullet: plansConfig.PlanBulletConfigType, index: number): JSX.Element {
    const { materialIcon, title, coloredTitle, info, isComingSoon, active, showWealthyhoodIcon } = bullet;

    return (
      <div key={`bullet-${index}`} className={"row mb-3"} style={{ minHeight: "34px" }}>
        <div className="col-1 align-self-center text-center" style={{ marginTop: "4px" }}>
          {active ? (
            <i className="material-symbols-outlined icon-primary align-self-center " style={{ fontSize: "20px" }}>
              {materialIcon}
            </i>
          ) : (
            <i className="material-symbols-outlined align-self-center disabled" style={{ fontSize: "20px" }}>
              lock
            </i>
          )}
        </div>
        <div className="col-11 d-flex justify-content-between align-items-center">
          <div className="d-flex flex-column justify-content-start">
            <div className="d-flex">
              <h6
                className={`${
                  active ? "fw-bolder" : "fw-normal text-decoration-line-through disabled"
                } mb-0 align-self-center`}
              >
                {title}
                {coloredTitle && <span className="text-primary">{" " + coloredTitle}</span>}
              </h6>
              {this._getInfoTooltip(info)}
            </div>
          </div>
          {active && isComingSoon && (
            <span className="align-self-center wh-coming-soon-card ms-2">Coming soon</span>
          )}
          {showWealthyhoodIcon && (
            <div
              className="rounded-circle"
              style={{
                backgroundColor: this._getWealthyhoodIconColor(),
                padding: "5px 8px",
                width: "fit-content"
              }}
            >
              <img src="/images/icons/wh-mini-logo-white.svg" />
            </div>
          )}
        </div>
      </div>
    );
  }

  private _getInfoTooltip(info: string): JSX.Element {
    return (
      <OverlayTrigger
        placement="bottom"
        overlay={
          <Popover id="popover-explanation">
            <Popover.Content>{PlanSelectionBullets._convertToJSX(info)}</Popover.Content>
          </Popover>
        }
      >
        <span className={"ms-1 cursor-pointer material-symbols-outlined"} style={{ fontSize: "16px" }}>
          info
        </span>
      </OverlayTrigger>
    );
  }

  private _getMMFBulletRow(): JSX.Element {
    const { selectedPrice } = this.props;
    const { user, locale, promotionalSavingsProductData } = this.context as PlansContextType;
    const { feeDetails, savingsProductId } = promotionalSavingsProductData;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const selectedPlan = PRICE_CONFIG[selectedPrice].plan;
    const netInterestForSelectedPlan = feeDetails.find((fee) => fee.plan === selectedPlan)?.netInterestRateValue;
    const showWealthyhoodIcon = selectedPlan !== "free";

    return (
      <div className={"row mb-3"} style={{ minHeight: "34px" }}>
        <div className="col-1 align-self-center text-center" style={{ marginTop: "4px" }}>
          <i className="material-symbols-outlined icon-primary align-self-center " style={{ fontSize: "20px" }}>
            humidity_percentage
          </i>
        </div>
        <div className="col-11 d-flex justify-content-between align-items-center">
          <div className="d-flex flex-column justify-content-start">
            <div className="d-flex">
              <h6 className={"fw-bolder mb-0 align-self-center"}>
                {`Earn ${netInterestForSelectedPlan.toLocaleString(locale, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })}% interest with MMFs`}
              </h6>

              <span
                className={"ms-1 cursor-pointer material-symbols-outlined"}
                style={{ fontSize: "16px" }}
                onClick={() =>
                  eventEmitter.emit(
                    EVENTS.savingsProductFeesModal,
                    savingsProductId,
                    SavingsProductFeesModalViewMode.PLANS,
                    selectedPlan
                  )
                }
              >
                info
              </span>
            </div>
          </div>
          {showWealthyhoodIcon && (
            <div
              className="rounded-circle"
              style={{
                backgroundColor: this._getWealthyhoodIconColor(),
                padding: "5px 8px",
                width: "fit-content"
              }}
            >
              <img src="/images/icons/wh-mini-logo-white.svg" />
            </div>
          )}
        </div>
      </div>
    );
  }

  render() {
    const { selectedPrice } = this.props;
    const { user, locale } = this.context as PlansContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const bullets = getBulletConfig(user.currency, locale, user.companyEntity)[PRICE_CONFIG[selectedPrice].plan];
    const bulletsBeforeMMF = bullets.slice(0, 5);
    const bulletsAfterMMF = bullets.slice(5);

    return (
      <>
        {bulletsBeforeMMF.map((bullet, index) => this._getPlanBulletRow(bullet, index))}
        {this._getMMFBulletRow()}
        {bulletsAfterMMF.map((bullet, index) => this._getPlanBulletRow(bullet, index))}
      </>
    );
  }
}

PlanSelectionBullets.contextType = PlansContext;

export default PlanSelectionBullets;
