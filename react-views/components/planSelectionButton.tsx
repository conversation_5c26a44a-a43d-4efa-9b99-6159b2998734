import { plansConfig } from "@wealthyhood/shared-configs";
import React, { Component } from "react";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { Modal } from "react-bootstrap";
import { SubscriptionDocument } from "../../models/Subscription";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  selectedPrice: plansConfig.PriceType;
  currentSubscription?: SubscriptionDocument;
  expiration?: {
    date: Date;
    downgradesTo: plansConfig.PriceType;
  };
  onButtonClick: () => Promise<void>;
};

type StateType = {
  showDowngradeConfirmationModal: boolean;
  downgradeText: string;
};

/**
 * This component decides based on current, selected and possibly downgraded plan the CTA text,
 * as well as some downgrade warnings
 */
class PlanSelectionButton extends Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      downgradeText: "",
      showDowngradeConfirmationModal: false
    };
  }

  private _formatDate(date: Date): string {
    return new Date(date).toLocaleDateString("en-GB", {
      day: "numeric",
      month: "long"
    });
  }

  private _getPlanName(price: plansConfig.PriceType): string {
    const { user } = this.context as GlobalContextType;

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return PLAN_CONFIG[PRICE_CONFIG[price].plan].name;
  }

  private _isSelectedPlanDowngrade(
    selectedPrice: plansConfig.PriceType,
    currentPrice: plansConfig.PriceType
  ): boolean {
    return (
      (selectedPrice === "free_monthly" && ["paid_low_monthly", "paid_mid_monthly"].includes(currentPrice)) ||
      (selectedPrice === "paid_low_monthly" && currentPrice === "paid_mid_monthly")
    );
  }

  /**
   * If plan is a downgrade, show downgrade modal first
   * else return onButtonClick from props
   */
  private _getOnClickFn(): () => Promise<void> {
    const { currentSubscription, selectedPrice, onButtonClick } = this.props;
    const { user } = this.context as GlobalContextType;

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    if (this._isSelectedPlanDowngrade(selectedPrice, currentSubscription?.price)) {
      // Show downgrade confirmation modal first
      return async () =>
        this.setState({
          showDowngradeConfirmationModal: true,
          downgradeText: PLAN_CONFIG[PRICE_CONFIG[this.props.selectedPrice].plan].downgradeMessage ?? ""
        });
    } else {
      return onButtonClick;
    }
  }

  private _isUpgrade = (from: plansConfig.PlanType, to: plansConfig.PlanType): boolean => {
    const { user } = this.context as GlobalContextType;

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const plans = Object.keys(PLAN_CONFIG) as plansConfig.PlanType[];

    return plans.indexOf(from) < plans.indexOf(to);
  };

  private _getButtonConfig(): {
    buttonText: string;
    disabled: boolean;
    onClick?: () => Promise<void>;
    expirationEl?: JSX.Element;
  } {
    const { expiration, currentSubscription, selectedPrice } = this.props;
    const { user } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    // Onboarding Config:
    if (!currentSubscription?.price && !expiration) {
      if (selectedPrice === "free_monthly") {
        return {
          buttonText: `Continue with Basic`,
          disabled: false,
          onClick: this._getOnClickFn()
        };
      } else
        return {
          buttonText: `Try ${this._getPlanName(selectedPrice)} for free`,
          disabled: false,
          onClick: this._getOnClickFn()
        };
    }

    // Direct debit deprecation:
    if (currentSubscription?.category === "DirectDebitSubscription")
      return {
        buttonText: `Continue with ${this._getPlanName(selectedPrice)}`,
        disabled: true
      };

    // When user has already downgraded config:
    if (expiration) {
      if (PRICE_CONFIG[selectedPrice].keyName === expiration.downgradesTo) {
        return {
          buttonText: "Upcoming Plan",
          disabled: true,
          expirationEl: <p className="text-muted m-0 me-3">{`Begins ${this._formatDate(expiration.date)}`}</p>
        };
      } else if (PRICE_CONFIG[selectedPrice].plan === PRICE_CONFIG[currentSubscription.price].plan) {
        return {
          buttonText: `Renew ${this._getPlanName(selectedPrice)}`,
          disabled: false,
          expirationEl: (
            <p className="text-danger m-0 me-3">{`Current - Expires ${this._formatDate(expiration.date)}`}</p>
          ),
          onClick: this._getOnClickFn()
        };
      } else if (PRICE_CONFIG[selectedPrice].recurrence === "yearly") {
        return {
          buttonText: `Get ${PRICE_CONFIG[selectedPrice].savePercentage} off ${this._getPlanName(selectedPrice)}`,
          disabled: false,
          onClick: this._getOnClickFn()
        };
      }
    }

    // User already has a subscription
    if (currentSubscription?.price === selectedPrice) {
      return {
        buttonText: "Current Plan",
        disabled: true
      };
    } else {
      // User is on the Basic plan, and they haven't used their free trial
      if (currentSubscription.price === "free_monthly" && !currentSubscription.hasUsedFreeTrial) {
        return {
          buttonText: `Try ${this._getPlanName(selectedPrice)} for free`,
          disabled: false,
          onClick: this._getOnClickFn()
        };
      }

      const { recurrence: currentRecurrence, plan: currentPlan } = PRICE_CONFIG[currentSubscription.price];
      const { recurrence: selectedRecurrence, plan: selectedPlan, savePercentage } = PRICE_CONFIG[selectedPrice];

      // User is moving from monthly (or free) -> yearly
      if (selectedRecurrence === "yearly") {
        if (currentPlan === selectedPlan) {
          return {
            buttonText: `Save ${savePercentage} with annual plan`,
            disabled: false,
            onClick: this._getOnClickFn()
          };
        } else if (this._isUpgrade(currentPlan, selectedPlan) || currentRecurrence === "monthly") {
          return {
            buttonText: `Get ${savePercentage} off ${this._getPlanName(selectedPrice)}`,
            disabled: false,
            onClick: this._getOnClickFn()
          };
        }
      }

      // User is moving from yearly to monthly of the same plan
      if (currentRecurrence === "yearly" && selectedRecurrence === "monthly" && currentPlan === selectedPlan) {
        return {
          buttonText: "Switch to monthly plan",
          disabled: false,
          onClick: this._getOnClickFn()
        };
      }

      // Any other scenario
      return {
        buttonText: `Continue with ${this._getPlanName(selectedPrice)}`,
        disabled: false,
        onClick: this._getOnClickFn()
      };
    }
  }
  private _setShowDowngradeConfirmationModal(showDowngradeConfirmationModal: boolean) {
    this.setState({ showDowngradeConfirmationModal });
  }

  render() {
    const config = this._getButtonConfig();
    const { showDowngradeConfirmationModal, downgradeText } = this.state;
    const { selectedPrice, onButtonClick } = this.props;

    return (
      <div className="d-flex align-items-center justify-content-end m-0 w-100">
        {config?.expirationEl}
        <LoadingOnSubmitButton
          type="button"
          className="btn btn-primary w-100 px-3"
          disabled={config.disabled}
          customonclick={config.onClick}
          enableOnCompletion={true}
        >
          {config.buttonText}
        </LoadingOnSubmitButton>

        {/** Downgrade Confirmation Modal */}
        <Modal
          show={showDowngradeConfirmationModal}
          onHide={() => this._setShowDowngradeConfirmationModal(false)}
          dialogClassName={"p-md-5"}
        >
          <Modal.Header className="justify-content-end border-bottom-0 pb-0" closeButton />
          <Modal.Body className="px-md-5 px-3 py-0">
            <h5 className="fw-bolder text-start mb-4">{`Downgrade to ${this._getPlanName(selectedPrice)}?`}</h5>
            <p className="text-muted">{downgradeText}</p>
          </Modal.Body>
          <Modal.Footer className="justify-content-center border-top-0 pt-3 pb-5">
            <div className="row m-0">
              <div className="col p-0">
                <button
                  className="btn btn-secondary text-nowrap me-md-4 me-2"
                  onClick={() => this._setShowDowngradeConfirmationModal(false)}
                >
                  Back
                </button>
              </div>
              <div className="col p-0">
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary text-nowrap"
                  customonclick={onButtonClick}
                  enableOnCompletion={true}
                >
                  {`Switch to ${this._getPlanName(selectedPrice)}`}
                </LoadingOnSubmitButton>
              </div>
            </div>
          </Modal.Footer>
        </Modal>
        {/** End Downgrade Confirmation Modal */}
      </div>
    );
  }
}

PlanSelectionButton.contextType = GlobalContext;

export default PlanSelectionButton;
