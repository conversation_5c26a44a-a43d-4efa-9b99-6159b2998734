import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import PlanSelectionCarouselBanner from "./planSelectionCarouselBanner";
import PlanSelectionCarouselHorizontalScroller from "./planSelectionCarouselHorizontalScroller";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  selectedPrice: plansConfig.PriceType;
  selectedRecurrence: plansConfig.PriceRecurrenceType;
  onSelection: (price: plansConfig.PriceType) => void;
  userCanGetFreeTrial: boolean;
};

class PlanSelectionCarousel extends React.Component<PropsType> {
  componentDidUpdate(prevProps: PropsType) {
    if (prevProps.selectedRecurrence != this.props.selectedRecurrence) {
      this._handlePlanSelection(this._getPlanIndex());
    }
  }

  private _handlePlanSelection = (index: number): void => {
    const selectedPrice = Object.values(this._getPriceConfig())[index].keyName;

    this.props.onSelection(selectedPrice);
  };

  private _getPriceConfig = () => {
    const { selectedRecurrence } = this.props;

    const user = (this.context as GlobalContextType).user;
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return Object.fromEntries(
      Object.entries(PRICE_CONFIG).filter(([planKey, planConfig]) => {
        if (planKey === "paid_mid_lifetime_sweatcoin_1") {
          return user.isSweatcoinReferred && planConfig.active;
        }

        return planConfig.active && (planConfig.recurrence === selectedRecurrence || !planConfig.recurrence);
      })
    );
  };

  private _getEquivalentPriceForRecurrence(): plansConfig.PriceType {
    const { selectedPrice, selectedRecurrence } = this.props;

    const user = (this.context as GlobalContextType).user;
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const selectedPlan = PRICE_CONFIG[selectedPrice].plan;

    return Object.values(PRICE_CONFIG).find(
      (priceConfig) => priceConfig.plan === selectedPlan && priceConfig.recurrence === selectedRecurrence
    ).keyName;
  }

  private _getPlanIndex = (): number => {
    const { selectedPrice, selectedRecurrence } = this.props;

    const indexStr = Object.entries(this._getPriceConfig()).findIndex(([, value]) => {
      const user = (this.context as GlobalContextType).user;
      const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

      if (
        PRICE_CONFIG[selectedPrice].recurrence &&
        PRICE_CONFIG[selectedPrice].recurrence !== selectedRecurrence
      ) {
        return value.keyName === this._getEquivalentPriceForRecurrence();
      }

      return value.keyName === selectedPrice;
    });

    return +indexStr;
  };

  private _getScrollDotOptions = (): { color: string }[] => {
    const user = (this.context as GlobalContextType).user;

    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    return Object.values(this._getPriceConfig()).map((planConfig) => {
      const plan = planConfig.plan;

      return { color: PLAN_CONFIG[plan].color };
    });
  };

  render() {
    const { userCanGetFreeTrial } = this.props;

    const index = this._getPlanIndex();

    return (
      <>
        <PlanSelectionCarouselHorizontalScroller
          id={"plan-scroller"}
          selectedScrollDot={index}
          scrollDotOptions={this._getScrollDotOptions()}
          onSelectionCb={this._handlePlanSelection}
        >
          {Object.values(this._getPriceConfig()).map((priceConfig, index) => (
            <PlanSelectionCarouselBanner
              key={`plan-selection-banner-${index}`}
              price={priceConfig.keyName}
              cursorPointer={true}
              userCanGetFreeTrial={userCanGetFreeTrial}
            />
          ))}
        </PlanSelectionCarouselHorizontalScroller>
      </>
    );
  }
}

PlanSelectionCarousel.contextType = GlobalContext;

export default PlanSelectionCarousel;
