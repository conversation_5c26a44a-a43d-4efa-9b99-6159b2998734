import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { PLAN_BG_IMAGE_CONFIG } from "../configs/plansConfig";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  price: plansConfig.PriceType;
  userCanGetFreeTrial: boolean;
  cursorPointer: boolean;
};

type StateType = {
  showInfoDialog: boolean;
};

class PlanSelectionCarouselBanner extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showInfoDialog: false
    };
  }

  private _getPriceSubtitle(
    amount: number,
    calculatedMonthlyAmount: number,
    monthlyAmountPreDiscount: number,
    recurrence: plansConfig.PriceRecurrenceType
  ): JSX.Element {
    const { user, locale } = this.context as GlobalContextType;

    if (amount === 0) {
      return <h5 className="mb-0">Free</h5>;
    } else if (recurrence === "monthly") {
      return (
        <div className={"d-flex"}>
          <h5 className="mb-0">{formatCurrency(amount, user.currency, locale)}</h5>
          <h6 className="t-875 mb-0 ms-1 align-self-end fw-lighter" style={{ opacity: 0.8 }}>
            /month
          </h6>
        </div>
      );
    } else if (recurrence === "yearly") {
      return (
        <>
          <div className={"d-flex"}>
            <h6
              className="mb-0 align-self-end me-1 text-white"
              style={{ textDecoration: "line-through", opacity: 0.8 }}
            >
              {formatCurrency(monthlyAmountPreDiscount, user.currency, locale)}
            </h6>
            <h5 className="mb-0">{formatCurrency(calculatedMonthlyAmount, user.currency, locale)}</h5>
            <h6 className="t-875 mb-0 ms-1 align-self-end fw-lighter" style={{ opacity: 0.8 }}>
              /month
            </h6>
          </div>
        </>
      );
    } else if (recurrence === "lifetime") {
      return (
        <>
          <div>
            <span className="fw-bolder me-1">{amount}</span>
            <span>for life</span>
          </div>
        </>
      );
    }
  }

  private _getFreeTrialSubtitle(): JSX.Element {
    const { price, userCanGetFreeTrial } = this.props;
    const user = (this.context as GlobalContextType).user;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const { promoSubtitle, freeTrialSubtitle } = PRICE_CONFIG[price];

    if (userCanGetFreeTrial && freeTrialSubtitle) {
      return <span className={"t-75 text-white"}>{freeTrialSubtitle}</span>;
    } else return <span className={"t-75 text-white font-weight-light"}>{promoSubtitle}</span>;
  }

  private _getSavePercentageLabel(): string {
    const { price } = this.props;
    const user = (this.context as GlobalContextType).user;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    const { savePercentage } = PRICE_CONFIG[price];

    if (savePercentage) {
      return `-${savePercentage}`;
    }
  }

  render(): JSX.Element {
    const { price, cursorPointer } = this.props;
    const user = (this.context as GlobalContextType).user;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const { title, plan, amount, recurrence, monthlyAmountPreDiscount, calculatedMonthlyAmount } =
      PRICE_CONFIG[price];
    const { secondaryLabel, colorLight } = PLAN_CONFIG[plan];

    const savePercentageLabel = this._getSavePercentageLabel();

    return (
      <>
        <div
          className={`card card-body wh-simple-card justify-content-center ${
            cursorPointer ? "cursor-pointer" : ""
          } border-0 px-4`}
          style={{
            backgroundImage: PLAN_BG_IMAGE_CONFIG[plan],
            minWidth: "295px",
            minHeight: "136px"
          }}
        >
          <div className="d-flex align-self-center flex-column w-100 p-0 justify-content-center">
            <div className="d-flex">
              <h2 className="fw-600 align-self-center m-0 me-2 text-white">{title}</h2>
            </div>
            <div className="d-flex mt-2">
              <div className="d-inline text-white">
                {this._getPriceSubtitle(amount, calculatedMonthlyAmount, monthlyAmountPreDiscount, recurrence)}
                {this._getFreeTrialSubtitle()}
              </div>
            </div>
          </div>

          <div className="d-flex m-2" style={{ position: "absolute", top: 0, right: 0 }}>
            {savePercentageLabel && (
              <div className="plan-secondary-label t-75 me-2" style={{ backgroundColor: colorLight }}>
                {savePercentageLabel}
              </div>
            )}
            {secondaryLabel && (
              <div className="plan-secondary-label t-75" style={{ backgroundColor: colorLight }}>
                {secondaryLabel}
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
}

PlanSelectionCarouselBanner.contextType = GlobalContext;

export default PlanSelectionCarouselBanner;
