import React, { Children } from "react";

type PropsType = {
  id: string;
  onSelectionCb: (index: number) => void;
  selectedScrollDot: number;
  scrollDotOptions: { color: string }[];
};

class PlanSelectionCarouselHorizontalScroller extends React.Component<PropsType> {
  private _sideScroll(direction: "left" | "right") {
    const { selectedScrollDot, scrollDotOptions } = this.props;

    if (selectedScrollDot < scrollDotOptions.length - 1 && direction === "right") {
      this._handleSelection(selectedScrollDot + 1);
    } else if (selectedScrollDot > 0 && direction === "left") {
      this._handleSelection(selectedScrollDot - 1);
    }
  }

  private _handleSelection(index: number): void {
    document
      .getElementById(`scroll-child-${this.props.id}-${index}`)
      .scrollIntoView({ block: "center", inline: "center", behavior: "smooth" });
    this.props.onSelectionCb(index);
  }

  componentDidMount(): void {
    /**
     * Slide to selected plan
     */
    this._handleSelection(this.props.selectedScrollDot);
  }

  render(): JSX.Element {
    const { id, children, selectedScrollDot, scrollDotOptions } = this.props;
    const childrenArray = Children.toArray(children);

    return (
      <>
        <div className={"d-flex align-self-center p-0 mb-4 px-lg-2 px-md-5 px-0"}>
          <div className="align-self-center pe-0 d-none d-sm-block">
            <button className="btn btn-clean" onClick={() => this._sideScroll("left")}>
              <i className="fa-solid text-primary fa-chevron-left" />
            </button>
          </div>
          <div className="d-flex flex-row flex-nowrap overflow-auto no-scroll-bar">
            {childrenArray.map((child, index) => (
              <div
                id={`scroll-child-${id}-${index}`}
                key={`scroll-child-${id}-${index}`}
                className={`d-flex m-0 p-0 ${index !== childrenArray.length - 1 ? "me-3" : ""}`}
                style={{ minWidth: "90%", maxWidth: "480px" }}
                onClick={() => {
                  this._handleSelection(index);
                }}
              >
                {child}
              </div>
            ))}
          </div>
          <div className="align-self-center pe-0 d-none d-sm-block">
            <button className="btn btn-clean" onClick={() => this._sideScroll("right")}>
              <i className="fa-solid text-primary fa-chevron-right" />
            </button>
          </div>
        </div>

        <div className={"d-flex mt-3 mb-4 justify-content-center"}>
          {Children.toArray(children).map((child, index) => {
            const style: React.CSSProperties = { fontSize: "18px" };
            if (selectedScrollDot === index) {
              style.color = scrollDotOptions[index].color;
            } else {
              style.color = "rgba(0, 0, 0, 0.22)";
            }

            return (
              <div
                id={`scroll-child-${id}-${index}`}
                key={`scroll-child-${id}-${index}`}
                className="d-flex m-0 p-0"
                onClick={() => {
                  this._handleSelection(index);
                }}
              >
                <i className={"material-icons align-self-center cursor-pointer"} style={style}>
                  fiber_manual_record
                </i>
              </div>
            );
          })}
        </div>
      </>
    );
  }
}

export default PlanSelectionCarouselHorizontalScroller;
