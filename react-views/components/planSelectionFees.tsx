import React from "react";
import { fees, plansConfig } from "@wealthyhood/shared-configs";
import { formatPercentage } from "../utils/formatterUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

const { FX_RATES, CUSTODY_RATES } = fees;

type PropsType = {
  selectedPrice: plansConfig.PriceType;
};

class PlanSelectionFees extends React.Component<PropsType> {
  render() {
    const { selectedPrice } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const plan = PRICE_CONFIG[selectedPrice].plan;
    const isFree = selectedPrice === "free_monthly";

    if (user.isRealtimeETFExecutionEnabled) {
      return (
        <>
          <div
            className="row border-top border-2 mt-4"
            style={{ borderTopColor: "rgba(220, 226, 253, 1) !important" }}
          >
            <div className="d-flex fw-bolder mt-4">
              <span className="material-symbols-outlined text-primary align-self-center me-2">payments</span>
              What you pay:
            </div>
            <div className="mt-4 ps-5">
              <div className="fw-bold mb-2">Commissions:</div>
              <ul className="text-muted">
                <li className="pb-2">Stocks: €0</li>
                <li className="pb-2">ETFs buys with Smart Execution: €0</li>
                <li className="pb-2">ETFs without Smart Execution: €1 per ETF</li>
              </ul>

              <div className="fw-bold mb-2 mt-3">Currency Conversion:</div>
              <ul className="text-muted">
                <li className="pb-2">{`${formatPercentage(
                  FX_RATES[plan],
                  locale
                )} when investing in different currencies`}</li>
              </ul>
            </div>
          </div>
        </>
      );
    }

    return (
      <>
        <div
          className="row border-top border-2 mt-4"
          style={{ borderTopColor: "rgba(220, 226, 253, 1) !important" }}
        >
          <div className="d-flex fw-bolder mt-4">
            <span className="material-symbols-outlined text-primary align-self-center me-2">payments</span>
            Fees:
          </div>
          <ul className="mt-4 ps-5 text-muted">
            <li className="pb-2">{`${formatPercentage(FX_RATES[plan], locale)} currency conversion`}</li>
            <li className={`pb-2 ${!isFree ? "invisible" : ""}`}>{`${formatPercentage(
              CUSTODY_RATES["free"],
              locale
            )} annual custody fee (charged monthly)`}</li>
          </ul>
        </div>
      </>
    );
  }
}

PlanSelectionFees.contextType = GlobalContext;

export default PlanSelectionFees;
