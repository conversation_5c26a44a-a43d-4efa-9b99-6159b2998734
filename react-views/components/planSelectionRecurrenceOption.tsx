import React from "react";
import { PartialRecord } from "../types/utils";
import { plansConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  recurrence: plansConfig.PriceRecurrenceType;
  isSelected: boolean;
  onSelection: (price: plansConfig.PriceRecurrenceType) => void;
};

const PriceRecurrenceConfig: PartialRecord<plansConfig.PriceRecurrenceType, { title: string; order: number }> = {
  monthly: {
    title: "Billed Monthly",
    order: 1
  },
  yearly: {
    title: "Billed Annually",
    order: 0
  }
};

class PlanSelectionRecurrenceOption extends React.Component<PropsType> {
  render(): JSX.Element {
    const { recurrence, onSelection, isSelected } = this.props;

    return (
      <div className="d-flex align-self-center p-2" style={{ order: PriceRecurrenceConfig[recurrence].order }}>
        <div
          className={`btn align-self-center ${
            isSelected ? "btn-plan-recurrence-selected" : "btn-plan-recurrence"
          }`}
          onClick={() => onSelection(recurrence)}
        >
          {PriceRecurrenceConfig[recurrence].title}
        </div>
      </div>
    );
  }
}

export default PlanSelectionRecurrenceOption;
