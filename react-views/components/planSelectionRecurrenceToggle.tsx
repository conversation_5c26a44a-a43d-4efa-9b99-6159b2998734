import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";
import PlanSelectionRecurrenceOption from "./planSelectionRecurrenceOption";

type PropsType = {
  selectedRecurrence: plansConfig.PriceRecurrenceType;
  onSelection: (price: plansConfig.PriceRecurrenceType) => void;
};

class PlanSelectionRecurrenceToggle extends React.Component<PropsType> {
  private _handleRecurrenceToggleSelection = (recurrence: plansConfig.PriceRecurrenceType): void => {
    this.props.onSelection(recurrence);
  };

  private _getPriceConfig = () => {
    const user = (this.context as GlobalContextType).user;
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);

    return Object.fromEntries(
      Object.entries(PRICE_CONFIG).filter(([planKey, planConfig]) => {
        if (planKey === "paid_mid_lifetime_sweatcoin_1") {
          return user.isSweatcoinReferred && planConfig.active;
        }
        return planConfig.active;
      })
    );
  };

  private _getRecurrencesToShow = () => {
    return [
      ...new Set(
        Object.values(this._getPriceConfig())
          .filter((price) => !!price.recurrence && price.recurrence !== "lifetime")
          .map((price) => price.recurrence)
      )
    ];
  };

  render() {
    const { selectedRecurrence } = this.props;

    return (
      <div className={"d-flex flex-row p-3 justify-content-center"}>
        {Object.values(this._getRecurrencesToShow()).map((recurrence) => (
          <PlanSelectionRecurrenceOption
            recurrence={recurrence}
            isSelected={selectedRecurrence === recurrence}
            onSelection={this._handleRecurrenceToggleSelection}
          />
        ))}
      </div>
    );
  }
}

PlanSelectionRecurrenceToggle.contextType = GlobalContext;

export default PlanSelectionRecurrenceToggle;
