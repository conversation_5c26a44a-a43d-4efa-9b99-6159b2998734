import { plansConfig } from "@wealthyhood/shared-configs";
import React from "react";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  selectedPrice: plansConfig.PriceType;
};

export default class PlanSelectionSubtitle extends React.Component<PropsType> {
  render() {
    const user = (this.context as GlobalContextType).user;
    const { promoDescription, description } = ConfigUtil.getPricing(user.companyEntity)[this.props.selectedPrice];

    return (
      <div className="mb-4">
        <h5 className="fw-bolder mb-3">{promoDescription}</h5>
        <p className="text-muted">{description}</p>
      </div>
    );
  }
}

PlanSelectionSubtitle.contextType = GlobalContext;
