import React from "react";
import PortfolioAllocationInsights from "./portfolioAllocationInsights";
import PortfolioAllocationAssetsLists from "./portfolioAllocationAssetsLists";
import PortfolioAllocationSelectAssetsCTA from "./portfolioAllocationSelectAssetsCTA";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PortfolioDocument } from "../../models/Portfolio";
import axios from "axios";
import qs from "qs";
import ConfigUtil from "../../utils/configUtil";
import { UserDocument } from "../../models/User";
import { allocationsMatch } from "../utils/portfolioUtil";
import GroupBy, { GroupByMethodEnum } from "../components/groupBy";

const portfolioGroupByMethods: GroupByMethodEnum[] = [
  GroupByMethodEnum.AssetClassAndSector,
  GroupByMethodEnum.AssetType
];

export type DurationType = "30y" | "20y" | "15y" | "10y" | "5y" | "3y" | "2y" | "1y" | "6m" | "1m";

type PropsType = {
  assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  beforeEditAssetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  user: UserDocument;
  portfolio: PortfolioDocument;
  onAssetAllocationChanged: (assetKey: investmentUniverseConfig.AssetType) => (percentage: number) => void;
  onAddClicked: (startingAllocationFromScratch: boolean) => void;
  onAssetClicked: (assetKey: investmentUniverseConfig.AssetType) => void;
  onEqualizeWeightsClicked: () => void;
  onAssetDelete?: (assetKey: investmentUniverseConfig.AssetType) => void;
  isInEditMode: boolean;
};

type StateType = {
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  activeAssetCategory: investmentUniverseConfig.AssetCategoryType;
  pastPerformance: {
    pastPerformance?: { [key in string]: Record<string, number> };
    metrics?: {
      [key in DurationType]: {
        annualised_return: number;
        maximum_drawdown: number;
        volatility: number;
      };
    };
  };
  pastPerformanceActiveDuration: DurationType;
  futurePerformanceActiveDuration: DurationType;
  futurePerformance: {
    futurePerformance?: { [key in string]: Record<string, number> };
    bestFuturePerformance?: { [key in string]: Record<string, number> };
    worstFuturePerformance?: { [key in string]: Record<string, number> };
  };
  groupByMethod: GroupByMethodEnum;
};

class PortfolioAllocation extends React.Component<PropsType, StateType> {
  private areAssetsSelected: boolean;
  private ASSET_CONFIG;
  constructor(props: PropsType) {
    super(props);
    this.ASSET_CONFIG = ConfigUtil.getActiveOnlyInvestmentUniverseAssets(this.props.user.companyEntity);
    this.areAssetsSelected = Boolean(Object.keys(this.props.assetAllocation).length);
    this.state = {
      activeAssetClass: this.areAssetsSelected ? this._getAssetClassUniverse()[0] : "equities",
      activeAssetCategory: this.areAssetsSelected ? this._getAssetCategoryUniverse()[0] : "etf",
      pastPerformance: {},
      futurePerformance: {},
      pastPerformanceActiveDuration: "10y",
      futurePerformanceActiveDuration: "30y",
      groupByMethod: GroupByMethodEnum.AssetType
    };

    this._setPastPerformanceActiveDuration = this._setPastPerformanceActiveDuration.bind(this);
    this._setFuturePerformanceActiveDuration = this._setFuturePerformanceActiveDuration.bind(this);
    this._setActiveAssetClass = this._setActiveAssetClass.bind(this);
    this._setActiveAssetCategory = this._setActiveAssetCategory.bind(this);
  }

  private _getAssetClassUniverse(): investmentUniverseConfig.AssetClassType[] {
    return Array.from(
      new Set(
        Object.keys(this.props.assetAllocation).map(
          (assetCommonId: investmentUniverseConfig.AssetType) => this.ASSET_CONFIG[assetCommonId].assetClass
        )
      )
    );
  }

  private _getAssetCategoryUniverse(): investmentUniverseConfig.AssetCategoryType[] {
    return Array.from(
      new Set(
        Object.keys(this.props.assetAllocation).map(
          (assetCommonId: investmentUniverseConfig.AssetType) => this.ASSET_CONFIG[assetCommonId].category
        )
      )
    );
  }

  componentDidMount() {
    if (this.areAssetsSelected) {
      this._fetchFuturePerformance();
      this._fetchPastPerformance();
      this._fetchFuturePerformanceMonteCarlo();
    }
  }

  private async _fetchPastPerformance(): Promise<void> {
    try {
      const response = await axios({
        method: "GET",
        url: "/portfolios/past-performance",
        headers: { "Content-Type": "application/json" },
        params: {
          initial: 10000,
          ...this.props.assetAllocation
        },
        paramsSerializer: (params) => {
          return qs.stringify(params, { arrayFormat: "repeat" });
        }
      });

      this.setState({ pastPerformance: response.data });
    } catch (err) {
      // Sentry.captureException(err);
    }
  }

  private async _fetchFuturePerformance(): Promise<void> {
    try {
      const response = await axios({
        method: "GET",
        url: "/portfolios/future-performance",
        headers: { "Content-Type": "application/json" },
        params: {
          // weeklyResample: 1,
          initial: 150,
          ...this.props.assetAllocation
        },
        paramsSerializer: (params) => {
          return qs.stringify(params, { arrayFormat: "repeat" });
        }
      });

      this.setState({
        futurePerformance: {
          futurePerformance: response.data.futurePerformance
        }
      });
    } catch (err) {
      // Sentry.captureException(err);
    }
  }

  private async _fetchFuturePerformanceMonteCarlo(): Promise<void> {
    try {
      const response = await axios({
        method: "GET",
        url: "/portfolios/future-performance-monte-carlo",
        headers: { "Content-Type": "application/json" },
        params: {
          initial: 150,
          ...this.props.assetAllocation
        },
        paramsSerializer: (params) => {
          return qs.stringify(params, { arrayFormat: "repeat" });
        }
      });

      this.setState((prevState) => ({
        ...prevState,
        futurePerformance: {
          bestFuturePerformance: response.data.futurePerformanceBest,
          worstFuturePerformance: response.data.futurePerformanceWorse,
          futurePerformance: prevState.futurePerformance.futurePerformance
        }
      }));
    } catch (err) {
      // Sentry.captureException(err);
    }
  }

  private _onSelectGroupByMethod(selectedGroupByMethod: GroupByMethodEnum) {
    this.setState({ groupByMethod: selectedGroupByMethod });
  }

  componentDidUpdate(prevProps: Readonly<PropsType>): void {
    const prevAreAssetsSelected = this.areAssetsSelected;
    this.areAssetsSelected = Boolean(Object.keys(this.props.assetAllocation).length);

    if (!this.areAssetsSelected && prevAreAssetsSelected) {
      this.setState({
        activeAssetClass: "equities",
        pastPerformance: {},
        futurePerformance: {}
      });
      return;
    }

    if (!this.areAssetsSelected) return;
    // if active asset class does not have any assets after update, choose another default
    if (!this._getAssetClassUniverse().includes(this.state.activeAssetClass)) {
      this.setState({
        activeAssetClass: this._getAssetClassUniverse()[0]
      });
    }

    const currentAllocation = this._getTotalAllocation(this.props.assetAllocation);
    if (!allocationsMatch(prevProps.assetAllocation, this.props.assetAllocation) && currentAllocation == 100) {
      this.setState({ pastPerformance: {}, futurePerformance: {} }, () => {
        this._fetchPastPerformance();
        this._fetchFuturePerformance();
        this._fetchFuturePerformanceMonteCarlo();
      });
    }
  }

  private _setActiveAssetClass = (activeAssetClass: investmentUniverseConfig.AssetClassType): void => {
    if (!this.areAssetsSelected) return;

    this.setState({ activeAssetClass });
  };

  private _setActiveAssetCategory = (activeAssetCategory: investmentUniverseConfig.AssetCategoryType): void => {
    if (!this.areAssetsSelected) return;

    this.setState({ activeAssetCategory });
  };

  private _getTotalAllocation(assetsAllocation: {
    [key in investmentUniverseConfig.AssetType]?: number;
  }): number {
    if (!this.areAssetsSelected) return;

    return Object.values(assetsAllocation).reduce((sum, value) => (value ? value : 0) + sum, 0);
  }

  private _getAssetClassAllocation(): { [key in investmentUniverseConfig.AssetClassType]?: number } {
    if (!this.areAssetsSelected) return;

    const { assetAllocation } = this.props;
    const assetClassAllocation: { [key in investmentUniverseConfig.AssetClassType]?: number } = {};

    Object.entries(assetAllocation).forEach(([assetKey, weight]: [investmentUniverseConfig.AssetType, number]) => {
      const assetClassKey = this.ASSET_CONFIG[assetKey].assetClass;
      if (weight) {
        if (assetClassAllocation[assetClassKey] > 0) {
          assetClassAllocation[assetClassKey] += weight;
        } else {
          assetClassAllocation[assetClassKey] = weight;
        }
      } else if (!(assetClassKey in assetClassAllocation)) {
        assetClassAllocation[assetClassKey] = 0;
      }
    });
    return assetClassAllocation;
  }

  private _setPastPerformanceActiveDuration(pastPerformanceActiveDuration: DurationType) {
    this.setState({ pastPerformanceActiveDuration });
  }

  private _setFuturePerformanceActiveDuration(futurePerformanceActiveDuration: DurationType) {
    this.setState({ futurePerformanceActiveDuration });
  }

  render(): JSX.Element {
    const {
      activeAssetClass,
      activeAssetCategory,
      pastPerformance,
      futurePerformance,
      pastPerformanceActiveDuration,
      futurePerformanceActiveDuration
    } = this.state;
    const {
      assetAllocation,
      beforeEditAssetAllocation,
      onAddClicked,
      onEqualizeWeightsClicked,
      onAssetClicked,
      onAssetAllocationChanged,
      onAssetDelete,
      isInEditMode
    } = this.props;

    const assetClassAllocation = this._getAssetClassAllocation();
    const targetAssetAllocationUniverse = new Set(Object.keys(assetAllocation));

    return (
      <>
        {this.areAssetsSelected ? (
          <>
            <PortfolioAllocationInsights
              assetClassAllocation={assetClassAllocation}
              activeAssetClass={activeAssetClass}
              pastPerformance={pastPerformance}
              futurePerformance={futurePerformance}
              pastPerformanceActiveDuration={pastPerformanceActiveDuration}
              futurePerformanceActiveDuration={futurePerformanceActiveDuration}
              setPastPerformanceActiveDuration={this._setPastPerformanceActiveDuration}
              setFuturePerformanceActiveDuration={this._setFuturePerformanceActiveDuration}
              onSetActiveAssetClass={this._setActiveAssetClass}
            />
            <div className="d-flex justify-content-center align-items-center gap-3 my-3 mb-5">
              <button
                className="btn btn-ghost d-flex align-items-center justify-content-center gap-2 px-2"
                onClick={() => onAddClicked(false)}
              >
                <i
                  className="d-flex material-symbols-outlined justify-content-center align-self-center"
                  style={{ fontSize: "16px" }}
                >
                  add
                </i>
                Add assets
              </button>
              <button
                className="btn btn-ghost d-flex align-items-center justify-content-center gap-2 px-2"
                onClick={onEqualizeWeightsClicked}
              >
                <i
                  className="d-flex material-symbols-outlined justify-content-center align-self-center"
                  style={{ fontSize: "16px" }}
                >
                  legend_toggle
                </i>
                Equal weights
              </button>
            </div>
            <GroupBy
              title="My target portfolio"
              activeGroupByMethod={this.state.groupByMethod}
              groupByMethods={portfolioGroupByMethods}
              onSelectGroupByMethodClick={(selectedGroupByMethod) =>
                this._onSelectGroupByMethod(selectedGroupByMethod)
              }
            />
            <PortfolioAllocationAssetsLists
              assetAllocation={assetAllocation}
              beforeEditAssetAllocation={beforeEditAssetAllocation}
              activeAssetClass={activeAssetClass}
              activeAssetCategory={activeAssetCategory}
              assetClassAllocation={assetClassAllocation}
              assetCategories={this._getAssetCategoryUniverse()}
              targetAssetAllocationUniverse={targetAssetAllocationUniverse}
              onSetActiveAssetClass={this._setActiveAssetClass}
              onSetActiveAssetCategory={this._setActiveAssetCategory}
              onAssetClicked={onAssetClicked}
              onAssetAllocationChanged={onAssetAllocationChanged}
              onAssetDelete={onAssetDelete}
              groupByMethod={this.state.groupByMethod}
              isInEditMode={isInEditMode}
            />
          </>
        ) : (
          <PortfolioAllocationSelectAssetsCTA onAddClicked={onAddClicked} />
        )}
      </>
    );
  }
}

export default PortfolioAllocation;
