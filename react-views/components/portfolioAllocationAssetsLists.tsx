import React from "react";
import AssetClassNavPills from "./assetClassNavPills";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import PortfolioSetupAssetRowNew from "./PortfolioSetupAssetRowNew";
import { getAssetIconUrl } from "../utils/universeUtil";
import { GroupByMethodEnum } from "./groupBy";
import AssetCategoryNavPills from "./assetCategoryNavPills";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { PartialRecord } from "../types/utils";

const { AssetClassArray, ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  beforeEditAssetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  activeAssetCategory: investmentUniverseConfig.AssetCategoryType;
  targetAssetAllocationUniverse: Set<string>;
  assetClassAllocation: {
    equities?: number;
    bonds?: number;
    commodities?: number;
    realEstate?: number;
  };
  assetCategories: investmentUniverseConfig.AssetCategoryType[];
  onSetActiveAssetClass: (activeAssetClass: investmentUniverseConfig.AssetClassType) => void;
  onSetActiveAssetCategory: (activeAssetCategory: investmentUniverseConfig.AssetCategoryType) => void;
  onAssetClicked: (assetKey: investmentUniverseConfig.AssetType) => void;
  onAssetAllocationChanged: (assetKey: investmentUniverseConfig.AssetType) => (percentage: number) => void;
  onAssetDelete?: (assetKey: investmentUniverseConfig.AssetType) => void;
  groupByMethod: GroupByMethodEnum;
  isInEditMode: boolean;
};

class PortfolioAllocationAssetsLists extends React.Component<PropsType> {
  private _getFilteredCategoryAssets(assetCategory: string) {
    const filteredAssets: PartialRecord<
      investmentUniverseConfig.AssetType,
      investmentUniverseConfig.AssetConfigType
    > = Object.fromEntries(
      Object.entries(
        ConfigUtil.getActiveOnlyInvestmentUniverseAssets((this.context as GlobalContextType)?.user?.companyEntity)
      ).filter(([, assetConfig]) => {
        return assetConfig.category === assetCategory;
      })
    );

    return filteredAssets;
  }

  renderAssetRows = (assetKeys: investmentUniverseConfig.AssetType[]) => {
    const { assetAllocation, onAssetClicked, onAssetAllocationChanged, onAssetDelete, isInEditMode } = this.props;
    let sortedAssets;
    if (isInEditMode) {
      // Sorting based on the original asset allocation before any edits
      sortedAssets = [...assetKeys].sort(
        (a, b) => this.props.beforeEditAssetAllocation[b] - this.props.beforeEditAssetAllocation[a]
      );
    } else {
      // Sorting based on the current asset allocation
      sortedAssets = [...assetKeys].sort((a, b) => assetAllocation[b] - assetAllocation[a]);
    }

    return sortedAssets.map((assetKey) => {
      const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[assetKey];
      return (
        <PortfolioSetupAssetRowNew
          key={assetKey}
          simpleName={simpleName}
          description={tickerWithCurrency + " • " + shortDescription}
          logoUrl={getAssetIconUrl(assetKey)}
          category={category}
          onAssetClick={() => onAssetClicked(assetKey)}
          onPercentageChange={(percentage) => onAssetAllocationChanged(assetKey)(percentage)}
          percentage={assetAllocation[assetKey]}
          onDelete={onAssetDelete ? () => onAssetDelete(assetKey) : undefined}
        />
      );
    });
  };

  render(): JSX.Element {
    const {
      activeAssetClass,
      activeAssetCategory,
      targetAssetAllocationUniverse,
      assetCategories,
      onSetActiveAssetClass,
      onSetActiveAssetCategory,
      assetClassAllocation,
      groupByMethod
    } = this.props;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses((this.context as GlobalContextType).user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors((this.context as GlobalContextType).user.companyEntity);

    const filteredCategoryAssets = Object.keys(
      this._getFilteredCategoryAssets(activeAssetCategory) as investmentUniverseConfig.AssetType[]
    ) as investmentUniverseConfig.AssetType[];

    const filteredSectorAssets = ASSET_CLASS_CONFIG[activeAssetClass].assets.filter((assetKey) =>
      targetAssetAllocationUniverse.has(assetKey)
    );

    return (
      <>
        {groupByMethod === GroupByMethodEnum.AssetType && (
          <>
            {/* Asset Classes Nav */}
            <AssetCategoryNavPills
              className={"mt-3 mb-3"}
              assetCategories={assetCategories}
              activeCategory={activeAssetCategory}
              onAssetTypeSelection={onSetActiveAssetCategory}
            />
            {/* End Asset Classes Nav */}
            {this.renderAssetRows(
              filteredCategoryAssets.filter((assetKey) => targetAssetAllocationUniverse.has(assetKey))
            )}
          </>
        )}
        {groupByMethod === GroupByMethodEnum.AssetClassAndSector && (
          <div className="row m-0">
            <div className="col p-0">
              <div className="d-flex align-self-center">
                {/* AssetClassNav */}
                <AssetClassNavPills
                  className={"mt-3 mb-5"}
                  assetClasses={AssetClassArray.filter((assetClassKey) => assetClassKey in assetClassAllocation)}
                  activeAssetClass={activeAssetClass}
                  onAssetClassSelection={onSetActiveAssetClass}
                />
                {/* AssetClassNav */}
              </div>

              {/* Asset List */}
              {activeAssetClass === "equities"
                ? investmentUniverseConfig.InvestmentSectorArray.map((sector) => {
                    const sectorAssets = filteredSectorAssets.filter(
                      (asset) => ASSET_CONFIG[asset].sector === sector
                    );
                    if (sectorAssets.length === 0) {
                      return null;
                    }

                    return (
                      <div key={`onboarding-editable-mobile-sector-${sector}`}>
                        <div className="col h6 fw-bold">{SECTOR_CONFIG[sector].fieldName}</div>
                        {this.renderAssetRows(sectorAssets)}
                      </div>
                    );
                  })
                : this.renderAssetRows(filteredSectorAssets)}
              {/* End Asset List */}
            </div>
          </div>
        )}
      </>
    );
  }
}

PortfolioAllocationAssetsLists.contextType = GlobalContext;

export default PortfolioAllocationAssetsLists;
