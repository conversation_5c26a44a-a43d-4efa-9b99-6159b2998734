import React from "react";
import AssetClass<PERSON>ie from "./assetClassPie";
import PastPerformanceChart from "./pastPerformanceChart";
import FuturePerformanceChart from "./futurePerformanceChart";
import LoadingSpinner from "./loadingSpinner";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import InfoModal from "./modals/infoModal";
import { formatCurrency } from "../utils/currencyUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { DurationType } from "./portfolioAllocation";

const TOOLTIPS_CONFIG: { [key in string]: string } = {
  annualised_return:
    "<h5 class='fw-bolder'>Expected return</h5> <br> <h6 class='fw-bolder'>How do we calculate the expected return?</h6> <br> <p>The expected return of your portfolio is the annualised rate of return that the portfolio generated over the past 10 years.</p> <br> <p>In particular, it’s calculated as the annualised rate of return of the indices that the individual ETFs track, over the past 10 years or since an index was first created if it's less than 10 years ago. </p> <br> <p>In simple terms, it’s the annual return you should expect to receive from investing in this portfolio, if the markets performed exactly as in the past 10 years.</p> <br> <p>However, keep in mind that past performance is not indicative of future returns.</p> ",
  maximum_drawdown:
    "<h5 class='fw-bolder'>Maximum drawdown</h5> <br> <h6 class='fw-bolder'>What is the maximum drawdown?</h6> <br> <p>Maximum drawdown is a measure of risk that shows the greatest decline from the highest point of your portfolio to the subsequent lowest point.</p> <br> <p>In general, portfolios with higher maximum drawdown are considered riskier, because the value drops are expected to be steeper.</p> <br> <p>The maximum drawdown measures the size of the largest loss, without taking into account the frequency of large losses. It focuses on capital preservation, which is a key concern for most investors. </p> <br> <p>If combined with volatility, it can be representative of the relative risk of one portfolio compared to another.</p> <br> <p>However, keep in mind that past performance is not indicative of future returns.</p> ",
  volatility:
    "<h5 class='fw-bolder'>Risk</h5> <br> <h6 class='fw-bolder'>How do we calculate risk?</h6> <br> <p>We calculate risk as the total expected volatility of your portfolio. </p> <br> <p>Volatility is a statistical measure that represents how much the value of your portfolio swings around its mean price! </p> <br> <p>More volatile portfolios are considered riskier than less volatile ones, because the value movements are expected to be steeper and therefore, less predictable. </p> <br> <p>We calculate the expected volatility of your portfolio, based on the performance of the indices that the individual ETFs track, over the past 10 years or since the index was first created if it's less than 10 years ago. </p> <br> <p>However, keep in mind that past performance is not indicative of future returns.</p>",
  future_performance:
    "<h5 class='fw-bolder'>How we calculate future performance</h5><br><p>We calculate future performance based on the past performance of the individual assets.</p><br><p>We calculate key metrics, like the return and volatility of each asset, to estimate how it would perform in the coming years.</p><br><p>However, as you know, the future is unpredictable and markets may perform better or worse than expected.</p><br><p>This is why we have added the two dashed lines to show what this means for the value of your portfolio.</p><br><p>For the math geeks, the dashed lines are calculated with a Monte Carlo simulation for an estimated 1-sigma move in your expected return. In a normal distribution, it is postulated that anything within the 1-sigma lines will be true 68% of the time.</p><br><p>You can select different time horizons to understand how your potential expected returns move.</p><br><p>As always, past performance is not a guarantee of future performance.</p>",
  diversification_score:
    "<h5 class='fw-bolder'>Diversification score</h5><br><h6 class='fw-bolder'>What is the diversification score?</h6><br><p>The diversification score (ratio) is a measure used to assess how well a portfolio is diversified, essentially showing if investments are spread across different assets to minimise risk.</p><br><h6 class='fw-bolder'>How we calculate it</h6><br><p>To determine the diversification score, first, we look at the average volatility (or price fluctuations) of each asset in the portfolio.<br> Next, we find the overall volatility of the entire portfolio. The score is then calculated by dividing the average volatility of the individual assets by the portfolio's overall volatility. <br>We then transform it to a percentage score ranging from 0 to 100. The higher the percentage, the more diversified the portfolio is.</p><br><h6 class='fw-bolder'>Why it's useful</h6><br><p>This score is crucial for understanding risk management in a portfolio. A higher diversification score suggests the portfolio is more diversified, which can help in reducing the risk of significant losses.<br>It's an important tool to evaluate if an investment strategy has concentrated too much in a few assets or if it's well spread out."
};

const METRICS_CONFIG: { [key in string]: string } = {
  annualised_return: "Expected return",
  volatility: "Risk",
  maximum_drawdown: "Max drawdown",
  diversification_score: "Diversification"
};

type TabType = "Breakdown" | "Past" | "Future" | "Metrics";

type PropsType = {
  activeAssetClass: investmentUniverseConfig.AssetClassType;
  assetClassAllocation: {
    equities?: number;
    bonds?: number;
    commodities?: number;
    realEstate?: number;
  };
  pastPerformance: {
    pastPerformance?: { [key in string]: Record<string, number> };
    metrics?: {
      [key in DurationType]: {
        annualised_return: number;
        maximum_drawdown: number;
        volatility: number;
      };
    };
  };
  pastPerformanceActiveDuration: DurationType;
  futurePerformanceActiveDuration: DurationType;
  futurePerformance: {
    futurePerformance?: { [key in string]: Record<string, number> };
    bestFuturePerformance?: { [key in string]: Record<string, number> };
    worstFuturePerformance?: { [key in string]: Record<string, number> };
  };
  setPastPerformanceActiveDuration: (duration: DurationType) => void;
  setFuturePerformanceActiveDuration: (duration: DurationType) => void;
  onSetActiveAssetClass: (activeAssetClass: investmentUniverseConfig.AssetClassType) => void;
};

type StateType = {
  activeTab: TabType;
  showInfoDialog: boolean;
  selectedInfo: string;
};

class PortfolioAllocationInsights extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      activeTab: "Breakdown",
      showInfoDialog: false,
      selectedInfo: ""
    };
  }

  private _setActiveTab(activeTab: TabType) {
    this.setState({ activeTab });
  }

  private _getDisplayedAssetClassAllocation(): { [key in investmentUniverseConfig.AssetClassType]?: number } {
    return Object.fromEntries(Object.entries(this.props.assetClassAllocation).filter(([, weight]) => weight > 0));
  }

  private _setSelectedInfo(selectedInfo: string) {
    this.setState({ selectedInfo, showInfoDialog: true });
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showInfoDialog });
  }

  render(): JSX.Element {
    const { activeTab, showInfoDialog, selectedInfo } = this.state;
    const {
      activeAssetClass,
      pastPerformance,
      pastPerformanceActiveDuration,
      futurePerformance,
      futurePerformanceActiveDuration,
      setPastPerformanceActiveDuration,
      setFuturePerformanceActiveDuration
    } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const renderBreakdownTab = () => (
      <AssetClassPie
        selectedAssetClass={activeAssetClass}
        allocation={this._getDisplayedAssetClassAllocation()}
        onAssetClassClick={this.props.onSetActiveAssetClass}
      />
    );

    const renderPastTab = () =>
      pastPerformance.pastPerformance ? (
        <PastPerformanceChart
          description={(duration) => (
            <p className="text-muted">
              {formatCurrency(10000, user.currency, locale, 0, 0)} invested {duration} ago
            </p>
          )}
          data={pastPerformance.pastPerformance}
          durations={["1m", "1y", "2y", "5y", "10y"]}
          activeDuration={pastPerformanceActiveDuration}
          onDurationChange={(duration) => setPastPerformanceActiveDuration(duration)}
        />
      ) : (
        <LoadingSpinner />
      );

    const renderFutureTab = () =>
      futurePerformance.futurePerformance ? (
        <FuturePerformanceChart
          description={(duration: string) => (
            <p className="text-muted d-flex">
              <span style={{ fontWeight: "500", color: "black" }}>{`${formatCurrency(
                150,
                user.currency,
                locale,
                0,
                0
              )} / month invested for ${duration}`}</span>
              <i
                className="material-symbols-outlined align-self-center text-primary cursor-pointer ps-1"
                style={{ fontSize: "16px" }}
                onClick={() => {
                  this._setSelectedInfo(TOOLTIPS_CONFIG.future_performance);
                  this._setShowInfoDialog(true);
                }}
              >
                info
              </i>
            </p>
          )}
          data={futurePerformance.futurePerformance}
          upperBoundData={futurePerformance?.bestFuturePerformance}
          lowerBoundData={futurePerformance?.worstFuturePerformance}
          durations={["5y", "10y", "20y", "30y"]}
          activeDuration={futurePerformanceActiveDuration}
          onDurationChange={(duration) => setFuturePerformanceActiveDuration(duration)}
        />
      ) : (
        <LoadingSpinner />
      );

    const renderMetricsTab = () => {
      if (pastPerformance && pastPerformance.metrics)
        return (
          <div className="row m-0 p-md-5 p-3">
            {Object.keys(METRICS_CONFIG).map((key) => (
              <div className="col-6 p-0 pb-4" key={`metric-${key}`}>
                <div className="d-flex flex-column align-items-start">
                  <div className="d-flex w-100">
                    <h6 className="fw-bolder text-primary mb-2">
                      {
                        pastPerformance.metrics["10y"][
                          key as "annualised_return" | "maximum_drawdown" | "volatility"
                        ]
                      }
                      %
                    </h6>
                  </div>
                  <div className="d-flex w-100">
                    <p className="text-dark-50">{METRICS_CONFIG[key]}</p>
                    {
                      <span
                        className="material-symbols-outlined cursor-pointer icon-primary align-self-baseline ms-1"
                        style={{
                          fontSize: "16px",
                          marginTop: "0.3rem"
                        }}
                        onClick={() => this._setSelectedInfo(TOOLTIPS_CONFIG[key])}
                      >
                        info
                      </span>
                    }
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      else return <LoadingSpinner />;
    };

    const renderTabContent = () => {
      if (activeTab === "Breakdown") {
        return renderBreakdownTab();
      } else if (activeTab === "Past") {
        return renderPastTab();
      } else if (activeTab === "Future") {
        return renderFutureTab();
      } else if (activeTab === "Metrics") {
        return renderMetricsTab();
      } else {
        return null;
      }
    };

    return (
      <>
        <div className="row wh-card m-0 p-md-4 p-1" style={{ minHeight: "524px" }}>
          <div className="col p-0">
            <h4 className="fw-bolder p-3">Insights</h4>
            <div className="d-flex px-3 pb-4 flex-row flex-nowrap overflow-auto no-scroll-bar">
              {["Breakdown", "Past", "Future", "Metrics"].map((tab, index) => (
                <div
                  key={`tenor-nav-${index}`}
                  className={
                    "cursor-pointer d-block col py-2 px-3 text-center align-self-center text-center text-nowrap fw-bold t-875 " +
                    (activeTab === tab ? "wh-card text-primary" : "text-muted")
                  }
                  onClick={() => this._setActiveTab(tab as TabType)}
                >
                  {tab}
                </div>
              ))}
            </div>
            {renderTabContent()}
          </div>
        </div>
        <InfoModal title={""} show={showInfoDialog} handleClose={() => this._setShowInfoDialog(false)}>
          <div dangerouslySetInnerHTML={{ __html: selectedInfo }} />
        </InfoModal>
      </>
    );
  }
}

PortfolioAllocationInsights.contextType = GlobalContext;

export default PortfolioAllocationInsights;
