import React from "react";

type PropsType = {
  onAddClicked: (startingAllocationFromScratch: boolean) => void;
};

class PortfolioAllocationSelectAssetsCTA extends React.Component<PropsType> {
  render(): JSX.Element {
    return (
      <div className="row wh-card m-0 p-md-4 p-1" style={{ minHeight: "524px" }}>
        <div className="col d-flex flex-column align-items-center justify-content-center gap-4">
          <div className="text-center">Add assets to get started building your portfolio!</div>
          <button
            className="btn btn-ghost d-flex align-items-center justify-content-center gap-2 px-2"
            onClick={() => this.props.onAddClicked(true)}
          >
            <i
              className="d-flex material-symbols-outlined justify-content-center align-self-center"
              style={{ fontSize: "16px" }}
            >
              add
            </i>
            Add assets
          </button>
        </div>
      </div>
    );
  }
}

export default PortfolioAllocationSelectAssetsCTA;
