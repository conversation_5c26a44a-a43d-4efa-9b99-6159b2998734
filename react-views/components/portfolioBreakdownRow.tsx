import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";
import AssetIcon from "./assetIcon";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type PropsType = {
  category: "etf" | "stock";
  advancedName: string;
  investmentAmountWithCurrency?: string;
  allocationPercent: number;
  logoUrl: string;
  performance: number;
  simpleName: string;
  onAssetClick: () => void;
};

class PortfolioBreakdownRow extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-75 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 4px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  render(): JSX.Element {
    const {
      advancedName,
      allocationPercent,
      investmentAmountWithCurrency,
      logoUrl,
      performance,
      simpleName,
      onAssetClick,
      category
    } = this.props;
    const { locale } = this.context as GlobalContextType;

    const performanceColor = performance >= 0 ? "success" : "danger";
    const performanceSign =
      performance >= 0 ? (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          trending_up
        </span>
      ) : (
        <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
          trending_down
        </span>
      );

    const allocationPercentageComp = (
      <span className="align-self-center cursor-pointer">
        <OverlayTrigger
          placement="bottom"
          overlay={
            <Popover id="popover-explanation">
              <Popover.Content>
                <span>Refers to holding allocation in portfolio</span>
              </Popover.Content>
            </Popover>
          }
        >
          <span>
            {allocationPercent.toLocaleString(locale, {
              style: "percent",
              maximumFractionDigits: 2
            })}
          </span>
        </OverlayTrigger>
      </span>
    );

    return (
      <div className="row m-0 my-2 cursor-pointer" onClick={onAssetClick}>
        <div className="col-2 p-0 mb-2 align-self-center text-center">
          <div className="position-relative" style={{ width: "fit-content" }}>
            <AssetIcon category={category} iconUrl={logoUrl} size="lg" />
            {PortfolioBreakdownRow._getLabel(category)}
          </div>
        </div>
        <div className="col-7 pt-md-3 ps-md-2 pt-3 ps-4">
          <div className="d-flex align-items-center mb-1">
            <h6 className="fw-bold m-0">{simpleName}</h6>
          </div>
          <div className="d-flex fw-bold t-875 text-nowrap text-muted">
            {advancedName}
            <span className="px-1 fw-bolder align-self-center">•</span>
            <span>
              {/* If the portfolio is real than display next to the investment amount the allocation percentage. */}
              {allocationPercentageComp}
            </span>
          </div>
        </div>
        <div className="col-3 text-center align-self-center">
          {/* Investment Amount & Returns */}
          {investmentAmountWithCurrency && (
            <div className="d-flex flex-row align-items-middle justify-content-end">
              <span className="fw-bold d-block pe-1">{investmentAmountWithCurrency}</span>
            </div>
          )}
          {/* If the portfolio is real display on the bottom the returns and for virtuals the allocation percentage. */}
          {/* For virtual portfolios we don't have a way to calculate asset money-weighted returns. */}
          {
            <OverlayTrigger
              placement="bottom"
              overlay={
                <Popover id="popover-explanation">
                  <Popover.Content>
                    <span>Money-weighted return since you first bought this ETF.</span>
                  </Popover.Content>
                </Popover>
              }
            >
              <div className={`d-flex justify-content-end text-${performanceColor} fw-bold`}>
                <span className="align-self-center">{performanceSign}</span>
                {Math.abs(performance).toLocaleString(locale, {
                  style: "percent",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })}
              </div>
            </OverlayTrigger>
          }
          {/* End Investment Amount & Returns */}
        </div>
      </div>
    );
  }
}

PortfolioBreakdownRow.contextType = GlobalContext;

export default PortfolioBreakdownRow;
