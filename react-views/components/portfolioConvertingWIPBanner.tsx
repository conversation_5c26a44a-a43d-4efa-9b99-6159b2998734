import React from "react";

class PortfolioConvertingWIPBanner extends React.Component {
  render(): JSX.Element {
    const displayBorder = true;

    return (
      <div className={`card overflow-hidden w-100 border-radius-lg ${displayBorder ? "shadow-xs" : "border-0"}`}>
        <div className="card-body py-15">
          <div className="row justify-content-md-start align-items-md-center text-center text-md-left">
            <div className="col-md-7 offset-md-3 mb-3 mb-md-0">
              <h4 className="mb-0">Your investment request has been placed and is pending settlement.</h4>
            </div>
          </div>

          {/* SVG Component */}
          <figure
            className="d-none d-md-block position-absolute top-0 mt-2"
            style={{ width: "15%", left: "40px !important" }}
          >
            <img
              className="img-fluid"
              src="/images/illustrations/transfer_money.svg"
              alt="Investing in progress"
            />
          </figure>
          {/* End SVG Component */}
        </div>
      </div>
    );
  }
}

export default PortfolioConvertingWIPBanner;
