import React from "react";

type PropsType = {
  description: string;
  icon: string;
  title: string;
  onSelectionCb: () => void;
};

class PortfolioCreationOption extends React.Component<PropsType> {
  render(): JSX.Element {
    const { description, icon, title, onSelectionCb } = this.props;
    const style = { background: "#F1F3FD", minHeight: "132px" };

    return (
      <div
        className="card card-body wh-simple-card cursor-pointer border-0 justify-content-center"
        style={style}
        onClick={(): void => onSelectionCb()}
      >
        <div className="row w-100 p-0 justify-content-start">
          <div className="col-4 text-start align-self-center text-center p-0">
            <img
              alt="icon"
              className="h-100 align-self-center"
              src={icon}
              style={{ width: "70px", height: "70px" }}
            />
          </div>
          <div className="col-8 text-start p-0 align-self-center">
            <h5 className="fw-bold">{title}</h5>
            <p className="text-secondary m-0">{description}</p>
          </div>
        </div>
      </div>
    );
  }
}

export default PortfolioCreationOption;
