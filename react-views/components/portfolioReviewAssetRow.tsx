import React from "react";
import AssetIcon from "./assetIcon";
import { AllocationActionType } from "./modals/portfolioReviewModal";

type PropsType = {
  category: "etf" | "stock";
  simpleName: string;
  description: string;
  logoUrl: string;
  isSelected: boolean;
  onAssetClick?: () => void;
  action: AllocationActionType;
};

class PortfolioReviewAssetRow extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-75 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 4px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  private static _getActionItem(action: AllocationActionType, isSelected: boolean): JSX.Element {
    let text, icon, actionClass;

    if (action === "toAdd") {
      if (isSelected) {
        text = "Added";
        icon = "check_circle";
        actionClass = "added-review-asset";
      } else {
        text = "Add";
        icon = "add_circle";
        actionClass = "add-review-asset";
      }
    } else if (action === "toRemove") {
      if (isSelected) {
        text = "Removed";
        icon = "check_circle";
        actionClass = "removed-review-asset";
      } else {
        text = "Remove";
        icon = "do_not_disturb_on";
        actionClass = "remove-review-asset";
      }
    }

    const iconColor = isSelected ? { color: "white" } : {};
    const iconStyleClass = isSelected ? "material-icons icon-primary" : "material-symbols-outlined";

    return (
      <div className={`col-3 text-center align-self-center review-asset ${actionClass}`}>
        <div className="d-flex align-items-center my-0 mx-2">
          <span className={`${iconStyleClass} align-self-center edit-review-asset-icon`} style={iconColor}>
            {icon}
          </span>
          {text}
        </div>
      </div>
    );
  }

  render(): JSX.Element {
    const { simpleName, description, logoUrl, isSelected, category, action, onAssetClick } = this.props;

    return (
      <div className="row m-0 my-2 cursor-pointer justify-content-between" onClick={onAssetClick}>
        <div className="d-flex col-9">
          <div className="p-0 m-0 align-self-center text-center">
            <div className="position-relative" style={{ width: "fit-content" }}>
              <AssetIcon category={category} iconUrl={logoUrl} size="lg" />
              {PortfolioReviewAssetRow._getLabel(category)}
            </div>
          </div>
          <div className="pt-md-3 ps-md-2 pt-3 ps-4">
            <div className="d-flex align-items-center mb-1">
              <h6 className="fw-bold m-0">{simpleName}</h6>
            </div>
            <p className="d-block t-875 text-muted text-truncate fw-bold">{description}</p>
          </div>
        </div>
        {PortfolioReviewAssetRow._getActionItem(action, isSelected)}
      </div>
    );
  }
}

export default PortfolioReviewAssetRow;
