import React from "react";
import { getAssetIconUrl, sortSearchAssets } from "../utils/universeUtil";
import { investmentUniverseConfig, localeConfig } from "@wealthyhood/shared-configs";
import PortfolioSetupAssetRow from "./portfolioSetupAssetRow";
import { formatPercentage } from "../utils/formatterUtil";
import { formatCurrency } from "../utils/currencyUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { AssetConfigType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PropsType = {
  filteredAssets: investmentUniverseConfig.AssetType[];
  searchTerm: string;
  investmentProducts?: InvestmentProductDocument[];
};
class PortfolioSearchAssetDiscoveryRow extends React.Component<PropsType> {
  private _investmentProductDict: Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;

  constructor(props: PropsType) {
    super(props);
    if (props.investmentProducts) {
      this._investmentProductDict = Object.fromEntries(
        props.investmentProducts.map((investmentProduct) => [investmentProduct.commonId, investmentProduct])
      ) as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;
    } else {
      this._investmentProductDict = {} as Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>;
    }
  }

  private static _formatPercentage(percentage: number, locale: localeConfig.LocaleType): JSX.Element {
    const formattedPercentage = formatPercentage(percentage, locale, 2, 2);

    if (percentage >= 0) {
      return (
        <span className="d-flex text-success">
          <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "14px" }}>
            trending_up
          </span>
          {formattedPercentage}
        </span>
      );
    } else {
      return (
        <span className="d-flex text-danger">
          <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "14px" }}>
            trending_down
          </span>
          {formattedPercentage}
        </span>
      );
    }
  }

  render(): JSX.Element {
    const { filteredAssets, searchTerm, investmentProducts } = this.props;
    const { locale } = this.context as GlobalContextType;

    const filteredAndSortedAssets = filteredAssets.sort((a, b) => sortSearchAssets(a, b, searchTerm));

    return (
      <>
        {filteredAndSortedAssets.map((assetKey) => {
          const { simpleName, tickerWithCurrency, shortDescription, category } = ASSET_CONFIG[
            assetKey
          ] as AssetConfigType;

          return (
            <PortfolioSetupAssetRow
              key={assetKey}
              category={category}
              simpleName={simpleName}
              description={`${tickerWithCurrency} • ${shortDescription}`}
              logoUrl={getAssetIconUrl(assetKey)}
              onAssetClick={() => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
              className="mb-3"
            >
              {/* If investmentProducts is provided in the props, and it has data, display percentage values */}
              {investmentProducts && Object.keys(investmentProducts).length > 0 && (
                <div className="d-flex flex-column align-items-end">
                  <span className="fw-bold d-block pb-1">
                    {formatCurrency(
                      this._investmentProductDict[assetKey]?.tradedPrice,
                      this._investmentProductDict[assetKey]?.tradedCurrency,
                      locale
                    )}
                  </span>
                  <span className="d-block">
                    {PortfolioSearchAssetDiscoveryRow._formatPercentage(
                      this._investmentProductDict[assetKey]?.currentTicker?.dailyReturnPercentage,
                      locale
                    )}
                  </span>
                </div>
              )}
            </PortfolioSetupAssetRow>
          );
        })}
      </>
    );
  }
}

PortfolioSearchAssetDiscoveryRow.contextType = GlobalContext;

export default PortfolioSearchAssetDiscoveryRow;
