import React from "react";
import AssetIcon from "./assetIcon";

type PropsType = {
  category: "etf" | "stock";
  simpleName: string;
  description: string;
  logoUrl: string;
  isSelected: boolean;
  onAssetClick?: () => void;
  onSelectionClick: () => void;
};

class PortfolioSetupAddableAssetRowNew extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-75 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 4px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  render(): JSX.Element {
    const { simpleName, description, logoUrl, isSelected, onAssetClick, onSelectionClick, category } = this.props;

    return (
      <div className="row m-0 my-2 cursor-pointer" onClick={onAssetClick}>
        <div className="col-2 p-0 m-0 align-self-center text-center position-relative">
          <div className="position-relative" style={{ width: "fit-content" }}>
            <AssetIcon category={category} iconUrl={logoUrl} size="lg" />
            {PortfolioSetupAddableAssetRowNew._getLabel(category)}
          </div>
        </div>
        <div className="col-8 pt-md-3 ps-md-2 pt-3 ps-4">
          <div className="d-flex align-items-center mb-1">
            <h6 className="fw-bold m-0">{simpleName}</h6>
          </div>
          <p className="d-block t-875 text-muted text-truncate fw-bold">{description}</p>
        </div>
        <div
          className="col-2 text-center align-self-center"
          onClick={(event) => {
            event.stopPropagation();
            onSelectionClick();
          }}
        >
          {!isSelected ? (
            <span
              className="material-symbols-outlined text-secondary align-self-center align-self-center"
              style={{
                height: "24px",
                width: "24px"
              }}
            >
              add_circle
            </span>
          ) : (
            <span
              className="material-icons icon-primary align-self-center align-self-center"
              style={{
                height: "24px",
                width: "24px"
              }}
            >
              check_circle
            </span>
          )}
        </div>
      </div>
    );
  }
}

export default PortfolioSetupAddableAssetRowNew;
