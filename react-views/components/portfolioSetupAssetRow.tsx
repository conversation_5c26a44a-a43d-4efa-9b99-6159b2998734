import React from "react";
import AssetIcon from "./assetIcon";

type PropsType = {
  category: "etf" | "stock";
  description: string;
  borderColor?: string;
  logoUrl: string;
  simpleName: string;
  className?: string;
  onAssetClick?: () => void;
};

class PortfolioSetupAssetRow extends React.Component<PropsType> {
  private static _getLabel(category: "stock" | "etf"): JSX.Element {
    if (category === "etf") {
      return (
        <div
          className="wh-primary-label t-50 position-absolute"
          style={{ top: "0", right: "0", padding: "1px 3px" }}
        >
          ETF
        </div>
      );
    } else return null;
  }

  render(): JSX.Element {
    const { description, logoUrl, simpleName, onAssetClick, children, category, className } = this.props;

    return (
      <div className={`d-flex justify-content-between m-0 p-0 cursor-pointer ${className}`} onClick={onAssetClick}>
        <div className="d-flex">
          <div className="p-0 m-0 align-self-center text-center">
            <div className="position-relative" style={{ width: "fit-content" }}>
              <AssetIcon category={category} iconUrl={logoUrl} size="md" />
              {PortfolioSetupAssetRow._getLabel(category)}
            </div>
          </div>
          <div className="d-flex flex-column justify-content-center ps-3">
            <div className="d-flex align-items-center mb-1">
              <h6 className="fw-bold m-0">{simpleName}</h6>
            </div>
            <div className="d-flex flex-row">
              <div className="d-block t-75 text-truncate text-muted">{description}</div>
            </div>
          </div>
        </div>
        <div
          className="text-center align-self-center"
          onClick={(event) => {
            event.stopPropagation();
            onAssetClick();
          }}
        >
          {children}
        </div>
      </div>
    );
  }
}

export default PortfolioSetupAssetRow;
