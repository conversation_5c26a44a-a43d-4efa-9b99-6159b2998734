import React from "react";
import { stepTitles } from "../configs/portfolioSetupWizardConfig";

type PropsType = {
  currentStep?: number;
};

class PortfolioSetupWizardNav extends React.Component<PropsType> {
  render(): JSX.Element {
    const { currentStep } = this.props;

    return (
      <div className="d-flex flex-column align-items-center">
        <p className="bg-light-primary py-2 px-3 border-radius-sm">{currentStep + 3}/7 Steps</p>
        <p className="font-size-h5 text-center">{stepTitles[currentStep - 1]}</p>
      </div>
    );
  }
}

export default PortfolioSetupWizardNav;
