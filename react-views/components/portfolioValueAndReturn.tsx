import React from "react";
import { formatCurrency } from "../utils/currencyUtil";
import { PortfolioReturnRates } from "../../models/Portfolio";
import InfoModal from "./modals/infoModal";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { TenorEnum } from "../configs/durationConfig";

const ACTIVE_TENOR_TEXT_CONFIG: Record<TenorEnum, string> = {
  max: "since you began investing",
  "1y": "in the last year",
  "6m": "in the last 6 months",
  "3m": "in the last 3 months",
  "1m": "in the last month",
  "1w": "in the last week"
};

type StateType = {
  showPortfolioReturnInfoModal: boolean;
};
type PropsType = {
  upByValues: PortfolioReturnRates;
  portfolioReturnsValues: PortfolioReturnRates;
  activeTenor: TenorEnum;
  portfolioValue: number;
};

class PortfolioValueAndReturn extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showPortfolioReturnInfoModal: false
    };
  }

  private _renderIcon = (iconName: string) => (
    <span className={"material-symbols-outlined align-self-center"} style={{ fontSize: "16px" }}>
      {iconName}
    </span>
  );

  private _setShowPortfolioReturnInfoModal(showPortfolioReturnInfoModal: boolean) {
    this.setState({ showPortfolioReturnInfoModal });
  }

  // Function to determine and render the appropriate icons based on values
  private _renderUpByValueIcons = (value: number, type: "sign" | "arrow") => {
    if (type === "arrow") {
      if (value > 0) {
        return <>{this._renderIcon("arrow_drop_up")}</>;
      } else if (value < 0) {
        return <>{this._renderIcon("arrow_drop_down")}</>;
      } else {
        return <></>;
      }
    } else if (type === "sign") {
      if (value > 0) {
        return <>{this._renderIcon("add")}</>;
      } else if (value < 0) {
        return <>{this._renderIcon("remove")}</>;
      } else {
        return <></>;
      }
    }
  };

  render() {
    const { activeTenor, portfolioValue } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    const upByValue = this.props.upByValues[activeTenor];
    const portfolioReturnsValue = this.props.portfolioReturnsValues[activeTenor];
    if (!upByValue && upByValue !== 0) return;
    return (
      <>
        <div className="d-flex p-0 flex-column align-items-start mb-3">
          <span className="h4 fw-bolder d-block mb-1">
            {formatCurrency(portfolioValue, user.currency, locale)}
          </span>
          <div className={`h6 ${upByValue >= 0 ? "text-success" : "text-danger"} d-flex gap-1 mb-1 me-1`}>
            <div className="d-flex align-items-center">
              {this._renderUpByValueIcons(upByValue, "arrow")}
              {formatCurrency(Math.abs(upByValue), user.currency, locale)}
            </div>
            {portfolioReturnsValue ? (
              <div className="ms-1 d-flex align-items-center">
                ({this._renderUpByValueIcons(portfolioReturnsValue, "sign")}
                {Math.abs(portfolioReturnsValue * 100).toLocaleString(locale, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })}
                %)
              </div>
            ) : (
              <></>
            )}
            <div className="text-secondary">{ACTIVE_TENOR_TEXT_CONFIG[activeTenor]}</div>
            <div
              className={"cursor-pointer material-symbols-outlined "}
              style={{ fontSize: "16px", color: "#536AE3" }}
              onClick={(event) => {
                this._setShowPortfolioReturnInfoModal(true);
                event.stopPropagation();
              }}
            >
              info
            </div>
          </div>
        </div>
        {/* Portfolio return info modal */}
        <InfoModal
          title={"How is your portfolio’s return calculated?"}
          show={this.state.showPortfolioReturnInfoModal}
          handleClose={() => this._setShowPortfolioReturnInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <h1 />
          <p className={"text-muted"}>
            Your portfolio’s return is measured as the Money-Weighted Rate of Return (MWRR). MWRR is a % that shows
            you how much your portfolio has increased or decreased in value.
          </p>
          <h6 className={"mb-3 mt-4"}>How it works?</h6>
          <p className={"text-muted"}>
            In contrast to the time-weighted rate of return, MWRR takes into account when and how much you have
            invested over time.
          </p>
          <p className={"text-muted"}>
            Estimating the performance of your portfolio by looking at the total deposits and withdrawals you’ve
            made gives a more personalised and accurate figure of your investments’ performance.
          </p>
          <p className={"text-muted"}>
            For the finance geeks, MWRR is equivalent to the &ldquo;Internal Rate of Return (IRR)&rdquo;.
          </p>
        </InfoModal>
        {/* End of Portfolio return info modal */}
      </>
    );
  }
}

PortfolioValueAndReturn.contextType = GlobalContext;

export default PortfolioValueAndReturn;
