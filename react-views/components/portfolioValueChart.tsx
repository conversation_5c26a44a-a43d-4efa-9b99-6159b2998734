import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ScaleOptions
} from "chart.js";
import { Nav } from "react-bootstrap";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { PortfolioPriceDataPointType, PortfolioPricesByTenorType } from "../types/portfolio";
import { formatDateToDDMONYYHHMM, formatDateToDDMONYYYY } from "../utils/dateUtil";
import { TenorEnum, TenorsByOrder } from "../configs/durationConfig";

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

type PropsType = {
  portfolioPricesByTenor: PortfolioPricesByTenorType;
  onTenorChange: (activeTenor: TenorEnum) => void;
  activeTenor: TenorEnum;
  fixedTicks?: boolean;
};

class PortfolioValueChart extends React.Component<PropsType> {
  private _parseChartData(data: PortfolioPriceDataPointType[]): {
    dataValues: number[];
    labels: string[];
  } {
    const dataValues = data.map(({ value }) => value);
    const labels = data.map(({ timestamp, displayIntraday }) => {
      const date = new Date(timestamp);
      if (displayIntraday) {
        return formatDateToDDMONYYHHMM(date);
      } else {
        return formatDateToDDMONYYYY(date);
      }
    });

    return { dataValues, labels };
  }

  private _getOptions = () => {
    const { portfolioPricesByTenor, fixedTicks } = this.props;
    const { user } = this.context as GlobalContextType;

    const showSinglePoint = portfolioPricesByTenor["max"].data.length <= 1;

    const yAxisOptions: ScaleOptions = {
      display: true,
      beginAtZero: false,
      grid: {
        color: "rgba(236, 240, 241, 1)",
        drawBorder: false,
        borderDash: [3, 4],
        lineWidth: 2
      } as any,
      ticks: {
        color: "#000",
        padding: 20,
        font: {
          size: 16
        },
        maxTicksLimit: 6,
        callback: (value: number) => {
          let unit = "";
          if (value >= 1000 && value < 1000000) {
            unit = "k";
            value = value / 1000;
          } else if (value >= 1000000) {
            unit = "M";
            value = value / 1000000;
          }

          return `${value.toLocaleString("en-US", {
            style: "currency",
            currency: user.currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          })}${unit}`;
        }
      }
    };

    // Conditionally add min and max if fixedTicks is true
    if (fixedTicks) {
      yAxisOptions.min = 0;
      yAxisOptions.max = 800;
    }

    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        datalabels: {
          display: false
        },
        tooltip: {
          enabled: true,
          intersect: false,
          mode: "nearest" as const,
          bodySpacing: 5,
          padding: {
            top: 10,
            bottom: 10,
            left: 10,
            right: 10
          },
          caretPadding: 0,
          displayColors: false,
          backgroundColor: "#101327",
          titleColor: "#ffffff",
          cornerRadius: 4,
          callbacks: {
            label(context: any) {
              return new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: user.currency,
                minimumFractionDigits: 0
              }).format(context.raw as number);
            }
          }
        }
      },
      scales: {
        x: {
          offset: showSinglePoint,
          display: false,
          grid: {
            display: false
          },
          ticks: {
            display: true,
            color: "#000",
            font: {
              size: 16
            },
            padding: 0,
            maxTicksLimit: 2.1,
            autoSkip: true,
            maxRotation: 0,
            minRotation: 0
          }
        },
        y: yAxisOptions
      },
      hover: {
        mode: "index" as const,
        intersect: false
      },
      elements: {
        line: {
          tension: 0.1
        },
        point: {
          radius: showSinglePoint ? 10 : 0
        }
      },
      layout: {
        padding: {
          left: 0,
          right: 0,
          top: 20,
          bottom: 10
        }
      }
    };
  };

  private _handleTenorSelect = (eventKey: TenorEnum): void => {
    this.props.onTenorChange(eventKey);
  };

  render(): JSX.Element {
    const { activeTenor, portfolioPricesByTenor } = this.props;
    const { dataValues, labels } = this._parseChartData(portfolioPricesByTenor[activeTenor].data);

    const chartData = {
      labels,
      datasets: [
        {
          data: dataValues,
          borderColor: "#5d78ff",
          borderWidth: 2,
          pointBackgroundColor: "rgba(0, 0, 0, 0)",
          fill: false
        }
      ]
    };

    return (
      <>
        <div className="row w-100 m-0">
          <div className="col p-0" style={{ height: "390px" }}>
            <Line data={chartData} options={this._getOptions() as ChartOptions<"line">} />
          </div>
        </div>
        <div className="row">
          <div className="col-12">
            <Nav
              variant="pills"
              defaultActiveKey={activeTenor}
              className="justify-content-center"
              onSelect={this._handleTenorSelect}
            >
              {TenorsByOrder.map((tenor, index) => (
                <Nav.Item key={`tenor-nav-${index}`}>
                  <Nav.Link eventKey={tenor} className="p-2 mx-2 font-size-lg">
                    {tenor.toUpperCase()}
                  </Nav.Link>
                </Nav.Item>
              ))}
            </Nav>
          </div>
        </div>
      </>
    );
  }
}

PortfolioValueChart.contextType = GlobalContext;

export default PortfolioValueChart;
