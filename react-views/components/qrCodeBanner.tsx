import React from "react";

type PropsType = {
  show: boolean;
  handleClose: () => void;
};

class QRCodeBanner extends React.Component<PropsType> {
  render(): JSX.Element {
    const { handleClose, show } = this.props;

    return (
      <div
        className={`card border-radius-xl shadow-sm overflow-hidden max-w-300px z-max-level position-fixed bottom-0 start-0 left-0 ms-4 mb-4 ml-6 ${
          show ? "" : "d-none"
        }`}
      >
        <div className="card-body d-flex flex-column position-relative">
          <button
            type="button"
            className="bg-transparent border-0 align-self-end z-max-level position-absolute"
            onClick={handleClose}
          >
            <span className="close icon-primary" style={{ color: "#546BE5" }}>
              &times;
            </span>
          </button>
          <div className="d-flex flex-column">
            <img
              className="position-absolute right-0 top-0 end-0"
              src="/images/mobile-banner/astronaut.png"
              alt={"Astronaut"}
            />
            <img
              className="position-absolute bottom-0 end-0 left-0"
              src="/images/mobile-banner/blob.svg"
              alt={"Background"}
            />
            <div
              className="bg-white border-radius-xl d-flex align-items-center justify-content-center z-max-level"
              style={{ height: "151px", width: "151px" }}
            >
              <img
                src="/images/mobile-banner/qr_code.svg"
                style={{ height: "138px", width: "138px" }}
                alt={"https://wealthyhood.onelink.me/TwZO/b236dq9r"}
              />
            </div>
            <div className="pt-5 pb-4 pt-8 pb-6 mb-0 font-weight-bolder fw-bolder font-size-xxl">
              <span>Scan the QR code to get the</span> <span style={{ color: "#546BE5" }}>Wealthyhood app!</span>
            </div>
            <div className="font-size-xl" style={{ color: "#101327", opacity: "60%" }}>
              <p className="mb-1">Learn, save, invest and automate your wealth-building today!</p>
              <p className="mb-0">Capital at risk.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default QRCodeBanner;
