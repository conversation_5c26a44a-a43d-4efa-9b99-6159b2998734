import axios from "axios";
import React from "react";
import { captureException } from "@sentry/react";
import validator from "validator";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import SelectETF from "./selectETF";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast } from "../utils/eventService";

type PropsType = {};
type StateType = {
  referrerEmail: string;
  referralEmail: string;
  referrerETF: string;
  referralETF: string;
  referrerConsiderationAmount: string;
  referralConsiderationAmount: string;
};

type InputNameType =
  | "referrerEmail"
  | "referralEmail"
  | "referrerETF"
  | "referralETF"
  | "referrerConsiderationAmount"
  | "referralConsiderationAmount";
class RewardsReferralSubmision extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      referrerEmail: "",
      referralEmail: "",
      referrerETF: "",
      referralETF: "",
      referrerConsiderationAmount: "",
      referralConsiderationAmount: ""
    };
  }

  private _canSubmitRewards = (): boolean => {
    const {
      referrerEmail,
      referralEmail,
      referrerETF,
      referralETF,
      referrerConsiderationAmount,
      referralConsiderationAmount
    } = this.state;

    return Boolean(
      validator.isEmail(referrerEmail) &&
        validator.isEmail(referralEmail) &&
        referrerETF &&
        referralETF &&
        validator.isNumeric(referrerConsiderationAmount) &&
        validator.isNumeric(referralConsiderationAmount)
    );
  };

  private _handleInputChange =
    (inputName: InputNameType) =>
    (event: any): void => {
      const value = event?.target?.value || "";
      this.setState((prevState) => {
        const { ...newState } = prevState;
        newState[inputName] = value;
        return newState;
      });
    };

  private _submitRewards = async (): Promise<void> => {
    const {
      referrerEmail,
      referralEmail,
      referrerETF,
      referralETF,
      referrerConsiderationAmount,
      referralConsiderationAmount
    } = this.state;

    try {
      await axios({
        method: "post",
        url: "/admin/rewards",
        data: {
          referrerEmail,
          referralEmail,
          referrerETF,
          referralETF,
          referrerConsiderationAmount,
          referralConsiderationAmount
        }
      });
      emitToast({
        content: "Rewards created successfully!",
        toastType: ToastTypeEnum.success
      });
      window.location.href = "/admin/rewards";
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred.",
        toastType: ToastTypeEnum.error
      });
    }
  };
  render() {
    const { referrerEmail, referralEmail, referrerConsiderationAmount, referralConsiderationAmount } = this.state;

    return (
      <>
        <div className="row">
          {/* Referrer Form */}
          <div className="col-lg-6">
            <div className="card border-radius-xl shadow-sm">
              <div className="card-body">
                <h3 className="mb-10 font-weight-bolder text-dark">Referrer</h3>
                <form className="form">
                  <div className="row">
                    {/* Email Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Email <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="referrerEmail"
                          placeholder="Email"
                          value={referrerEmail}
                          onChange={this._handleInputChange("referrerEmail")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Email Input */}

                    {/* ETF Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          ETF <span className="text-primary">*</span>
                        </label>
                        <SelectETF
                          defaultValue={""}
                          name="referrerETF"
                          onChange={this._handleInputChange("referrerETF")}
                          required
                        />
                      </div>
                    </div>
                    {/* End ETF Input */}

                    {/* Last Name Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Consideration Amount <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="referrerConsiderationAmount"
                          placeholder="Amount"
                          value={referrerConsiderationAmount}
                          onChange={this._handleInputChange("referrerConsiderationAmount")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Last Name Input */}
                  </div>
                </form>
              </div>
            </div>
          </div>
          {/* End Referrer Form */}

          {/* Referral Form */}
          <div className="col-lg-6">
            <div className="card border-radius-xl shadow-sm">
              <div className="card-body">
                <h3 className="mb-10 font-weight-bolder text-dark">Referral</h3>
                <form className="form">
                  <div className="row">
                    {/* Email Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Email <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="referralEmail"
                          placeholder="Email"
                          value={referralEmail}
                          onChange={this._handleInputChange("referralEmail")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Email Input */}

                    {/* ETF Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          ETF <span className="text-primary">*</span>
                        </label>
                        <SelectETF
                          defaultValue={""}
                          name="referralETF"
                          onChange={this._handleInputChange("referralETF")}
                          required
                        />
                      </div>
                    </div>
                    {/* End ETF Input */}

                    {/* Last Name Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Consideration Amount <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="referralConsiderationAmount"
                          placeholder="Amount"
                          value={referralConsiderationAmount}
                          onChange={this._handleInputChange("referralConsiderationAmount")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Last Name Input */}
                  </div>
                </form>
              </div>
            </div>
          </div>
          {/* End Referral Form */}
        </div>

        {/* Submission Button */}
        <div className="row mt-6">
          <div className="col-lg-6">
            {this._canSubmitRewards() ? (
              <LoadingOnSubmitButton
                type="button"
                className="btn btn-lg btn-primary"
                customonclick={async () => this._submitRewards()}
              >
                Create Rewards
              </LoadingOnSubmitButton>
            ) : (
              <button type="button" className="btn btn-lg btn-primary" disabled>
                Some details missing
              </button>
            )}
          </div>
        </div>
        {/* End Submission Button */}
      </>
    );
  }
}

export default RewardsReferralSubmision;
