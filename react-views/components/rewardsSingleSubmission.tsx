import axios from "axios";
import React from "react";
import { captureException } from "@sentry/react";
import validator from "validator";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import SelectETF from "../components/selectETF";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast } from "../utils/eventService";

type PropsType = {};
type StateType = {
  targetUserEmail: string;
  asset: string;
  considerationAmount: string;
};

type InputNameType = "targetUserEmail" | "asset" | "considerationAmount";
class RewardsSingleSubmision extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      targetUserEmail: "",
      asset: "",
      considerationAmount: ""
    };
  }

  private _canSubmitRewards = (): boolean => {
    const {
      targetUserEmail: targetUserEmail,
      asset: asset,
      considerationAmount: considerationAmount
    } = this.state;

    return Boolean(validator.isEmail(targetUserEmail) && asset && validator.isNumeric(considerationAmount));
  };

  private _handleInputChange =
    (inputName: InputNameType) =>
    (event: any): void => {
      const value = event?.target?.value || "";
      this.setState((prevState) => {
        const { ...newState } = prevState;
        newState[inputName] = value;
        return newState;
      });
    };

  private _submitRewards = async (): Promise<void> => {
    const {
      targetUserEmail: targetUserEmail,
      asset: asset,
      considerationAmount: considerationAmount
    } = this.state;

    try {
      await axios({
        method: "post",
        url: "/admin/rewards/create-single",
        data: {
          targetUserEmail,
          asset,
          considerationAmount
        }
      });
      emitToast({
        content: "Rewards created successfully!",
        toastType: ToastTypeEnum.success
      });
      window.location.href = "/admin/rewards";
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred.",
        toastType: ToastTypeEnum.error
      });
    }
  };
  render() {
    const { targetUserEmail, considerationAmount } = this.state;

    return (
      <>
        <div className="row">
          {/* Receiver Form */}
          <div className="col-lg-6">
            <div className="card border-radius-xl shadow-sm">
              <div className="card-body">
                <h3 className="mb-10 font-weight-bolder text-dark">Receiver</h3>
                <form className="form">
                  <div className="row">
                    {/* Email Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Email <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="targetUserEmail"
                          placeholder="Email"
                          value={targetUserEmail}
                          onChange={this._handleInputChange("targetUserEmail")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Email Input */}

                    {/* ETF Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          ETF <span className="text-primary">*</span>
                        </label>
                        <SelectETF
                          defaultValue={""}
                          name="asset"
                          onChange={this._handleInputChange("asset")}
                          required
                        />
                      </div>
                    </div>
                    {/* End ETF Input */}

                    {/* Last Name Input */}
                    <div className="col-12">
                      <div className="form-group">
                        <label>
                          Consideration Amount <span className="text-primary">*</span>
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          name="considerationAmount"
                          placeholder="Amount"
                          value={considerationAmount}
                          onChange={this._handleInputChange("considerationAmount")}
                          required
                        />
                      </div>
                    </div>
                    {/* End Last Name Input */}
                  </div>
                </form>
              </div>
            </div>
          </div>
          {/* End Receiver Form */}
        </div>

        {/* Submission Button */}
        <div className="row mt-6">
          <div className="col-lg-6">
            {this._canSubmitRewards() ? (
              <LoadingOnSubmitButton
                type="button"
                className="btn btn-lg btn-primary"
                customonclick={async () => this._submitRewards()}
              >
                Create Rewards
              </LoadingOnSubmitButton>
            ) : (
              <button type="button" className="btn btn-lg btn-primary" disabled>
                Some details missing
              </button>
            )}
          </div>
        </div>
        {/* End Submission Button */}
      </>
    );
  }
}

export default RewardsSingleSubmision;
