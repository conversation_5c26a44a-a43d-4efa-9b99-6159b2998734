import React from "react";

type PropsType = {
  icon: string;
  label: string;
  popoverText?: string;
  onClick: () => void;
};

class LabSidebarButton extends React.Component<PropsType> {
  render(): JSX.Element {
    const { icon, label, popoverText, onClick } = this.props;

    return (
      <div
        className="card border-radius-lg shadow-xs bg-light bg-hover-secondary w-100 h-auto my-4"
        style={{ maxHeight: "8rem", maxWidth: "11rem" }}
        data-toggle="popover"
        data-content={popoverText}
        onClick={onClick}
      >
        <div className="card-body px-2 py-3">
          <div className="d-flex flex-row align-items-center">
            <img className="mr-2 mb-0" src={icon} style={{ width: "25px" }} />
            <h5 className="text-center mb-0">{label}</h5>
          </div>
        </div>
      </div>
    );
  }
}

export default LabSidebarButton;
