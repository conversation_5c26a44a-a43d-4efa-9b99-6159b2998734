import React from "react";

type PropsType = {
  risk: number;
  onRiskChanged: (risk: number) => void;
};

const BARS_NUM = 21;

class RiskBarSelection extends React.Component<PropsType> {
  private _getFilledBars(): number[] {
    const { risk } = this.props;

    const steps = [];
    for (let i = 1; i <= risk; i++) {
      steps.push(i);
    }

    return steps;
  }

  private _getEmptyBars(): number[] {
    const { risk } = this.props;

    const steps = [];
    for (let i = risk + 1; i <= BARS_NUM; i++) {
      steps.push(i);
    }

    return steps;
  }

  render(): JSX.Element {
    const { onRiskChanged } = this.props;

    return (
      <div className="signal-bars sizing-box">
        {this._getFilledBars().map((bar) => (
          <div
            key={`risk-bar-${bar}`}
            className={`bar-${bar} filled-bar bar cursor-pointer`}
            onClick={() => onRiskChanged(bar)}
          />
        ))}
        {this._getEmptyBars().map((bar) => (
          <div
            key={`risk-bar-${bar}`}
            className={`bar-${bar} bar cursor-pointer`}
            onClick={() => onRiskChanged(bar)}
          />
        ))}
      </div>
    );
  }
}

export default RiskBarSelection;
