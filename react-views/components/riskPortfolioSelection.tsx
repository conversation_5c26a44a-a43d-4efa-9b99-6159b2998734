import React from "react";
import Nouislider from "nouislider-react";
import RiskExplanationModal from "./modals/riskExplanationModal";

type PropsType = {
  risk: number;
  onRiskUpdateCb: (risk: number) => void;
};
type StateType = {
  displayModal: boolean;
  risk: number;
};
class RiskPortfolioSelection extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      displayModal: false,
      risk: props.risk
    };
  }

  private _setDisplayModalState = (displayModal: boolean): void => {
    this.setState({ displayModal });
  };

  private _onSliderChange = (values: number[]): void => {
    const risk = Number(values[0]);
    this.setState({ risk });
    this.props.onRiskUpdateCb(risk);
  };

  render(): JSX.Element {
    const { displayModal, risk } = this.state;

    return (
      <>
        <style
          dangerouslySetInnerHTML={{
            __html: `
              #risk-slider {
                width: calc(100% ) !important;
                order: 10;
                margin: 0;
                border: none;
                background-color: rgba(138, 141, 147, 0.3);
                margin-top: 2vw;
                border-radius: 2vw !important;
                height: 10px !important;
              }
              #risk-slider .noUi-connect {
                background-color: #5867dd;
                border-radius: 2vw;
                cursor: pointer;
              }
              #risk-slider .noUi-handle {
                top: -8px !important;
              }
            `
          }}
        />
        <div className="row mb-5">
          <div className="col-12 d-flex justify-content-center">
            <span
              className="text-center text-primary cursor-pointer"
              onClick={(): void => this._setDisplayModalState(true)}
            >
              Learn more about risk
            </span>
          </div>
        </div>
        <div className="row">
          <div className="col-12">
            <div className="pt-2 pb-5 pb-md-2 pb-lg-0">
              <span className="font-size-lg text-dark-50 float-left">Lower Risk/Reward</span>
              <span className="font-size-lg text-dark-50 float-right">Higher Risk/Reward</span>
            </div>
            <div className="w-100">
              <Nouislider
                id="risk-slider"
                className="risk slider"
                animate={false}
                range={{
                  min: 0,
                  max: 20
                }}
                start={risk}
                step={1}
                connect={[true, false]}
                onChange={this._onSliderChange}
              />
            </div>
            <div className="d-flex justify-content-between pt-4">
              <h5>Conservative</h5>
              <h5>Balanced</h5>
              <h5>Adventurous</h5>
            </div>
          </div>
        </div>
        <RiskExplanationModal show={displayModal} handleClose={(): void => this._setDisplayModalState(false)} />
      </>
    );
  }
}

export default RiskPortfolioSelection;
