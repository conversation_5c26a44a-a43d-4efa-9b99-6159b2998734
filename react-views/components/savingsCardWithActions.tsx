import React, { Component } from "react";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { UserSavingsItemType } from "../types/savings";
import { eventEmitter, EVENTS } from "../utils/eventService";
import InfoModal from "./modals/infoModal";
import { SavingsProductFeesModalViewMode } from "./modals/savingsProductFeesModal";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;

const BUTTONS = [
  {
    icon: "add",
    popoverText: "Add money",
    handleClick: (options: {
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      displaySavingsAmount: string;
      netInterestRate: string;
      savingsAmount: number;
    }) => eventEmitter.emit(EVENTS.savingsTopupModal, options.savingsProductId, options.netInterestRate)
  },
  {
    icon: "remove",
    popoverText: "Withdraw",
    handleClick: (options: {
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      displaySavingsAmount: string;
      netInterestRate: string;
      savingsAmount: number;
    }) =>
      eventEmitter.emit(
        EVENTS.savingsWithdrawalModal,
        options.savingsProductId,
        options.displaySavingsAmount,
        options.netInterestRate,
        options.savingsAmount
      )
  },
  {
    icon: "other_admission",
    popoverText: "Details",
    handleClick: (options: {
      savingsProductId: savingsUniverseConfig.SavingsProductType;
      displaySavingsAmount: string;
      netInterestRate: string;
      savingsAmount: number;
    }) =>
      eventEmitter.emit(
        EVENTS.savingsProductModal,
        options.savingsProductId,
        options.displaySavingsAmount,
        options.netInterestRate
      )
  }
];

type SavingsCardPropsType = UserSavingsItemType;

type StateType = {
  showInfoDialog: boolean;
  selectedInfoConfig?: savingsUniverseConfig.SavingsProductInfoSectionType;
};

export default class SavingsCardWithActions extends Component<SavingsCardPropsType, StateType> {
  constructor(props: SavingsCardPropsType) {
    super(props);
    this.state = { showInfoDialog: false };
  }

  private _setShowInfoDialog(
    showInfoDialog: boolean,
    selectedInfoConfig?: savingsUniverseConfig.SavingsProductInfoSectionType
  ) {
    this.setState({ showInfoDialog, selectedInfoConfig });
  }

  render() {
    const {
      netInterestRate,
      displayUnrealisedInterest,
      displaySavingsAmount,
      savingsProductId,
      savingsAmount,
      currency
    } = this.props;
    const { showInfoDialog, selectedInfoConfig } = this.state;

    return (
      <div className="d-flex flex-column">
        <div className="wh-account-card-small bg-white d-flex flex-column justify-content-center align-items-center shadow-sm px-4 py-5">
          <div className="d-flex align-items-center text-light text-nowrap mb-1">
            <p className="text-muted m-0">{`${currency} • Earn ${netInterestRate} interest`}</p>
            <span
              className="text-muted material-symbols-outlined ms-1 cursor-pointer"
              style={{ fontSize: "18px" }}
              onClick={() =>
                eventEmitter.emit(
                  EVENTS.savingsProductFeesModal,
                  savingsProductId,
                  SavingsProductFeesModalViewMode.EARN_INTEREST
                )
              }
            >
              info
            </span>
          </div>
          <h1 className="fw-600 text-black m-0">{displaySavingsAmount}</h1>
          {displayUnrealisedInterest && (
            <button
              className="btn d-flex savings-interest-incoming mt-2"
              onClick={() =>
                this._setShowInfoDialog(
                  true,
                  SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId].infoConfig.unrealisedInterest
                )
              }
            >
              <span>{displayUnrealisedInterest}</span>
              <span className="material-icons material-symbols-outlined ms-1">event</span>
            </button>
          )}
        </div>
        <div className="row m-0 w-100 mt-5 justify-content-center">
          {BUTTONS.map(({ icon, popoverText, handleClick }, index) => (
            <div
              key={`wealthyhood-cash-balance-${index}`}
              className="col p-0 text-center d-flex flex-column align-items-center"
            >
              <button
                className="d-flex align-items-center justify-content-center text-muted text-decoration-none text-start btn btn-light btn-small mx-4 account-card-btn"
                data-toggle="popover"
                data-content={popoverText}
                data-placement="bottom"
                onClick={() =>
                  handleClick({ savingsProductId, displaySavingsAmount, netInterestRate, savingsAmount })
                }
              >
                <div className="d-flex h-100 align-self-center justify-content-center">
                  <span
                    className="mx-auto material-symbols-outlined align-self-center px-3"
                    style={{ fontSize: "20px", color: "#000" }}
                  >
                    {icon}
                  </span>
                </div>
              </button>
              <div className="fw-bolder t-875 mt-2">{popoverText}</div>
            </div>
          ))}
        </div>

        <InfoModal
          title={selectedInfoConfig?.title}
          show={showInfoDialog}
          handleClose={() => this._setShowInfoDialog(false)}
        >
          {selectedInfoConfig?.paragraphs &&
            selectedInfoConfig.paragraphs.map((paragraph, index) => {
              return (
                <p className="text-muted" key={index}>
                  {paragraph}
                </p>
              );
            })}
        </InfoModal>
      </div>
    );
  }
}
