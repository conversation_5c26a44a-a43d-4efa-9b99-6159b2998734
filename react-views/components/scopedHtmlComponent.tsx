import React from "react";

type ScopedHtmlComponentProps = {
  htmlContent: string;
} & React.HTMLAttributes<HTMLDivElement>;

class ScopedHtmlComponent extends React.Component<ScopedHtmlComponentProps> {
  private containerRef: React.RefObject<HTMLDivElement>;

  constructor(props: ScopedHtmlComponentProps) {
    super(props);
    this.containerRef = React.createRef();
  }

  componentDidMount() {
    if (this.containerRef.current) {
      const shadowRoot = this.containerRef.current.attachShadow({ mode: "open" });
      let processedHtml = this.processHtml(this.props.htmlContent);
      shadowRoot.innerHTML = processedHtml;
    }
  }

  componentDidUpdate(prevProps: ScopedHtmlComponentProps) {
    if (prevProps.htmlContent !== this.props.htmlContent) {
      const shadowRoot = this.containerRef.current?.shadowRoot;

      if (shadowRoot) {
        let processedHtml = this.processHtml(this.props.htmlContent);
        shadowRoot.innerHTML = processedHtml;
      }
    }
  }

  processHtml(html: string): string {
    return html.replace(/<body([^>]*)>/i, "<div$1>").replace(/<\/body>/i, "</div>");
  }

  render() {
    const { htmlContent, ...divProps } = this.props;
    return <div ref={this.containerRef} {...divProps}></div>;
  }
}

export default ScopedHtmlComponent;
