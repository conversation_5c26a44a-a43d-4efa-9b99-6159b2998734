import React from "react";
import { AmlScreeningResultEnum } from "../../models/User";

type PropsType = {
  defaultValue: string;
  name: string;
};

class SelectAmlScreening extends React.Component<PropsType> {
  render(): JSX.Element {
    const { defaultValue, name } = this.props;
    const options: AmlScreeningResultEnum[] = Object.values(AmlScreeningResultEnum);

    return (
      <select defaultValue={defaultValue} name={name} className="form-control">
        <option value="">Select</option>
        {options.map((amlScreeningResult) => (
          <option value={amlScreeningResult} key={amlScreeningResult}>
            {amlScreeningResult}
          </option>
        ))}
      </select>
    );
  }
}

export default SelectAmlScreening;
