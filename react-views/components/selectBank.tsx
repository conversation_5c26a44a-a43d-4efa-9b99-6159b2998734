import React from "react";
import { ProviderType } from "../../services/truelayerService";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { captureException } from "@sentry/react";

type PropsType = {
  defaultValue: string;
  name: string;
  onSelectionChange: (provider: ProviderType) => void;
};

type StateType = {
  isLoadingProviders: boolean;
  banks: ProviderType[];
};

class SelectBank extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isLoadingProviders: true,
      banks: []
    };
  }

  componentDidMount() {
    this._getTruelayerProviders();
  }

  private _getTruelayerProviders = async (): Promise<void> => {
    try {
      const banks: ProviderType[] = (await axios.get("/investor/truelayer-providers")).data;

      this.setState(
        {
          banks
        },
        () => this.setState({ isLoadingProviders: false })
      );
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Could not fetch list of banks",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _onChange = (event: any): void => {
    const { banks } = this.state;
    const { onSelectionChange } = this.props;

    const provider = banks.find((provider) => provider.display_name === event.target.value);

    onSelectionChange(provider);
  };

  render(): JSX.Element {
    const { defaultValue, name } = this.props;
    const { isLoadingProviders, banks } = this.state;

    return (
      <>
        <div className="row m-0">
          <div className="col p-0">
            <label className="form-label">Your bank</label>
          </div>
        </div>
        {isLoadingProviders ? (
          <div className="row">
            <div className="d-flex justify-content-center">
              <span id="cover-spin-relative" />
            </div>
          </div>
        ) : (
          <div className="position-relative">
            <select
              onChange={this._onChange}
              defaultValue={defaultValue}
              name={name}
              className="verification-input"
              required
            >
              <option value="">Select bank</option>
              {banks.map(({ display_name }) => (
                <option value={display_name} key={`bank-${display_name}`}>
                  {display_name}
                </option>
              ))}
            </select>
            <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
              expand_more
            </span>
          </div>
        )}
      </>
    );
  }
}

export default SelectBank;
