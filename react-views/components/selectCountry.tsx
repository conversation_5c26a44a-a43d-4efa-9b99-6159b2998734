import Select from "react-select";
import React from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  defaultValue: string;
  name: string;
  isDisabled: boolean;
  onChange: (e: any) => void;
} & React.SelectHTMLAttributes<HTMLSelectElement>;

interface OptionType {
  label: string;
  value: countriesConfig.CountryCodesType;
}

type StateType = {
  selectedOption: OptionType | null;
};

class SelectCountry extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);

    const countryMatchingDefaultValue = countriesConfig.countries.find(
      (config) => config.code == (props.defaultValue as string)
    );
    const initialSelectedOption = countryMatchingDefaultValue
      ? {
          label: countryMatchingDefaultValue.name,
          value: countryMatchingDefaultValue.code
        }
      : null;

    this.state = {
      selectedOption: initialSelectedOption
    };
  }

  handleChange = (selectedOption: OptionType) => {
    const { onChange } = this.props;
    this.setState({ selectedOption });
    const event = {
      target: {
        value: selectedOption.value
      }
    };
    onChange(event);
  };

  render(): JSX.Element {
    const { selectedOption } = this.state;
    const options = countriesConfig.countries.map(({ code, name }) => ({
      value: code,
      label: name
    }));

    const customStyles = {
      control: (styles: any) => ({ ...styles, backgroundColor: "none", borderStyle: "none", boxShadow: "none" })
    };

    return (
      <div className="verification-input" style={{ padding: "10px" }}>
        <Select
          isDisabled={this.props.isDisabled}
          required
          instanceId={this.props.name}
          id={this.props.name}
          options={options}
          value={selectedOption}
          onChange={this.handleChange}
          styles={customStyles}
          menuPlacement="auto"
          minMenuHeight={300}
        />
      </div>
    );
  }
}

export default SelectCountry;
