import React from "react";
import { taxResidencyConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  defaultValue: string;
  name: string;
};

class SelectDocumentType extends React.Component<PropsType> {
  render(): JSX.Element {
    const { defaultValue, name } = this.props;

    return (
      <select defaultValue={defaultValue} name={name} className="form-control">
        <option value="">Select</option>
        {taxResidencyConfig.IdentifierArray.map((identifier) => (
          <option value={identifier} key={`country-code-${identifier}`}>
            {identifier}
          </option>
        ))}
      </select>
    );
  }
}

export default SelectDocumentType;
