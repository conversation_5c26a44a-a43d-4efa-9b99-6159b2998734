import React, { Component } from "react";
import Select, { OptionProps, components, ValueContainerProps } from "react-select";

interface OptionType {
  label: string;
  value: string;
}

const { Option, ValueContainer } = components;

const CustomValueContainer = (props: ValueContainerProps) => {
  const values = props.getValue() as OptionType[];
  let label = "";
  const CHAR_LIM = 45;

  if (values.length > 0) {
    label = values.map((item) => item.label).join(", ");
    if (label.length > CHAR_LIM) {
      label = label.substring(0, CHAR_LIM) + "..."; // Truncate to fit the design
    }
  }

  /**
   * This filtering removes options that appear as badges from "react-select"
   * Instead of these badges, we display a span with the conatenated options
   */
  const filteredChildren = Object.values(props.children).filter((el) => !Array.isArray(el));

  return (
    <ValueContainer {...props}>
      {label}
      {filteredChildren}
    </ValueContainer>
  );
};

const CustomOption = (props: OptionProps<OptionType>) => {
  return (
    <Option className={props.isSelected ? "custom-option-selected" : "custom-option"} {...props}>
      {props.label}
    </Option>
  );
};

type PropsType = {
  options: OptionType[];
  handleChange: (value: string[]) => void;
};

export default class SelectSourceOfWealth extends Component<PropsType> {
  render(): JSX.Element {
    const { options, handleChange } = this.props;

    const colourStyles = {
      control: (styles: any) => ({ ...styles, backgroundColor: "none", borderStyle: "none", boxShadow: "none" }),
      dropdownIndicator: (styles: any) => ({
        ...styles,
        cursor: "pointer",
        svg: {
          fill: "#536AE3"
        }
      }),
      indicatorSeparator: (styles: any) => ({ display: "none" }),
      clearIndicator: (styles: any) => ({ ...styles, cursor: "pointer" }),
      option: (styles: any, { data, isDisabled, isFocused, isSelected }: any) => {
        return {
          ...styles,
          margin: "5px",
          backgroundColor: isSelected ? "#F1F3FD" : styles.backgroundColor,
          color: "#11152E",
          width: "auto"
        };
      }
    };

    return (
      <div className="verification-input" style={{ padding: "10px 5px" }}>
        <Select
          isMulti
          closeMenuOnSelect={false}
          hideSelectedOptions={false}
          placeholder="Select one or more"
          components={{ ValueContainer: CustomValueContainer, Option: CustomOption }}
          name="color"
          options={options}
          styles={colourStyles}
          onChange={(e: any) => handleChange(e.map(({ value }: any) => value))}
        />
      </div>
    );
  }
}
