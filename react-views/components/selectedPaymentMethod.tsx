import { FrequencySetting, PaymentMethod } from "../types/modal";
import React from "react";
import { BankProviderType, LinkedBankAccount } from "../types/bank";
import { formatCurrency } from "../utils/currencyUtil";
import { GiftDocument } from "../../models/Gift";
import Decimal from "decimal.js";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { isAllowedOneStepInvest } from "../utils/userUtil";

type PropsType = {
  selectedBank?: BankProviderType;
  selectedLinkedBankAccount?: LinkedBankAccount;
  paymentMethod: PaymentMethod;
  availableCash?: number;
  onClick?: () => void;
  gift?: GiftDocument;
  frequency: FrequencySetting;
};

export class SelectedPaymentMethod extends React.Component<PropsType> {
  private _userHasMultipleChoices() {
    const { availableCash, gift, frequency, paymentMethod } = this.props;
    const { user } = this.context as GlobalContextType;

    if (isAllowedOneStepInvest(user) || paymentMethod === "BANK_ACCOUNT_TOP_UP" || frequency !== "ONE_TIME") {
      return true;
    }

    return availableCash && gift;
  }

  render(): JSX.Element {
    const { paymentMethod, selectedLinkedBankAccount, availableCash, onClick, gift, selectedBank } = this.props;
    const { user, locale } = this.context as GlobalContextType;

    return (
      <>
        {/* Selected Payment Method Field */}
        <div
          className={`py-2 d-flex m-0 payment-method-select outline-hover mx-auto ${
            this._userHasMultipleChoices() ? "cursor-pointer" : ""
          }`}
          onClick={this._userHasMultipleChoices() ? onClick : undefined}
        >
          <div className="ps-2 align-self-center text-center">
            {/* Provider Icon */}
            {selectedLinkedBankAccount && paymentMethod == "BANK_ACCOUNT_TOP_UP" ? (
              <img
                className="h-100 align-self-center"
                style={{ maxHeight: "44px", maxWidth: "44px" }}
                src={selectedLinkedBankAccount.bankIconURL}
                alt={""}
              />
            ) : selectedBank && paymentMethod === "EASY_BANK_TOP_UP" ? (
              <img
                className="h-100 align-self-center"
                style={{ maxHeight: "44px", maxWidth: "44px" }}
                src={selectedBank.logo}
                alt={""}
              />
            ) : (
              <img className="h-100" src={"/images/icons/logo-circular.svg"} alt={""} />
            )}
            {/* End Provider Icon */}
          </div>
          <div className="px-3 fw-light align-self-center">
            {/* Account Details */}
            <div className="d-flex flex-column">
              {paymentMethod == "CASH" ? (
                <>
                  <span className="text-truncate t-875">
                    Cash balance · {formatCurrency(availableCash, user.currency, locale)}
                  </span>
                </>
              ) : paymentMethod === "BANK_ACCOUNT_TOP_UP" ? (
                <span className="text-truncate t-875">
                  {selectedLinkedBankAccount.displayBankName} ·{" "}
                  {selectedLinkedBankAccount.displayAccountIdentifier}
                </span>
              ) : paymentMethod === "EASY_BANK_TOP_UP" ? (
                <span className="text-truncate t-875">{selectedBank.name}</span>
              ) : (
                <span className="text-truncate t-875">
                  Redeem my{" "}
                  {formatCurrency(
                    Decimal.div(gift.consideration.amount, 100).toNumber(),
                    gift.consideration.currency,
                    locale,
                    0,
                    0
                  )}{" "}
                  gift
                </span>
              )}
            </div>
            {/* End Account Details */}
          </div>
          {this._userHasMultipleChoices() && (
            <div className={"pe-3 align-self-center text-center cursor-pointer"}>
              <i className="material-symbols-outlined align-self-center" style={{ fontSize: "14px" }}>
                keyboard_arrow_down
              </i>
            </div>
          )}
        </div>
        {/* Selected Payment Method Field */}
      </>
    );
  }
}

SelectedPaymentMethod.contextType = GlobalContext;

export default SelectedPaymentMethod;
