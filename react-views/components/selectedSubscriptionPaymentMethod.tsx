import React from "react";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import { UserDocument } from "../../models/User";

type PropsType = {
  user: UserDocument;
  selectedPaymentMethod: PaymentMethodDocument;
  onClick: () => void;
};

export class SelectedSubscriptionPaymentMethod extends React.Component<PropsType> {
  private _getSelectedPaymentMethodIcon(): JSX.Element {
    const { selectedPaymentMethod } = this.props;

    if (selectedPaymentMethod.wallet) {
      return (
        <img
          className="h-100 align-self-center"
          style={{ maxHeight: "60px" }}
          src={`/images/card-brands/${selectedPaymentMethod.wallet}.png`}
        />
      );
    } else if (selectedPaymentMethod) {
      return (
        <img
          className="h-100 align-self-center"
          style={{ maxHeight: "60px", borderRadius: "6px" }}
          src={`/images/card-brands/${selectedPaymentMethod.brand}.png`}
        />
      );
    } else return <img className="h-100" src={"/images/icons/logo-dark.svg"} />;
  }

  render(): JSX.Element {
    const { selectedPaymentMethod, onClick, user } = this.props;

    return (
      <>
        {/* Selected Payment Method Field */}
        <div className="row m-0 wh-account-card-option outline-hover" onClick={onClick}>
          <div className="col-2 p-0 align-self-center text-center">{this._getSelectedPaymentMethodIcon()}</div>
          <div className="col-9 p-0 align-self-center">
            <span className="fw-bold text-truncate">{`${user.firstName} ${user.lastName}`}</span>
            <div className="text-muted fw-bold">
              <span className="me-2">••••</span>
              <span>{selectedPaymentMethod.lastFourDigits}</span>
            </div>
          </div>
          <div className="col-1 p-0 align-self-center text-center">
            <i className="material-symbols-outlined icon-primary align-self-center" style={{ fontSize: "20px" }}>
              chevron_right
            </i>
          </div>
        </div>
        {/* Selected Payment Method Field */}
      </>
    );
  }
}

export default SelectedSubscriptionPaymentMethod;
