import React from "react";
import { formatDateToDayDashHHMM } from "../utils/dateUtil";
import { ExecutionWindowType } from "../../models/Transaction";
import HoverableInfoIcon from "./hoverableInfoIcon";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";

type StocksExecutionWindowPreviewRowProps = {
  executionWindow: ExecutionWindowType;
  showBottomBorder: boolean;
};

class StocksExecutionWindowPreviewRow extends React.Component<StocksExecutionWindowPreviewRowProps> {
  private _getFormattedExecutionWindow(executionWindow: ExecutionWindowType): string {
    if (executionWindow.executionType === "MARKET_HOURS") {
      return formatDateToDayDashHHMM(new Date(executionWindow.start));
    } else {
      return "Instant";
    }
  }

  private _getHoverText(): string {
    const { user } = this.context as GlobalContextType;

    if (user.isRealtimeETFExecutionEnabled) {
      return "Stock orders placed during US market hours (9:30 AM - 4:00 PM ET) are executed instantly. Orders placed outside market hours are executed at the next market opening.\n\nAll stock orders enjoy ZERO COMMISSIONS.";
    }

    return "Orders for individual stocks placed within US market hours, will be executed instantly. If an order is placed outside market hours, it will be executed at the opening of the next market session. The order execution price at the market opening may be different from the last market close due to changes in the market.";
  }

  render(): JSX.Element {
    const { showBottomBorder, executionWindow } = this.props;

    return (
      <>
        <div className={`row pb-3 mb-3 ${showBottomBorder ? "border-bottom" : ""}`}>
          <div className="col text-start">
            <div className="d-flex w-100">
              <p className="m-0 align-self-center text-nowrap me-2">Stocks execution time</p>
              <HoverableInfoIcon hoverText={this._getHoverText()} colorHex={"#536AE3"} />
            </div>
          </div>
          <div className="col text-end">
            <span className="fw-bolder text-nowrap">{this._getFormattedExecutionWindow(executionWindow)}</span>
          </div>
        </div>
      </>
    );
  }
}

StocksExecutionWindowPreviewRow.contextType = GlobalContext;

export default StocksExecutionWindowPreviewRow;
