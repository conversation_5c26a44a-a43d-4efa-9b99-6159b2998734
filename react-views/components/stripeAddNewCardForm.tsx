import axios from "axios";
import React from "react";
import { PaymentElement } from "@stripe/react-stripe-js";
import { PagePropsType } from "../types/page";
import { Stripe, StripeElements } from "@stripe/stripe-js";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { plansConfig } from "@wealthyhood/shared-configs";

export type PropsType = {
  stripe: Stripe;
  elements: StripeElements;
  selectedPrice?: plansConfig.PriceType;
  source: "select-plan" | "change-plan" | "billing";
} & PagePropsType;

class StripeAddNewCardForm extends React.Component<PropsType> {
  private _handleSubmit = async () => {
    const { stripe, elements, selectedPrice, source } = this.props;

    if (elements == null || !stripe) {
      return;
    }

    const { error: submitError } = await elements.submit();
    if (submitError) {
      emitToast({
        content: submitError.message,
        toastType: ToastTypeEnum.error
      });

      return;
    }

    // Create the PaymentIntent and obtain clientSecret
    const res = await axios.post("/investor/payment-methods/initiate-stripe");

    const { error } = await stripe.confirmSetup({
      elements,
      clientSecret: res.data.clientSecret,
      confirmParams: {
        return_url: `${process.env.DOMAIN_URL}/investor/stripe-add-payment-method-callback?source=${source}&selectedPrice=${selectedPrice}&initialShowCardPaymentModal=true`
      }
    });

    if (error) {
      emitToast({
        content: error.message,
        toastType: ToastTypeEnum.error
      });
    }
  };

  render() {
    const { stripe, elements } = this.props;

    return (
      <>
        <PaymentElement options={{ terms: { card: "never" } }} />
        <LoadingOnSubmitButton
          disabled={!stripe || !elements}
          enableOnCompletion={true}
          customonclick={async () => this._handleSubmit()}
          type="button"
          className="btn btn-primary fw-100 mt-4"
        >
          Add card
        </LoadingOnSubmitButton>
      </>
    );
  }
}

export default StripeAddNewCardForm;
