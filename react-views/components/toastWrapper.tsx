import React from "react";
import { EventToastBodyType, EventToastType } from "../types/event";
import { eventEmitter, EVENTS } from "../utils/eventService";
import CustomToast from "./customToast";
import { nanoid } from "nanoid";

type StateType = {
  toastList: EventToastType[];
};

// eslint-disable-next-line @typescript-eslint/ban-types
type PropsType = {};

class ToastWrapper extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      toastList: []
    };
  }

  private _removeToast = (toastId: string): void => {
    this.setState((prevState) => {
      const newToastList = [...prevState.toastList];
      return { toastList: newToastList.filter((toast) => toast.id != toastId) };
    });
  };

  componentDidMount(): void {
    eventEmitter.on(EVENTS.toast, (toast: EventToastBodyType) => {
      const toastId = nanoid();
      this.setState(
        (prevState) => {
          const newToastList = [...prevState.toastList];
          newToastList.push({ id: toastId, ...toast });
          return { toastList: newToastList };
        },
        () => {
          ((toastId: string): void => {
            setTimeout((): void => this._removeToast(toastId), toast.delay);
          })(toastId);
        }
      );
    });
  }

  private _onToastClose = (currentToast: EventToastType): void => {
    this._removeToast(currentToast.id);
  };

  render(): JSX.Element {
    const { toastList } = this.state;
    const showToasts = toastList.length > 0;

    return (
      <>
        {showToasts && (
          <div className="d-flex flex-column col-8 col-md-5 col-lg-4 col-xl-3 fixed-top align-items-stretch  ms-auto pt-5 pe-4 z-max-level">
            {toastList.map((toast) => (
              <CustomToast
                delay={toast.delay}
                key={toast.id}
                content={toast.content}
                toastType={toast.toastType}
                show
                onClose={(): void => {
                  this._onToastClose(toast);
                }}
              />
            ))}
          </div>
        )}
      </>
    );
  }
}

export default ToastWrapper;
