import React, { MouseEvent } from "react";

type PropsType = {
  checked?: boolean;
  disabled?: boolean;
  customonclick?: (event: MouseEvent) => Promise<void>;
  className?: string;
};

class ToggleSwitch extends React.Component<PropsType> {
  render(): JSX.Element {
    const { customonclick, disabled, className, checked } = this.props;

    return (
      <div
        className={`toggle-switch ${className || ""} ${disabled ? "disabled" : ""} ${checked ? "checked" : ""}`}
        onClick={(event) => !disabled && customonclick?.(event)}
        role="switch"
        aria-checked={checked}
        aria-disabled={disabled}
      >
        <div className="toggle-track" />
        <div className="toggle-handle">
          <span
            style={{ fontSize: "14px", opacity: checked ? 1 : 0 }}
            className={"toggle-icon material-symbols-outlined"}
          >
            check
          </span>
        </div>
      </div>
    );
  }
}

export default ToggleSwitch;
