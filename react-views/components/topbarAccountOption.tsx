import React from "react";
import { OverlayTrigger, Popover } from "react-bootstrap";
import InvestorMoreActions from "./investorMoreActions";
import { plansConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { PLAN_ICON_IMAGE_CONFIG } from "../configs/plansConfig";

type PropsType = { activePage: string };

type StateType = { showMenu: boolean };

class TopbarAccountOption extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      showMenu: false
    };
  }

  private _getPlanName(): string {
    const { user } = this.context as GlobalContextType;

    const { price } = user.subscription;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    return PLAN_CONFIG[PRICE_CONFIG[price].plan].name;
  }

  private _getPlanConfig(): plansConfig.PlanConfigType | null {
    const { user } = this.context as GlobalContextType;

    // user maybe hasn't subscription yet.
    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    const plan = PRICE_CONFIG[user.subscription?.price]?.plan;
    return PLAN_CONFIG[plan];
  }

  render(): JSX.Element {
    const { activePage } = this.props;
    const { showMenu } = this.state;

    const { user } = this.context as GlobalContextType;

    const config = this._getPlanConfig();

    const { firstName, subscription } = user;
    const defaultColor = "#101327";
    const defaultColorLight = "#F3F3F4";
    const color = config?.color;
    const colorLight = config?.colorLight;
    const plan = config?.keyName ?? "free";

    return (
      <div className="d-flex justify-content-start p-0">
        <div className={"position-relative me-2"}>
          <img alt="icon" src={PLAN_ICON_IMAGE_CONFIG[plan]} style={{ width: "48px", height: "48px" }} />
        </div>
        <div className="d-flex flex-column justify-content-center ms-1 me-3">
          <div className="fw-bolder">Hi {firstName ? firstName : "there!"}</div>
          {subscription && (
            <div
              className="wh-primary-label fw-bold d-inline-block t-875"
              style={{
                backgroundColor: colorLight ?? defaultColorLight,
                color: color ?? defaultColor,
                width: "fit-content"
              }}
            >
              {this._getPlanName()}
            </div>
          )}
        </div>
        <div className="align-self-center">
          <OverlayTrigger
            placement="bottom"
            rootClose={true}
            trigger="click"
            show={showMenu}
            onToggle={(show) => this.setState({ showMenu: show })}
            overlay={
              <Popover id="popover-explanation" className="mt-4 wh-card-body bg-white p-3">
                <Popover.Content>
                  <InvestorMoreActions
                    activePage={activePage}
                    onOptionClick={() => this.setState({ showMenu: false })}
                  />
                </Popover.Content>
              </Popover>
            }
          >
            <i className="material-symbols-outlined cursor-pointer align-self-center text-decoration-none">
              arrow_drop_down
            </i>
          </OverlayTrigger>
        </div>
      </div>
    );
  }
}

TopbarAccountOption.contextType = GlobalContext;

export default TopbarAccountOption;
