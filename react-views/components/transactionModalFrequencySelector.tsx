import React from "react";
import { GlobalContext } from "../contexts/globalContext";
import { FREQUENCY_CONFIG } from "../configs/frequencyConfig";
import DayOfMonthSelectionDropdown from "./modals/dayOfMonthSelectionDropdown";
import { FrequencySetting, PaymentMethod } from "../types/modal";
import { AllocationMethodEnum } from "./transactionModalTargetAllocationSelector";
import { ALLOCATION_METHOD_CONFIG } from "../configs/allocationMethodConfig";

export type PropsType = {
  frequency: FrequencySetting;
  paymentMethod: PaymentMethod;
  selectedDayOfMonth: number;
  handleClick: () => void;
  handleSelectDayOfMonth: (selectedDayOfMonth: number) => void;
};

class TransactionModalFrequencySelector extends React.Component<PropsType> {
  render(): JSX.Element {
    const { frequency, paymentMethod, selectedDayOfMonth, handleSelectDayOfMonth, handleClick } = this.props;

    if (paymentMethod === "GIFT") {
      return <></>;
    }

    return (
      <>
        {/* Frequency & Day of Month Settings */}
        <div className="d-flex justify-content-center">
          <div className={"rounded-pill p-2 d-flex"}>
            <div className="p-0 align-self-center">
              <i
                className={
                  "pe-2 d-flex material-symbols-outlined justify-content-center align-self-center t-875 text-primary"
                }
                style={{ fontSize: "12px" }}
              >
                {FREQUENCY_CONFIG[frequency].iconKey}
              </i>
            </div>
            <div className="align-self-center">
              <div className="fw-light t-875 text-primary me-2 d-flex" onClick={handleClick}>
                <span>{FREQUENCY_CONFIG[frequency].nameDisplay}</span>
              </div>
            </div>
          </div>

          {frequency !== "ONE_TIME" ? (
            <DayOfMonthSelectionDropdown
              handleOnDayOfMonthChange={handleSelectDayOfMonth}
              selectedDayOfMonth={selectedDayOfMonth}
              includeFrequencyKeyword={false}
            />
          ) : (
            <></>
          )}
        </div>
        {/* End Frequency & Day of Month Settings */}
      </>
    );
  }
}

TransactionModalFrequencySelector.contextType = GlobalContext;

export default TransactionModalFrequencySelector;
