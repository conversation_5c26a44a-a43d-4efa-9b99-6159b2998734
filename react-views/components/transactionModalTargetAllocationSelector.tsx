import React from "react";
import { ALLOCATION_METHOD_CONFIG } from "../configs/allocationMethodConfig";

export enum AllocationMethodEnum {
  HOLDINGS = "HOLDINGS",
  TARGET_ALLOCATION = "TARGET_ALLOCATION"
}

type PropsType = {
  allocationMethod: AllocationMethodEnum;
  onClick: () => void;
};

class TransactionModalTargetAllocationSelector extends React.Component<PropsType> {
  render(): JSX.Element {
    const { allocationMethod, onClick } = this.props;

    return (
      <div className="p-0 mb-3 mt-4 mx-auto text-primary fw-light cursor-pointer" onClick={onClick}>
        <div className="d-flex h-100 flex-row align-self-center">
          <div className={"align-self-center text-center"}>
            <i className="pe-2 material-symbols-outlined align-self-center" style={{ fontSize: "14px" }}>
              {ALLOCATION_METHOD_CONFIG[allocationMethod].iconKey}
            </i>
          </div>
          <div className="align-self-center">{ALLOCATION_METHOD_CONFIG[allocationMethod].nameDisplay}</div>
          <div className={"ps-2 pe-3 align-self-center text-center"}>
            <i className="material-symbols-outlined align-self-center" style={{ fontSize: "14px" }}>
              keyboard_arrow_down
            </i>
          </div>
        </div>
      </div>
    );
  }
}

export default TransactionModalTargetAllocationSelector;
