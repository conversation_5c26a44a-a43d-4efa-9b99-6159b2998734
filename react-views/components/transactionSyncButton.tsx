import React from "react";
import axios from "axios";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSync } from "@fortawesome/free-solid-svg-icons";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast } from "../utils/eventService";

type PropsType = {
  transactionId: string;
};
type StateType = {
  isSyncing: boolean;
};

class TransactionSyncButton extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isSyncing: false
    };
  }

  private _onSyncClick = (event: React.MouseEvent): void => {
    event.preventDefault();
    this.setState({ isSyncing: true }, async () => {
      let content: string;
      let toastType: ToastTypeEnum;

      // Make axios request to initiate WK transaction sync
      const { transactionId } = this.props;
      try {
        await axios.post(`/admin/transactions/${transactionId}/sync`);
        // If we reach this point we have a 2xx http status so display success
        content = "Transaction synced successfully!";
        toastType = ToastTypeEnum.success;
      } catch (err) {
        if (err.response) {
          // Received response but not in the 2xx range -> Toast will display specific server abort message
          content = err.response.data;
        } else {
          content = "An error occurred when syncing the transaction";
        }
        toastType = ToastTypeEnum.error;
      }
      emitToast({ content, toastType });
      this.setState({ isSyncing: false });
      window.location.reload();
    });
  };

  render(): JSX.Element {
    const { isSyncing } = this.state;

    return (
      <button
        type="button"
        className="btn btn-icon btn-white btn-sm text-primary shadow-effect-sm mr-3"
        onClick={this._onSyncClick}
      >
        {isSyncing ? (
          <span className="spinner-border spinner-border-sm"></span>
        ) : (
          <FontAwesomeIcon icon={faSync} className="fas fa-sync" />
        )}
      </button>
    );
  }
}

export default TransactionSyncButton;
