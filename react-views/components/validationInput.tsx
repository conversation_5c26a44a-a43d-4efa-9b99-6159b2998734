import React from "react";

type PropsType = {
  isValid: (value: any) => boolean;
  onChange: (value: any) => void;
  value: any;
  placeholder: string;
  errorMessage: string;
  required: boolean;
  showError?: boolean;
};

class ValidationInput extends React.Component<PropsType> {
  static defaultProps = {
    showError: true
  };

  render(): JSX.Element {
    const { isValid, onChange, value, placeholder, errorMessage, required, showError } = this.props;

    return (
      <div className="d-flex flex-column">
        <div className="position-relative">
          <input
            className={
              !isValid(value) && showError
                ? "verification-error-input bg-light-danger text-danger"
                : "verification-input"
            }
            type="text"
            placeholder={placeholder}
            value={value}
            onChange={(event: any): void => {
              const newValue = event.target.value;
              onChange(newValue);
            }}
            required={required}
          />
          {!isValid(value) && showError && (
            <span className="material-icons text-danger position-absolute top-50 end-0 translate-middle-y me-3">
              warning_amber
            </span>
          )}
        </div>
        {!isValid(value) && showError && (
          <div className="mt-3">
            <span className="d-block text-danger text-center">{errorMessage}</span>
          </div>
        )}
      </div>
    );
  }
}

export default ValidationInput;
