import React from "react";
import axios, { AxiosResponse } from "axios";
import StepWizard, { StepWizardChildProps } from "react-step-wizard";
import { captureException } from "@sentry/react";
import VerificationWizardStepAddress from "./verificationWizardStepAddress";
import VerificationWizardStepPassport from "./verificationWizardStepPassport";
import VerificationWizardStepTaxResidency from "./verificationWizardStepTaxResidency";
import { animatedTransitions } from "../configs/personalDetailsWizardConfig";
import { dateFriendlyFormatToISO, dateIsValid, isoDateToFriendlyFormat, isOverEighteen } from "../utils/dateUtil";
import { AddressInterface } from "../../models/Address";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { isTaxIdentifierValid } from "../utils/validationUtil";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import ConfigurableWizardActions from "./configurableWizardActions";
import VerificationWizardStepEmploymentInfo from "./verificationWizardStepEmploymentInfo";
import { EmploymentInfoConfiguration, EmploymentInfoType } from "./../types/employmentConfig";
import ConfigUtil from "../../utils/configUtil";

type TaxResidencyInputType = "value";
type PassportInputType = "firstName" | "lastName" | "dateOfBirth" | "nationality";
type AddressInputType = "addressLine1" | "addressLine2" | "postCode" | "city" | "country";
type EmploymentInfoInputType = "incomeRangeId" | "employmentStatus" | "sourcesOfWealth" | "industry";

type StepProceedStatus = {
  canProceedNext: boolean;
  reason: string;
};

type PropsType = {
  address: AddressInterface;
  taxResidencyProofValue: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  residencyCountry: countriesConfig.CountryCodesType;
  nationality: countriesConfig.CountryCodesType | "";
  onStepChange: (step: number) => void;
  employmentConfig: EmploymentInfoConfiguration;
  employmentInfo?: EmploymentInfoType;
};

type StateType = {
  currentStep: number;
  wizardInstance: StepWizardChildProps;
  taxResidency: {
    value: string;
    skipped: boolean;
  };
  passport: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    nationality: countriesConfig.CountryCodesType | "";
  };
  address: {
    addressLine1: string;
    addressLine2: string;
    postCode: string;
    city: string;
    country: string;
  };
  employmentInfo: {
    incomeRangeId: string;
    sourcesOfWealth: string[];
    employmentStatus: string;
    industry?: string;
  };
};

class VerificationWizard extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    const { address, employmentInfo } = this.props;

    this.state = {
      currentStep: 1,
      wizardInstance: null,
      taxResidency: {
        value: props.taxResidencyProofValue || "",
        skipped: false
      },
      passport: {
        firstName: props.firstName || "",
        lastName: props.lastName || "",
        dateOfBirth: props.dateOfBirth ? isoDateToFriendlyFormat(props.dateOfBirth) : "",
        nationality: props.nationality || ""
      },
      address: {
        addressLine1: address ? address.line1 : "",
        addressLine2: address ? address.line2 : "",
        postCode: address ? address.postalCode : "",
        city: address ? address.city : "",
        country: props.residencyCountry
      },
      employmentInfo: {
        incomeRangeId: employmentInfo?.incomeRangeId || "",
        employmentStatus: employmentInfo?.employmentStatus || "",
        sourcesOfWealth: employmentInfo?.sourcesOfWealth || [],
        industry: employmentInfo?.industry || ""
      }
    };
  }

  private _getStepProceedStatus = (
    stepName: "address" | "passport" | "taxResidency" | "employmentInfo"
  ): StepProceedStatus => {
    const STEP_ALLOWED_CONFIG = {
      address: (): StepProceedStatus => {
        const addressInfo = this.state.address;
        const { addressLine1, postCode, city, country } = addressInfo;

        return {
          canProceedNext: Boolean(addressLine1 && postCode && city && country),
          reason: "Required info is missing"
        };
      },
      passport: (): StepProceedStatus => {
        const passportInfo = this.state.passport;
        const { firstName, lastName, dateOfBirth, nationality } = passportInfo;

        if (nationality == "US") {
          return {
            canProceedNext: false,
            reason: "Not available to US investors"
          };
        }

        return {
          canProceedNext: Boolean(
            firstName &&
              lastName &&
              dateOfBirth &&
              nationality &&
              dateIsValid(new Date(dateFriendlyFormatToISO(dateOfBirth))) &&
              isOverEighteen(new Date(dateFriendlyFormatToISO(dateOfBirth)))
          ),
          reason: "Required info is missing"
        };
      },
      taxResidency: (): StepProceedStatus => {
        const { taxResidency } = this.state;
        const { residencyCountry } = this.props;
        const { inputError, required } = ConfigUtil.getTaxResidencyConfig(residencyCountry);

        const isValid = Boolean(isTaxIdentifierValid(residencyCountry, taxResidency.value));

        return {
          canProceedNext: required ? isValid : taxResidency.skipped || isValid,
          reason: inputError
        };
      },
      employmentInfo: (): StepProceedStatus => {
        const { employmentConfig } = this.props;
        const { incomeRangeId, employmentStatus, sourcesOfWealth, industry } = this.state.employmentInfo;

        const isIndustryValid = employmentConfig.employmentStatusThatRequireIndustry.includes(employmentStatus)
          ? Boolean(industry)
          : true;

        return {
          canProceedNext: Boolean(
            !!incomeRangeId && employmentStatus && sourcesOfWealth?.length > 0 && isIndustryValid
          ),
          reason: ""
        };
      }
    };

    return STEP_ALLOWED_CONFIG[stepName]();
  };

  private _getWizardNavStepConfig = (currentStep: number): any => {
    const { wizardInstance } = this.state;

    const STEP_NAV_CONFIG = [
      {
        nextStep: {
          disabled: !this._getStepProceedStatus("passport").canProceedNext,
          action: async (): Promise<void> => {
            try {
              await this._submitPersonalDetails();
              wizardInstance.nextStep();
            } catch (err) {
              captureException(err);
              emitToast({
                content: "We couldn't submit your personal details, please try later.",
                toastType: ToastTypeEnum.error
              });
            }
          },
          label: this._getStepProceedStatus("passport").canProceedNext
            ? "Next"
            : this._getStepProceedStatus("passport").reason
        }
      },
      {
        nextStep: {
          disabled: !this._getStepProceedStatus("address").canProceedNext,
          action: async (): Promise<void> => {
            try {
              await this._submitAddress();
              wizardInstance.nextStep();
            } catch (err) {
              if (err.response?.status === 400) {
                emitToast({
                  content: "Invalid address details, please edit and try again.",
                  toastType: ToastTypeEnum.error
                });
              } else {
                captureException(err);
                emitToast({
                  content: "We couldn't submit your address details, please try later.",
                  toastType: ToastTypeEnum.error
                });
              }
            }
          },
          label: "Next"
        },
        previousStep: {
          action: wizardInstance.previousStep,
          label: "Back"
        }
      },
      {
        previousStep: {
          action: wizardInstance.previousStep,
          label: "Back"
        },
        nextStep: {
          disabled: !this._getStepProceedStatus("taxResidency").canProceedNext,
          action: async (): Promise<void> => {
            try {
              await this._submitTaxResidency();
              wizardInstance.nextStep();
            } catch (err) {
              captureException(err);
              emitToast({
                content: "We couldn't submit your Tax details, please try later.",
                toastType: ToastTypeEnum.error
              });
            }
          },
          label: "Next"
        }
      },
      {
        previousStep: {
          action: wizardInstance.previousStep,
          label: "Back"
        },
        completeWizard: {
          disabled: !this._getStepProceedStatus("employmentInfo").canProceedNext,
          action: async (): Promise<void> => {
            try {
              await this._submitEmploymentInfo();
              window.location.href = "/investor/email-verified";
            } catch (err) {
              captureException(err);
              emitToast({
                content: "We couldn't submit your employment details, please try later.",
                toastType: ToastTypeEnum.error
              });
            }
          },
          label: "Complete"
        }
      }
    ];

    return STEP_NAV_CONFIG[currentStep - 1];
  };

  private _handleInputChanges =
    (stepName: "taxResidency" | "passport" | "address" | "employmentInfo") =>
    (inputName: TaxResidencyInputType | PassportInputType | AddressInputType | EmploymentInfoInputType) =>
    (value: string | string[] | number | boolean): void => {
      this.setState((prevState) => ({
        ...prevState,
        [stepName]: {
          ...prevState[stepName],
          [inputName]: value
        }
      }));
    };

  private _setCurrentStep = ({ activeStep }: { previousStep: number; activeStep: number }): void => {
    this.setState({ currentStep: activeStep }, () => this.props.onStepChange(activeStep));
  };

  private _setWizardInstance = (wizardInstance: StepWizardChildProps): void => {
    this.setState({ wizardInstance }, () => {
      if (!this._getStepProceedStatus("passport").canProceedNext) {
        wizardInstance && wizardInstance.goToStep(1);
      } else if (!this._getStepProceedStatus("address").canProceedNext) {
        wizardInstance && wizardInstance.goToStep(2);
      } else if (!this._getStepProceedStatus("taxResidency").canProceedNext) {
        wizardInstance && wizardInstance.goToStep(3);
      } else {
        wizardInstance && wizardInstance.goToStep(4);
      }
    });
  };

  private _submitAddress = async (): Promise<AxiosResponse> => {
    const addressInfo = this.state.address;

    // Remove all whitespaces form postcode before submitting the address
    addressInfo.postCode = addressInfo.postCode.replace(/\s/g, "");

    return axios({
      method: "post",
      url: "/investor/address",
      data: { ...addressInfo }
    });
  };

  private _submitPersonalDetails = async (): Promise<void> => {
    const { dateOfBirth, firstName, lastName, nationality } = this.state.passport;

    await axios({
      method: "post",
      url: "/investor/personal-details",
      data: {
        firstName,
        lastName,
        nationality,
        dateOfBirth: dateFriendlyFormatToISO(dateOfBirth) // convert to yyyy/mm/dd
      }
    });
  };

  private _submitTaxResidency = async (): Promise<AxiosResponse> => {
    const { taxResidency } = this.state;
    // remove whitespaces from tax residency value
    taxResidency.value = taxResidency.value.replace(/ /g, "");
    return axios({
      method: "post",
      url: "/investor/tax-residency",
      data: { ...taxResidency }
    });
  };

  private _submitEmploymentInfo = async (): Promise<AxiosResponse> => {
    const { employmentInfo } = this.state;
    return axios({
      method: "post",
      url: "/investor/employment-info",
      data: {
        incomeRangeId: employmentInfo.incomeRangeId,
        employmentStatus: employmentInfo.employmentStatus,
        industry: employmentInfo.industry,
        sourcesOfWealth: employmentInfo.sourcesOfWealth
      }
    });
  };

  render(): JSX.Element {
    const { employmentConfig, residencyCountry } = this.props;
    const { currentStep, wizardInstance, taxResidency, passport, address, employmentInfo } = this.state;

    return (
      <>
        <style
          dangerouslySetInnerHTML={{
            __html: `
              .nav {
                margin-bottom: 15px;
                text-align: center;
              }

              .dot {
                color: black;
                cursor: pointer;
                font-size: 36px;
                line-height: 1;
                margin: 0 15px;
                opacity: .4;
                text-shadow: none;
                transition: opacity 1s ease,
                    text-shadow 1s ease;
                will-change: opacity, text-shadow;
              }

              .active {
                color: var(--blue);
                opacity: 1;
                text-shadow: 0 0px 8px;
              }

              .wizard.wizard-3 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"]:last-child::after, .wizard.wizard-3 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"]::after {
                background-color: #3699FF !important;
              }

              .wizard.wizard-3 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"]:last-child .wizard-label .wizard-number {
                display: -webkit-box !important;
                display: -ms-flexbox !important;
                display: flex !important;
              }

              .wizard.wizard-3 .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"]:last-child .wizard-label .wizard-check {
                display: none !important;
              }

              .animated {
                animation-duration: .8192s;
                animation-fill-mode: backwards;
                transform-style: preserve-3d;
              }
              
              /** intro */
              @keyframes intro {
                from {
                  opacity: 0;
                  transform: perspective(500px) translate3d(0, 0, -50px);
                }
              
                to {
                  opacity: 1;
                  transform: none;
                }
              }
              
              .intro {
                animation: intro 1s ease-out;
              }
              
              /** enterRight */
              @keyframes enterRight {
                from {
                  opacity: 0;
                  transform: perspective(500px) translate3d(20%, 0, 0);
                }
              
                to {
                  opacity: 1;
                  transform: none;
                }
              }
            `
          }}
        />

        <div className="w-100 wizard wizard-3">
          <StepWizard
            nav={null}
            instance={this._setWizardInstance}
            transitions={animatedTransitions}
            onStepChange={this._setCurrentStep}
          >
            <VerificationWizardStepPassport
              firstName={passport.firstName}
              lastName={passport.lastName}
              dateOfBirth={passport.dateOfBirth}
              nationality={passport.nationality}
              onInputChange={this._handleInputChanges("passport")}
            />
            {wizardInstance && (
              <VerificationWizardStepAddress
                addressLine1={address.addressLine1}
                addressLine2={address.addressLine2}
                postCode={address.postCode}
                city={address.city}
                country={address.country}
                onInputChange={this._handleInputChanges("address")}
              />
            )}
            {wizardInstance && (
              <VerificationWizardStepTaxResidency
                proofValue={taxResidency.value}
                country={residencyCountry}
                onInputChange={this._handleInputChanges("taxResidency")}
                onSkip={() => {
                  this.setState((prevState) => ({
                    ...prevState,
                    taxResidency: {
                      ...prevState.taxResidency,
                      skipped: true
                    }
                  }));
                  wizardInstance.nextStep();
                }}
              />
            )}
            {wizardInstance && (
              <VerificationWizardStepEmploymentInfo
                incomeRangeId={employmentInfo.incomeRangeId}
                employmentStatus={employmentInfo.employmentStatus}
                sourcesOfWealth={employmentInfo.sourcesOfWealth}
                industry={employmentInfo.industry}
                employmentConfig={employmentConfig}
                onInputChange={this._handleInputChanges("employmentInfo")}
              />
            )}
          </StepWizard>
          {wizardInstance && (
            <ConfigurableWizardActions fixed={false} actions={this._getWizardNavStepConfig(currentStep)} />
          )}
        </div>
      </>
    );
  }
}

export default VerificationWizard;
