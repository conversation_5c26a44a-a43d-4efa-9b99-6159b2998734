import React from "react";
import SelectCountry from "./selectCountry";

type PropsType = {
  addressLine1: string;
  addressLine2: string;
  postCode: string;
  city: string;
  country: string;
  onInputChange: (
    inputName: "addressLine1" | "addressLine2" | "postCode" | "city" | "country"
  ) => (value: string) => void;
};

class VerificationWizardStepAddress extends React.Component<PropsType> {
  private handleInputChange =
    (inputName: "addressLine1" | "addressLine2" | "postCode" | "city" | "country") =>
    (event: any): void => {
      const { onInputChange } = this.props;
      onInputChange(inputName)(event.target.value);
    };

  render(): JSX.Element {
    const { addressLine1, addressLine2, postCode, city, country } = this.props;

    return (
      <div className="row m-0 p-0">
        {/* <!--begin::Input--> */}
        <div className="row m-0 mb-md-4 mt-3 p-0">
          <div className="col p-0">
            <div className="form-group">
              <label className="fw-bolder mb-2">Country</label>
              <SelectCountry
                isDisabled={true}
                defaultValue={country}
                name="country"
                onChange={this.handleInputChange("country")}
                required
              />
            </div>
          </div>
        </div>
        {/* <!--end::Input--> */}
        <>
          {/* <!--begin::Input--> */}
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              <div className="form-group">
                <label className="fw-bolder mb-2">Address Line 1</label>
                <input
                  type="text"
                  className="verification-input"
                  name="addressline1"
                  placeholder="Address Line 1"
                  value={addressLine1}
                  onChange={this.handleInputChange("addressLine1")}
                  required
                />
              </div>
            </div>
          </div>
          {/* <!--end::Input--> */}
          {/* <!--begin::Input--> */}
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              <div className="form-group">
                <label className="fw-bolder mb-2">
                  Address Line 2 <span className="text-muted">(Optional)</span>
                </label>
                <input
                  type="text"
                  className="verification-input"
                  name="addressline2"
                  placeholder="Address Line 2"
                  value={addressLine2}
                  onChange={this.handleInputChange("addressLine2")}
                />
              </div>
            </div>
          </div>
          {/* <!--end::Input--> */}
          {/* <!--begin::Input--> */}
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col-md-6 p-0 pe-md-2">
              <div className="form-group">
                <label className="fw-bolder mb-2">Postcode</label>
                <input
                  type="text"
                  className="verification-input"
                  name="postcode"
                  placeholder="Postcode"
                  value={postCode}
                  onChange={this.handleInputChange("postCode")}
                  required
                />
              </div>
            </div>
            {/* <!--end::Input--> */}
            {/* <!--begin::Input--> */}
            <div className="col-md-6 p-0 ps-md-2 mt-md-0 mt-3 ">
              <div className="form-group">
                <label className="fw-bolder mb-2">City</label>
                <input
                  type="text"
                  className="verification-input"
                  name="city"
                  placeholder="City"
                  value={city}
                  onChange={this.handleInputChange("city")}
                  required
                />
              </div>
            </div>
          </div>
          {/* <!--end::Input--> */}
        </>
      </div>
    );
  }
}

export default VerificationWizardStepAddress;
