import React, { Component } from "react";
import { EmploymentInfoConfiguration } from "../types/employmentConfig";
import SelectSourceOfWealth from "./selectSourceOfWealth";

type PropsType = {
  incomeRangeId: string;
  sourcesOfWealth: string[];
  employmentStatus: string;
  industry?: string;
  employmentConfig: EmploymentInfoConfiguration;
  onInputChange: (
    inputName: "incomeRangeId" | "employmentStatus" | "sourcesOfWealth" | "industry"
  ) => (value: string | string[] | number) => void;
};

export default class VerificationWizardStepEmploymentInfo extends Component<PropsType> {
  private _getIncomeRangeSelectView(): JSX.Element {
    const { employmentConfig, onInputChange } = this.props;

    return (
      <div className="position-relative">
        <select
          className="verification-input"
          name="annualIncome"
          onChange={(e) => onInputChange("incomeRangeId")(e.target.value)}
          required
        >
          <option value="">Select</option>
          {employmentConfig.incomeRanges.map(({ id, label }) => (
            <option value={id} key={`annual-income-${id}`}>
              {label}
            </option>
          ))}
        </select>
        <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
          expand_more
        </span>
      </div>
    );
  }

  private _getSourceOfWealthSelectView(): JSX.Element {
    const { employmentConfig, onInputChange } = this.props;

    const options = employmentConfig.sourcesOfWealth.map(({ id, label }) => ({
      value: id,
      label
    }));

    return (
      <SelectSourceOfWealth
        options={options}
        handleChange={(value: string[]) => onInputChange("sourcesOfWealth")(value)}
      />
    );
  }

  private _getEmploymentStatusSelectView(): JSX.Element {
    const { employmentConfig, onInputChange } = this.props;

    return (
      <div className="position-relative">
        <select
          className="verification-input"
          name="employmentStatus"
          onChange={(e) => onInputChange("employmentStatus")(e.target.value)}
          required
        >
          <option value="">Select</option>
          {employmentConfig.employmentStatuses.map(({ id, label }, index) => (
            <option value={id} key={`employment-status-${index}`}>
              {label}
            </option>
          ))}
        </select>
        <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
          expand_more
        </span>
      </div>
    );
  }

  private _getIndustrySelectView(): JSX.Element {
    const { employmentConfig, onInputChange } = this.props;

    return (
      <div className="position-relative">
        <select
          className="verification-input"
          name="industry"
          onChange={(e) => onInputChange("industry")(e.target.value)}
          required
        >
          <option value="">Select</option>
          {employmentConfig.industries.map(({ id, label }, index) => (
            <option value={id} key={`industry-${index}`}>
              {label}
            </option>
          ))}
        </select>
        <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
          expand_more
        </span>
      </div>
    );
  }

  render() {
    const {
      employmentConfig: { employmentStatusThatRequireIndustry },
      employmentStatus
    } = this.props;

    const showIndustrySelectView = employmentStatusThatRequireIndustry.includes(employmentStatus);

    return (
      <>
        <div className="row m-0 p-0">
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              <div className="form-group">
                <label className="fw-bolder mb-2">Annual Income</label>
                {this._getIncomeRangeSelectView()}
              </div>
            </div>
          </div>
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              <div className="form-group">
                <label className="fw-bolder mb-2">Source of funds</label>
                {this._getSourceOfWealthSelectView()}
              </div>
            </div>
          </div>
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              <div className="form-group">
                <label className="fw-bolder mb-2">Employment Status</label>
                {this._getEmploymentStatusSelectView()}
              </div>
            </div>
          </div>
          {showIndustrySelectView && (
            <div className="row m-0 mb-md-4 mt-3 p-0">
              <div className="col p-0">
                <div className="form-group">
                  <label className="fw-bolder mb-2">Industry</label>
                  {this._getIndustrySelectView()}
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    );
  }
}
