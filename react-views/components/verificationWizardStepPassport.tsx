import React from "react";
import SelectCountry from "./selectCountry";
import { dateFriendlyFormatToISO, dateIsValid, isOverEighteen } from "../utils/dateUtil";
import { countriesConfig } from "@wealthyhood/shared-configs";
import MaskedInput from "react-text-mask";

type PropsType = {
  firstName: string;
  lastName: string;
  nationality: countriesConfig.CountryCodesType | "";
  dateOfBirth: string;
  onInputChange: (inputName: "firstName" | "lastName" | "dateOfBirth" | "nationality") => (value: string) => void;
};

class VerificationWizardStepPassport extends React.Component<PropsType> {
  private _handleInputChange =
    (inputName: "firstName" | "lastName" | "dateOfBirth" | "nationality") =>
    (event: any): void => {
      const { onInputChange } = this.props;
      let value = event.target.value;
      if (["firstName", "lastName"].includes(inputName)) {
        const specialCharsRegex = new RegExp(/[`~!@#$%^&*()_|+\-=?;:'",.<>{}[\]\\/]/, "gi");
        value = value.replace(specialCharsRegex, "");
      }
      onInputChange(inputName)(value);
    };

  render(): JSX.Element {
    const { firstName, lastName, dateOfBirth, nationality } = this.props;

    return (
      <>
        <div className="row m-0 p-0">
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              {/* First Name Input */}
              <div className="form-group">
                <label className="fw-bolder mb-2">First Name</label>
                <input
                  type="text"
                  className="verification-input"
                  name="firstname"
                  placeholder="First Name"
                  value={firstName}
                  onChange={this._handleInputChange("firstName")}
                  required
                />
              </div>
              {/* End First Name Input */}
            </div>
          </div>
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              {/* Last Name Input */}
              <div className="form-group">
                <label className="fw-bolder mb-2">Last Name</label>
                <input
                  type="text"
                  className="verification-input"
                  name="lastname"
                  placeholder="Last Name"
                  value={lastName}
                  onChange={this._handleInputChange("lastName")}
                  required
                />
              </div>
              {/* End Last Name Input */}
            </div>
          </div>
          <div className="row m-0 mb-md-4 mt-3 p-0">
            <div className="col p-0">
              {/* Date of Birth Input */}
              <div className="form-group">
                <label className="fw-bolder mb-2">Date of Birth</label>
                <div className="position-relative">
                  <MaskedInput
                    mask={[/[0-3]/, /[0-9]/, "/", /[0-1]/, /[0-9]/, "/", /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/]}
                    className="verification-input input-warning-icon"
                    name="dateofbirth"
                    placeholder="dd/mm/yyyy"
                    value={dateOfBirth}
                    guide={true}
                    onChange={this._handleInputChange("dateOfBirth")}
                  />
                  <span className="material-icons icon-primary position-absolute top-50 end-0 translate-middle-y me-3">
                    event
                  </span>
                </div>
                {dateOfBirth && !dateIsValid(new Date(dateFriendlyFormatToISO(dateOfBirth))) && (
                  <span className="d-block text-danger pt-2">Date is not valid</span>
                )}
                {dateIsValid(new Date(dateFriendlyFormatToISO(dateOfBirth))) &&
                  !isOverEighteen(new Date(dateFriendlyFormatToISO(dateOfBirth))) && (
                    <span className="d-block text-danger pt-2">Must be over 18 years old</span>
                  )}
              </div>
              {/* End Date of Birth Input */}
            </div>
          </div>
          <div className="row m-0 mt-3 p-0">
            <div className="col p-0">
              {/* Nationality Input */}
              <div className="form-group">
                <label className="fw-bolder mb-2">Nationality</label>
                <SelectCountry
                  isDisabled={false}
                  defaultValue={nationality}
                  name="nationality"
                  onChange={this._handleInputChange("nationality")}
                  required
                />
              </div>
              {/* End Nationality Input */}
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default VerificationWizardStepPassport;
