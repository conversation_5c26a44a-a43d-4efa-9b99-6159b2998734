import React from "react";
import { isTaxIdentifierValid } from "../utils/validationUtil";
import ValidationInput from "./validationInput";
import { countriesConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";

type PropsType = {
  proofValue: string;
  country: countriesConfig.CountryCodesType;
  onInputChange: (inputName: "value") => (value: string | boolean) => void;
  onSkip: () => void;
};

class VerificationWizardStepTaxResidency extends React.Component<PropsType> {
  private _getFormattedTaxResidencyValue(): string {
    const { proofValue, country } = this.props;

    if (country !== "GB") {
      return proofValue;
    }

    const letters = [...proofValue];
    const fixedLetters = [];
    for (let i = 0; i < letters.length; i++) {
      if ([2, 5, 8].includes(i)) {
        fixedLetters.push(" ");
      }

      fixedLetters.push(letters[i].toUpperCase());

      if (i == 8) {
        break;
      }
    }

    return fixedLetters.join("");
  }

  render(): JSX.Element {
    const { onInputChange, country, onSkip } = this.props;
    const proofValue = this._getFormattedTaxResidencyValue();

    const { inputPlaceholder, inputTitle, inputError, inputLength, required } =
      ConfigUtil.getTaxResidencyConfig(country);

    return (
      <div className="row m-0 p-0">
        <div className="form-group row m-0 mt-3 mb-5 p-0">
          <div className="col p-0">
            <label className="fw-bolder mb-2">{inputTitle}</label>
            <ValidationInput
              isValid={(taxIdentifier) => {
                const cleansedIdentifier = taxIdentifier.replace(/ /g, "");
                if (cleansedIdentifier.length >= inputLength) {
                  return isTaxIdentifierValid(country, taxIdentifier);
                }

                return true;
              }}
              onChange={(taxIdentifier) => {
                const cleansedIdentifier = taxIdentifier.replace(/ /g, "");
                onInputChange("value")(cleansedIdentifier.toUpperCase());
              }}
              value={proofValue}
              placeholder={inputPlaceholder}
              errorMessage={inputError}
              required={false}
            />
          </div>

          {!required && (
            <h6 className="mt-5 text-center text-primary cursor-pointer" onClick={onSkip}>
              Skip for now
            </h6>
          )}
        </div>
      </div>
    );
  }
}

export default VerificationWizardStepTaxResidency;
