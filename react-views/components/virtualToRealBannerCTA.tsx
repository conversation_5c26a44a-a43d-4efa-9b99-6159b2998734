import React from "react";
import { UserDocument } from "../../models/User";
import { eventEmitter, EVENTS } from "../utils/eventService";
import MainCard from "../layouts/mainCard";

type PropsType = {
  user: UserDocument;
};

class VirtualToRealBannerCTA extends React.Component<PropsType> {
  private _getConfigByUserState(): { title?: string; message: JSX.Element; button?: JSX.Element } {
    const { user } = this.props;

    if (user.portfolioConversionStatus === "completed") return null;

    if (user.portfolioConversionStatus === "inProgress") {
      return {
        title: "We’re processing your investment!",
        message: (
          <p className="text-muted">
            {
              "We’ll let you know once it has been executed. In the meantime, you can check the status of your orders in your Accounts tab."
            }
          </p>
        ),
        button: (
          <div className="wh-card-body p-3 bg-light-warning text-warning d-flex align-self-center">
            <span className="material-symbols-outlined align-self-center me-1" style={{ fontSize: "16px" }}>
              hourglass_top
            </span>
            Processing your first investment
          </div>
        )
      };
    }

    if (user.portfolioConversionStatus === "fullyWithdrawn") {
      return {
        title: "You have no active investments 😔",
        message: (
          <p className="text-muted">
            {"Your portfolio is empty at the moment. Make an investment to keep building your wealth!"}
          </p>
        )
      };
    }

    if (user.portfolioConversionStatus === "notStarted" && !user?.portfolios?.[0]?.isTargetAllocationSetup) {
      return {
        title: "Nice, you’re all set to start investing!",
        message: (
          <p className="text-muted">
            {
              "Time in the market beats timing the market! Make your first investment and start building your wealth today!"
            }
          </p>
        ),
        button: (
          <>
            <button
              className="btn btn-primary w-100 mb-4"
              onClick={() => (window.location.href = "/portfolios/asset-discovery")}
            >
              Discover stocks & ETFs
            </button>
            <a href="/portfolios/creation?shouldSetupTargetAllocation=true" className="btn btn-primary w-100">
              Set up a target portfolio
            </a>
          </>
        )
      };
    }

    if (user.portfolioConversionStatus === "notStarted") {
      return {
        title: "Nice, you’re all set to start investing!",
        message: (
          <p className="text-muted">
            {
              "Time in the market beats timing the market! Make your first investment and start building your wealth today!"
            }
          </p>
        ),
        button: (
          <>
            <button
              className="btn btn-primary w-100 mb-4"
              onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
            >
              Make your first investment
            </button>
            <a href="/portfolios/target" className="btn btn-primary w-100">
              Edit your portfolio
            </a>
          </>
        )
      };
    }
  }

  render(): JSX.Element {
    const config = this._getConfigByUserState();

    return config ? (
      <MainCard>
        <div className={`row p-0 m-0 text-center ${config.button ? "mb-4" : ""}`}>
          <div className="col p-0">
            {config.title && <h5 className="fw-bolder mb-4">{config.title}</h5>}
            {config.message}
          </div>
        </div>
        {config.button && (
          <div className="row text-center">
            <div className="col p-0">
              <div className="d-inline-block">{config.button}</div>
            </div>
          </div>
        )}
      </MainCard>
    ) : (
      <></>
    );
  }
}

export default VirtualToRealBannerCTA;
