import React from "react";
import Step<PERSON><PERSON><PERSON>, { StepWizardProps } from "react-step-wizard";
import ArrowToRightIcon from "./icons/arrowToRightIcon";

export const animatedTransitions = {
  enterRight: "animated intro",
  enterLeft: "animated intro",
  exitRight: "animated intro",
  exitLeft: "animated intro",
  intro: "animated intro"
};

class WelcomeWizardStep1 extends React.Component<{ nextStep?: () => void }> {
  render(): JSX.Element {
    const { nextStep } = this.props;

    return (
      <>
        <p>Are you a long term investor? Then you are in the right place!</p>
        <p>
          We{"'"}ll provide you with the perfect mix of personalisation and automation to put your money to work
          hard and build your investing mindset over time.
        </p>
        <p>First, let{"'"}s start by creating your portfolio!</p>
        <div className="d-flex justify-content-center pt-10">
          <button type="button" className="btn btn-lg btn-primary font-weight-bolder" onClick={nextStep}>
            Next
            <span className="svg-icon svg-icon-md ml-3">
              <ArrowToRightIcon />
            </span>
          </button>
        </div>
      </>
    );
  }
}

class WelcomeWizardStep2 extends React.Component {
  render(): JSX.Element {
    return (
      <>
        <p>
          Over the next few steps, we{"'"}ll help you select your investment preferences, pick the right assets for
          you and set your portfolio allocation.
        </p>
        <div className="d-flex justify-content-center pt-10">
          <a href="/portfolios/creation" className="btn btn-lg btn-primary font-weight-bolder">
            Get Started
          </a>
        </div>
      </>
    );
  }
}

type PropsType = {};
type StateType = {
  wizardInstance: StepWizardProps & { nextStep: () => void; previousStep: () => void };
};

class WelcomeWizard extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      wizardInstance: null
    };
  }

  private _setWizardInstance = (wizardInstance: any): void => {
    this.setState({ wizardInstance });
  };

  render(): JSX.Element {
    const { wizardInstance } = this.state;

    return (
      <>
        <style
          dangerouslySetInnerHTML={{
            __html: `
          .animated {
            animation-duration: .8192s;
            animation-fill-mode: backwards;
            transform-style: preserve-3d;
          }
          
          /** intro */
          @keyframes intro {
            from {
              opacity: 0;
              transform: perspective(500px) translate3d(0, 0, -50px);
            }
          
            to {
              opacity: 1;
              transform: none;
            }
          }
          
          .intro {
            animation: intro 1s ease-out;
          }
          
          /** enterRight */
          @keyframes enterRight {
            from {
              opacity: 0;
              transform: perspective(500px) translate3d(20%, 0, 0);
            }
          
            to {
              opacity: 1;
              transform: none;
            }
          }
        `
          }}
        />

        <div className="w-100 wizard wizard-3">
          <StepWizard transitions={animatedTransitions} instance={this._setWizardInstance}>
            <WelcomeWizardStep1 />
            {wizardInstance && <WelcomeWizardStep2 />}
          </StepWizard>
        </div>
      </>
    );
  }
}

export default WelcomeWizard;
