import React from "react";

enum StepStateEnum {
  CURRENT = "current",
  DONE = "done",
  PENDING = "pending"
}

type StepPropsType = {
  label: number;
  stepState: StepStateEnum;
  title: string;
};

class WizardNavStep extends React.Component<StepPropsType> {
  render(): JSX.Element {
    const { label, stepState, title } = this.props;

    return (
      <div className="wizard-step" data-wizard-type="step" data-wizard-state={stepState}>
        <div className={`wizard-label cursor-default ${stepState !== StepStateEnum.PENDING ? "bg-primary" : ""}`}>
          <span className="wizard-number">{label}</span>
          <span className="wizard-check">
            <span className="svg-icon svg-icon-2x">
              {/* <!--begin::Svg Icon --> */}
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                  <polygon points="0 0 24 0 24 24 0 24"></polygon>
                  <path
                    d="M6.26193932,17.6476484 C5.90425297,18.0684559 5.27315905,18.1196257 4.85235158,17.7619393 C4.43154411,17.404253 4.38037434,16.773159 4.73806068,16.3523516 L13.2380607,6.35235158 C13.6013618,5.92493855 14.2451015,5.87991302 14.6643638,6.25259068 L19.1643638,10.2525907 C19.5771466,10.6195087 19.6143273,11.2515811 19.2474093,11.6643638 C18.8804913,12.0771466 18.2484189,12.1143273 17.8356362,11.7474093 L14.0997854,8.42665306 L6.26193932,17.6476484 Z"
                    fill="#000000"
                    fillRule="nonzero"
                    transform="translate(11.999995, 12.000002) rotate(-180.000000) translate(-11.999995, -12.000002)"
                  ></path>
                </g>
              </svg>
              {/* <!--end::Svg Icon--> */}
            </span>
          </span>
        </div>
        <div className="wizard-title">{title}</div>
      </div>
    );
  }
}

type WizardNavPropsType = {
  currentStep?: number;
  goToStep?: any;
  stepTitles: string[];
};

class WizardNav extends React.Component<WizardNavPropsType> {
  render(): JSX.Element {
    const { currentStep, stepTitles } = this.props;

    return (
      <div className="wizard-nav">
        <div className="wizard-steps px-8 py-5 px-lg-15 pt-lg-5 pb-lg-15">
          {stepTitles.map((title, index) => {
            let stepState: StepStateEnum = StepStateEnum.PENDING;
            if (index + 1 < currentStep) {
              stepState = StepStateEnum.DONE;
            } else if (index + 1 === currentStep) {
              stepState = StepStateEnum.CURRENT;
            }

            return (
              <WizardNavStep label={index + 1} title={title} stepState={stepState} key={`wizard-step-${index}`} />
            );
          })}
        </div>
      </div>
    );
  }
}

export default WizardNav;
