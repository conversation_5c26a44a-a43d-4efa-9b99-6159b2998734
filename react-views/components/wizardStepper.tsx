import React from "react";

export type PropsType = {
  currentStep: number;
  maxStepsDisplayed: number;
  stepsConfig: { title: string; body: string }[];
};

class WizardStepper extends React.Component<PropsType> {
  private _getDoneSteps(): number[] {
    const steps = [];
    for (let i = 1; i < this._getFixedCurrentStep(); i++) {
      steps.push(i);
    }

    return steps;
  }

  private _getRemainingSteps(): number[] {
    const { maxStepsDisplayed } = this.props;
    const steps = [];
    for (let i = this._getFixedCurrentStep() + 1; i <= maxStepsDisplayed; i++) {
      steps.push(i);
    }

    return steps;
  }

  private _getFixedCurrentStep(): number {
    const { currentStep, maxStepsDisplayed } = this.props;
    return currentStep >= maxStepsDisplayed ? maxStepsDisplayed : currentStep;
  }

  render(): JSX.Element {
    const currentStep = this._getFixedCurrentStep();
    const { stepsConfig, maxStepsDisplayed } = this.props;
    const { title, body } = stepsConfig[currentStep - 1];

    return (
      <>
        <div className="row m-0 p-0 d-none d-sm-block">
          <div className="row mb-5 p-0">
            {/* <!-- progressbar --> */}
            <ul id="progressbar" key={"progressbar"}>
              {this._getDoneSteps().map((step) => {
                return (
                  <React.Fragment key={"progressbar-step-" + step}>
                    <li key={"progressbar-step-" + step} className="done" id={"step-" + step} />
                    <li key={"progressbar-connector-" + step} className="li-connector-done" />
                  </React.Fragment>
                );
              })}
              <li key={"progressbar-step-" + currentStep} className="active" id={"step-" + currentStep} />
              {this._getRemainingSteps().map((step) => {
                return (
                  <React.Fragment key={"progressbar-step-" + step}>
                    <li key={"progressbar-connector-" + step} className="li-connector" />
                    <li key={"progressbar-step-" + step} id={"step-" + step} />
                  </React.Fragment>
                );
              })}
            </ul>
          </div>
          <div className="row mt-3 p-0">
            <h1 className="fw-bold mb-3 p-0">{title}</h1>
            <h4 className="p-0">{body}</h4>
          </div>
        </div>

        <div className="row m-0 p-0 d-block d-sm-none">
          <div className="d-flex justify-content-center align-self-center mb-2 p-0">
            <div className="steps-container">
              {this._getFixedCurrentStep()} / {maxStepsDisplayed}
            </div>
          </div>
          <div className="row m-0 p-0 text-center">
            <h4 className="fw-bold p-0">{title}</h4>
            <p className="p-0 m-0">{body}</p>
          </div>
        </div>
      </>
    );
  }
}

export default WizardStepper;
