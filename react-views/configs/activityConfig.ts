import { RewardActivityFilterEnum } from "../../models/Reward";
import {
  TransactionCashActivityFilterEnum,
  TransactionInvestmentActivityFilterEnum
} from "../../models/Transaction";

export type InvestmentActivityFilterEnum =
  | RewardActivityFilterEnum
  | TransactionInvestmentActivityFilterEnum
  | "All";
export type CashActivityFilterEnum = TransactionCashActivityFilterEnum | "All";

export const INVESTMENT_ACTIVITY_FILTER_CONFIG: Record<
  InvestmentActivityFilterEnum,
  {
    order: number;
    notFoundMessage: string;
  }
> = {
  All: {
    order: 0,
    notFoundMessage: "made any transactions"
  },
  Buy: {
    order: 1,
    notFoundMessage: "made any Buy orders"
  },
  Sell: {
    order: 2,
    notFoundMessage: "made any Sell orders"
  },
  Asset: { notFoundMessage: "made any transactions", order: 3 },
  Rebalance: {
    order: 4,
    notFoundMessage: "made any Rebalance orders"
  },
  Dividends: {
    order: 5,
    notFoundMessage: "received any dividends"
  },
  Rewards: {
    order: 6,
    notFoundMessage: "received any rewards"
  }
};

export const CASH_ACTIVITY_FILTER_CONFIG: Record<
  CashActivityFilterEnum,
  {
    order: number;
    notFoundMessage: string;
  }
> = {
  All: {
    order: 0,
    notFoundMessage: "made any transactions"
  },
  Deposit: {
    order: 1,
    notFoundMessage: "made any Deposits"
  },
  Withdraw: {
    order: 2,
    notFoundMessage: "made any Withdrawals"
  },
  Investments: {
    order: 3,
    notFoundMessage: "made any Investments"
  },
  Dividends: {
    order: 4,
    notFoundMessage: "received any dividends"
  },
  Bonus: {
    order: 5,
    notFoundMessage: "received any bonuses"
  }
};
