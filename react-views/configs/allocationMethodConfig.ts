import { AllocationMethodEnum } from "../components/transactionModalTargetAllocationSelector";

export const ALLOCATION_METHOD_CONFIG: Record<
  AllocationMethodEnum,
  { nameDisplay: string; iconKey: string; subtitle: string }
> = {
  HOLDINGS: {
    iconKey: "list_alt",
    nameDisplay: "My investments",
    subtitle: "Allocation of your current holdings"
  },
  TARGET_ALLOCATION: {
    iconKey: "donut_large",
    nameDisplay: "Target portfolio",
    subtitle: "Allocation of your target portfolio"
  }
};
