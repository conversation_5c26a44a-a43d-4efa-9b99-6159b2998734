import { GiftDocument } from "../../models/Gift";
import { RewardDocument } from "../../models/Reward";
import { PartialRecord } from "../types/utils";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { ANALYST_INSIGHT_ICON_CONFIG } from "./learningHubConfig";
export type BannerDataType = {
  bannerId: BannerEnum;
  data?: {
    rewards?: RewardDocument[];
    gifts?: GiftDocument[];
    title?: string;
    imageURL?: string;
    analystInsightId?: string;
    slug?: string;
    modalTitle?: string;
    modalContent?: string[];
    modalButtonText?: string;
  };
};

export enum BannerTagEnum {
  AnalystInsight = "Analyst insights",
  Analysis = "Analysis",
  QuickTake = "Quick take",
  WeeklyReview = "Weekly review",
  LearningGuide = "Learning guide"
}

export const bannerTagConfig: Record<
  BannerTagEnum,
  { color: string; backgroundColor: string; iconClass: string; icon: string }
> = {
  [BannerTagEnum.AnalystInsight]: {
    color: "#905FB8",
    backgroundColor: "#F9F1FF",
    iconClass: "material-icons",
    icon: "bolt"
  },
  [BannerTagEnum.LearningGuide]: {
    color: "#536AE3",
    backgroundColor: "#fff",
    iconClass: "material-symbols-outlined",
    icon: "emoji_objects"
  },
  [BannerTagEnum.Analysis]: {
    color: ANALYST_INSIGHT_ICON_CONFIG.ANALYSIS.color,
    backgroundColor: ANALYST_INSIGHT_ICON_CONFIG.ANALYSIS.backgroundColor,
    iconClass: "material-icons",
    icon: ANALYST_INSIGHT_ICON_CONFIG.ANALYSIS.materialIcon
  },
  [BannerTagEnum.QuickTake]: {
    color: ANALYST_INSIGHT_ICON_CONFIG.QUICK_TAKE.color,
    backgroundColor: ANALYST_INSIGHT_ICON_CONFIG.QUICK_TAKE.backgroundColor,
    iconClass: "material-icons",
    icon: ANALYST_INSIGHT_ICON_CONFIG.QUICK_TAKE.materialIcon
  },
  [BannerTagEnum.WeeklyReview]: {
    color: ANALYST_INSIGHT_ICON_CONFIG.WEEKLY_REVIEW.color,
    backgroundColor: ANALYST_INSIGHT_ICON_CONFIG.WEEKLY_REVIEW.backgroundColor,
    iconClass: "material-icons",
    icon: ANALYST_INSIGHT_ICON_CONFIG.WEEKLY_REVIEW.materialIcon
  }
};

export enum BannerEnum {
  /** Static */
  RedeemGift = "RedeemGift",
  UnlockFreeShare = "UnlockFreeShare",
  SendGift = "SendGift",
  /**Investment Promotion */
  WhySetUpMonthlyInvestment = "WhySetUpMonthlyInvestment",
  WhatIsDCA = "WhatIsDCA",
  LatestAnalysis = "LatestAnalysis",
  LatestQuickTake = "LatestQuickTake",
  LatestWeeklyReview = "LatestWeeklyReview",
  //TODO: Deprecate this when it is no longer needed
  LatestAnalystInsight = "LatestAnalystInsight",
  EarnFreeShares = "EarnFreeShares",

  /** Savings Promotion */
  EarnInterestOnYourMoney = "EarnInterestOnYourMoney",

  /** Plan Promotion */
  // START - These banners have a "Plus" suffix but dont necessarily have to be Plus plan promotions
  EarnBonusDividendPlus = "EarnBonusDividendPlus",
  EarnCashbackPlus = "EarnCashbackPlus",
  // END
  PlanUpgradePlus = "PlanUpgradePlus",

  /** Learning Guide Promotion */
  NewToInvesting = "NewToInvesting",
  InvestingInETFs = "InvestingInETFs",
  HowToBuildPortfolio = "HowToBuildPortfolio",
  ShouldYouRebalance = "ShouldYouRebalance",
  SaveInvestTrade = "SaveInvestTrade",
  GovBonds = "GovBonds",
  CorpBonds = "CorpBonds",
  InvestingInGold = "InvestingInGold",
  RealEstateInvesting = "RealEstateInvesting",
  TaxWrapper = "TaxWrapper",
  HowToThinkOfRisk = "HowToThinkOfRisk",
  WhenToStartInvesting = "WhenToStartInvesting",
  WhatAreInterestRates = "WhatAreInterestRates",
  InvestingInESG = "InvestingInESG",
  InvestingInThematics = "InvestingInThematics",
  GrowYourSavingsWithMoneyMarketFunds = "GrowYourSavingsWithMoneyMarketFunds",
  /** Temporary Sweatcoin Promotion */
  SweatcoinLifetimeGoldDeal = "SweatcoinLifetimeGoldDeal",
  /** End Temporary Sweatcoin Promotion */
  PendingTransaction = "PendingTransaction"
}
export const NotDismissableBannersArray: readonly BannerEnum[] = [
  BannerEnum.RedeemGift,
  BannerEnum.UnlockFreeShare,
  BannerEnum.SweatcoinLifetimeGoldDeal,
  BannerEnum.PendingTransaction
];

export const AlwaysVisibleIfAvailableBannersArray: readonly BannerEnum[] = [
  BannerEnum.RedeemGift,
  BannerEnum.UnlockFreeShare,
  BannerEnum.SendGift,
  BannerEnum.SweatcoinLifetimeGoldDeal,
  BannerEnum.PendingTransaction
];

export const PlanPromotionBannersArray: readonly BannerEnum[] = [
  BannerEnum.EarnBonusDividendPlus,
  BannerEnum.EarnCashbackPlus,
  BannerEnum.PlanUpgradePlus
];

export const LearningGuideBannersArray: readonly BannerEnum[] = [
  BannerEnum.NewToInvesting,
  BannerEnum.InvestingInETFs,
  BannerEnum.HowToBuildPortfolio,
  BannerEnum.ShouldYouRebalance,
  BannerEnum.SaveInvestTrade,
  BannerEnum.GovBonds,
  BannerEnum.CorpBonds,
  BannerEnum.InvestingInGold,
  BannerEnum.RealEstateInvesting,
  BannerEnum.TaxWrapper,
  BannerEnum.HowToThinkOfRisk,
  BannerEnum.WhenToStartInvesting,
  BannerEnum.WhatAreInterestRates,
  BannerEnum.InvestingInESG,
  BannerEnum.InvestingInThematics
];

const BannerEnumToIconPath: PartialRecord<BannerEnum, string> = {
  [BannerEnum.EarnCashbackPlus]: "/images/icons/cashback-1.png",
  [BannerEnum.EarnBonusDividendPlus]: "/images/icons/dividend.png",
  [BannerEnum.PlanUpgradePlus]: "/images/icons/growth-1.png",
  // investment promotion
  [BannerEnum.WhySetUpMonthlyInvestment]: "/images/icons/why-set-up-repeating-investment.png",
  [BannerEnum.WhatIsDCA]: "/images/icons/dca.png",
  [BannerEnum.EarnFreeShares]: "/images/icons/astronaut-soccer-1.png",
  // Analyst insight
  [BannerEnum.LatestAnalystInsight]: "/images/icons/lightning-strike.png",
  [BannerEnum.LatestAnalysis]: "/images/icons/lightning-strike.png",
  [BannerEnum.LatestQuickTake]: "/images/icons/lightning-strike.png",
  [BannerEnum.LatestWeeklyReview]: "/images/icons/lightning-strike.png",
  [BannerEnum.SendGift]: "/images/icons/gift-1.png",
  [BannerEnum.RedeemGift]: "/images/icons/gift-1.png",
  [BannerEnum.UnlockFreeShare]: "images/icons/gift-box-1.png",
  // learning guides
  [BannerEnum.NewToInvesting]: "images/icons/money-plant.png",
  [BannerEnum.InvestingInETFs]: "images/icons/etf.png",
  [BannerEnum.HowToBuildPortfolio]: "images/icons/pie.png",
  [BannerEnum.ShouldYouRebalance]: "images/icons/balance-scale.png",
  [BannerEnum.SaveInvestTrade]: "images/icons/piggy-bank-1.png",
  [BannerEnum.GovBonds]: "images/icons/bonds-1.png",
  [BannerEnum.CorpBonds]: "images/icons/corp-bonds.png",
  [BannerEnum.InvestingInGold]: "images/icons/investing-in-gold.png",
  [BannerEnum.RealEstateInvesting]: "images/icons/real-estate-investing.png",
  [BannerEnum.TaxWrapper]: "images/icons/discount-gift.png",
  [BannerEnum.HowToThinkOfRisk]: "images/icons/balance-scale-1.png",
  [BannerEnum.WhenToStartInvesting]: "images/icons/clock.png",
  [BannerEnum.WhatAreInterestRates]: "images/icons/bank.png",
  [BannerEnum.InvestingInESG]: "images/icons/investing-in-esg.png",
  [BannerEnum.InvestingInThematics]: "images/icons/investing-in-thematics.png",
  [BannerEnum.PendingTransaction]: "images/icons/update.png",
  [BannerEnum.GrowYourSavingsWithMoneyMarketFunds]: "images/icons/money-marker-funds-lg.png",
  [BannerEnum.EarnInterestOnYourMoney]: "images/icons/percent-droplet.png"
};

type BannerStylesConfigType = {
  gradientStart: string;
  gradientMiddle: string;
  gradientEnd: string;
  color?: "light";
};

type BaseBannerCardConfigType = {
  id: BannerEnum;
  imageSource: string;
  imageOverride?: string;
  styles?: BannerStylesConfigType;
  tag?: BannerTagEnum;
};

type BannerCardWithModal = BaseBannerCardConfigType & {
  opensModal: true;
  modalButtonAction: () => void;
  directAction?: never; // Ensure directAction is not allowed
};

type BannerCardWithDirectAction = BaseBannerCardConfigType & {
  opensModal?: false; // Optional, but defaults to false
  directAction: (options?: {
    hasSubscription?: boolean;
    analystInsightId?: string;
    learningGuideSlug?: string;
  }) => void;
  modalButtonAction?: never; // Ensure modalButtonAction is not allowed
};

export type BannerCardConfigType = BannerCardWithModal | BannerCardWithDirectAction;

export const BANNER_CARD_CONFIG: Record<BannerEnum, BannerCardConfigType> = {
  [BannerEnum.EarnCashbackPlus]: {
    id: BannerEnum.EarnCashbackPlus,
    imageSource: BannerEnumToIconPath[BannerEnum.EarnCashbackPlus],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/change-plan?select=paid_mid_monthly")
  },
  [BannerEnum.EarnBonusDividendPlus]: {
    id: BannerEnum.EarnBonusDividendPlus,
    imageSource: BannerEnumToIconPath[BannerEnum.EarnBonusDividendPlus],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/change-plan?select=paid_mid_monthly")
  },
  [BannerEnum.PlanUpgradePlus]: {
    id: BannerEnum.PlanUpgradePlus,
    imageSource: BannerEnumToIconPath[BannerEnum.PlanUpgradePlus],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(246, 141, 176, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/change-plan?select=paid_low_monthly")
  },
  [BannerEnum.LatestAnalystInsight]: {
    id: BannerEnum.LatestAnalystInsight,
    imageSource: BannerEnumToIconPath[BannerEnum.LatestAnalystInsight],
    styles: {
      gradientStart: "rgba(255, 255, 255, 1) 0%",
      gradientMiddle: "rgba(255, 255, 255, 1) 0%",
      gradientEnd: "rgba(255, 255, 255, 1) 0%"
    },
    directAction: () => (window.location.href = "/investor/learning-hub?tab=insights"),
    tag: BannerTagEnum.AnalystInsight
  },
  [BannerEnum.LatestAnalysis]: {
    id: BannerEnum.LatestAnalysis,
    imageSource: BannerEnumToIconPath[BannerEnum.LatestAnalysis],
    styles: {
      gradientStart: "rgba(255, 255, 255, 1) 0%",
      gradientMiddle: "rgba(255, 255, 255, 1) 0%",
      gradientEnd: "rgba(255, 255, 255, 1) 0%"
    },
    directAction: ({ analystInsightId }) =>
      (window.location.href = `/investor/learning-hub/articles/analyst-insights/${analystInsightId}?returnTo=/`),
    tag: BannerTagEnum.Analysis
  },
  [BannerEnum.LatestQuickTake]: {
    id: BannerEnum.LatestQuickTake,
    imageSource: BannerEnumToIconPath[BannerEnum.LatestQuickTake],
    styles: {
      gradientStart: "rgba(255, 255, 255, 1) 0%",
      gradientMiddle: "rgba(255, 255, 255, 1) 0%",
      gradientEnd: "rgba(255, 255, 255, 1) 0%"
    },
    directAction: ({ analystInsightId }) =>
      (window.location.href = `/investor/learning-hub/articles/analyst-insights/${analystInsightId}?returnTo=/`),
    tag: BannerTagEnum.QuickTake
  },
  [BannerEnum.LatestWeeklyReview]: {
    id: BannerEnum.LatestWeeklyReview,
    imageSource: BannerEnumToIconPath[BannerEnum.LatestWeeklyReview],
    styles: {
      gradientStart: "rgba(255, 255, 255, 1) 0%",
      gradientMiddle: "rgba(255, 255, 255, 1) 0%",
      gradientEnd: "rgba(255, 255, 255, 1) 0%"
    },
    directAction: ({ analystInsightId }) =>
      (window.location.href = `/investor/learning-hub/articles/analyst-insights/${analystInsightId}?returnTo=/`),
    tag: BannerTagEnum.WeeklyReview
  },
  [BannerEnum.EarnFreeShares]: {
    id: BannerEnum.EarnFreeShares,
    imageSource: BannerEnumToIconPath[BannerEnum.EarnFreeShares],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(211, 160, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/earn-free-shares")
  },
  [BannerEnum.WhySetUpMonthlyInvestment]: {
    id: BannerEnum.WhySetUpMonthlyInvestment,
    imageSource: BannerEnumToIconPath[BannerEnum.WhySetUpMonthlyInvestment],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(198, 207, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => eventEmitter.emit(EVENTS.promptToRepeatingInvestmentModal)
  },
  [BannerEnum.WhatIsDCA]: {
    id: BannerEnum.WhatIsDCA,
    imageSource: BannerEnumToIconPath[BannerEnum.WhatIsDCA],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(198, 207, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => eventEmitter.emit(EVENTS.promptToRepeatingInvestmentModal)
  },
  [BannerEnum.SendGift]: {
    id: BannerEnum.SendGift,
    imageSource: BannerEnumToIconPath[BannerEnum.SendGift],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/send-gift")
  },
  [BannerEnum.UnlockFreeShare]: {
    id: BannerEnum.UnlockFreeShare,
    imageSource: BannerEnumToIconPath[BannerEnum.UnlockFreeShare],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => eventEmitter.emit(EVENTS.portfolioBuyModal)
  },
  [BannerEnum.RedeemGift]: {
    id: BannerEnum.RedeemGift,
    imageSource: BannerEnumToIconPath[BannerEnum.RedeemGift],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(161, 255, 146, 0.58) 100%"
    },
    directAction: () => eventEmitter.emit(EVENTS.pendingGiftModal)
  },
  [BannerEnum.SweatcoinLifetimeGoldDeal]: {
    id: BannerEnum.SweatcoinLifetimeGoldDeal,
    imageSource: "/images/banners/sweatcoin_lifetime_gold.svg",
    directAction: (options?: { hasSubscription?: boolean }) => {
      options.hasSubscription
        ? window.location.replace("/investor/change-plan?select=paid_mid_lifetime_sweatcoin_1")
        : window.location.replace("/investor/select-plan?select=paid_mid_lifetime_sweatcoin_1");
    }
  },
  // Savings Promotion
  [BannerEnum.EarnInterestOnYourMoney]: {
    id: BannerEnum.EarnInterestOnYourMoney,
    imageSource: BannerEnumToIconPath[BannerEnum.EarnInterestOnYourMoney],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(198, 207, 255, 0.58) 100%"
    },
    opensModal: true,
    modalButtonAction: () => (window.location.href = "/investor/cash?selectedAccount=savings")
  },
  // Learning guides
  [BannerEnum.NewToInvesting]: {
    id: BannerEnum.NewToInvesting,
    imageSource: BannerEnumToIconPath[BannerEnum.NewToInvesting],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(211, 160, 255, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.InvestingInETFs]: {
    id: BannerEnum.InvestingInETFs,
    imageSource: BannerEnumToIconPath[BannerEnum.InvestingInETFs],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(255, 196, 60, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.HowToBuildPortfolio]: {
    id: BannerEnum.HowToBuildPortfolio,
    imageSource: BannerEnumToIconPath[BannerEnum.HowToBuildPortfolio],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(96, 234, 238, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.ShouldYouRebalance]: {
    id: BannerEnum.ShouldYouRebalance,
    imageSource: BannerEnumToIconPath[BannerEnum.ShouldYouRebalance],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(255, 224, 115, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.SaveInvestTrade]: {
    id: BannerEnum.SaveInvestTrade,
    imageSource: BannerEnumToIconPath[BannerEnum.SaveInvestTrade],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(246, 141, 176, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.GovBonds]: {
    id: BannerEnum.GovBonds,
    imageSource: BannerEnumToIconPath[BannerEnum.GovBonds],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.CorpBonds]: {
    id: BannerEnum.CorpBonds,
    imageSource: BannerEnumToIconPath[BannerEnum.CorpBonds],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(156, 172, 255, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.InvestingInGold]: {
    id: BannerEnum.InvestingInGold,
    imageSource: BannerEnumToIconPath[BannerEnum.InvestingInGold],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(255, 212, 60, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.RealEstateInvesting]: {
    id: BannerEnum.RealEstateInvesting,
    imageSource: BannerEnumToIconPath[BannerEnum.RealEstateInvesting],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(96, 234, 238, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.TaxWrapper]: {
    id: BannerEnum.TaxWrapper,
    imageSource: BannerEnumToIconPath[BannerEnum.TaxWrapper],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(161, 255, 146, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.InvestingInThematics]: {
    id: BannerEnum.InvestingInThematics,
    imageSource: BannerEnumToIconPath[BannerEnum.InvestingInThematics],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(255, 196, 60, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.HowToThinkOfRisk]: {
    id: BannerEnum.HowToThinkOfRisk,
    imageSource: BannerEnumToIconPath[BannerEnum.HowToThinkOfRisk],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(255, 146, 172, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.WhatAreInterestRates]: {
    id: BannerEnum.WhatAreInterestRates,
    imageSource: BannerEnumToIconPath[BannerEnum.WhatAreInterestRates],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(184, 189, 255, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.InvestingInESG]: {
    id: BannerEnum.InvestingInESG,
    imageSource: BannerEnumToIconPath[BannerEnum.InvestingInESG],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(161, 255, 146, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.WhenToStartInvesting]: {
    id: BannerEnum.WhenToStartInvesting,
    imageSource: BannerEnumToIconPath[BannerEnum.WhenToStartInvesting],
    styles: {
      gradientStart: "rgba(255, 255, 255, 0.58) 0%",
      gradientMiddle: "rgba(255, 255, 255, 0.58) 53.78%",
      gradientEnd: "rgba(96, 234, 238, 0.58) 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.GrowYourSavingsWithMoneyMarketFunds]: {
    id: BannerEnum.GrowYourSavingsWithMoneyMarketFunds,
    imageSource: BannerEnumToIconPath[BannerEnum.GrowYourSavingsWithMoneyMarketFunds],
    styles: {
      gradientStart: " #FFFFFF 0%",
      gradientMiddle: "#FFFFFF 60%",
      gradientEnd: "#91F3D8 100%"
    },
    opensModal: false,
    directAction: ({ learningGuideSlug }) =>
      (window.location.href = `/investor/learning-hub/guides/${learningGuideSlug}`),
    tag: BannerTagEnum.LearningGuide
  },
  [BannerEnum.PendingTransaction]: {
    id: BannerEnum.PendingTransaction,
    imageSource: BannerEnumToIconPath[BannerEnum.PendingTransaction],
    styles: {
      gradientStart: "rgba(117, 135, 232, 1) 100%",
      gradientMiddle: "rgba(117, 135, 232, 1) 100%",
      gradientEnd: "rgba(117, 135, 232, 1) 100%",
      color: "light"
    },
    directAction: () => (window.location.href = "/investor/pending-transactions")
  }
};
