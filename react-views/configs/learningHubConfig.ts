export enum ArticleContentTypeEnum {
  NEWS = "newsWealthyhub",
  ANALYST_INSIGHT = "analystInsights"
}

export type ArticleDataType = {
  createdAt: Date;
  title: string;
  key: string;
  tags: string[];
  contentHTML: string;
  previewImageURL: string;
  fullImageURL: string;
  readingTime: string;
  contentType: ArticleContentTypeEnum;
  // Only in analyst insights:
  publishedAt: Date;
  id?: string;
  analystInsightType?: AnalystInsightTypeEnum;
};

export type LearningGuideDataType = {
  key: string;
  title: string;
  description: string;
  guideIconURL: string;
  webCoverImageURL: string;
  slug: string;
  chapterCount: number;
};

export type LearningGuideWithChaptersDataType = LearningGuideDataType & {
  chapters: LearningGuideChapterType[];
};

export type LearningGuideChapterType = {
  key: string;
  title: string;
  body: string;
  slug: string;
};

export enum AnalystInsightTypeEnum {
  ANALYSIS = "ANALYSIS",
  WEEKLY_REVIEW = "WEEKLY_REVIEW",
  QUICK_TAKE = "QUICK_TAKE"
}

export const ANALYST_INSIGHT_ICON_CONFIG: Record<
  AnalystInsightTypeEnum,
  { color: string; backgroundColor: string; text: string; materialIcon: string }
> = {
  [AnalystInsightTypeEnum.ANALYSIS]: {
    color: "#905FB8",
    backgroundColor: "#F9F1FF",
    text: "Analysis",
    materialIcon: "query_stats"
  },
  [AnalystInsightTypeEnum.QUICK_TAKE]: {
    color: "#C78830",
    backgroundColor: "#FFEDBF",
    text: "Quick take",
    materialIcon: "electric_bolt"
  },
  [AnalystInsightTypeEnum.WEEKLY_REVIEW]: {
    color: "#228091",
    backgroundColor: "#DEF8FC",
    text: "Weekly review",
    materialIcon: "date_range"
  }
};

export const ARTICLE_ICON_CONFIG: Record<
  ArticleContentTypeEnum,
  { color: string; backgroundColor: string; text: string; materialIcon: string }
> = {
  [ArticleContentTypeEnum.ANALYST_INSIGHT]: {
    color: "#905FB8",
    backgroundColor: "#F9F1FF",
    text: "Analyst Insights",
    materialIcon: "query_stats"
  },
  [ArticleContentTypeEnum.NEWS]: {
    color: "#536AE3",
    backgroundColor: "#F1F3FD",
    text: "News",
    materialIcon: "event_available"
  }
};
