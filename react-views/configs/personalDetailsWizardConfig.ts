import { countriesConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";

export const animatedTransitions = {
  enterRight: "animated intro",
  enterLeft: "animated intro",
  exitRight: "animated intro",
  exitLeft: "animated intro",
  intro: "animated intro"
};

export const getStepsConfig = (country: countriesConfig.CountryCodesType) => {
  const { pageTitle, pageDescription } = ConfigUtil.getTaxResidencyConfig(country);

  return [
    {
      title: "Personal details",
      body: "Enter your name, date of birth and nationality."
    },
    {
      title: "Address",
      body: "Enter your country of residence and address."
    },
    {
      title: pageTitle,
      body: pageDescription
    },
    {
      title: "Additional info",
      body: "We need some additional info to get to know you a bit better."
    }
  ];
};
