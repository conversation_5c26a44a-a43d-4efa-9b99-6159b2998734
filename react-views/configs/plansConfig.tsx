import { plansConfig } from "@wealthyhood/shared-configs";

export const PLAN_BG_IMAGE_CONFIG: Record<plansConfig.PlanType, string> = {
  free: "url('/images/banners/free_bg.png')",
  paid_low: "url('/images/banners/paid_low_bg.png')",
  paid_mid: "url('/images/banners/paid_mid_bg.png')"
};

export const PLAN_ICON_IMAGE_CONFIG: Record<plansConfig.PlanType, string> = {
  free: "/images/icons/free-plan-icon.png",
  paid_low: "/images/icons/paid-low-plan-icon.png",
  paid_mid: "/images/icons/paid-mid-plan-icon.png"
};
