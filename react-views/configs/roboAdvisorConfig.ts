import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

export enum RoboAdvisorRiskLevelEnum {
  DEFENSIVE = "DEFENSIVE",
  CAUTIOUS = "CAUTIOUS",
  CONFIDENT = "CONFIDENT",
  GROWTH = "GROWTH"
}

type RoboAdvisorRiskLevelConfigType = {
  displayName: string;
  title: string;
  description: string;
  icon: string;
  assetId: investmentUniverseConfig.AssetType;
};

export const ROBO_ADVISOR_CONFIG: Record<RoboAdvisorRiskLevelEnum, RoboAdvisorRiskLevelConfigType> = {
  [RoboAdvisorRiskLevelEnum.DEFENSIVE]: {
    displayName: "Defensive",
    title: "Low risk & reward",
    description:
      "Minimising loss is the priority. Small movements up and down are acceptable, aiming to beat inflation and preserve capital.",
    icon: "/images/ready-made-portfolios/defensive.png",
    assetId: "readymade_lifestrategy_20_equity"
  },
  [RoboAdvisorRiskLevelEnum.CAUTIOUS]: {
    displayName: "Cautious",
    title: "Low-moderate risk & reward",
    description:
      "Protecting your investment is important while seeking gentle growth. Modest movements up and down are acceptable, aiming for steady returns.",
    icon: "/images/ready-made-portfolios/cautious.png",
    assetId: "readymade_lifestrategy_40_equity"
  },
  [RoboAdvisorRiskLevelEnum.CONFIDENT]: {
    displayName: "Confident",
    title: "Moderate risk & reward",
    description:
      "Balancing growth and protection equally. Regular movements up and down are acceptable, aiming for solid long-term growth.",
    icon: "/images/ready-made-portfolios/confident.png",
    assetId: "readymade_lifestrategy_60_equity"
  },
  [RoboAdvisorRiskLevelEnum.GROWTH]: {
    displayName: "Growth",
    title: "High risk & reward",
    description:
      "Maximising returns is the priority. The risk of big movements up and down are acceptable, aiming for the highest growth possible.",
    icon: "/images/ready-made-portfolios/growth.png",
    assetId: "readymade_lifestrategy_80_equity"
  }
};
