export const TOAST_DELAY = 3000;
export const TOAST_CONFIG = {
  success: {
    bgColor: "bg-light-success",
    textColor: "text-success",
    icon: "fas fa-check"
  },
  info: {
    bgColor: "bg-white",
    textColor: "text-primary",
    icon: "fas fa-exclamation-circle"
  },
  warning: {
    bgColor: "bg-light-warning",
    textColor: "text-warning",
    icon: "fas fa-exclamation-triangle"
  },
  error: {
    bgColor: "bg-light-danger",
    textColor: "text-danger",
    icon: "fas fa-octagon-exclamation"
  }
};
export const enum ToastTypeEnum {
  success = "success",
  info = "info",
  warning = "warning",
  error = "error"
}
