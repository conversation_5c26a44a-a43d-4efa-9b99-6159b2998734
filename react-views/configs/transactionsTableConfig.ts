import { OrderSideType } from "../../services/wealthkernelService";
import UpRightIcon from "../components/icons/upRightIcon";
import DownLeftIcon from "../components/icons/downLeftIcon";
import LayersIcon from "../components/icons/layersIcon";
import RepeatIcon from "../components/icons/repeatIcon";
import PoundIcon from "../components/icons/poundIcon";
import { TransactionCategoryType, TransactionStatusType } from "../../models/Transaction";
import ArrowToLeftIcon from "../components/icons/arrowToLeftIcon";
import { OrderStatusType } from "../../models/Order";
import SplitIcon from "../components/icons/splitIcon";

export const oldConfig: Record<
  TransactionCategoryType,
  { [key in "nameDisplay" | "icon" | "iconColorClass"]: any }
> = {
  DepositCashTransaction: {
    nameDisplay: "Top-up",
    icon: UpRightIcon,
    iconColorClass: "success"
  },
  WithdrawalCashTransaction: {
    nameDisplay: "Withdrawal",
    icon: DownLeftIcon,
    iconColorClass: "danger"
  },
  AssetTransaction: {
    nameDisplay: "Portfolio",
    icon: LayersIcon,
    iconColorClass: "info"
  },
  RebalanceTransaction: {
    nameDisplay: "Rebalance",
    icon: RepeatIcon,
    iconColorClass: "info"
  },
  DividendTransaction: {
    nameDisplay: "Dividend",
    icon: UpRightIcon,
    iconColorClass: "success"
  },
  AssetDividendTransaction: {
    nameDisplay: "Asset Dividend",
    icon: UpRightIcon,
    iconColorClass: "success"
  },
  ChargeTransaction: {
    nameDisplay: "Charge",
    icon: PoundIcon,
    iconColorClass: "success"
  },
  RevertRewardTransaction: {
    nameDisplay: "Revert Reward",
    icon: ArrowToLeftIcon,
    iconColorClass: "success"
  },
  CashbackTransaction: {
    nameDisplay: "Cashback",
    icon: UpRightIcon,
    iconColorClass: "success"
  },
  WealthyhoodDividendTransaction: {
    nameDisplay: "Wealthyhood Dividend",
    icon: UpRightIcon,
    iconColorClass: "success"
  },
  SavingsTopupTransaction: {
    nameDisplay: "Savings Topup",
    icon: PoundIcon,
    iconColorClass: "success"
  },
  SavingsWithdrawalTransaction: {
    nameDisplay: "Savings Withdrawal",
    icon: PoundIcon,
    iconColorClass: "success"
  },
  SavingsDividendTransaction: {
    nameDisplay: "Savings Interest",
    icon: PoundIcon,
    iconColorClass: "primary"
  },
  StockSplitTransaction: {
    nameDisplay: "Stock Split",
    icon: SplitIcon,
    iconColorClass: "primary"
  }
};

const config: {
  [key: string]: { [key in "nameDisplay" | "description" | "icon" | "iconColorClass"]: any };
} = {
  DepositCashTransaction: {
    nameDisplay: "Deposit",
    description: "",
    icon: "download",
    iconColorClass: "primary"
  },
  WithdrawalCashTransaction: {
    nameDisplay: "Withdraw",
    description: "",
    icon: "upload",
    iconColorClass: "danger"
  },
  AssetTransaction: {
    nameDisplay: "My Portfolio",
    description: "",
    icon: "data_usage",
    iconColorClass: "primary"
  },
  RebalanceTransaction: {
    nameDisplay: "My Portfolio",
    description: "Rebalance",
    icon: "sync",
    iconColorClass: "muted"
  },
  DividendTransaction: {
    nameDisplay: "Dividend",
    description: "Dividend",
    icon: "trending_up",
    iconColorClass: "primary"
  },
  ChargeTransaction: {
    nameDisplay: "Subscription",
    description: "",
    icon: "request_quote",
    iconColorClass: "secondary"
  },
  CashbackTransaction: {
    nameDisplay: "Cashback",
    description: "",
    icon: "payments",
    iconColorClass: "primary"
  },
  WealthyhoodDividendTransaction: {
    nameDisplay: "Dividend",
    description: "",
    icon: "payments",
    iconColorClass: "primary"
  },
  SavingsTopupTransaction: {
    nameDisplay: "Savings Topup",
    description: "",
    icon: "multiple_stop",
    iconColorClass: "primary"
  },
  SavingsWithdrawalTransaction: {
    nameDisplay: "Savings Withdrawal",
    description: "",
    icon: "multiple_stop",
    iconColorClass: "primary"
  },
  SavingsDividendTransaction: {
    nameDisplay: "Savings Interest",
    description: "",
    icon: "humidity_percentage",
    iconColorClass: "primary"
  }
};

export const STATUS_CONFIG: Record<TransactionStatusType, { color: string; label: string }> = {
  Cancelled: { color: "danger", label: "Cancelled" },
  Pending: { color: "warning", label: "Pending" },
  PendingReinvestment: { color: "warning", label: "Pending" },
  PendingTopUp: { color: "warning", label: "Pending" },
  PendingDeposit: { color: "warning", label: "Pending" },
  PendingWealthkernelCharge: { color: "warning", label: "Pending" },
  PendingGift: { color: "warning", label: "Pending" },
  Rejected: { color: "danger", label: "Rejected" },
  DepositFailed: { color: "danger", label: "Rejected" },
  Settled: { color: "success", label: "Settled" }
};

export const ORDER_SIDE_COLOR: Record<OrderSideType, string> = {
  Buy: "primary",
  Sell: "danger"
};
export const ORDER_STATUS_CONFIG: Record<OrderStatusType, { color: string; label: string }> = {
  Pending: {
    color: "warning",
    label: "Pending"
  },
  Matched: {
    color: "success",
    label: "Completed"
  },
  Settled: { color: "success", label: "Completed" },
  Cancelled: {
    color: "danger",
    label: "Cancelled"
  },
  Rejected: {
    color: "danger",
    label: "Rejected"
  }
};

export default config;
