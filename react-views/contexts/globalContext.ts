import React from "react";
import { ConfigCatFeatureStatusType } from "../../config/featuresConfig";
import { UserDocument } from "../../models/User";
import { localeConfig } from "@wealthyhood/shared-configs";

export type GlobalContextType = {
  featureFlags?: ConfigCatFeatureStatusType[];
  user?: UserDocument;
  locale?: localeConfig.LocaleType;
};

const initialGlobalContextData: GlobalContextType = {};

export const GlobalContext = React.createContext(initialGlobalContextData);
