import React from "react";
import { SavingsProductFeeDetailsWithIdType } from "../types/savings";
import { UserDocument } from "../../models/User";
import { localeConfig } from "@wealthyhood/shared-configs";

export type PlansContextType = {
  promotionalSavingsProductData?: SavingsProductFeeDetailsWithIdType;
  user?: UserDocument;
  locale?: localeConfig.LocaleType;
};

const initialPlansContextData: PlansContextType = {};

export const PlansContext = React.createContext(initialPlansContextData);
