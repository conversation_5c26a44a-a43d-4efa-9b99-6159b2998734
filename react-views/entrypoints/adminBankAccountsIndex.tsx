import React from "react";
import { hydrate } from "react-dom";
import AdminBankAccountsPage, { AdminBankAccountsPropsType } from "../pages/adminBankAccountsPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminBankAccountsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminBankAccountsPage, props), document.getElementById("root"));
