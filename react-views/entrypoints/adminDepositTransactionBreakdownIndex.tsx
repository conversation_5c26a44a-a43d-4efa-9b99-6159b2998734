import React from "react";
import { hydrate } from "react-dom";
import AdminDepositTransactionBreakdownPage, {
  AdminDepositTransactionBreakdownPropsType
} from "../pages/adminDepositTransactionBreakdownPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminDepositTransactionBreakdownPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminDepositTransactionBreakdownPage, props), document.getElementById("root"));
