import React from "react";
import { hydrate } from "react-dom";
import AdminEditPartyPage, { AdminEditPartyPropsType } from "../pages/adminEditPartyPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminEditPartyPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminEditPartyPage, props), document.getElementById("root"));
