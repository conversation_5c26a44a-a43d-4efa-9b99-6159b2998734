import React from "react";
import { hydrate } from "react-dom";
import AdminFailedAccountsPage, { AdminFailedAccountsPagePropsType } from "../pages/adminFailedAccountsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminFailedAccountsPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminFailedAccountsPage, props), document.getElementById("root"));
