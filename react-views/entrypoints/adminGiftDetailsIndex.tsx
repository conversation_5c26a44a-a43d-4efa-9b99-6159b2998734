import React from "react";
import { hydrate } from "react-dom";
import AdminGiftDetailsPage, { AdminGiftDetailsPropsType } from "../pages/adminGiftDetailsPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminGiftDetailsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminGiftDetailsPage, props), document.getElementById("root"));
