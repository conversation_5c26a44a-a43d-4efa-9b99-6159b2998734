import React from "react";
import { hydrate } from "react-dom";
import AdminGiftsPage, { AdminGiftsPropsType } from "../pages/adminGiftsPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminGiftsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminGiftsPage, props), document.getElementById("root"));
