import React from "react";
import { hydrate } from "react-dom";
import AdminOrderAnalyticsBreakdownPage, {
  AdminOrderAnalyticsBreakdownPropsType
} from "../pages/adminOrderAnalyticBreakdownPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminOrderAnalyticsBreakdownPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminOrderAnalyticsBreakdownPage, props), document.getElementById("root"));
