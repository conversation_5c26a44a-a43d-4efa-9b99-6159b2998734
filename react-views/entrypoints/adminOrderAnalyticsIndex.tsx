import React from "react";
import { hydrate } from "react-dom";
import AdminOrderAnalyticsPage, { AdminOrderAnalyticsPropsType } from "../pages/adminOrderAnalyticsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminOrderAnalyticsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminOrderAnalyticsPage, props), document.getElementById("root"));
