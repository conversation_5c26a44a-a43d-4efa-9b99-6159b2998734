import React from "react";
import { hydrate } from "react-dom";
import AdminOrderManagementPage, { AdminOrderManagementPropsType } from "../pages/adminOrderManagementPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminOrderManagementPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminOrderManagementPage, props), document.getElementById("root"));
