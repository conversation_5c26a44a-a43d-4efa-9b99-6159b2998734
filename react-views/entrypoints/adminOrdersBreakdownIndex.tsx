import React from "react";
import { hydrate } from "react-dom";
import AdminOrdersBreakdownPage, { AdminOrdersBreakdownPropsType } from "../pages/adminOrdersBreakdownPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminOrdersBreakdownPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminOrdersBreakdownPage, props), document.getElementById("root"));
