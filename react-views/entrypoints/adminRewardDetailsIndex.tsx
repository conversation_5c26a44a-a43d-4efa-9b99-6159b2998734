import React from "react";
import { hydrate } from "react-dom";
import AdminRewardDetailsPage, { AdminRewardDetailsPropsType } from "../pages/adminRewardDetailsPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminRewardDetailsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminRewardDetailsPage, props), document.getElementById("root"));
