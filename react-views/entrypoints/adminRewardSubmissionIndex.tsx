import React from "react";
import { hydrate } from "react-dom";
import AdminRewardSubmissionPage, { AdminRewardSubmissionPropsType } from "../pages/adminRewardSubmissionPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminRewardSubmissionPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminRewardSubmissionPage, props), document.getElementById("root"));
