import React from "react";
import { hydrate } from "react-dom";
import AdminRewardsPage, { AdminRewardsPropsType } from "../pages/adminRewardsPage";
// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminRewardsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminRewardsPage, props), document.getElementById("root"));
