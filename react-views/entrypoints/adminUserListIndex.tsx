import React from "react";
import { hydrate } from "react-dom";
import AdminUserListPage, { AdminUserListPropsType } from "../pages/adminUserListPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminUserListPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminUserListPage, props), document.getElementById("root"));
