import React from "react";
import { hydrate } from "react-dom";
import AdminWithdrawalTransactionBreakdownPage, {
  AdminWithdrawalTransactionBreakdownPropsType
} from "../pages/adminWithdrawalTransactionBreakdownPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminWithdrawalTransactionBreakdownPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminWithdrawalTransactionBreakdownPage, props), document.getElementById("root"));
