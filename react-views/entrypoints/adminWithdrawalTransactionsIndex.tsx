import React from "react";
import { hydrate } from "react-dom";
import AdminWithdrawalTransactionsPage, {
  AdminWithdrawalTransactionsPropsType
} from "../pages/adminWithdrawalTransactionsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AdminWithdrawalTransactionsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminWithdrawalTransactionsPage, props), document.getElementById("root"));
