import React from "react";
import { hydrate } from "react-dom";
import AssetDiscoveryPage, { AssetDiscoveryPropsType } from "../pages/assetDiscoveryPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AssetDiscoveryPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AssetDiscoveryPage, props), document.getElementById("root"));
