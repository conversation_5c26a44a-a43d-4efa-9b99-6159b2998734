import React from "react";
import { hydrate } from "react-dom";
import AdminAssetTransactionsPage, { AssetTransactionsAdminPropsType } from "../pages/adminAssetTransactionsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AssetTransactionsAdminPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminAssetTransactionsPage, props), document.getElementById("root"));
