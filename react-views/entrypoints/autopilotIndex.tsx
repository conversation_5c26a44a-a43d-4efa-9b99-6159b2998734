import React from "react";
import { hydrate } from "react-dom";
import AutopilotPage, { AutopilotPropsType } from "../pages/autopilotPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: AutopilotPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AutopilotPage, props), document.getElementById("root"));
