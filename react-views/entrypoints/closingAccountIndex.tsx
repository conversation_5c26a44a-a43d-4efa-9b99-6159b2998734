import React from "react";
import { hydrate } from "react-dom";
import ClosingAccountPage, { ClosingAccountPropsType } from "../pages/closingAccountPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: ClosingAccountPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(ClosingAccountPage, props), document.getElementById("root"));
