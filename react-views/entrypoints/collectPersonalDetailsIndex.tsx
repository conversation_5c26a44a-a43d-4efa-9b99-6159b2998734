import React from "react";
import { hydrate } from "react-dom";
import CollectPersonalDetailsPage, {
  CollectPersonalDetailsPagePropsType
} from "../pages/collectPersonalDetailsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: CollectPersonalDetailsPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(CollectPersonalDetailsPage, props), document.getElementById("root"));
