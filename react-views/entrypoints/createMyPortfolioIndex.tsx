import React from "react";
import { hydrate } from "react-dom";
import CreateMyPortfolioPage, { CreateMyPortfolioPropsType } from "../pages/createMyPortfolioPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: CreateMyPortfolioPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(CreateMyPortfolioPage, props), document.getElementById("root"));
