import React from "react";
import { hydrate } from "react-dom";
import AdminDepositTransactionsPage, {
  DepositTransactionsAdminPropsType
} from "../pages/adminDepositTransactionsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: DepositTransactionsAdminPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(AdminDepositTransactionsPage, props), document.getElementById("root"));
