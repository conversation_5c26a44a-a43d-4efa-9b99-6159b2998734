import React from "react";
import { hydrate } from "react-dom";
import EmailDisposablePage, { EmailDisposablePropsType } from "../pages/emailDisposablePage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: EmailDisposablePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(EmailDisposablePage, props), document.getElementById("root"));
