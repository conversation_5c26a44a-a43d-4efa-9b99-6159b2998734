import React from "react";
import { hydrate } from "react-dom";
import EmailVerifiedPage, { EmailVerifiedPropsType } from "../pages/emailVerifiedPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: EmailVerifiedPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(EmailVerifiedPage, props), document.getElementById("root"));
