import React from "react";
import { hydrate } from "react-dom";
import IdVerificationPollingPage, { IdVerificationPollingPropsType } from "../pages/idVerificationPollingPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: IdVerificationPollingPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(IdVerificationPollingPage, props), document.getElementById("root"));
