import React from "react";
import { hydrate } from "react-dom";
import InvestmentSuccessPage, { InvestmentSuccessPropsType } from "../pages/investmentSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: InvestmentSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(InvestmentSuccessPage, props), document.getElementById("root"));
