import React from "react";
import { hydrate } from "react-dom";
import InvestmentsPage, { DashboardPagePropsType } from "../pages/investmentsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: DashboardPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(InvestmentsPage, props), document.getElementById("root"));
