import React from "react";
import { hydrate } from "react-dom";
import { PagePropsType } from "../types/page";
import InvestorAccountDetailsPage from "../pages/investorAccountDetailsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(InvestorAccountDetailsPage, props), document.getElementById("root"));
