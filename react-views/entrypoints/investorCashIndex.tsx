import React from "react";
import { hydrate } from "react-dom";
import InvestorCashPage, { InvestorCashPagePropsType } from "../pages/investorCashPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: InvestorCashPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(InvestorCashPage, props), document.getElementById("root"));
