import React from "react";
import { hydrate } from "react-dom";
import InviteFriendPage, { InviteFriendPropsType } from "../pages/inviteFriendPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: InviteFriendPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(InviteFriendPage, props), document.getElementById("root"));
