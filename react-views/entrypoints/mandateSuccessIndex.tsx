import React from "react";
import { hydrate } from "react-dom";
import MandateSuccessPage, { MandateSuccessPropsType } from "../pages/mandateSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: MandateSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(MandateSuccessPage, props), document.getElementById("root"));
