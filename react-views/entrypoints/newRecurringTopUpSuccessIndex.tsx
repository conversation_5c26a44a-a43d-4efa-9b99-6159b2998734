import React from "react";
import { hydrate } from "react-dom";
import NewRecurringTopUpSuccessPage, {
  NewRecurringTopUpSuccessPropsType
} from "../pages/newRecurringTopUpSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: NewRecurringTopUpSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(NewRecurringTopUpSuccessPage, props), document.getElementById("root"));
