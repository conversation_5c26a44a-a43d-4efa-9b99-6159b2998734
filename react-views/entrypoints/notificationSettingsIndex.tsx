import React from "react";
import { hydrate } from "react-dom";
import NotificationSettingsPage, { NotificationSettingsPropsType } from "../pages/notificationSettingsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: NotificationSettingsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(NotificationSettingsPage, props), document.getElementById("root"));
