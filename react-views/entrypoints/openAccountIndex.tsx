import React from "react";
import { hydrate } from "react-dom";
import OpenAccountPage, { OpenAccountPagePropsType } from "../pages/openAccountPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: OpenAccountPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(OpenAccountPage, props), document.getElementById("root"));
