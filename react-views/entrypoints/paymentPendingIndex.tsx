import React from "react";
import { hydrate } from "react-dom";
import PaymentPendingPage, { PaymentPendingPropsType } from "../pages/paymentPendingPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PaymentPendingPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PaymentPendingPage, props), document.getElementById("root"));
