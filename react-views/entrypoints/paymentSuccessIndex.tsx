import React from "react";
import { hydrate } from "react-dom";
import PaymentSuccessPage, { PaymentSuccessPropsType } from "../pages/paymentSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PaymentSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PaymentSuccessPage, props), document.getElementById("root"));
