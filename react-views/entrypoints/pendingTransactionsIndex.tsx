import React from "react";
import { hydrate } from "react-dom";
import PendingTransactionsPage, { PendingTransactionsPropsType } from "../pages/pendingTransactionsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PendingTransactionsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PendingTransactionsPage, props), document.getElementById("root"));
