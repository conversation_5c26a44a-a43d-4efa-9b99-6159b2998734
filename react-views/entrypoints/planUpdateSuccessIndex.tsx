import React from "react";
import { hydrate } from "react-dom";
import PlanUpdateSuccessPage, { PlanUpdateSuccessPropsType } from "../pages/planUpdateSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PlanUpdateSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PlanUpdateSuccessPage, props), document.getElementById("root"));
