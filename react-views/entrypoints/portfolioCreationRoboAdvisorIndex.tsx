import React from "react";
import { hydrate } from "react-dom";
import PortfolioCreationRoboAdvisorPage, {
  PortfolioCreationRoboAdvisorPropsType
} from "../pages/portfolioCreationRoboAdvisorPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PortfolioCreationRoboAdvisorPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioCreationRoboAdvisorPage, props), document.getElementById("root"));
