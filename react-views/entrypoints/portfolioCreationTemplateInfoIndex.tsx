import React from "react";
import { hydrate } from "react-dom";
import PortfolioCreationTemplateInfoPage, {
  PoPortfolioCreationTemplateInfoPropsType
} from "../pages/portfolioCreationTemplateInfoPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PoPortfolioCreationTemplateInfoPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioCreationTemplateInfoPage, props), document.getElementById("root"));
