import React from "react";
import { hydrate } from "react-dom";
import PortfolioPersonalisationPage, {
  PortfolioPersonalisationPropsType
} from "../pages/portfolioPersonalisationPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PortfolioPersonalisationPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioPersonalisationPage, props), document.getElementById("root"));
