import React from "react";
import { hydrate } from "react-dom";
import PortfolioSetupAsStepPage, { PortfolioSetupAsStepPagePropsType } from "../pages/portfolioSetupAsStepPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PortfolioSetupAsStepPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioSetupAsStepPage, props), document.getElementById("root"));
