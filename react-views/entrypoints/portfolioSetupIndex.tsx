import React from "react";
import { hydrate } from "react-dom";
import PortfolioSetupPage, { PortfolioSetupPropsType } from "../pages/portfolioSetupPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PortfolioSetupPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioSetupPage, props), document.getElementById("root"));
