import React from "react";
import { hydrate } from "react-dom";
import PortfolioTargetPage, { PortfolioTargetPagePropsType } from "../pages/portfolioTargetPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PortfolioTargetPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(PortfolioTargetPage, props), document.getElementById("root"));
