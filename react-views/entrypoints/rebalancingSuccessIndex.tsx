import React from "react";
import { hydrate } from "react-dom";
import RebalancingSuccessPage, { RebalancingSuccessPropsType } from "../pages/rebalancingSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: RebalancingSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(RebalancingSuccessPage, props), document.getElementById("root"));
