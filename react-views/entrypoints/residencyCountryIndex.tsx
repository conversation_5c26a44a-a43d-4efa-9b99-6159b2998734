import React from "react";
import { hydrate } from "react-dom";
import ResidencyCountryPage, { ResidencyCountryPropsType } from "../pages/residencyCountryPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: ResidencyCountryPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(ResidencyCountryPage, props), document.getElementById("root"));
