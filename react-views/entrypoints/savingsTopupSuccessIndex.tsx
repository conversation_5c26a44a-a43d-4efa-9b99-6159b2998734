import React from "react";
import { hydrate } from "react-dom";
import SavingsTopupSuccessPage, { SavingsTopupSuccessPagePropsType } from "../pages/savingsTopupSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: SavingsTopupSuccessPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(SavingsTopupSuccessPage, props), document.getElementById("root"));
