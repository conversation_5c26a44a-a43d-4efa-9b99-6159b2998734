import React from "react";
import { hydrate } from "react-dom";
import SavingsWithdrawalSuccessPage, {
  SavingsWithdrawalSuccessPagePropsType
} from "../pages/savingsWithdrawalSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: SavingsWithdrawalSuccessPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(SavingsWithdrawalSuccessPage, props), document.getElementById("root"));
