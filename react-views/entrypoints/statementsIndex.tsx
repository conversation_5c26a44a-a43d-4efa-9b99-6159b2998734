import React from "react";
import { hydrate } from "react-dom";
import StatementsPage, { StatementsPropsType } from "../pages/statementsPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: StatementsPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(StatementsPage, props), document.getElementById("root"));
