import React from "react";
import { hydrate } from "react-dom";
import DailySummaryPage, { DailySummaryPagePropsType } from "../pages/dailySummaryPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: DailySummaryPagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(DailySummaryPage, props), document.getElementById("root"));
