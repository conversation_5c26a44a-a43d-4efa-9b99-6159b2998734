import React from "react";
import { hydrate } from "react-dom";
import TransactionCancellationSuccessPage from "../pages/transactionCancellationSuccessPage";
import { PagePropsType } from "../types/page";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: PagePropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(TransactionCancellationSuccessPage, props), document.getElementById("root"));
