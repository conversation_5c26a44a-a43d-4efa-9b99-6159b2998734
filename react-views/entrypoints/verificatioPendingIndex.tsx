import React from "react";
import { hydrate } from "react-dom";
import VerificationPendingPage, { VerificationPendingPropsType } from "../pages/verificationPendingPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: VerificationPendingPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(VerificationPendingPage, props), document.getElementById("root"));
