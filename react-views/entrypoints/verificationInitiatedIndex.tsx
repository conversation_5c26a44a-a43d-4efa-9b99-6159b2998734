import React from "react";
import { hydrate } from "react-dom";
import VerificationInitiatedPage, { VerificationInitiatedPropsType } from "../pages/verificationInitiatedPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: VerificationInitiatedPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(VerificationInitiatedPage, props), document.getElementById("root"));
