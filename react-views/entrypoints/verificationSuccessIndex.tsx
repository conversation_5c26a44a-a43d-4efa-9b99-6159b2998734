import React from "react";
import { hydrate } from "react-dom";
import VerificationSuccessPage, { VerificationSuccessPropsType } from "../pages/verificationSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: VerificationSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(VerificationSuccessPage, props), document.getElementById("root"));
