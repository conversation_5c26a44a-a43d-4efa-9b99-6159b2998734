import React from "react";
import { hydrate } from "react-dom";
import WithdrawalSuccessPage, { WithdrawalSuccessPropsType } from "../pages/withdrawalSuccessPage";

// Workaround to avoid getting warning that __REACT__SSR__DATA__ doesn't exist on window
const anyWindow: any = window;
const props: WithdrawalSuccessPropsType = anyWindow.__REACT__SSR__DATA__;

hydrate(React.createElement(WithdrawalSuccessPage, props), document.getElementById("root"));
