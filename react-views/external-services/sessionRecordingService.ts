import { UserDocument } from "../../models/User";
import LogRocket from "logrocket";
import { datadogRum } from "@datadog/browser-rum";
import { isInvestor } from "../utils/userUtil";
import { envIsProd } from "../../utils/environmentUtil";

export type RecordingProvidersConfigType = {
  DataDog?: boolean;
  LogRocket?: boolean;
};

export default class SessionRecordingService {
  /**
   * PUBLIC METHODS
   */
  public static record(
    user: UserDocument,
    providers: RecordingProvidersConfigType = { DataDog: true, LogRocket: true }
  ) {
    if (envIsProd() && isInvestor(user)) {
      if (providers.DataDog) {
        SessionRecordingService._initDatadogRecording(user);
      }
      if (providers.LogRocket) {
        SessionRecordingService._initLogRocketRecording(user);
      }
    }
  }

  /**
   * PRIVATE METHODS
   */
  public static _initDatadogRecording(user: UserDocument) {
    datadogRum.init({
      applicationId: `${process.env.DATADOG_APP_ID}`,
      clientToken: `${process.env.DATADOG_CLIENT_TOKEN}`,
      site: `${process.env.DATADOG_SITE}`,
      service: `${process.env.DATADOG_SERVICE}`,
      env: `${process.env.NODE_ENV}`,
      sampleRate: 100,
      premiumSampleRate: 100,
      trackInteractions: true,
      defaultPrivacyLevel: "allow"
    });

    datadogRum.setUser({
      name: user.firstName ? `${user.firstName} ${user.lastName}` : "",
      email: user.email,
      type: "investor"
    });
    datadogRum.startSessionReplayRecording();
  }

  private static _initLogRocketRecording(user: UserDocument) {
    LogRocket.init(process.env.LOGROCKET_ID);
    LogRocket.identify(user.id, {
      name: user.firstName ? `${user.firstName} ${user.lastName}` : "",
      email: user.email,
      type: "investor"
    });
  }
}
