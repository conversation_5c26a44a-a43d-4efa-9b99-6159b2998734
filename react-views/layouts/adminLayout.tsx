import React from "react";
import MainContent from "../components/mainContent";
import MainContentBody from "../components/mainContentBody";
import MainContentFooter from "../components/mainContentFooter";
import MainContentHeader from "../components/mainContentHeader";
import MainHeaderMobile from "../components/mainHeaderMobile";
import MainLeftAsideAdmin from "../components/mainLeftAside";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";
import MainLeftAsideAdminOptions from "../components/mainLeftAsideAdminOptions";

class AdminLayout extends React.Component<PagePropsType> {
  render(): JSX.Element {
    const { children, activePage, subtitle, title, user } = this.props;

    return (
      <>
        <BaseLayout user={user}>
          <MainHeaderMobile />
          <MainLeftAsideAdmin email={user.email}>
            <MainLeftAsideAdminOptions activePage={activePage} />
          </MainLeftAsideAdmin>
          <MainContent asideExists>
            <MainContentHeader subTitle={subtitle} title={title} />
            <MainContentBody>{children}</MainContentBody>
            <MainContentFooter />
          </MainContent>
        </BaseLayout>
      </>
    );
  }
}

export default AdminLayout;
