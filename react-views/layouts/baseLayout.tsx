import React from "react";
import * as Sentry from "@sentry/react";
import { Integrations } from "@sentry/tracing";
import { PagePropsType } from "../types/page";
import ToastWrapper from "../components/toastWrapper";
import QRCodeBanner from "../components/qrCodeBanner";
import Cookies from "universal-cookie";
import { isAdmin } from "../utils/userUtil";
import SessionRecordingService from "../external-services/sessionRecordingService";
import { envIsDev, envIsProd } from "../../utils/environmentUtil";
import axios from "axios";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import ConfigUtil from "../../utils/configUtil";

/**
 * App Opened event emission interval in seconds. We want the event to be sent every 60 minutes.
 */
const APP_OPEN_EVENT_INTERVAL = 60 * 60;

type StateType = {
  showQRCodeBanner: boolean;
};

class BaseLayout extends React.Component<PagePropsType, StateType> {
  constructor(props: PagePropsType) {
    super(props);
    this.state = {
      showQRCodeBanner: false // We'll show the QR code banner only if the user has not dismissed it in the current session
    };

    const { user } = props;
    SessionRecordingService.record(user);
  }

  componentDidMount = (): void => {
    Sentry.init({
      enabled: !envIsDev(),
      dsn: process.env.SENTRY_CLIENT_DSN,
      environment: process.env.NODE_ENV,
      integrations: [new Integrations.BrowserTracing()],
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
      tracesSampleRate: 1.0
    });
    Sentry.configureScope((scope) => {
      const { user } = this.props;
      if (user) {
        scope.setUser({ email: user.email });
      }
    });

    const cookies = new Cookies();
    const hasDismissedBanner = cookies.get("hasDismissedBanner") as boolean;
    if (!hasDismissedBanner && envIsProd()) {
      this._showQRCodeBanner();
    }

    const isAppOpenedEventEmitted = cookies.get("isAppOpenedEventEmitted") as boolean;
    if (!isAppOpenedEventEmitted) {
      // No need to catch error, because we always return 204
      (async () => {
        await axios.post("/events", { eventId: "appOpened" });
        cookies.set("isAppOpenedEventEmitted", true, {
          path: "/",
          maxAge: APP_OPEN_EVENT_INTERVAL,
          httpOnly: false,
          domain: envIsProd() ? ".wealthyhood.com" : "localhost",
          sameSite: "strict",
          secure: true
        });
      })();
    }
  };

  private _showQRCodeBanner = (): void => {
    const { user } = this.props;

    if (!isAdmin(user)) {
      this.setState({
        showQRCodeBanner: true
      });
    }
  };

  private _hideQRCodeBanner = (): void => {
    const cookies = new Cookies();
    cookies.set("hasDismissedBanner", true, {
      path: "/",
      maxAge: 60 * 60 * 24, // in secs - 1 day
      httpOnly: false,
      domain: ".wealthyhood.com",
      sameSite: "strict",
      secure: true
    });

    this.setState({
      showQRCodeBanner: false
    });
  };

  render(): JSX.Element {
    const { children, featureFlags, user } = this.props;
    const { showQRCodeBanner } = this.state;

    const context: GlobalContextType = {
      featureFlags,
      user,
      locale: ConfigUtil.getDefaultUserLocale(user.residencyCountry)
    };

    return (
      <GlobalContext.Provider value={context}>
        <ToastWrapper />
        {children}
        <QRCodeBanner show={showQRCodeBanner} handleClose={(): void => this._hideQRCodeBanner()} />
      </GlobalContext.Provider>
    );
  }
}

export default BaseLayout;
