import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";
import ModalsWrapper from "../components/modalsWrapper";

export type PropsType = {
  enableModals?: boolean;
} & PagePropsType;

class CenteredOnboardingLayout extends React.Component<PropsType> {
  render(): JSX.Element {
    const { enableModals, children } = this.props;

    return (
      <BaseLayout activePage={this.props.activePage} user={this.props.user}>
        {enableModals && <ModalsWrapper user={this.props.user} />}

        <div
          className="container-fluid p-0 bg-primary d-sm-block d-flex flex-column"
          style={{ minHeight: "100vh", height: "100vh" }}
        >
          <div className="row mt-md-5 ms-md-5 mb-md-0 mt-4 mb-4 align-self-top">
            <span className="d-block  text-md-start text-center">
              <img alt="" src="/images/icons/wealthyhood-logo.svg" />
            </span>
          </div>
          {children}
        </div>
      </BaseLayout>
    );
  }
}

export default CenteredOnboardingLayout;
