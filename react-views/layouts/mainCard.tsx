import React from "react";

type PropsType = {
  maxWidth?: string;
  className?: string;
  customStyle?: string;
  marginBottom?: string;
  disableMarginBottom?: boolean;
};

class MainCard extends React.Component<PropsType> {
  render(): JSX.Element {
    const { children, maxWidth, className = "", marginBottom, disableMarginBottom, customStyle } = this.props;

    const marginClass = disableMarginBottom ? "" : "mb-md-5 mb-0";

    return (
      <div
        className={`wh-card-body bg-white p-md-5 py-4 px-3 ${marginClass} ${className}`}
        style={{ maxWidth: maxWidth ?? "100%", marginBottom: `${marginBottom} !important ${customStyle}` ?? "" }}
      >
        {children}
      </div>
    );
  }
}

export default MainCard;
