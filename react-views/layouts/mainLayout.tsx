import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";
import MainLeftAsideInvestorOptions from "../components/mainLeftAsideInvestorOptions";
import TopbarAccountOption from "../components/topbarAccountOption";
import Intercom from "react-intercom";
import IntercomUtil from "../utils/intercomUtil";

type PropsType = { expand?: boolean } & PagePropsType;
type StateType = { showSideMenu: boolean; slideDirection: "in" | "out" };

class MainLayout extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      slideDirection: "in",
      showSideMenu: false
    };
  }

  private _showSideMenu() {
    this.setState({ showSideMenu: true });
  }

  private _hideSideMenu() {
    this.setState({ slideDirection: "out" });
    setTimeout(() => this.setState({ showSideMenu: false, slideDirection: "in" }), 500);
  }

  render(): JSX.Element {
    const { children, activePage, user, expand, featureFlags } = this.props;
    const { slideDirection, showSideMenu } = this.state;

    return (
      <BaseLayout user={user} featureFlags={featureFlags}>
        <div className="wh-main-header fixed-top bg-white w-100 d-none d-sm-block">
          <div className="container-fluid h-100">
            <div className="row h-100">
              <div className="col-3 col-xxl-4 pe-5 align-self-center text-end">
                <img className="wh-header-logo" src="/images/icons/wealthyhood-logo-dark.svg" />
              </div>
              <div className="col-6 col-xxl-4" />
              <div className="col-3 col-xxl-4 align-self-center">
                {user.isVerified && <TopbarAccountOption activePage={activePage} />}
              </div>
            </div>
          </div>
        </div>

        {/* Dummy spacer to solve overlapping issue with fixed bottom nav buttons */}
        <div className="row m-0 d-none d-sm-block" style={{ height: "90px" }} />
        <div className="container-fluid bg-main d-none d-sm-block">
          <div className="row justify-content-center m-0">
            <div className="col-md-3 col-xxl-4 col-4 wh-side-bar pe-5 fixed-top">
              <div className="d-flex flex-column justify-content-end ">
                {user.isVerified && <MainLeftAsideInvestorOptions activePage={activePage} />}
              </div>
            </div>
          </div>
        </div>
        <div className="container-fluid bg-main p-0 m-0" onClick={() => IntercomUtil.hide()}>
          {/* Dummy spacer to solve overlapping issue with fixed top nav */}
          <div className="row m-0 d-block d-sm-none" style={{ height: "90px" }} />

          {/* Mobile Top Bar */}
          <div className="d-flex flex-row m-0 px-3 px-md-5 p-3 mb-3 bg-white shadow-sm fixed-top d-block d-sm-none">
            <span>
              <img alt="logo" src="/images/icons/wealthyhood-logo-dark.svg" />
            </span>
            {user.isVerified && (
              <div className="d-flex flex-row-reverse w-100 p-0">
                <i
                  className="d-flex material-symbols-outlined justify-content-center text-muted align-self-center cursor-pointer"
                  style={{ fontSize: "36px" }}
                  onClick={() => this._showSideMenu()}
                >
                  menu
                </i>
              </div>
            )}
          </div>
          {/* End Mobile Top Bar */}

          <div className="row justify-content-auto m-0">
            <div className="col-md-3 col-xxl-4 col-sm-0" />
            <div className={`${expand ? "col-xxl-8 col-md-9" : "col-xxl-4 col-md-6 col-sm-10"} px-0 pt-md-5 pt-0`}>
              <div className="row m-0 p-0 px-xl-0 px-md-4 px-0 py-md-0 justify-content-center">
                <div className="container-fluid p-0 m-0">{children}</div>
              </div>
            </div>
            {!expand && <div className="col-md-3 col-xxl-4 col-sm-0" />}
          </div>
        </div>
        {/* Mobile Side Menu */}
        {showSideMenu && (
          <div
            className="row m-0 vh-100 justify-content-end fixed-top etf-side-mask"
            style={{ overflowY: "scroll" }}
            onScroll={(e) => {
              e.stopPropagation();
            }}
          >
            <div className="col-sm-6 col-4 cursor-pointer" onClick={() => this._hideSideMenu()} />
            <div className={`col-sm-6 col-8 p-3 p-md-5 bg-light position-relative slide-${slideDirection}`}>
              <div className="d-flex ps-3 mt-2 mb-4">
                <i
                  className="d-flex material-symbols-outlined justify-content-center text-primary align-self-center cursor-pointer me-3"
                  style={{ fontSize: "28px" }}
                  onClick={() => this._hideSideMenu()}
                >
                  arrow_back
                </i>
              </div>
              <MainLeftAsideInvestorOptions activePage={activePage} />
            </div>
          </div>
        )}
        {/* End Mobile Side Menu */}

        {/* Intercom App Launcher */}
        <Intercom
          appID={process.env.INTERCOM_APP_ID}
          apiKey={process.env.INTERCOM_WEB_VERIFICATION_SECRET}
          name={user.firstName}
          user_id={user.id}
          email={user.email}
          alignment={"right"}
        />
        {/* End Intercom App Launcher */}
      </BaseLayout>
    );
  }
}

export default MainLayout;
