import React from "react";
import { PagePropsType } from "../types/page";
import MainContent from "../components/mainContent";
import OnboardingHeaderMobile from "../components/onboardingHeaderMobile";
import OnboardingContentHeader from "../components/onboardingContentHeader";
import OnboardingContentBody from "../components/onboardingContentBody";
import OnboardingContentFooter from "../components/onboardingContentFooter";
import BaseLayout from "./baseLayout";
import SessionRecordingService from "../external-services/sessionRecordingService";

class OnboardingLayout extends React.Component<PagePropsType> {
  constructor(props: PagePropsType) {
    super(props);

    const { user } = props;
    SessionRecordingService.record(user);
  }

  componentDidMount() {
    // add listener to force onboarding pages to reload on browser back button,
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceEntry/entryType
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceNavigationTiming
    window.addEventListener("pageshow", function (event) {
      const entriesList = performance.getEntriesByType("navigation");
      const navigationType = entriesList.length > 0 ? (entriesList[0] as PerformanceNavigationTiming).type : null;
      const historyTraversal = event.persisted || navigationType == "back_forward";
      if (historyTraversal) {
        window.location.reload();
      }
    });
  }

  render(): JSX.Element {
    const { children, title } = this.props;
    return (
      <BaseLayout user={this.props.user}>
        <OnboardingHeaderMobile />
        <MainContent>
          <OnboardingContentHeader />
          <OnboardingContentBody title={title}>{children}</OnboardingContentBody>
          <OnboardingContentFooter />
        </MainContent>
      </BaseLayout>
    );
  }
}

export default OnboardingLayout;
