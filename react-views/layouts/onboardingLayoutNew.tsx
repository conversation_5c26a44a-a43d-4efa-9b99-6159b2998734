import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";

export type OnboardingLayoutPropsType = {
  alignment: "center" | "start";
  leftSideChild: JSX.Element;
} & PagePropsType;

type StateType = {
  isMobileView: boolean;
};

class OnboardingLayoutNew extends React.Component<OnboardingLayoutPropsType, StateType> {
  constructor(props: OnboardingLayoutPropsType) {
    super(props);
    this.state = {
      isMobileView: false
    };
  }

  componentDidMount() {
    window.matchMedia("(max-width: 1190px)").addListener((e) => this.setState({ isMobileView: e.matches }));

    // add listener to force onboarding pages to reload on browser back button,
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceEntry/entryType
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceNavigationTiming
    window.addEventListener("pageshow", function (event) {
      const entriesList = performance.getEntriesByType("navigation");
      const navigationType = entriesList.length > 0 ? (entriesList[0] as PerformanceNavigationTiming).type : null;
      const historyTraversal = event.persisted || navigationType == "back_forward";
      if (historyTraversal) {
        window.location.reload();
      }
    });
  }

  render(): JSX.Element {
    const { leftSideChild, children, alignment } = this.props;
    const { isMobileView } = this.state;

    return (
      <BaseLayout user={this.props.user} featureFlags={this.props.featureFlags}>
        <div className="container-fluid p-0 bg-white">
          <div className="row justify-content-center m-0">
            {isMobileView ? (
              <>
                <div className="bg-primary p-0 text-light d-block d-sm-none">
                  <div className="row text-center mt-3">
                    <span>
                      <img alt="logo" src="/images/icons/wealthyhood-logo.svg" />
                    </span>
                  </div>
                  <div className="row justify-content-center m-0">
                    <div className="col align-self-center p-md-5 p-5 pt-3">{leftSideChild}</div>
                  </div>
                </div>
                {/* <!-- /Mobile --> */}
                {/* <!-- /Mobile --> */}
                <div
                  className={
                    "row p-0 m-0 bg-primary d-block d-sm-none onboarding-container align-self-" + alignment
                  }
                  style={{ overflowY: "hidden" }}
                >
                  <div className="col justify-content-center p-3 bg-white rounded-top w-100 h-100 m-0">
                    {/* <!-- Main Content --> */}
                    {children}
                    {/* <!-- /Main Content --> */}
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* <!-- Desktop mode fixed left stepper --> */}
                <div className="col-md-7 p-0 bg-primary text-light vh-100 fixed-top d-none d-sm-block">
                  <div className="row mt-5 ms-5">
                    <span>
                      <img alt="logo" src="/images/icons/wealthyhood-logo.svg" />
                    </span>
                  </div>
                  <div className="row h-100 justify-content-center m-0">
                    <div className="col-md-7 align-self-center p-0">{leftSideChild}</div>
                  </div>
                </div>
                {/* <!-- This is a dummy div behind fixed-top div above to keep spacing--> */}
                <div className="col-md-7 p-0 text-light vh-100 d-none d-sm-block" />
                {/* <!-- /Desktop mode fixed left stepper --> */}
                <div
                  className={
                    "col-md-5 col p-md-5 p-3 onboarding-container d-none d-sm-block align-self-" + alignment
                  }
                >
                  {/* <!-- Main Content --> */}
                  {children}
                  {/* <!-- /Main Content --> */}
                </div>
              </>
            )}
          </div>
        </div>
      </BaseLayout>
    );
  }
}

export default OnboardingLayoutNew;
