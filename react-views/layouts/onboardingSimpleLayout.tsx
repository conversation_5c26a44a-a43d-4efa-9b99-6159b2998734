import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";
import SessionRecordingService from "../external-services/sessionRecordingService";

export type OnboardingLayoutPropsType = {
  alignment: "center" | "start";
  title: string;
  body: string;
} & PagePropsType;

class OnboardingSimpleLayout extends React.Component<OnboardingLayoutPropsType> {
  constructor(props: OnboardingLayoutPropsType) {
    super(props);

    const { user } = props;
    SessionRecordingService.record(user);
  }

  componentDidMount() {
    // add listener to force onboarding pages to reload on browser back button,
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceEntry/entryType
    // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceNavigationTiming
    window.addEventListener("pageshow", function (event) {
      const entriesList = performance.getEntriesByType("navigation");
      const navigationType: NavigationType =
        entriesList.length > 0 ? (entriesList[0] as PerformanceNavigationTiming).type : null;
      const historyTraversal = event.persisted || navigationType == "back_forward";
      if (historyTraversal) {
        window.location.reload();
      }
    });
  }

  render(): JSX.Element {
    const { children, title, body, alignment } = this.props;

    return (
      <BaseLayout user={this.props.user}>
        <div className="container-fluid p-0">
          <div className="row justify-content-center m-0">
            {/* <!-- Desktop mode fixed left stepper --> */}
            <div className="col-md-7 p-0 bg-primary text-light vh-100 fixed-top d-none d-sm-block">
              <div className="row mt-5 ms-5">
                <span>
                  <img alt="logo" src="/images/icons/wealthyhood-logo.svg" />
                </span>
              </div>
              <div className="row h-100 justify-content-center m-0">
                <div className="col-md-7 align-self-center p-0">
                  <div className="row mt-3">
                    <h1 className="fw-bold mb-3 p-0">{title}</h1>
                    <h4 className="p-0">{body}</h4>
                  </div>
                </div>
              </div>
            </div>
            {/* <!-- This is a dummy div behind fixed-top div above to keep spacing--> */}
            <div className="col-md-7 p-0 text-light vh-100 d-none d-sm-block" />
            {/* <!-- /Desktop mode fixed left stepper --> */}

            {/* <!-- Mobile --> */}
            <div className="bg-primary p-0 text-light d-block d-sm-none">
              <div className="row justify-content-center m-0">
                <div className="col align-self-center p-5">
                  <div className="row m-0 text-center">
                    <h4 className="fw-bold p-0">{title}</h4>
                    <p className="p-0 m-0">{body}</p>
                  </div>
                </div>
              </div>
            </div>
            {/* <!-- /Mobile --> */}

            <div
              className={"col-md-5 col p-md-5 p-3 onboarding-container d-none d-sm-block align-self-" + alignment}
            >
              {/* <!-- Main Content --> */}
              {children}
              {/* <!-- /Main Content --> */}
            </div>

            {/* <!-- /Mobile --> */}
            <div
              className={"row p-0 m-0 bg-primary d-block d-sm-none onboarding-container align-self-" + alignment}
              style={{ overflowY: "hidden" }}
            >
              <div className="col justify-content-center p-3 bg-white rounded-top w-100 h-100 m-0">
                {/* <!-- Main Content --> */}
                {children}
                {/* <!-- /Main Content --> */}
              </div>
            </div>
            {/* <!-- /Mobile --> */}
          </div>
        </div>
      </BaseLayout>
    );
  }
}

export default OnboardingSimpleLayout;
