import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "./baseLayout";
import ModalsWrapper from "../components/modalsWrapper";
import Intercom from "react-intercom";

export type PropsType = {
  imageUrl?: string;
  actionElement: JSX.Element;
  enableModals?: boolean;
  enableIntercom?: boolean;
} & PagePropsType;

type StateType = {
  isMobileView: boolean;
};

class SuccessLayout extends React.Component<PropsType, StateType> {
  constructor(props: PropsType) {
    super(props);
    this.state = {
      isMobileView: false
    };
  }

  componentDidMount() {
    window.matchMedia("(max-width: 1190px)").addListener((e) => this.setState({ isMobileView: e.matches }));
  }

  render(): JSX.Element {
    const { imageUrl, actionElement, enableModals, children, enableIntercom } = this.props;
    const { isMobileView } = this.state;

    return (
      <BaseLayout activePage={this.props.activePage} user={this.props.user}>
        {enableModals && <ModalsWrapper user={this.props.user} />}

        {isMobileView ? (
          <>
            {/* Mobile version */}
            <div className="container-fluid p-0 bg-white vh-100 d-block d-sm-none">
              <div className="row mt-md-5 ms-md-5 mb-md-0 mt-4 mb-4 fixed-top">
                <span className="d-flex justify-content-center align-self-center">
                  <img alt="" src="/images/icons/wealthyhood-logo-dark.svg" />
                </span>
              </div>
              <div className="d-flex flex-column justify-content-center align-self-center px-3 h-100">
                <div
                  className="wh-card wh-card-body bg-white pb-4 px-0 pt-0 align-self-center"
                  style={{ maxWidth: "470px" }}
                >
                  {imageUrl && (
                    <div className="d-flex justify-content-center align-self-center mb-4">
                      <img src={imageUrl} style={{ width: "200px" }} />
                    </div>
                  )}
                  {children}
                </div>
              </div>
              {/* <!-- Dummy spacer to solve overlapping issue with fixed bottom nav buttons --> */}
              <div className="row bg-white" style={{ height: "73px" }} />
              <div className="row p-0 m-0 bg-white fixed-bottom justify-content-center">
                <div
                  className="d-flex flex-column justify-content-center px-md-5 px-3 py-3"
                  style={{ maxWidth: "400px" }}
                >
                  {actionElement}
                </div>
              </div>
            </div>
            {/* End Mobile version */}
          </>
        ) : (
          <div className="container-fluid p-0 bg-primary vh-100 d-none d-sm-block">
            <div className="row mt-md-5 ms-md-5 mb-md-0 mt-4 mb-4">
              <span className="d-none d-sm-block">
                <img alt="" src="/images/icons/wealthyhood-logo.svg" />
              </span>
              <span className="d-flex justify-content-center align-self-center d-block d-sm-none">
                <img alt="" src="/images/icons/wealthyhood-logo-dark.svg" />
              </span>
            </div>
            <div className="row justify-content-center m-0 h-100">
              <div className="d-flex flex-column justify-content-center align-self-center position-relative">
                <div
                  className="wh-card wh-card-body bg-white px-md-5 pb-md-5 pt-5 pb-4 px-4  align-self-center"
                  style={{
                    maxWidth: "470px",
                    minWidth: "300px",
                    maxHeight: "650px"
                  }}
                >
                  <div className="d-flex flex-column overflow-auto m-0 p-0 mb-4">{children}</div>
                  <div className="row m-0 w-100 d-none d-sm-block">{actionElement}</div>
                  {imageUrl && (
                    <div className="d-flex justify-content-center align-self-center position-absolute top-0 start-50 translate-middle">
                      <img
                        src={imageUrl}
                        style={{
                          height: "200px",
                          marginBottom: "70%"
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Intercom App Launcher */}
        {enableIntercom && (
          <Intercom
            appID={process.env.INTERCOM_APP_ID}
            apiKey={process.env.INTERCOM_WEB_VERIFICATION_SECRET}
            name={this.props.user.firstName}
            user_id={this.props.user.id}
            email={this.props.user.email}
            alignment={"right"}
          />
        )}
        {/* End Intercom App Launcher */}
      </BaseLayout>
    );
  }
}

export default SuccessLayout;
