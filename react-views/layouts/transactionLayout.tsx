import React from "react";

type PropsType = { ownerId?: string };

class TransactionLayout extends React.Component<PropsType> {
  render(): JSX.Element {
    const { children, ownerId } = this.props;
    return (
      <>
        {ownerId ? (
          <div className="row">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs mb-15">
                <div className="card-body px-15">
                  <div className="d-flex flex-row justify-content-between ">
                    <h2>Owner:</h2>
                    <a href={`/admin/users/${ownerId}`} className="text-primary font-size-h4">
                      {ownerId}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <></>
        )}
        {children}
      </>
    );
  }
}

export default TransactionLayout;
