import React from "react";
import { PagePropsType } from "../types/page";
import { AssetTransactionDocument, TransactionDocument } from "../../models/Transaction";
import { oldConfig } from "../configs/transactionsTableConfig";
import AdminTransactionRow from "../components/adminTransactionRow";
import { UserDocument } from "../../models/User";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

export type AssetTransactionsAdminPropsType = {
  transactions: TransactionDocument[];
  transactionsSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminAssetTransactionsPage extends React.Component<AssetTransactionsAdminPropsType> {
  render(): JSX.Element {
    const { transactions, transactionsSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="table-responsive">
          <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
            <thead>
              <tr>
                <th className="p-0" />
                <th className="p-0 min-w-100px" />
                <th className="p-0 text-muted">
                  <span>Amount</span>
                </th>
                <th className="p-0 min-w-100px text-muted">
                  <span>Date</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Status</span>
                </th>
                <th className="p-0 text-muted">
                  <span>User Email</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {(transactions as AssetTransactionDocument[]).map(
                (
                  {
                    _id,
                    orders,
                    category,
                    consideration,
                    createdAt,
                    displayStatus,
                    owner,
                    portfolioTransactionCategory
                  },
                  index
                ) => {
                  const transactionConfig = oldConfig[category];
                  const Icon = transactionConfig.icon;

                  let transactionName = `${transactionConfig.nameDisplay} - ${portfolioTransactionCategory}`;
                  let amount = consideration.amount;

                  const isSingleOrderUpdate = portfolioTransactionCategory == "update" && orders.length == 1;
                  if (isSingleOrderUpdate) {
                    const { isin, side, displayAmount } = orders[0];
                    transactionName =
                      Object.values(ASSET_CONFIG).find((config) => config.isin == isin)?.simpleName + " - " + side;
                    amount = displayAmount;
                  }

                  return (
                    <AdminTransactionRow
                      amount={amount}
                      currency={consideration.currency}
                      date={new Date(createdAt)}
                      icon={<Icon />}
                      iconColorClass={transactionConfig.iconColorClass}
                      name={transactionName}
                      status={displayStatus}
                      transactionId={_id}
                      transactionUrl={`/admin/transactions/${_id}`}
                      key={`transaction_${index}`}
                    >
                      <td className="pl-0">
                        <a className="text-dark-75 font-size-h5">{(owner as UserDocument).email}</a>
                      </td>
                    </AdminTransactionRow>
                  );
                }
              )}
            </tbody>
          </table>
        </div>
        <Pagination resultsSize={transactionsSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminAssetTransactionsPage;
