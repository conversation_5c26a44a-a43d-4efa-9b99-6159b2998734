import React from "react";
import { PagePropsType } from "../types/page";
import AdminLayout from "../layouts/adminLayout";
import { BankAccountDocument } from "../../models/BankAccount";
import AdminBankAccountRow from "../components/adminBankAccountRow";
import axios from "axios";
import { captureException } from "@sentry/react";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingSpinner from "../components/loadingSpinner";

export type AdminBankAccountsPropsType = {
  pendingBankAccounts: BankAccountDocument[];
} & PagePropsType;

enum SelectedTabEnum {
  Pending = "Pending",
  Suspended = "Suspended"
}

type StateType = {
  selectedTab: SelectedTabEnum;
  suspendedBankAccounts: BankAccountDocument[];
  pendingBankAccounts: BankAccountDocument[];
  isLoading: boolean;
};

class AdminBankAccountsPage extends React.Component<AdminBankAccountsPropsType, StateType> {
  constructor(props: AdminBankAccountsPropsType) {
    super(props);
    this.state = {
      selectedTab: SelectedTabEnum.Pending,
      isLoading: false,
      suspendedBankAccounts: [],
      pendingBankAccounts: this.props.pendingBankAccounts
    };
  }

  private _setSelectedTab(selectedTab: SelectedTabEnum) {
    this.setState({ selectedTab });
  }

  private async _onSelectSuspendedAccount(): Promise<void> {
    if (this.state.suspendedBankAccounts.length === 0) await this._fetchSuspendedBankAccounts();
    this._setSelectedTab(SelectedTabEnum.Suspended);
  }

  private async _onSelectPendingAccount(): Promise<void> {
    if (this.state.pendingBankAccounts.length === 0) await this._fetchPendingBankAccounts();
    this._setSelectedTab(SelectedTabEnum.Pending);
  }

  private async _activateBankAccount(id: string): Promise<void> {
    this.setState({ isLoading: true });
    try {
      await axios.post(`/admin/bank-accounts/${id}/activate`, {
        headers: { "Content-Type": "application/json" }
      });
      emitToast({
        content: "Bank account updated successfully.",
        toastType: ToastTypeEnum.success
      });
      window.location.reload();
    } catch (error) {
      captureException(error);
      emitToast({
        content: "An error occurred while trying to activate bank account. Please try again.",
        toastType: ToastTypeEnum.error
      });
      this.setState({ isLoading: false });
    }
  }

  private async _fetchSuspendedBankAccounts(): Promise<void> {
    this.setState({ isLoading: true });
    try {
      const response = await axios.get("/admin/bank-accounts/suspended", {
        headers: { "Content-Type": "application/json" }
      });
      this.setState({
        suspendedBankAccounts: response.data,
        isLoading: false
      });
    } catch (error) {
      captureException(error);
      emitToast({
        content: "An error occurred while fetching suspended bank accounts. Please try again.",
        toastType: ToastTypeEnum.error
      });
      this.setState({ isLoading: false });
    }
  }

  private async _fetchPendingBankAccounts(): Promise<void> {
    this.setState({ isLoading: true });
    try {
      const response = await axios.get("/admin/bank-accounts/pending", {
        headers: { "Content-Type": "application/json" }
      });
      this.setState({
        pendingBankAccounts: response.data,
        isLoading: false
      });
    } catch (error) {
      captureException(error);
      emitToast({
        content: "An error occurred while fetching pending bank accounts. Please try again.",
        toastType: ToastTypeEnum.error
      });
      this.setState({ isLoading: false });
    }
  }

  private _sortBankAccounts(bacA: BankAccountDocument, bacB: BankAccountDocument): number {
    return new Date(bacB.createdAt)?.getTime() - new Date(bacA.createdAt)?.getTime();
  }

  render(): JSX.Element {
    const { selectedTab, suspendedBankAccounts, pendingBankAccounts, isLoading } = this.state;

    let bankAccountsToDisplay;
    if (selectedTab === SelectedTabEnum.Pending) bankAccountsToDisplay = pendingBankAccounts;
    else if (selectedTab === SelectedTabEnum.Suspended) bankAccountsToDisplay = suspendedBankAccounts;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="d-flex" style={{ gap: "1rem" }}>
          <button
            disabled={selectedTab === SelectedTabEnum.Pending}
            className="btn btn-primary font-weight-bolder mb-10"
            onClick={async () => await this._onSelectPendingAccount()}
          >
            {SelectedTabEnum.Pending} Accounts
          </button>
          <button
            disabled={selectedTab === SelectedTabEnum.Suspended}
            className="btn btn-primary font-weight-bolder mb-10"
            onClick={async () => await this._onSelectSuspendedAccount()}
          >
            {SelectedTabEnum.Suspended} Accounts
          </button>
        </div>

        {isLoading && <LoadingSpinner />}

        {!isLoading && (
          <div>
            {bankAccountsToDisplay.length > 0 ? (
              <div className="table-responsive">
                <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
                  <thead>
                    <tr>
                      <th className="p-0" />
                      <th className="p-0 min-w-100px text-muted">
                        <span>Date</span>
                      </th>
                      <th className="p-0 text-muted">
                        <span>Status</span>
                      </th>
                      <th className="p-0 text-muted">
                        <span>Name</span>
                      </th>
                      <th className="p-0 text-muted">
                        <span>Owner</span>
                      </th>
                      <th className="p-0 text-muted">
                        <span>WK ID</span>
                      </th>
                      <th className="p-0 text-muted">
                        <span>Action</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {bankAccountsToDisplay.sort(this._sortBankAccounts).map((bankAccount, index) => (
                      <AdminBankAccountRow
                        onActivateBankAccount={() => this._activateBankAccount(bankAccount.id)}
                        bankAccount={bankAccount}
                        key={`bankAccount-row-${index}`}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="mt-4 pd-4 mx-auto text-center">
                <p className="font-size-h3 text-center"> No {selectedTab} bank accounts to display </p>
              </div>
            )}
          </div>
        )}
      </AdminLayout>
    );
  }
}

export default AdminBankAccountsPage;
