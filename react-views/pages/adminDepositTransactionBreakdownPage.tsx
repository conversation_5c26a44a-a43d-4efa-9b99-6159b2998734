import React from "react";
import { PagePropsType } from "../types/page";
import { DepositStatusType } from "../../services/wealthkernelService";
import { TruelayerPaymentStatusType } from "../../services/truelayerService";
import { formatCurrency } from "../utils/currencyUtil";
import AdminLayout from "../layouts/adminLayout";
import TransactionLayout from "../layouts/transactionLayout";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import { BilateralPaymentType, TransferWithIntermediaryStageEnum } from "../../models/Transaction";

export type AdminDepositTransactionBreakdownPropsType = {
  depositAmount: number;
  currency: currenciesConfig.MainCurrencyType;
  transactionId: string;
  ownerId: string;
  providers: {
    wealthkernel: {
      id: string;
      status: DepositStatusType;
    };
    truelayer: {
      id: string;
      status: TruelayerPaymentStatusType;
      failureReason: string;
    };
  };
  transferWithIntermediary?: {
    [TransferWithIntermediaryStageEnum.ACQUISITION]?: BilateralPaymentType;
    [TransferWithIntermediaryStageEnum.COLLECTION]?: BilateralPaymentType;
  };
} & PagePropsType;

class AdminDepositTransactionBreakdownPage extends React.Component<AdminDepositTransactionBreakdownPropsType> {
  render(): JSX.Element {
    const { depositAmount, currency, transactionId, providers, ownerId, transferWithIntermediary } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <TransactionLayout ownerId={ownerId}>
          <div className="row">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs">
                <div className="card-body px-15">
                  <div className="d-flex flex-row justify-content-between pb-20">
                    <h2 className="display-4 font-weight-bold">{formatCurrency(depositAmount, currency, "en")}</h2>
                  </div>

                  {/* Database Info */}
                  <div className="d-flex flex-column mb-3">
                    <p className="text-dark-50 mb-0">Document ID</p>
                    <p className="text-primary font-size-h4">{transactionId}</p>
                  </div>
                  {/* End Database Info */}

                  <div className="row">
                    {/* Wealthkernel Info */}
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Wealthkernel ID</p>
                        <p className="text-primary font-size-h4">{providers?.wealthkernel?.id || "-"}</p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Wealthkernel Status</p>
                        <p className="text-primary font-size-h4">{providers?.wealthkernel?.status || "-"}</p>
                      </div>
                    </div>
                    {/* End Wealthkernel Info */}

                    {/* Truelayer Info */}
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Truelayer ID</p>
                        <p className="text-primary font-size-h4">{providers?.truelayer?.id || "-"}</p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Truelayer Status</p>
                        <p className="text-primary font-size-h4">{providers?.truelayer?.status || "-"}</p>
                      </div>
                      {providers?.truelayer?.failureReason ? (
                        <div className="d-flex flex-column">
                          <p className="text-dark-50 mb-0">Truelayer Failure Reason</p>
                          <p className="text-primary font-size-h4">{providers?.truelayer.failureReason}</p>
                        </div>
                      ) : (
                        <></>
                      )}
                    </div>
                    {/* End Truelayer Info */}
                  </div>
                  <div className="row">
                    {/* Devengo Info */}
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Devengo ID (incoming collection)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.id || "-"}
                        </p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Devengo Status (incoming collection)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.collection?.incomingPayment?.providers?.devengo?.status ||
                            "-"}
                        </p>
                      </div>
                    </div>
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Devengo ID (outgoing collection)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.id || "-"}
                        </p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Devengo Status (outgoing collection)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ||
                            "-"}
                        </p>
                      </div>
                    </div>
                    {/* End Devengo Info */}
                  </div>
                  <div className="row">
                    {/* Devengo Info */}
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Devengo ID (incoming acquisition)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.id || "-"}
                        </p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Devengo Status (incoming acquisition)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status ||
                            "-"}
                        </p>
                      </div>
                    </div>
                    <div className="col-lg-6">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Devengo ID (outgoing acquisition)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.acquisition?.outgoingPayment?.providers?.devengo?.id || "-"}
                        </p>
                      </div>
                      <div className="d-flex flex-column">
                        <p className="text-dark-50 mb-0">Devengo Status (outgoing acquisition)</p>
                        <p className="text-primary font-size-h4">
                          {transferWithIntermediary?.acquisition?.outgoingPayment?.providers?.devengo?.status ||
                            "-"}
                        </p>
                      </div>
                    </div>
                    {/* End Devengo Info */}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TransactionLayout>
      </AdminLayout>
    );
  }
}

export default AdminDepositTransactionBreakdownPage;
