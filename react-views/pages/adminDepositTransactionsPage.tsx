import React from "react";
import { PagePropsType } from "../types/page";
import { DepositCashTransactionDocument } from "../../models/Transaction";
import { oldConfig } from "../configs/transactionsTableConfig";
import AdminTransactionRow from "../components/adminTransactionRow";
import { UserDocument } from "../../models/User";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";

export type AdminDepositTransactionsAdminPropsType = {
  transactions: DepositCashTransactionDocument[];
  transactionsSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminDepositTransactionsPage extends React.Component<AdminDepositTransactionsAdminPropsType> {
  render(): JSX.Element {
    const { transactions, transactionsSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="table-responsive">
          <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
            <thead>
              <tr>
                <th className="p-0" />
                <th className="p-0 min-w-100px" />
                <th className="p-0 text-muted">
                  <span>Amount</span>
                </th>
                <th className="p-0 min-w-100px text-muted">
                  <span>Date</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Status</span>
                </th>
                <th className="p-0 text-muted">
                  <span>User Email</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {transactions.map(({ _id, category, consideration, createdAt, displayStatus, owner }, index) => {
                const transactionConfig = oldConfig[category];
                const Icon = transactionConfig.icon;

                const transactionName = `${transactionConfig.nameDisplay}`;
                return (
                  <AdminTransactionRow
                    amount={consideration.amount}
                    currency={consideration.currency}
                    date={new Date(createdAt)}
                    icon={<Icon />}
                    iconColorClass={transactionConfig.iconColorClass}
                    name={transactionName}
                    status={displayStatus}
                    transactionId={_id}
                    transactionUrl={`/admin/transactions/${_id}`}
                    key={`transaction_${index}`}
                  >
                    <td className="pl-0">
                      <a className="text-dark-75 font-size-h5">
                        {(owner as UserDocument)?.email ?? "Deleted User"}
                      </a>
                    </td>
                  </AdminTransactionRow>
                );
              })}
            </tbody>
          </table>
        </div>
        <Pagination resultsSize={transactionsSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminDepositTransactionsPage;
