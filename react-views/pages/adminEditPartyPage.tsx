import React from "react";
import axios from "axios";
import validator from "validator";
import AdminSelectCountry from "../components/adminSelectCountry";
import SelectDocumentType from "../components/selectDocumentType";
import SelectAmlScreening from "../components/selectAmlScreening";
import AdminTransactionRow from "../components/adminTransactionRow";
import { oldConfig } from "../configs/transactionsTableConfig";
import { PagePropsType } from "../types/page";
import { UserDocument } from "../../models/User";
import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  DepositCashTransactionDocument,
  RebalanceTransactionDocument,
  TransactionDocument
} from "../../models/Transaction";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast } from "../utils/eventService";
import AdminLayout from "../layouts/adminLayout";
import { captureException } from "@sentry/react";
import { UserDataRequestInterface, UserDataRequestType } from "../../models/UserDataRequest";
import ConfirmationModal from "../components/modals/confirmationModal";
import { banksConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import LoadingOnSubmitButton from "../../react-views/components/buttons/loadingOnSubmitButton";
import { capitalizeFirstLetter } from "../utils/stringUtil";
import AdminClientRiskAssessmentCard from "../components/adminClientRiskAssessmentCard";
import { RewardDocument } from "../../models/Reward";
import AdminRewardRow from "../../react-views/components/adminRewardRow";

const MAXIMUM_ALLOWED_BONUS = 150;

const { ASSET_CONFIG } = investmentUniverseConfig;
type InputNameType = "bonusDepositAmount" | "taxReportStartDate" | "taxReportEndDate";
export type AdminEditPartyPropsType = {
  viewedUser: UserDocument;
  transactions: TransactionDocument[];
  userDataRequests: UserDataRequestInterface[];
  rewards: RewardDocument[];
  wealthkernelPortfolioId: string;
} & PagePropsType;

type FormFieldComponentTypes = "input" | "selectCountry" | "selectDocumentType" | "selectAmlScreening";

type SectionType = {
  title: string;
  fields: {
    componentType?: FormFieldComponentTypes;
    disabled?: boolean;
    label: string;
    name: string;
    defaultValue?: string;
    placeholder?: string;
  }[];
};

type StateType = {
  showGDPRDeletionConfirmationModal: boolean;
  showDisassociationConfirmationModal: boolean;
  showRemoveKycPassportFlagModal: boolean;
  showOverrideKycDecisionModal: boolean;
  bonusDepositAmount: string;
  taxReportEndDate: string;
  taxReportStartDate: string;
};

const FORM_FIELD_CONFIG = {
  input: {
    component: "input",
    defaultProps: {
      className: "form-control form-control-lg form-control-solid",
      type: "text"
    }
  },
  selectCountry: {
    component: AdminSelectCountry,
    defaultProps: {}
  },
  selectDocumentType: {
    component: SelectDocumentType,
    defaultProps: {}
  },
  selectAmlScreening: {
    component: SelectAmlScreening,
    defaultProps: {}
  }
};

export const USER_REQUEST_CONFIG: Record<UserDataRequestType, string> = {
  "gdpr-delete": "GDPR Deletion",
  disassociation: "Disassociation"
};

class AdminEditPartyPage extends React.Component<AdminEditPartyPropsType, StateType> {
  constructor(props: AdminEditPartyPropsType) {
    super(props);
    this.state = {
      showGDPRDeletionConfirmationModal: false,
      showDisassociationConfirmationModal: false,
      showRemoveKycPassportFlagModal: false,
      showOverrideKycDecisionModal: false,
      bonusDepositAmount: "",
      taxReportStartDate: "",
      taxReportEndDate: ""
    };
  }

  private _formatRiskAssessmentValue(value: string, score: number) {
    if (value) {
      return value + " → " + score;
    }
    return "";
  }

  private _canSubmitBonusDeposit = (): boolean => {
    const { bonusDepositAmount } = this.state;
    return Boolean(
      validator.isNumeric(bonusDepositAmount) && !(parseFloat(bonusDepositAmount) > MAXIMUM_ALLOWED_BONUS)
    );
  };

  private _submitBonusDeposit = async (): Promise<void> => {
    const { bonusDepositAmount } = this.state;

    try {
      await axios({
        method: "post",
        url: "/admin/portfolios/add-bonus-deposit",
        data: {
          bonusDepositAmount,
          targetUserId: this.props.viewedUser.id
        }
      });
      emitToast({
        content: "Bonus deposit created successfully!",
        toastType: ToastTypeEnum.success
      });
      window.location.reload();
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _handleInputChange =
    (inputName: InputNameType) =>
    (event: any): void => {
      const value = event?.target?.value || "";
      this.setState((prevState) => {
        const newState: any = { ...prevState };
        newState[inputName] = value;
        return newState;
      });
    };

  private _setShowGDPRDeletionConfirmationModal = (show: boolean) => () => {
    this.setState({ showGDPRDeletionConfirmationModal: show });
  };

  private _setShowDisassociationConfirmationModal = (show: boolean) => () => {
    this.setState({ showDisassociationConfirmationModal: show });
  };

  private _setShowRemoveKycPassportFlagModal = (show: boolean) => () => {
    this.setState({ showRemoveKycPassportFlagModal: show });
  };

  private _setShowOverrideKycDecisionModal = (show: boolean) => () => {
    this.setState({ showOverrideKycDecisionModal: show });
  };

  private _submitUserDeletionRequest = async (requestType: UserDataRequestType): Promise<void> => {
    const { viewedUser } = this.props;
    try {
      await axios({
        method: "post",
        url: `/admin/users/${viewedUser.id}/delete`,
        data: {
          requestType
        }
      });
      window.location.reload();
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred.",
        toastType: ToastTypeEnum.error
      });
      return;
    }
  };

  private _getTransactionURL = (transaction: TransactionDocument): string => {
    const transactionShouldBeClickable =
      [
        "AssetTransaction",
        "RebalanceTransaction",
        "RevertRewardTransaction",
        "WithdrawalCashTransaction",
        "DepositCashTransaction"
      ].includes(transaction.category) ||
      (transaction.category === "ChargeTransaction" &&
        ["combined", "holdings"].includes((transaction as ChargeTransactionDocument).chargeMethod));

    if (transactionShouldBeClickable) {
      return `/admin/transactions/${transaction.id}`;
    } else return null;
  };

  private _isRepeatingTransaction = (transaction: TransactionDocument): boolean => {
    if (
      (transaction as DepositCashTransactionDocument | AssetTransactionDocument | RebalanceTransactionDocument)
        .linkedAutomation
    ) {
      return true;
    }

    return false;
  };

  private static _getFormFieldType(componentType?: FormFieldComponentTypes, componentProps?: any): any {
    const formFieldConfig = FORM_FIELD_CONFIG[componentType] || FORM_FIELD_CONFIG.input;
    const props = Object.assign(formFieldConfig.defaultProps, componentProps);
    return React.createElement(formFieldConfig.component, props);
  }

  private _getSectionsForUser = (user: UserDocument): SectionType[] => [
    {
      title: "Passport Details",
      fields: [
        {
          label: "First Name",
          name: "firstName",
          defaultValue: user.firstName
        },
        {
          label: "Last Name",
          name: "lastName",
          defaultValue: user.lastName
        },
        {
          label: "Date of Birth",
          name: "dateOfBirth",
          defaultValue: user.dateOfBirth?.toString(),
          placeholder: "yyyy-MM-dd"
        },
        {
          componentType: "selectCountry",
          label: "Nationality",
          name: "nationality",
          defaultValue: user.nationalities?.length > 0 ? user.nationalities[0] : ""
        }
      ]
    },
    {
      title: "Tax Residency",
      fields: [
        {
          componentType: "selectCountry",
          label: "Country Code",
          name: "taxResidencyCountryCode",
          defaultValue: user.taxResidency?.countryCode
        },
        {
          componentType: "selectDocumentType",
          label: "Proof Type",
          name: "taxResidencyProofType",
          defaultValue: user.taxResidency?.proofType
        },
        {
          label: "Proof Value",
          name: "taxResidencyProofValue",
          defaultValue: user.taxResidency?.value
        }
      ]
    },
    {
      title: "Address",
      fields: [
        {
          label: "Line 1",
          name: "addressLine1",
          defaultValue: user.addresses[0]?.line1
        },
        {
          label: "Line 2",
          name: "addressLine2",
          defaultValue: user.addresses[0]?.line2
        },
        {
          label: "Line 3",
          name: "addressLine3",
          defaultValue: user.addresses[0]?.line3
        },
        {
          label: "City",
          name: "addressCity",
          defaultValue: user.addresses[0]?.city
        },
        {
          componentType: "selectCountry",
          label: "Country Code",
          name: "addressCountryCode",
          defaultValue: user.addresses[0]?.countryCode
        },
        {
          label: "Post Code",
          name: "addressPostCode",
          defaultValue: user.addresses[0]?.postalCode
        }
      ]
    },
    {
      title: "Referral Information",
      fields: [
        {
          label: "Referred By (e-mail)",
          name: "referredByEmail",
          defaultValue: user.referredByEmail
        }
      ]
    }
  ];

  private async _downloadTaxReport(): Promise<void> {
    const { viewedUser } = this.props;
    const { taxReportStartDate, taxReportEndDate } = this.state;
    let query = "";
    if (taxReportStartDate) {
      query += `startDate=${taxReportStartDate}&`;
    }
    if (taxReportEndDate) {
      query += "endDate=" + taxReportEndDate;
    }

    const response = await axios.post(`/admin/users/${viewedUser.id}/account-statements/generate?${query}`);

    window.open(response.data.fileUri, "_blank");
  }

  private async _removeDuplicateFlag(): Promise<void> {
    try {
      const { viewedUser } = this.props;
      await axios.post(`/admin/users/${viewedUser.id}/remove-duplicate-flag`);
      window.location.reload();
    } catch (err) {
      emitToast({ content: "Failed to remove duplicate flag. Try again later!", toastType: ToastTypeEnum.error });
    }
  }

  private async _removeKycPassportFlag(): Promise<void> {
    try {
      const { viewedUser } = this.props;
      await axios.post(`/admin/users/${viewedUser.id}/remove-kyc-passport-flag`);
      window.location.reload();
    } catch (err) {
      emitToast({ content: "Failed to remove duplicate flag. Try again later!", toastType: ToastTypeEnum.error });
    }
  }

  private async _overrideKycDecision(): Promise<void> {
    try {
      const { viewedUser } = this.props;
      await axios.post(`/admin/users/${viewedUser.id}/override-kyc-decision`);
      window.location.reload();
    } catch (err) {
      emitToast({ content: "Failed to remove duplicate flag. Try again later!", toastType: ToastTypeEnum.error });
    }
  }

  private _getKycCssClass(): string {
    const { viewedUser } = this.props;
    if (viewedUser?.kycOperation?.status === "Passed") {
      return "text-success";
    }

    if (viewedUser?.kycOperation?.status === "Pending") {
      return "text-warning";
    }

    if (viewedUser?.kycOperation?.status === "Failed") {
      return "text-danger";
    }
  }

  private _getKycApplicantUrl(): string {
    const { viewedUser } = this.props;

    const applicantId = viewedUser?.providers?.sumsub?.id;

    if (applicantId) {
      return process.env.SUMSUB_APPLICANTS_URL + `/${applicantId}`;
    }
  }

  private _getWealthkernelPartyUrl(wealthkernelPartyId: string): string {
    if (wealthkernelPartyId) {
      return process.env.WEALTHKERNEL_DASHBOARD_URL + `/parties/${wealthkernelPartyId}`;
    }
  }

  private _getWealthkernelPortfolioUrl(wealthkernelPortfolioId: string): string {
    if (wealthkernelPortfolioId) {
      return process.env.WEALTHKERNEL_DASHBOARD_URL + `/portfolios/${wealthkernelPortfolioId}`;
    }
  }

  private _copyToClipboard(value: string): void {
    navigator.clipboard.writeText(value);
  }

  render(): JSX.Element {
    const { transactions, viewedUser, userDataRequests, wealthkernelPortfolioId, rewards } = this.props;
    const {
      showGDPRDeletionConfirmationModal,
      showDisassociationConfirmationModal,
      showRemoveKycPassportFlagModal,
      showOverrideKycDecisionModal,
      taxReportStartDate,
      taxReportEndDate
    } = this.state;
    const sections = this._getSectionsForUser(viewedUser);

    const showOverrideKycDecisionBtn = viewedUser?.kycOperation?.status === "Failed";
    const isPassportFlagAvailable =
      viewedUser?.kycOperation?.isProcessed && typeof viewedUser.isPassportVerified === "boolean";
    const wealthkernelPartyId = viewedUser.providers?.wealthkernel?.id;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="row justify-content-center mb-20 mb-lg-0">
          {/* Investor Summary */}
          <div className="col-lg-5">
            <div className="d-flex align-items-end mb-10">
              <h2 className="font-weight-bolder mb-0 mr-6">Investor Summary</h2>
            </div>
            <div className="row pb-5">
              <div className="col-lg-8">
                <div className="d-flex align-items-center mb-2">
                  <span className="h5 text-dark-50 mr-4 mb-0">User ID</span>
                  <p className="text-muted font-weight-bold mb-0">{viewedUser._id.toString()}</p>
                  <span
                    className="material-symbols-outlined cursor-pointer text-muted p-2 ms-1"
                    onClick={() => {
                      this._copyToClipboard(viewedUser._id.toString());
                    }}
                    style={{
                      fontSize: "15px"
                    }}
                  >
                    content_copy
                  </span>
                </div>

                <div className="d-flex align-items-center mb-3">
                  <span className="h5 text-dark-50 mr-4 mb-0">Wealthkernel party</span>
                  <a
                    href={this._getWealthkernelPartyUrl(wealthkernelPartyId)}
                    target="_blank"
                    rel="noreferrer"
                    className="text-muted font-weight-bold d-flex align-items-center"
                  >
                    {wealthkernelPartyId}
                    <span
                      className="material-symbols-outlined cursor-pointer text-muted px-2"
                      style={{
                        fontSize: "15px"
                      }}
                    >
                      open_in_new
                    </span>
                  </a>
                </div>

                <div className="d-flex align-items-middle mb-2">
                  <span className="h5 text-dark-50 mr-4">Converted Portfolio</span>
                  <a
                    href="#"
                    className={`${
                      viewedUser.hasConvertedPortfolio ? "text-success" : "text-warning"
                    } font-weight-bold`}
                  >
                    {Boolean(viewedUser.hasConvertedPortfolio).toString()}
                  </a>
                </div>

                <div className="d-flex align-items-center mb-3">
                  <span className="h5 text-dark-50 mr-4 mb-0">Wealthkernel portfolio</span>
                  {wealthkernelPortfolioId ? (
                    <a
                      href={this._getWealthkernelPortfolioUrl(wealthkernelPortfolioId)}
                      target="_blank"
                      rel="noreferrer"
                      className="text-muted font-weight-bold d-flex align-items-center"
                    >
                      {wealthkernelPortfolioId}
                      <span
                        className="material-symbols-outlined cursor-pointer text-muted px-2"
                        style={{
                          fontSize: "15px"
                        }}
                      >
                        open_in_new
                      </span>
                    </a>
                  ) : (
                    "Not Available"
                  )}
                </div>

                <div className="d-flex align-items-center mb-3">
                  <span className="h5 text-dark-50 mb-0 mr-4">Is marked duplicate?</span>
                  <span
                    className={`font-weight-bold mr-4 ${
                      viewedUser.isPotentiallyDuplicateAccount ? "text-danger" : "text-success"
                    }`}
                  >
                    {Boolean(viewedUser.isPotentiallyDuplicateAccount).toString()}
                  </span>
                  {viewedUser.isPotentiallyDuplicateAccount && (
                    <button
                      className="btn btn-white text-primary shadow-effect-sm"
                      onClick={() => this._removeDuplicateFlag()}
                    >
                      Remove flag
                    </button>
                  )}
                </div>

                <div className="d-flex align-items-center mb-3">
                  <span className="h5 text-dark-50 mb-0 mr-4">Identity documents correct?</span>
                  <span
                    className={`font-weight-bold mr-4 ${
                      isPassportFlagAvailable
                        ? viewedUser.isPassportVerified
                          ? "text-success"
                          : "text-danger"
                        : ""
                    }`}
                  >
                    {isPassportFlagAvailable ? Boolean(viewedUser.isPassportVerified).toString() : "Not Available"}
                  </span>
                  {isPassportFlagAvailable && !viewedUser.isPassportVerified && (
                    <button
                      className="btn btn-white text-primary shadow-effect-sm"
                      onClick={this._setShowRemoveKycPassportFlagModal(true)}
                    >
                      Remove flag
                    </button>
                  )}
                </div>

                {viewedUser.kycOperation?.isProcessed && (
                  <div className="d-flex align-items-middle mb-2">
                    <span className="h5 text-dark-50 mr-4">KYC Transaction ID</span>
                    <a
                      href={this._getKycApplicantUrl()}
                      target="_blank"
                      rel="noreferrer"
                      className="text-muted font-weight-bold"
                    >
                      {viewedUser.providers?.sumsub?.id}
                      <span
                        className="material-symbols-outlined cursor-pointer text-muted px-2"
                        style={{
                          fontSize: "15px"
                        }}
                      >
                        open_in_new
                      </span>
                    </a>
                  </div>
                )}

                <div className="d-flex align-items-center mb-3">
                  <span className="h5 text-dark-50 mb-0 mr-4">Sumsub status:</span>
                  <span className={`font-weight-bold mr-4 ${this._getKycCssClass()}`}>
                    {viewedUser?.kycOperation?.status ?? "Not Available"}
                  </span>
                  {showOverrideKycDecisionBtn && (
                    <button
                      className="btn btn-white text-primary shadow-effect-sm"
                      onClick={this._setShowOverrideKycDecisionModal(true)}
                    >
                      Override Kyc
                    </button>
                  )}
                </div>

                {viewedUser?.providers?.wealthkernel ? (
                  <>
                    <div className="d-flex align-items-middle mb-2">
                      <span className="h5 text-dark-50 mr-4">KYC Passed</span>
                      <a href="#" className="text-muted font-weight-bold">
                        {Boolean(viewedUser.hasPassedKyc).toString()}
                      </a>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="d-flex align-items-middle mb-2">
                      <span className="h5 text-dark-50 mr-4">Passport Submitted</span>
                      <span className="text-muted font-weight-bold">
                        {viewedUser.passportSubmitted.toString()}
                      </span>
                    </div>
                    <div className="d-flex align-items-middle mb-2">
                      <span className="h5 text-dark-50 mr-4">Address Submitted</span>
                      <span className="text-muted font-weight-bold">{viewedUser.addressSubmitted.toString()}</span>
                    </div>
                    <div className="d-flex align-items-middle mb-2">
                      <span className="h5 text-dark-50 mr-4">Tax Res. Submitted</span>
                      <span className="text-muted font-weight-bold">
                        {viewedUser.taxResidencySubmitted.toString()}
                      </span>
                    </div>
                    <div className="d-flex align-items-middle mb-2">
                      <span className="h5 text-dark-50 mr-4">Bank Account Linked</span>
                      <span className="text-muted font-weight-bold">{viewedUser.bankLinked.toString()}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
          {/* End Investor Summary */}

          <div className="col-lg-3">
            <h2 className="mb-10 font-weight-bolder text-dark">Send Cash</h2>
            <form className="form">
              <div className="row">
                {/* Amount */}
                <div className="col-12">
                  <div className="form-group">
                    <label>
                      Amount <span className="text-primary">*</span>
                    </label>
                    <input
                      type="text"
                      className="form-control w-auto"
                      name="bonusDepositAmount"
                      placeholder="Amount"
                      value={this.state.bonusDepositAmount}
                      onChange={this._handleInputChange("bonusDepositAmount")}
                      required
                    />
                  </div>
                </div>
                {/* End of Amount */}
              </div>
            </form>
            {/* Submission Button */}
            <div className="row mt-6">
              <div className="col-lg-6">
                {this._canSubmitBonusDeposit() ? (
                  <LoadingOnSubmitButton
                    type="button"
                    className="btn btn-lg btn-primary"
                    enableOnCompletion={true}
                    customonclick={async () => this._submitBonusDeposit()}
                  >
                    Send Bonus
                  </LoadingOnSubmitButton>
                ) : (
                  <button type="button" className="btn btn-lg btn-primary" disabled>
                    {this.state.bonusDepositAmount ? "Invalid" : "Specify"} Amount
                  </button>
                )}
              </div>
            </div>
            {/* End Submission Button */}
          </div>
          <div className="col-lg-2">
            <h2 className="mb-10 font-weight-bolder text-dark">Tax Report</h2>
            <form className="form">
              <div className="row">
                {/* Amount */}
                <div className="col-12">
                  <div className="form-group">
                    <label>Start Date</label>
                    <input
                      type="date"
                      className="form-control w-auto"
                      name="taxReportStartDate"
                      value={taxReportStartDate}
                      onChange={this._handleInputChange("taxReportStartDate")}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label>End date</label>
                    <input
                      type="date"
                      className="form-control w-auto"
                      name="taxReportEndDate"
                      value={taxReportEndDate}
                      onChange={this._handleInputChange("taxReportEndDate")}
                      required
                    />
                  </div>
                </div>
                {/* End of Amount */}
              </div>
            </form>

            <div className="row mt-6">
              <div className="col-lg-6">
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-lg btn-primary"
                  enableOnCompletion={true}
                  customonclick={async () => this._downloadTaxReport()}
                >
                  Download Tax Report
                </LoadingOnSubmitButton>
              </div>
            </div>
          </div>
        </div>

        {/* Form Investor Details */}
        <div className="row justify-content-center mt-10 mb-20">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">Edit User Details</h2>
            <div className="card shadow-sm border-radius-xl">
              <div className="card-body mt-4">
                <form method="POST" action={`/admin/users/${viewedUser._id}`}>
                  {sections.map(({ title, fields }, sectionIndex) => (
                    <React.Fragment key={`section-${sectionIndex}`}>
                      <div className="row">
                        <label className="col-xl-3" />
                        <div className="col-lg-9 col-xl-6">
                          <h3 className="font-weight-bold my-10">{title}</h3>
                        </div>
                      </div>
                      {fields.map(
                        ({ componentType, disabled, label, name, defaultValue, placeholder }, fieldIndex) => (
                          <div className="form-group row" key={`label-${name}-${fieldIndex}`}>
                            <label className="col-xl-3 col-lg-3 col-form-label text-right">{label}</label>
                            <div className="col-lg-9 col-xl-6">
                              {AdminEditPartyPage._getFormFieldType(componentType, {
                                defaultValue,
                                disabled,
                                name,
                                placeholder
                              })}
                            </div>
                          </div>
                        )
                      )}
                    </React.Fragment>
                  ))}
                  <div className="row my-10">
                    <div className="col-xl-3 col-lg-3" />
                    <div className="col-lg-9 col-xl-6">
                      <div className="float-right">
                        <button type="submit" className="btn btn-light-primary font-weight-bolder px-10 py-3 mr-3">
                          Update
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        {/* End Form Investor Details */}

        {/* AML Screening Section */}
        <div className="row justify-content-center mb-20">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">AML Screening</h2>
            <div className="card shadow-sm border-radius-xl">
              <div className="card-body mt-4">
                <form method="POST" action={`/admin/users/${viewedUser._id}`}>
                  <div className="form-group row">
                    <label className="col-xl-3 col-lg-3 col-form-label text-right">AML Screening</label>
                    <div className="col-lg-9 col-xl-6">
                      <SelectAmlScreening name="amlScreening" defaultValue={viewedUser.amlScreening} />
                    </div>
                  </div>
                  <div className="row mt-10">
                    <div className="col-xl-3 col-lg-3" />
                    <div className="col-lg-9 col-xl-6">
                      <div className="float-right">
                        <button type="submit" className="btn btn-light-primary font-weight-bolder px-10 py-3">
                          Update AML Screening
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        {/* End AML Screening Section */}

        {/* Bank Accounts Section */}
        <div className="row justify-content-center mb-20">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">Bank Accounts</h2>
            <div className="card shadow-sm border-radius-xl">
              <div className="card-body mt-4">
                {viewedUser.bankAccounts.length > 0 ? (
                  <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
                    <thead>
                      <tr>
                        <th className="text-muted">
                          <span>Bank</span>
                        </th>
                        <th className="text-muted">
                          <span>Identifier</span>
                        </th>
                        <th className="text-muted">
                          <span>Status</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {viewedUser.bankAccounts.map((bankAccount, index) => (
                        <tr key={index}>
                          <td>
                            <span className="text-dark-50 font-weight-bolder">
                              {banksConfig.BANKS_CONFIG[bankAccount.bankId]?.name ?? "Undefined Bank"}
                            </span>
                          </td>
                          <td>
                            <span>{bankAccount.displayAccountIdentifier}</span>
                          </td>
                          <td>
                            <span
                              className={`label label-dot label-${bankAccount.active ? "success" : "danger"}`}
                            />
                            <span
                              className={`font-weight-bold text-${bankAccount.active ? "success" : "danger"} ml-2`}
                            >
                              {bankAccount.active ? "Active" : "Inactive"}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <p>No bank accounts</p>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* End Bank Accounts Section */}

        {/* Client Risk Assessment */}
        <div className="row justify-content-center mb-20">
          <div className="col-lg-10">
            <div className="row">
              <div className="col-lg-12">
                <AdminClientRiskAssessmentCard assessment={viewedUser.latestRiskAssessment} />
              </div>
            </div>
          </div>
        </div>
        {/* End Client Risk Assessment */}

        {/* Transactions */}
        <div className="row justify-content-center mb-20">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">Transactions</h2>
            <div className="row">
              <div className="col-lg-12">
                {transactions.length > 0 ? (
                  <div className="table-responsive">
                    <table
                      className="table table-borderless table-vertical-center"
                      style={{ fontFamily: "Roboto" }}
                    >
                      <thead>
                        <tr>
                          <th className="p-0" />
                          <th className="p-0 text-muted min-w-150px">
                            <span>Type</span>
                          </th>
                          <th className="p-0 text-muted">
                            <span>Amount</span>
                          </th>
                          <th className="p-0 min-w-100px text-muted">
                            <span>Date</span>
                          </th>
                          <th className="p-0 text-muted">
                            <span>Status</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {transactions.map((transaction, index) => {
                          // is deposit, asset or withdrawal
                          const { _id, category, consideration, createdAt, displayStatus } =
                            transaction as TransactionDocument;
                          const transactionConfig = oldConfig[category];
                          const Icon = transactionConfig.icon;
                          let transactionName = transactionConfig.nameDisplay;
                          let amount = consideration.amount;

                          if (category === "AssetTransaction") {
                            const { orders, portfolioTransactionCategory } =
                              transaction as AssetTransactionDocument;

                            transactionName += ` - ${portfolioTransactionCategory}`;

                            const isSingleOrderUpdate =
                              portfolioTransactionCategory == "update" && orders.length == 1;
                            if (isSingleOrderUpdate) {
                              const order = orders[0];
                              const isin = order.isin;
                              const side = order.side;
                              transactionName =
                                Object.values(ASSET_CONFIG).find((config) => config.isin == isin)?.simpleName +
                                " - " +
                                side;
                              amount = order.displayAmount;
                            }
                          } else if (category === "ChargeTransaction") {
                            const { chargeMethod, chargeType } = transaction as ChargeTransactionDocument;

                            transactionName = `${capitalizeFirstLetter(
                              chargeType
                            )} ${transactionName} (${chargeMethod})`;
                          }

                          return (
                            <AdminTransactionRow
                              amount={amount}
                              currency={consideration.currency}
                              date={new Date(createdAt)}
                              icon={<Icon />}
                              iconColorClass={transactionConfig.iconColorClass}
                              name={transactionName}
                              status={displayStatus}
                              transactionId={_id}
                              transactionUrl={this._getTransactionURL(transaction)}
                              repeating={this._isRepeatingTransaction(transaction)}
                              key={`transaction_${index}`}
                            />
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p>No transactions made yet.</p>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* End Transactions */}

        {/* Rewards */}
        <div className="row justify-content-center mb-20">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">Rewards</h2>
            <div className="row">
              <div className="col-lg-12">
                {rewards.length > 0 ? (
                  <div className="table-responsive">
                    <table
                      className="table table-borderless table-vertical-center"
                      style={{ fontFamily: "Roboto" }}
                    >
                      <thead>
                        <tr>
                          <th className="p-0" />
                          <th className="p-0 min-w-100px" />
                          <th className="p-0 text-muted">
                            <span>Amount</span>
                          </th>
                          <th className="p-0 min-w-100px text-muted">
                            <span>Date</span>
                          </th>
                          <th className="p-0 text-muted">
                            <span>Deposit Status</span>
                          </th>
                          <th className="p-0 text-muted">
                            <span>Order Status</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {rewards.map((reward, index) => (
                          <AdminRewardRow reward={reward} key={`reward-row-${index}`} userPage={true} />
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p>No rewards yet.</p>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* End Rewards */}

        {/* User Deletion Area */}
        <div className="row justify-content-center">
          <div className="col-lg-10">
            <h2 className="font-weight-bolder mb-10 pt-3">⚠️ Danger Area ⚠️</h2>
            <div className="row">
              <div className="col-lg-12">
                {userDataRequests.length > 0 ? (
                  <div className="table-responsive">
                    <table
                      className="table table-borderless table-vertical-center"
                      style={{ fontFamily: "Roboto" }}
                    >
                      <thead>
                        <tr>
                          <th className="p-0 text-muted">
                            <span>Type</span>
                          </th>
                          <th className="p-0 min-w-100px text-muted">
                            <span>Created At</span>
                          </th>
                          <th className="p-0 min-w-100px text-muted">
                            <span>Updated At</span>
                          </th>
                          <th className="p-0 text-muted">
                            <span>Status</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {userDataRequests.map((userDataRequest, index) => {
                          return (
                            <tr key={index}>
                              <td className="pl-0">
                                <span className="text-primary font-weight-bolder d-block font-size-h5">
                                  {USER_REQUEST_CONFIG[userDataRequest.requestType]}
                                </span>
                              </td>
                              <td className="pl-0">
                                <span className="text-dark-75 font-weight-500 font-size-h6">
                                  {new Date(userDataRequest.createdAt).toLocaleDateString("en-GB", {
                                    day: "numeric",
                                    month: "short",
                                    year: "numeric"
                                  })}
                                </span>
                              </td>
                              <td className="pl-0">
                                <span className="text-dark-75 font-weight-500 font-size-h6">
                                  {new Date(userDataRequest.updatedAt).toLocaleDateString("en-GB", {
                                    day: "numeric",
                                    month: "short",
                                    year: "numeric"
                                  })}
                                </span>
                              </td>
                              <td className="pl-0 text-nowrap">
                                <span
                                  className={`label label-dot label-${
                                    userDataRequest.status === "Completed" ? "success" : "warning"
                                  }`}
                                />
                                <span
                                  className={`font-weight-bold text-${
                                    userDataRequest.status === "Completed" ? "success" : "warning"
                                  } ml-2`}
                                >
                                  {userDataRequest.status}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="row">
                    <div className="col-lg-4">
                      <div className="d-flex justify-content-between">
                        <button
                          type="button"
                          className="btn-lg btn-danger font-weight-bold"
                          onClick={this._setShowDisassociationConfirmationModal(true)}
                        >
                          Disassociate User
                        </button>
                        <button
                          type="button"
                          className="btn-lg btn-danger font-weight-bold"
                          onClick={this._setShowGDPRDeletionConfirmationModal(true)}
                        >
                          GDPR Delete User
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <ConfirmationModal
          title="GDPR Delete User"
          description="Are you sure you want to GDPR delete this user?"
          handleConfirmation={() => this._submitUserDeletionRequest("gdpr-delete")}
          show={showGDPRDeletionConfirmationModal}
          handleClose={this._setShowGDPRDeletionConfirmationModal(false)}
        />
        <ConfirmationModal
          title="Disassociate User"
          description="Are you sure you want to disassociate this user?"
          handleConfirmation={() => this._submitUserDeletionRequest("disassociation")}
          show={showDisassociationConfirmationModal}
          handleClose={this._setShowDisassociationConfirmationModal(false)}
        />
        <ConfirmationModal
          title="Remove Kyc Passport flag"
          description="Are you sure you want to remove KYC passport flag for this user?"
          handleConfirmation={() => this._removeKycPassportFlag()}
          show={showRemoveKycPassportFlagModal}
          handleClose={this._setShowRemoveKycPassportFlagModal(false)}
        />
        <ConfirmationModal
          title="Override Kyc Decision"
          description="Are you sure you want to want to override KYC decision and set status to 'Manually Passed'?"
          handleConfirmation={() => this._overrideKycDecision()}
          show={showOverrideKycDecisionModal}
          handleClose={this._setShowOverrideKycDecisionModal(false)}
        />
        {/* End Transactions */}
      </AdminLayout>
    );
  }
}

export default AdminEditPartyPage;
