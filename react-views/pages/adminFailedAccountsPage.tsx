import React from "react";
import { PagePropsType } from "../types/page";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";
import { UserDocument } from "../../models/User";
import { countriesConfig } from "@wealthyhood/shared-configs";

export const DurationArray = ["1d", "1w", "1m", "3m", "6m", "1y", "max"] as const;
export type DurationType = typeof DurationArray[number];

const DURATION_LABEL_CONFIG: Record<DurationType, string> = {
  "1d": "1 day",
  "1w": "1 week",
  "1m": "1 month",
  "3m": "3 months",
  "6m": "6 months",
  "1y": "1 year",
  max: "all time"
};

const NATIONALITIES_TO_FLAG: countriesConfig.CountryCodesType[] = ["US"];

export type AdminFailedAccountsPagePropsType = {
  users: UserDocument[];
  usersSize: number;
  pageSize: number;
  currentPage: number;
  duration: DurationType;
} & PagePropsType;

type StateType = {
  duration: DurationType;
};

class AdminFailedAccountsPage extends React.Component<AdminFailedAccountsPagePropsType, StateType> {
  constructor(props: AdminFailedAccountsPagePropsType) {
    super(props);
    this.state = {
      duration: this.props.duration
    };
  }

  private handleDurationChange = (event: any) => {
    this.setState({ duration: event.target.value });
  };

  private handleSubmit = (event: any) => {
    event.preventDefault(); // Prevent the default form submission

    // Construct query params from state
    const queryParams = new URLSearchParams(this.state).toString();

    // Redirect by updating window's location with query params
    window.location.href = `${window.location.pathname}?${queryParams}`;
  };

  private _createPassportMatchColumn(user: UserDocument): JSX.Element {
    if (user.isPassportVerified === true) {
      return <td className={"align-middle text-success"}>{"True"}</td>;
    } else if (user.isPassportVerified === false) {
      return <td className={"align-middle text-danger"}>{"False"}</td>;
    } else {
      return <td className={"align-middle text-muted"}>{"Not Available"}</td>;
    }
  }

  private _createKycProviderColumn(user: UserDocument): JSX.Element {
    const hasProcessedKycOperation = user.kycOperation?.isProcessed;

    if (!hasProcessedKycOperation) {
      return <td className={"align-middle text-muted"}>{"Not Available"}</td>;
    }

    const kycOperationStatus = user.kycOperation?.status;
    if (kycOperationStatus === "Passed") {
      return <td className={"align-middle text-success"}>{"Success"}</td>;
    } else if (kycOperationStatus === "Failed") {
      return <td className={"align-middle text-danger"}>{"Fail"}</td>;
    } else if (kycOperationStatus === "Pending") {
      return <td className={"align-middle text-warning"}>{"Pending"}</td>;
    } else if (kycOperationStatus === "ManuallyPassed") {
      return <td className={"align-middle text-muted"}>{"ManuallyPassed"}</td>;
    }
  }

  private _createWkKycColumn(user: UserDocument): JSX.Element {
    const hasWkAccount = !!user.accounts?.[0]?.providers?.wealthkernel?.id;

    if (!hasWkAccount) {
      return <td className={"align-middle text-muted"}>{"Not Available"}</td>;
    }

    const wkAccountStatus = user.accounts?.[0]?.providers?.wealthkernel?.status;
    if (wkAccountStatus === "Active") {
      return <td className={"align-middle text-success"}>{"Success"}</td>;
    } else if (wkAccountStatus === "Pending") {
      return <td className={"align-middle text-warning"}>{"Pending"}</td>;
    } else {
      return <td className={"align-middle text-danger"}>{"Fail"}</td>;
    }
  }

  private _createDuplicateColumn(user: UserDocument): JSX.Element {
    if (user.isPotentiallyDuplicateAccount) {
      return <td className={"align-middle text-danger"}>{"True"}</td>;
    } else {
      return <td className={"align-middle text-success"}>{"False"}</td>;
    }
  }

  private _getSubmitButtonMessage(): JSX.Element {
    const { duration } = this.state;

    if (duration === "max") {
      return (
        <span>
          {"Get "}
          <u>{"all"}</u>
          {" users that failed KYC (Newest -> Oldest)"}
        </span>
      );
    }

    return (
      <span>
        {"Get users that failed KYC in the last "}
        <u>{`${DURATION_LABEL_CONFIG[duration]}`}</u>
        {" (Newest -> Oldest)"}
      </span>
    );
  }

  private _createNationalityColumn(user: UserDocument): JSX.Element {
    const nationality = user.nationalities[0];

    if (!nationality) {
      return <td className="align-middle">{"-"}</td>;
    }

    if (NATIONALITIES_TO_FLAG.includes(nationality)) {
      return <td className="align-middle text-danger">{nationality}</td>;
    } else {
      return <td className="align-middle text-success">{nationality}</td>;
    }
  }

  render(): JSX.Element {
    const { users, usersSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <h2>Query options:</h2>
        <form className="d-flex align-items-center" onSubmit={this.handleSubmit}>
          <div className="form-group mr-3">
            <label htmlFor="durationSelect">Duration</label>
            <select
              className="form-control"
              id="durationSelect"
              onChange={this.handleDurationChange}
              value={this.state.duration}
            >
              {DurationArray.map((duration) => (
                <option key={duration} value={duration}>
                  {DURATION_LABEL_CONFIG[duration]}
                </option>
              ))}
            </select>
          </div>
          <button type="submit" className="btn btn-primary">
            {this._getSubmitButtonMessage()}
          </button>
        </form>
        {users.length > 0 && (
          <div className="row">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs">
                <div className="card-body px-0 pb-0 table-responsive">
                  <table className="table">
                    <thead className="thead-light">
                      <tr className="text-uppercase">
                        <th>Email</th>
                        <th>Name</th>
                        <th>Nationality</th>
                        <th>Passport Verified</th>
                        <th>KYC Provider</th>
                        <th>Wealthkernel KYC</th>
                        <th>Duplicate</th>
                        <th />
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user, index) => {
                        return (
                          <tr key={`user-item-${index}`}>
                            <td className="align-middle">
                              <a href={`${user.id}`} className="font-size-lg font-weight-bold">
                                {user.email}
                              </a>
                            </td>
                            <td className="align-middle">{user.fullName || "-"}</td>
                            {this._createNationalityColumn(user)}
                            {this._createPassportMatchColumn(user)}
                            {this._createKycProviderColumn(user)}
                            {this._createWkKycColumn(user)}
                            {this._createDuplicateColumn(user)}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}
        <Pagination resultsSize={usersSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminFailedAccountsPage;
