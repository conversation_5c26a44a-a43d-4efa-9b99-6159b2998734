import React from "react";
import { UserDocument } from "../../models/User";
import { PagePropsType } from "../types/page";
import { isoDateToFriendlyFormat } from "../utils/dateUtil";
import AdminLayout from "../layouts/adminLayout";
import { GiftDocument } from "../../models/Gift";
import Decimal from "decimal.js";
import { formatCurrency } from "../utils/currencyUtil";
import { AssetTransactionDocument } from "../../models/Transaction";

export type AdminGiftDetailsPropsType = {
  gift: GiftDocument;
} & PagePropsType;

class AdminGiftDetailsPage extends React.Component<AdminGiftDetailsPropsType> {
  render(): JSX.Element {
    const { gift } = this.props;
    const {
      consideration,
      createdAt,
      targetUserEmail,
      gifter,
      hasViewedAppModal,
      used,
      deposit,
      linkedAssetTransaction
    } = gift;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* Gift Details */}
        <div className="row mb-15">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                <div className="row">
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Created At</p>
                      <p className="text-primary font-size-h4">{isoDateToFriendlyFormat(createdAt.toString())}</p>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Amount</p>
                      <p className="text-primary font-size-h4">
                        {formatCurrency(
                          Decimal.div(consideration.amount, 100).toNumber(),
                          gift.consideration.currency,
                          "en"
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* User Details Row */}
                <div className="row">
                  {/* Gifter Email */}
                  {gifter ? (
                    <div className="col-md-4">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Gifter Email</p>
                        <p className="text-primary font-size-h4">{(gifter as UserDocument).email}</p>
                      </div>
                    </div>
                  ) : (
                    <></>
                  )}
                  {/* End Gifter Email */}

                  {/* Target Email */}
                  <div className="col-md-8">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Target Email</p>
                      <p className="text-primary font-size-h4">{targetUserEmail}</p>
                    </div>
                  </div>
                  {/* End Target Email */}
                </div>
                {/* End User Details Row */}

                {/* User Interaction Row */}
                <div className="row">
                  {/* Viewed Gift */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Viewed</p>
                      <p className="text-primary font-size-h4">{hasViewedAppModal ? "Yes" : "No"}</p>
                    </div>
                  </div>
                  {/* End Viewed Gift */}

                  {/* Used Gift */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Used</p>
                      <p className="text-primary font-size-h4">{used ? "Yes" : "No"}</p>
                    </div>
                  </div>
                  {/* End Used Gift */}
                </div>
                {/* End User Interaction Row */}
              </div>
            </div>
          </div>
        </div>
        {/* End Gift Details */}

        {/* Bonus Details */}
        {deposit?.providers?.wealthkernel?.id ? (
          <div className="row">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs">
                <div className="card-body px-15">
                  <div className="d-flex flex-row justify-content-between pb-8">
                    <h2 className="font-size-h2 font-weight-bold">Bonus ID</h2>
                  </div>
                  <p className="text-primary font-size-h4">{deposit?.providers?.wealthkernel?.id}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <></>
        )}
        {/* End Bonus Details */}

        {/* Bonus Details */}
        {linkedAssetTransaction ? (
          <div className="row mt-4">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs">
                <div className="card-body px-15">
                  <div className="d-flex flex-row justify-content-between pb-8">
                    <h2 className="font-size-h2 font-weight-bold">Asset Transaction</h2>
                  </div>
                  <a
                    className="text-primary font-size-h4"
                    href={`/admin/transactions/${(linkedAssetTransaction as AssetTransactionDocument).id}`}
                  >
                    {(linkedAssetTransaction as AssetTransactionDocument).id}
                  </a>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <></>
        )}
        {/* End Bonus Details */}
      </AdminLayout>
    );
  }
}

export default AdminGiftDetailsPage;
