import React from "react";
import { PagePropsType } from "../types/page";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";
import { GiftDocument } from "../../models/Gift";
import AdminGiftRow from "../components/adminGiftRow";

export type AdminGiftsPropsType = {
  gifts: GiftDocument[];
  giftsSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminGiftsPage extends React.Component<AdminGiftsPropsType> {
  render(): JSX.Element {
    const { gifts, giftsSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {gifts.length > 0 && (
          <div className="table-responsive">
            <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
              <thead>
                <tr>
                  <th className="p-0" />
                  <th className="p-0 min-w-100px" />
                  <th className="p-0 text-muted">
                    <span>Amount</span>
                  </th>
                  <th className="p-0 min-w-100px text-muted">
                    <span>Date</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>Target User Email</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>Gifter User Email</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {gifts.map((gift, index) => (
                  <AdminGiftRow gift={gift} key={`gift-row-${index}`} />
                ))}
              </tbody>
            </table>
          </div>
        )}
        <Pagination resultsSize={giftsSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminGiftsPage;
