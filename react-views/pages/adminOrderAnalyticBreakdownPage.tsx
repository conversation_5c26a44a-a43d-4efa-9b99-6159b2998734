import React from "react";
import { PagePropsType } from "../types/page";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AdminLayout from "../layouts/adminLayout";

type LineType = {
  buyOrders: number;
  buyConsideration: number;
  sellOrders: number;
  sellConsideration: number;
  allMatched: boolean;
  allInTerminalState: boolean;
};

export type AdminOrderAnalyticsBreakdownPropsType = {
  date: string;
  lines: { [etf in investmentUniverseConfig.AssetType]: LineType };
} & PagePropsType;

class AdminOrderAnalyticsBreakdownPage extends React.Component<AdminOrderAnalyticsBreakdownPropsType> {
  private static _getAnalyticsLineStatus(lineAnalytics: LineType): JSX.Element {
    if (lineAnalytics.allMatched)
      return (
        <>
          <span className={"label label-dot label-success"} />
          <span className={"font-weight-bold text-success ml-2"}>Settled</span>
        </>
      );
    else if (lineAnalytics.allInTerminalState)
      return (
        <>
          <span className={"label label-dot label-danger"} />
          <span className={"font-weight-bold text-danger ml-2"}>Settled</span>
        </>
      );
    else
      return (
        <>
          <span className={"label label-dot label-warning"} />
          <span className={"font-weight-bold text-warning ml-2"}>Pending</span>
        </>
      );
  }

  render(): JSX.Element {
    const { date, lines } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="row">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                <div className="d-flex flex-row justify-content-between pb-20">
                  <h2 className="display-4 font-weight-bold">{date}</h2>
                </div>
                <div className="table-responsive">
                  <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
                    <thead>
                      <tr>
                        <th className="p-0 text-muted">
                          <span>ETF Name</span>
                        </th>
                        <th className="p-0 min-w-100px text-muted">
                          <span># Orders - Buy</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Sell</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Total</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Buy Consideration</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Sell Consideration</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Status</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(lines).map((line) => {
                        const commonId = line[0] as investmentUniverseConfig.AssetType;
                        const lineAnalytics = line[1];
                        return (
                          <tr key={commonId}>
                            <td className="pl-0">
                              <a
                                href={`/admin/order-management/analytics/breakdown/orders?&assetId=${commonId}&submissionDay=${date}`}
                                className="text-primary font-weight-bolder d-block font-size-h5"
                              >
                                {investmentUniverseConfig.ASSET_CONFIG[commonId].simpleName}
                              </a>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.buyOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellOrders + lineAnalytics.buyOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.buyConsideration.toLocaleString("en", {
                                  style: "currency",
                                  currency: "GBP",
                                  maximumFractionDigits: 2
                                })}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellConsideration.toLocaleString("en", {
                                  style: "currency",
                                  currency: "GBP",
                                  maximumFractionDigits: 2
                                })}
                              </span>
                            </td>
                            <td className="pl-0 text-nowrap">
                              {AdminOrderAnalyticsBreakdownPage._getAnalyticsLineStatus(lineAnalytics)}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>{" "}
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }
}

export default AdminOrderAnalyticsBreakdownPage;
