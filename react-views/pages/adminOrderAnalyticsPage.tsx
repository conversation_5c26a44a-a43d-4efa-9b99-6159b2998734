import React from "react";
import { PagePropsType } from "../types/page";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AdminLayout from "../layouts/adminLayout";

type LinesType = {
  [etf in investmentUniverseConfig.AssetType]: {
    buyOrders: number;
    buyConsideration: number;
    sellOrders: number;
    sellConsideration: number;
    allMatched: boolean;
    allInTerminalState: boolean;
  };
};

export type AdminOrderAnalyticsPropsType = {
  analytics: {
    date: string;
    lines: LinesType;
  }[];
  currentPage: number;
} & PagePropsType;

class AdminOrderAnalyticsPage extends React.Component<AdminOrderAnalyticsPropsType> {
  private static _getAnalyticLinesStatus(lines: LinesType): JSX.Element {
    const linesForDate = Object.entries(lines);

    const allMatched = linesForDate.every((analytic) => analytic[1].allMatched);
    const allInTerminalState = linesForDate.every((analytic) => analytic[1].allInTerminalState);

    if (allMatched)
      return (
        <>
          <span className={"label label-dot label-success"} />
          <span className={"font-weight-bold text-success ml-2"}>Settled</span>
        </>
      );
    else if (allInTerminalState)
      return (
        <>
          <span className={"label label-dot label-danger"} />
          <span className={"font-weight-bold text-danger ml-2"}>Settled</span>
        </>
      );
    else
      return (
        <>
          <span className={"label label-dot label-warning"} />
          <span className={"font-weight-bold text-warning ml-2"}>Pending</span>
        </>
      );
  }

  render(): JSX.Element {
    const { analytics, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="table-responsive">
          <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
            <thead>
              <tr>
                <th className="p-0 text-muted">
                  <span>Date</span>
                </th>
                <th className="p-0 min-w-100px text-muted">
                  <span># Lines - Buy</span>
                </th>
                <th className="p-0 text-muted">
                  <span># Lines - Sell</span>
                </th>
                <th className="p-0 text-muted">
                  <span># Lines - Total</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Total Buy Consideration</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Total Sell Consideration</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Status</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {analytics.map((analytic) => {
                const linesForDate = Object.entries(analytic.lines);

                const numberOfLinesBuy = linesForDate.filter((analytic) => analytic[1].buyOrders > 0).length;
                const numberOfLinesSell = linesForDate.filter((analytic) => analytic[1].sellOrders > 0).length;
                const numberOfLinesTotal = numberOfLinesBuy + numberOfLinesSell;
                const totalBuyConsideration = linesForDate
                  .map((analytic) => analytic[1].buyConsideration)
                  .reduce((sum, value) => sum + value, 0);
                const totalSellConsideration = linesForDate
                  .map((analytic) => analytic[1].sellConsideration)
                  .reduce((sum, value) => sum + value, 0);

                return (
                  <tr key={analytic.date}>
                    <td className="pl-0">
                      <a
                        className="text-primary font-weight-bolder d-block font-size-h5"
                        href={`/admin/order-management/analytics/breakdown?date=${analytic.date}`}
                      >
                        {analytic.date}
                      </a>
                    </td>
                    <td className="pl-0">
                      <span className="text-dark-75 font-weight-500 font-size-h6">{numberOfLinesBuy}</span>
                    </td>
                    <td className="pl-0">
                      <span className="text-dark-75 font-weight-500 font-size-h6">{numberOfLinesSell}</span>
                    </td>
                    <td className="pl-0">
                      <span className="text-dark-75 font-weight-500 font-size-h6">{numberOfLinesTotal}</span>
                    </td>
                    <td className="pl-0">
                      <span className="text-dark-75 font-weight-500 font-size-h6">
                        {totalBuyConsideration.toLocaleString("en", {
                          style: "currency",
                          currency: "GBP",
                          maximumFractionDigits: 2
                        })}
                      </span>
                    </td>
                    <td className="pl-0">
                      <span className="text-dark-75 font-weight-500 font-size-h6">
                        {totalSellConsideration.toLocaleString("en", {
                          style: "currency",
                          currency: "GBP",
                          maximumFractionDigits: 2
                        })}
                      </span>
                    </td>
                    <td className="pl-0 text-nowrap">
                      {AdminOrderAnalyticsPage._getAnalyticLinesStatus(analytic.lines)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <div className="d-flex justify-content-center mb-2">Each page represents an offset of months ago</div>
        <div className="d-flex justify-content-center">
          {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((page) => (
            <a
              key={`month-offset-${page}`}
              className={`btn ${currentPage == page ? "btn-primary" : "btn-secondary"} mr-2`}
              href={`?page=${page}`}
            >
              {page == 0 ? "Current" : page}
            </a>
          ))}
        </div>
      </AdminLayout>
    );
  }
}

export default AdminOrderAnalyticsPage;
