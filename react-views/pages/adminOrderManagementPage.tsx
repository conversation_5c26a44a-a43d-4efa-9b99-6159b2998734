import React from "react";
import { PagePropsType } from "../types/page";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import OrderLineStatusButton from "../components/OrderLineStatusButton";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { PartialRecord } from "../types/utils";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import AdminLayout from "../layouts/adminLayout";

export type AdminOrderManagementPropsType = {
  lines: {
    [etf in investmentUniverseConfig.AssetType]: {
      buyOrders: number;
      buyConsideration: number;
      sellOrders: number;
      sellConsideration: number;
    };
  };
  investmentProductsDict: PartialRecord<investmentUniverseConfig.AssetType, InvestmentProductDocument>;
} & PagePropsType;

class AdminOrderManagementPage extends React.Component<AdminOrderManagementPropsType> {
  private _onStatusChangeClick = async (isPaused: boolean, assetId: string, side: string): Promise<void> => {
    try {
      if (isPaused) {
        await axios.post(`/admin/investment-products/${assetId}/resume?side=${side}`);
      } else {
        await axios.post(`/admin/investment-products/${assetId}/pause?side=${side}`);
      }
      emitToast({
        content: `Line ${isPaused ? "resumed" : "paused"} successfully!`,
        toastType: ToastTypeEnum.success
      });
      window.location.reload();
    } catch (err) {
      emitToast({
        content: "An error occurred when changing the status of the line",
        toastType: ToastTypeEnum.error
      });
    }
  };

  render(): JSX.Element {
    const { lines, investmentProductsDict } = this.props;

    const linesWithoutOrders = investmentUniverseConfig.AssetArrayConst.filter(
      (commonId) => investmentProductsDict[commonId] && !Object.keys(lines).includes(commonId)
    );

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* Actions */}
        <div className="row mb-10">
          <div className="col-12 d-flex align-items-center">
            <a className="btn btn-primary shadow-effect mr-4" href="/admin/order-management/analytics">
              Analytics History
            </a>
          </div>
        </div>
        {/* End Actions */}

        {/* Lines With Orders List */}
        <div className="row mb-10">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                <div className="d-flex flex-row justify-content-between pb-20">
                  <h2 className="display-4 font-weight-bold">Lines with orders</h2>
                </div>
                <div className="table-responsive">
                  <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
                    <thead>
                      <tr>
                        <th className="p-0 text-muted">
                          <span>ETF Name</span>
                        </th>
                        <th className="p-0 min-w-100px text-muted">
                          <span># Orders - Buy</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Sell</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Total</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Buy £</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Sell £</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Status - Buy</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Status - Sell</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(lines).map((line) => {
                        const commonId = line[0] as investmentUniverseConfig.AssetType;
                        const lineAnalytics = line[1];
                        return (
                          <tr key={commonId}>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {investmentUniverseConfig.ASSET_CONFIG[commonId].simpleName}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.buyOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellOrders + lineAnalytics.buyOrders}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.buyConsideration.toLocaleString("en", {
                                  style: "currency",
                                  currency: "GBP",
                                  maximumFractionDigits: 2
                                })}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {lineAnalytics.sellConsideration.toLocaleString("en", {
                                  style: "currency",
                                  currency: "GBP",
                                  maximumFractionDigits: 2
                                })}
                              </span>
                            </td>
                            <td>
                              <OrderLineStatusButton
                                isPaused={!investmentProductsDict[commonId].buyLine.active}
                                onStatusChangeClick={() =>
                                  this._onStatusChangeClick(
                                    !investmentProductsDict[commonId].buyLine.active,
                                    investmentProductsDict[commonId].id,
                                    "Buy"
                                  )
                                }
                              />
                            </td>
                            <td>
                              <OrderLineStatusButton
                                isPaused={!investmentProductsDict[commonId].sellLine.active}
                                onStatusChangeClick={() =>
                                  this._onStatusChangeClick(
                                    !investmentProductsDict[commonId].sellLine.active,
                                    investmentProductsDict[commonId].id,
                                    "Sell"
                                  )
                                }
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>{" "}
              </div>
            </div>
          </div>
        </div>
        {/* End Lines With Orders List */}

        {/* Lines Without Orders List */}
        <div className="row">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                <div className="d-flex flex-row justify-content-between pb-20">
                  <h2 className="display-4 font-weight-bold">Lines without orders</h2>
                </div>
                <div className="table-responsive">
                  <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
                    <thead>
                      <tr>
                        <th className="p-0 text-muted">
                          <span>ETF Name</span>
                        </th>
                        <th className="p-0 min-w-100px text-muted">
                          <span># Orders - Buy</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Sell</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span># Orders - Total</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Buy £</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Total Sell £</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Status - Buy</span>
                        </th>
                        <th className="p-0 text-muted">
                          <span>Status - Sell</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {linesWithoutOrders.map((commonId) => {
                        return (
                          <tr key={commonId}>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">
                                {investmentUniverseConfig.ASSET_CONFIG[commonId].simpleName}
                              </span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">-</span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">-</span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">-</span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">-</span>
                            </td>
                            <td className="pl-0">
                              <span className="text-dark-75 font-weight-500 font-size-h6">-</span>
                            </td>
                            <td>
                              <OrderLineStatusButton
                                isPaused={!investmentProductsDict[commonId].buyLine.active}
                                onStatusChangeClick={() =>
                                  this._onStatusChangeClick(
                                    !investmentProductsDict[commonId].buyLine.active,
                                    investmentProductsDict[commonId].id,
                                    "Buy"
                                  )
                                }
                              />
                            </td>
                            <td>
                              <OrderLineStatusButton
                                isPaused={!investmentProductsDict[commonId].sellLine.active}
                                onStatusChangeClick={() =>
                                  this._onStatusChangeClick(
                                    !investmentProductsDict[commonId].sellLine.active,
                                    investmentProductsDict[commonId].id,
                                    "Sell"
                                  )
                                }
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>{" "}
              </div>
            </div>
          </div>
        </div>
        {/* End Lines Without Orders List */}
      </AdminLayout>
    );
  }
}

export default AdminOrderManagementPage;
