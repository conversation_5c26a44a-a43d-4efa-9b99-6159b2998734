import React from "react";
import { OrderDocument } from "../../models/Order";
import { PagePropsType } from "../types/page";
import OrdersTable, { OrderTableColumnsType } from "../components/ordersTable";
import TransactionSyncButton from "../components/transactionSyncButton";
import AdminLayout from "../layouts/adminLayout";
import TransactionLayout from "../layouts/transactionLayout";

export type AdminOrdersBreakdownPropsType = {
  enableSync: boolean;
  ownerId?: string;
  orders: OrderDocument[];
  columns?: OrderTableColumnsType[];
  transactionId?: string; // transactionId can be null when order belongs to reward
} & PagePropsType;

class AdminOrdersBreakdownPage extends React.Component<AdminOrdersBreakdownPropsType> {
  static defaultProps = {
    columns: [
      "WK Order ID",
      "Name",
      "Status",
      "Reason",
      "Action",
      "Money",
      "Quantity",
      "Created"
    ] as OrderTableColumnsType[]
  };

  constructor(props: AdminOrdersBreakdownPropsType) {
    super(props);
  }

  render(): JSX.Element {
    const { enableSync, orders, transactionId, columns, ownerId } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <TransactionLayout ownerId={ownerId}>
          <div className="row">
            <div className="col-12">
              <div className="card border-radius-xl shadow-xs">
                <div className="card-body px-0 pb-0">
                  {" "}
                  <div className="d-flex justify-content-between pl-8 pb-8">
                    <h2 className="my-auto">Orders</h2>
                    {enableSync && <TransactionSyncButton transactionId={transactionId} />}
                  </div>
                  <OrdersTable orders={orders} columns={columns} />
                </div>
              </div>
            </div>
          </div>
        </TransactionLayout>
      </AdminLayout>
    );
  }
}

export default AdminOrdersBreakdownPage;
