import axios from "axios";
import React from "react";
import { captureException } from "@sentry/react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import { ToastTypeEnum } from "../configs/toastConfig";
import { RewardDocument } from "../../models/Reward";
import { UserDocument } from "../../models/User";
import { PagePropsType } from "../types/page";
import { formatCurrency } from "../utils/currencyUtil";
import { isoDateToFriendlyFormat } from "../utils/dateUtil";
import { emitToast } from "../utils/eventService";
import AdminLayout from "../layouts/adminLayout";

export type AdminRewardDetailsPropsType = {
  reward: RewardDocument;
} & PagePropsType;

type StateType = {
  depositId: string;
};

class AdminRewardDetailsPage extends React.Component<AdminRewardDetailsPropsType, StateType> {
  constructor(props: AdminRewardDetailsPropsType) {
    super(props);
    this.state = {
      depositId: ""
    };
  }

  private _handleDepositIdChange = (event: any): void => {
    const depositId = event?.target?.value || "";
    this.setState({ depositId });
  };

  private _getRewardAcceptedValue = (accepted: any): string => {
    if (accepted === undefined) {
      return "Not responded yet";
    } else if (accepted) {
      return "Yes";
    } else return "No";
  };

  private _submitDepositId = async (): Promise<void> => {
    const { reward } = this.props;
    const { depositId } = this.state;

    try {
      await axios({
        method: "post",
        url: `/admin/rewards/${reward.id}`,
        data: {
          depositId
        }
      });
      emitToast({
        content: "Reward updated successfully!",
        toastType: ToastTypeEnum.success
      });
      window.location.reload();
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  render(): JSX.Element {
    const { depositId } = this.state;
    const { reward } = this.props;
    const {
      consideration,
      asset,
      createdAt,
      unrestrictedAt,
      referral,
      referrer,
      targetUser,
      depositStatus,
      orderStatus,
      deposit,
      hasViewedAppModal,
      accepted
    } = reward;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* Reward Details */}
        <div className="row mb-15">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                <div className="d-flex flex-row justify-content-between pb-20">
                  <h2 className="display-4 font-weight-bold">
                    {formatCurrency(consideration.amount / 100, consideration.currency, "en")}{" "}
                    {investmentUniverseConfig.ASSET_CONFIG[asset].simpleName}
                  </h2>
                </div>

                {/* Date Details Row */}
                <div className="row">
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Created At</p>
                      <p className="text-primary font-size-h4">{isoDateToFriendlyFormat(createdAt.toString())}</p>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="d-flex flex-column">
                      <p className="text-dark-50 mb-0">Unrestricted At</p>
                      <p className="text-primary font-size-h4">
                        {isoDateToFriendlyFormat(unrestrictedAt.toString())}
                      </p>
                    </div>
                  </div>
                </div>
                {/* End Date Details Row */}

                {/* Status Details Row */}
                <div className="row">
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Deposit Status</p>
                      <p className="text-primary font-size-h4">{depositStatus}</p>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="d-flex flex-column">
                      <p className="text-dark-50 mb-0">Order Status</p>
                      <p className="text-primary font-size-h4">{orderStatus}</p>
                    </div>
                  </div>
                </div>
                {/* End Status Details Row */}

                {/* User Details Row */}
                <div className="row">
                  {/* Referrer Email */}
                  {referrer ? (
                    <div className="col-md-4">
                      <div className="d-flex flex-column mb-3">
                        <p className="text-dark-50 mb-0">Referrer Email</p>
                        <p className="text-primary font-size-h4">{(referrer as UserDocument).email}</p>
                      </div>
                    </div>
                  ) : (
                    <></>
                  )}
                  {/* End Referrer Email */}

                  {/* Referral Email */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Referral Email</p>
                      <p className="text-primary font-size-h4">{(referral as UserDocument).email}</p>
                    </div>
                  </div>
                  {/* End Referral Email */}

                  {/* Target Email */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Target Email</p>
                      <p className="text-primary font-size-h4">{(targetUser as UserDocument).email}</p>
                    </div>
                  </div>
                  {/* End Target Email */}
                </div>
                {/* End User Details Row */}

                {/* User Interaction Row */}
                <div className="row">
                  {/* Viewed Reward */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Viewed</p>
                      <p className="text-primary font-size-h4">{hasViewedAppModal ? "Yes" : "No"}</p>
                    </div>
                  </div>
                  {/* End Viewed Reward */}

                  {/* Accepted Reward */}
                  <div className="col-md-4">
                    <div className="d-flex flex-column mb-3">
                      <p className="text-dark-50 mb-0">Accepted</p>
                      <p className="text-primary font-size-h4">{this._getRewardAcceptedValue(accepted)}</p>
                    </div>
                  </div>
                  {/* End Accepted Reward */}
                </div>
                {/* End User Interaction Row */}
              </div>
            </div>
          </div>
        </div>
        {/* End Reward Details */}

        {/* Transaction Input */}
        <div className="row">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-15">
                {/* Card Title */}
                <div className="d-flex flex-row justify-content-between pb-8">
                  <h2 className="font-size-h2 font-weight-bold">Deposit Transaction ID</h2>
                </div>
                {/* End Card Title */}
                {depositStatus === "Empty" ? (
                  <div className="row">
                    <div className="col-lg-6">
                      <div className="d-flex flex-row">
                        <input
                          type="text"
                          className="form-control"
                          name="depositId"
                          placeholder="Deposit ID"
                          value={depositId}
                          onChange={this._handleDepositIdChange}
                          required
                        />
                        <LoadingOnSubmitButton
                          type="button"
                          className="btn btn-primary ml-3"
                          customonclick={async () => this._submitDepositId()}
                        >
                          Submit
                        </LoadingOnSubmitButton>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-primary font-size-h4">{deposit.providers?.wealthkernel?.id}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }
}

export default AdminRewardDetailsPage;
