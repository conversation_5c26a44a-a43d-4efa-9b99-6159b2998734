import React from "react";
import { PagePropsType } from "../types/page";
import AdminLayout from "../layouts/adminLayout";
import RewardsReferralSubmission from "../components/rewardsReferralSubmission";
import RewardsSingleSubmission from "../components/rewardsSingleSubmission";

enum SelectedTabEnum {
  ReferralRewards = "Referral Rewards",
  SingleReward = "Single Reward"
}
type StateType = {
  selectedTab: SelectedTabEnum;
};
export type AdminRewardSubmissionPropsType = PagePropsType;

class AdminRewardSubmissionPage extends React.Component<AdminRewardSubmissionPropsType, StateType> {
  constructor(props: AdminRewardSubmissionPropsType) {
    super(props);
    this.state = {
      selectedTab: SelectedTabEnum.ReferralRewards
    };
  }

  private _setSelectedTab(selectedTab: SelectedTabEnum) {
    this.setState({ selectedTab });
  }

  render(): JSX.Element {
    const { selectedTab } = this.state;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="d-flex" style={{ gap: "1rem" }}>
          <button
            disabled={selectedTab === SelectedTabEnum.ReferralRewards}
            onClick={() => this._setSelectedTab(SelectedTabEnum.ReferralRewards)}
            className="btn btn-primary font-weight-bolder mb-10"
          >
            {SelectedTabEnum.ReferralRewards}
          </button>

          <button
            disabled={selectedTab === SelectedTabEnum.SingleReward}
            onClick={() => this._setSelectedTab(SelectedTabEnum.SingleReward)}
            className="btn btn-primary font-weight-bolder mb-10"
          >
            {SelectedTabEnum.SingleReward}
          </button>
        </div>
        {selectedTab === SelectedTabEnum.ReferralRewards && <RewardsReferralSubmission />}
        {selectedTab === SelectedTabEnum.SingleReward && <RewardsSingleSubmission />}
      </AdminLayout>
    );
  }
}

export default AdminRewardSubmissionPage;
