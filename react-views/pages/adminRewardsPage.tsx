import React from "react";
import { RewardDocument } from "../../models/Reward";
import AdminRewardRow from "../components/adminRewardRow";
import { PagePropsType } from "../types/page";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";

export type AdminRewardsPropsType = {
  rewards: RewardDocument[];
  rewardsSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminRewardsPage extends React.Component<AdminRewardsPropsType> {
  render(): JSX.Element {
    const { rewards, rewardsSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* Reward Submission Button */}
        <a className="btn btn-primary font-weight-bolder mb-10" href="/admin/reward-submission">
          Create Rewards
        </a>
        {/* End Reward Submission Button */}

        {rewards.length > 0 && (
          <div className="table-responsive">
            <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
              <thead>
                <tr>
                  <th className="p-0" />
                  <th className="p-0 min-w-100px" />
                  <th className="p-0 text-muted">
                    <span>Amount</span>
                  </th>
                  <th className="p-0 min-w-100px text-muted">
                    <span>Date</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>Deposit Status</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>Order Status</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>User Email</span>
                  </th>
                  <th className="p-0 text-muted">
                    <span>WK Portfolio</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {rewards.map((reward, index) => (
                  <AdminRewardRow reward={reward} key={`reward-row-${index}`} />
                ))}
              </tbody>
            </table>
          </div>
        )}
        <Pagination resultsSize={rewardsSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminRewardsPage;
