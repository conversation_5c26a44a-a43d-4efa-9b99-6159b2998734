import React from "react";
import { PagePropsType } from "../types/page";
import { UserDocument } from "../../models/User";
import InvestorTaskCompletionLabel from "../components/investorTaskCompletionLabel";
import { formatCurrency } from "../utils/currencyUtil";
import Pagination from "../components/pagination";
import AdminLayout from "../layouts/adminLayout";
import { isVerified } from "../utils/userUtil";

export type AdminUserListPropsType = {
  users: UserDocument[];
  usersSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminUserListPage extends React.Component<AdminUserListPropsType> {
  render(): JSX.Element {
    const { users, usersSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="row">
          <div className="col-12">
            <div className="card border-radius-xl shadow-xs">
              <div className="card-body px-0 pb-0 table-responsive">
                <table className="table">
                  <thead className="thead-light">
                    <tr className="text-uppercase">
                      <th>Email</th>
                      <th>Name</th>
                      <th>Residency</th>
                      <th>Stage</th>
                      <th>Cash</th>
                      <th>Portfolio</th>
                      <th />
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user, index) => (
                      <tr key={`user-item-${index}`}>
                        <td className="align-middle">
                          <a href={`users/${user.id}`} className="font-size-lg font-weight-bold">
                            {user.email}
                          </a>
                        </td>
                        <td className="align-middle">{user.fullName || "-"}</td>
                        <td className="align-middle">{user.residencyCountry || "-"}</td>
                        <td className="align-middle">
                          <InvestorTaskCompletionLabel
                            classNames={["mr-2", "mt-2"]}
                            label="Virtual"
                            taskCompleted={!!user.portfolios?.[0]?.initialHoldingsAllocation?.length}
                          />
                          <InvestorTaskCompletionLabel
                            classNames={["mr-2", "mt-2"]}
                            label="Personal"
                            taskCompleted={user.passportSubmitted}
                          />
                          <InvestorTaskCompletionLabel
                            classNames={["mr-2", "mt-2"]}
                            label="Bank"
                            taskCompleted={user.bankLinked}
                          />
                          <InvestorTaskCompletionLabel
                            classNames={["mr-2", "mt-2"]}
                            label="KYC"
                            taskCompleted={isVerified(user)}
                          />
                        </td>
                        <td className="align-middle">
                          <span className="font-size-lg font-weight-bold text-dark-75">
                            {user.currency
                              ? formatCurrency(
                                  user.portfolios[0]?.cash?.[user.currency]?.available || 0,
                                  user.currency,
                                  "en"
                                )
                              : "-"}
                          </span>
                        </td>
                        <td className="align-middle">
                          <span className="font-size-lg font-weight-bold text-dark-75">
                            {user.currency
                              ? formatCurrency(user.portfolios[0]?.currentTicker?.price || 0, user.currency, "en")
                              : "-"}
                          </span>
                        </td>
                        <td className="align-middle">
                          {user.hasConvertedPortfolio ? (
                            <a
                              href={`users/portfolios/${user.portfolios[0].id}?userId=${user.id}`}
                              className="font-size-lg font-weight-bold"
                            >
                              🔗
                            </a>
                          ) : (
                            user.portfolios?.[0] && (
                              <a
                                href={`portfolios/${user.portfolios?.[0]?.id}?userId=${user.id}`}
                                className="font-size-lg font-weight-bold"
                              >
                                🔗
                              </a>
                            )
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <Pagination resultsSize={usersSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminUserListPage;
