import React from "react";
import {
  TransactionDocument,
  TransactionStatusType,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import { UserDocument } from "../../models/User";
import Pagination from "../components/pagination";
import AdminTransactionRow from "../components/adminTransactionRow";
import { oldConfig } from "../configs/transactionsTableConfig";
import { PagePropsType } from "../types/page";
import AdminLayout from "../layouts/adminLayout";

export type AdminWithdrawalTransactionsPropsType = {
  transactions: WithdrawalCashTransactionDocument[];
  transactionsSize: number;
  pageSize: number;
  currentPage: number;
} & PagePropsType;

class AdminWithdrawalTransactionsPage extends React.Component<AdminWithdrawalTransactionsPropsType> {
  private _getEffectiveStatus(transaction: TransactionDocument): TransactionStatusType {
    if (transaction.category !== "WithdrawalCashTransaction") {
      return transaction.displayStatus;
    }

    if ((transaction as WithdrawalCashTransactionDocument).providers?.wealthkernel?.status === "Rejected") {
      return "Rejected";
    }

    return transaction.displayStatus;
  }

  render(): JSX.Element {
    const { transactions, transactionsSize, pageSize, currentPage } = this.props;

    return (
      <AdminLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="table-responsive">
          <table className="table table-borderless table-vertical-center" style={{ fontFamily: "Roboto" }}>
            <thead>
              <tr>
                <th className="p-0" />
                <th className="p-0 min-w-100px" />
                <th className="p-0 text-muted">
                  <span>Amount</span>
                </th>
                <th className="p-0 min-w-100px text-muted">
                  <span>Date</span>
                </th>
                <th className="p-0 text-muted">
                  <span>Status</span>
                </th>
                <th className="p-0 text-muted">
                  <span>User Email</span>
                </th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction, index) => {
                const { _id, category, consideration, createdAt, owner } = transaction;
                const transactionConfig = oldConfig[category];
                const Icon = transactionConfig.icon;

                const transactionName = `${transactionConfig.nameDisplay}`;
                return (
                  <AdminTransactionRow
                    amount={consideration.amount}
                    currency={consideration.currency}
                    date={new Date(createdAt)}
                    icon={<Icon />}
                    iconColorClass={transactionConfig.iconColorClass}
                    name={transactionName}
                    status={this._getEffectiveStatus(transaction)}
                    transactionId={_id}
                    transactionUrl={`/admin/transactions/${_id}`}
                    key={`transaction_${index}`}
                  >
                    <td className="pl-0">
                      <a className="text-dark-75 font-size-h5">{(owner as UserDocument).email}</a>
                    </td>
                  </AdminTransactionRow>
                );
              })}
            </tbody>
          </table>
        </div>
        <Pagination resultsSize={transactionsSize} currentPage={currentPage} pageSize={pageSize} />
      </AdminLayout>
    );
  }
}

export default AdminWithdrawalTransactionsPage;
