import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import LearningHubItemPaywall from "../components/learningHubItemPaywall";
import ScopedHtmlComponent from "../components/scopedHtmlComponent";
import {
  ANALYST_INSIGHT_ICON_CONFIG,
  ARTICLE_ICON_CONFIG,
  ArticleContentTypeEnum,
  ArticleDataType
} from "../configs/learningHubConfig";
import { formatDateToDDMONYYYY } from "../utils/dateUtil";

export type ArticlePropsType = {
  article: ArticleDataType;
  dateToDisplay: Date;
  returnTo?: string;
} & PagePropsType;

//TODO: @Takaros999 add option for dashboard as well
const ARTICLE_REDIRECT_URL_DICT = {
  [ArticleContentTypeEnum.NEWS]: "/investor/learning-hub?tab=news",
  [ArticleContentTypeEnum.ANALYST_INSIGHT]: "/investor/learning-hub?tab=insights"
};

class ArticlePage extends React.Component<ArticlePropsType> {
  private _getArticleTypeIcon(): JSX.Element {
    const { article } = this.props;

    const { materialIcon, text, color, backgroundColor } = article?.analystInsightType
      ? ANALYST_INSIGHT_ICON_CONFIG[article.analystInsightType]
      : ARTICLE_ICON_CONFIG[article.contentType];

    return (
      <>
        <div style={{ color: color }} className="d-flex gap-2 align-items-center me-3">
          <span
            style={{ backgroundColor: backgroundColor }}
            className="material-icons align-self-center learning-hub-article-preview-icon"
          >
            {materialIcon}
          </span>
          <span className="fw-bold">{text} </span>
        </div>
      </>
    );
  }

  private _shouldActivatePaywall() {
    const { article, user } = this.props;
    const subscription = user.subscription;

    if (article.contentType === ArticleContentTypeEnum.NEWS) {
      return false;
    }

    if (!subscription) {
      return true;
    }

    if (subscription.isPaidPlan) {
      return false;
    }

    return true;
  }

  render(): JSX.Element {
    const { article, returnTo, dateToDisplay } = this.props;
    const date = formatDateToDDMONYYYY(new Date(dateToDisplay));

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="px-md-0 px-3">
          {/* Back Button */}
          <div className="row p-0 m-0 mb-3">
            <div className="col p-0">
              <span
                className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                onClick={() => (window.location.href = returnTo ?? ARTICLE_REDIRECT_URL_DICT[article.contentType])}
                style={{
                  fontSize: "24px"
                }}
              >
                arrow_back
              </span>
            </div>
          </div>
          {/* End Back Button*/}
          <div className="position-relative mb-4">
            <img className="w-100 learning-hub-article-image" src={article.fullImageURL} />
          </div>
          <div className="d-flex mb-5 gap-2">
            <div
              className="d-flex flex-column justify-content-center gap-3"
              id="text-column that takes 8 and i also a flexbox"
            >
              <h2 className="fw-bolder learning-hub-article-title m-0">{article.title}</h2>
              <div className="d-flex align-items-center">
                {this._getArticleTypeIcon()}
                <span>{date}</span>
                <span className="mx-2"> • </span>
                <span>{article.readingTime}</span>
              </div>
              <div className="d-flex gap-2 flex-wrap">
                {article.tags?.map((tag: string, index: number) => {
                  return (
                    <span className="learning-hub-article-tag" key={index}>
                      {tag}
                    </span>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <MainCard className={"px-md-0 px-0"}>
          <ScopedHtmlComponent
            className={`px-md-5 px-0 learning-hub-html-container ${
              this._shouldActivatePaywall() ? "paywalled" : ""
            }`}
            htmlContent={article.contentHTML}
          />
          {this._shouldActivatePaywall() && <LearningHubItemPaywall />}
        </MainCard>
      </MainLayout>
    );
  }
}

export default ArticlePage;
