import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import ModalsWrapper from "../components/modalsWrapper";
import InfoModal from "../components/modals/infoModal";
import { AssetDiscoveryDataType } from "../types/assetDiscovery";
import AssetDiscoveryTopMoversSection from "../components/assetDiscoveryTopMoversSection";
import AssetDiscoveryPopularSection from "../components/assetDiscoveryPopularSection";
import AssetDiscoveryEtfSection from "../components/assetDiscoveryEtfSection";
import AssetDiscoveryPopularIndexEtfSection from "../components/assetDiscoveryPopularIndexEtfSection";
import AssetDiscoverySectorSection from "../components/assetDiscoverySectorSection";
import AssetDiscoverAssetClassSection from "../components/assetDiscoveryAssetClassSection";
import AssetDiscoveryCollectionSection from "../components/assetDiscoveryCollectionSection";
import { AssetDiscoveryListViewModeType } from "../components/assetDiscoveryList";
import { eventEmitter, EVENTS } from "../utils/eventService";
import ConfigUtil, { InvestmentUniverseAssets } from "../../utils/configUtil";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import AssetDiscoverySearchBar from "../components/assetDiscoverySearchBar";
import AssetDiscoveryReadyMadeSection from "../components/assetDiscoveryReadyMadeSection";

type InfoKeyType =
  | "assetClass"
  | "popularAssets"
  | "etfs"
  | "topMovers"
  | "sectors"
  | "popularIndexEtfs"
  | "readyMadeEtfs";

const INFO_CONFIG: Record<InfoKeyType, string> = {
  popularAssets:
    "<h5 class='fw-bolder-black'>Popular this week</h5><br><p class='text-muted'>Discover the most popular stocks and ETFs on Wealthyhood, the most traded and frequently searched for by our users over the past week.</p>",
  topMovers:
    "<h5 class='fw-bolder-black'>Top movers</h5><br><p class='text-muted'>Here are the stocks that gained or lost the most during their last trading session.</p>",
  assetClass:
    "<h5 class='fw-bolder-black'>Asset classes</h5><br><p class='text-muted'>Asset classes refer to groups of investments that share similar characteristics, behave similarly in the market, and are subject to the same laws and regulations.</p><p class='text-muted'>The main asset classes are stocks, bonds, commodities, real estate and cash. Investors often diversify their portfolios across different asset classes to manage risk and optimise returns.</p><p class='text-muted'>In this section, you can explore assets grouped by their asset class.</p>",
  sectors:
    "<h5 class='fw-bolder-black'>Sectors</h5><br><p class='text-muted'>Sectors refer to specific areas of the economy and include companies that share similar characteristics.</p><p class='text-muted'>The major sectors are technology, healthcare, consumer goods, energy, communication, financials, industrials, utilities and materials.</p><p class='text-muted'>In this section, you can explore stocks and ETFs that focus on each of these sectors of the economy.</p>",
  etfs: "<h5 class='fw-bolder-black'>ETFs</h5><br><p class='text-muted'>ETFs (Exchange-Traded Funds) are investment funds that hold a basket of stocks, bonds, or other assets and can be bought and sold on a stock exchange, just like regular stocks.</p><p class='text-muted'>ETFs track the movements of an index, making it easy to invest in a whole host of markets – from European shares to US government bonds and gold.</p><p class='text-muted'>In this section, you can explore ETFs from different asset classes (stocks, bonds, commodities and real estate), geographies and industries.</p>",
  popularIndexEtfs:
    "<h5 class='fw-bolder-black'>Popular Index ETFs</h5><br><p class='text-muted'>Discover the most popular index ETFs, mirroring the movements of renowned indexes, like the S&P 500, the FTSE 100 and the tech-heavy NASDAQ 100.</p>",
  readyMadeEtfs:
    "<h5 class='fw-bolder-black'>Ready-made portfolios</h5><br><p class='text-muted'>Ready-made portfolios are professionally designed investment mixes that do the heavy lifting for you. Instead of picking individual stocks or funds, you get a complete, balanced portfolio in one simple investment.</p><p class='text-muted'>They are actively managed, allocating specific % to global equities and bonds, depending on their risk profile.</p>"
};

export type AssetDiscoveryPropsType = {
  // this could be undefined in case the API call to edge server fails
  assetDiscoveryData?: AssetDiscoveryDataType;
  investmentProducts: InvestmentProductDocument[];
  fromPage: "home" | "investments";
} & PagePropsType;

type StateType = {
  showInfoDialog: boolean;
  infoHTML: string;
  isUserSearching: boolean;
};

class AssetDiscoveryPage extends React.Component<AssetDiscoveryPropsType, StateType> {
  constructor(props: AssetDiscoveryPropsType) {
    super(props);

    this.state = {
      showInfoDialog: false,
      infoHTML: "",
      isUserSearching: false
    };
  }

  private _setShowInfoDialog(showInfoDialog: boolean, infoHTML?: string) {
    this.setState({ showInfoDialog, infoHTML });
  }

  private _constructShowInfoCb(infoKey: InfoKeyType): () => void {
    return () => {
      this._setShowInfoDialog(true, INFO_CONFIG[infoKey]);
    };
  }

  private _showAssetList(assetListViewMode: AssetDiscoveryListViewModeType): void {
    eventEmitter.emit(EVENTS.assetListModal, assetListViewMode, this.props.assetDiscoveryData?.collections);
  }

  private _onUserSearch = (active: boolean) => {
    this.setState({ isUserSearching: active });
  };

  render(): JSX.Element {
    const { assetDiscoveryData, fromPage } = this.props;
    const { showInfoDialog, infoHTML, isUserSearching } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        <ModalsWrapper user={this.props.user} activePage={this.props.activePage} />
        {/* Back Button */}
        <div className="row p-0 m-0 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
              onClick={() => (window.location.href = fromPage === "home" ? "/" : "/investor/investments")}
              style={{
                fontSize: "24px"
              }}
            >
              arrow_back
            </span>
          </div>
        </div>

        {/*/!* About *!/*/}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col align-self-center p-0">
            {/* Description */}
            <h2 className="fw-bolder p-0 mb-4">Discover</h2>
            {/* End Description */}
          </div>
        </div>
        {/*/!* End About *!/*/}

        <MainCard>
          <div className="mb-5">
            <AssetDiscoverySearchBar
              universe={
                ConfigUtil.getActiveOnlyInvestmentUniverseAssets(
                  this.props.user.companyEntity
                ) as InvestmentUniverseAssets
              }
              onUserSearch={this._onUserSearch}
              investmentProducts={this.props.investmentProducts}
            />
          </div>
          {!isUserSearching && (
            <>
              {/* Popular Index ETFs */}
              {assetDiscoveryData?.etfSection.popularIndex && (
                <AssetDiscoveryPopularIndexEtfSection
                  popularIndexEtfs={assetDiscoveryData.etfSection.popularIndex}
                  showInfo={this._constructShowInfoCb("popularIndexEtfs")}
                />
              )}
              {/* End Popular Index ETFs */}

              {/* Sectors */}
              <AssetDiscoverySectorSection
                showInfo={this._constructShowInfoCb("sectors")}
                onSectorClick={(sector: investmentUniverseConfig.InvestmentSectorType) =>
                  this._showAssetList(sector)
                }
              />
              {/* End Sectors */}

              {/* Popular this week */}
              {assetDiscoveryData?.popularAssetsSection && (
                <AssetDiscoveryPopularSection
                  popularAssets={assetDiscoveryData.popularAssetsSection}
                  showInfo={this._constructShowInfoCb("popularAssets")}
                />
              )}
              {/* End - Popular this week */}

              {/* Top Movers */}
              {assetDiscoveryData?.topMovers && (
                <AssetDiscoveryTopMoversSection
                  topMovers={assetDiscoveryData.topMovers}
                  showInfo={this._constructShowInfoCb("topMovers")}
                />
              )}
              {/* End Top Movers */}

              {/* Asset Classes */}
              <AssetDiscoverAssetClassSection
                showInfo={this._constructShowInfoCb("assetClass")}
                onAssetClassClick={(assetClass: investmentUniverseConfig.AssetClassType) =>
                  this._showAssetList(assetClass)
                }
                companyEntity={this.props.user.companyEntity}
              />
              {/* End Asset Classes */}

              {/* ETFs */}
              {assetDiscoveryData?.etfSection?.top && (
                <AssetDiscoveryEtfSection
                  topEtfs={assetDiscoveryData.etfSection.top}
                  showInfo={this._constructShowInfoCb("etfs")}
                  onSeeAllClick={() => this._showAssetList("etfs")}
                />
              )}
              {/* End ETFs */}

              {/* Ready-made Portfolios */}
              <AssetDiscoveryReadyMadeSection
                readyMadeEtfs={ConfigUtil.getAssetClasses(this.props.user.companyEntity)?.readyMade?.assets}
                showInfo={this._constructShowInfoCb("readyMadeEtfs")}
              />
              {/* End Ready-made Portfolios */}

              {/* Collections */}
              {assetDiscoveryData?.collections && (
                <AssetDiscoveryCollectionSection
                  assetCollections={assetDiscoveryData.collections}
                  onCollectionClick={(collection: investmentUniverseConfig.InvestmentCollectionType) =>
                    this._showAssetList(collection)
                  }
                />
              )}
              {/* End Collections */}

              <div className="row w-100">
                <button
                  className="btn btn-asset-discovery-see-all fw-bolder-black"
                  onClick={() => this._showAssetList("all")}
                >
                  See all
                </button>
              </div>
            </>
          )}
        </MainCard>

        <InfoModal title={null} show={showInfoDialog} handleClose={() => this._setShowInfoDialog(false)}>
          <div dangerouslySetInnerHTML={{ __html: infoHTML }} />
        </InfoModal>
      </MainLayout>
    );
  }
}

export default AssetDiscoveryPage;
