import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import ModalsWrapper from "../components/modalsWrapper";
import { MandateDocument } from "../../models/Mandate";
import MainCard from "../layouts/mainCard";
import {
  AutomationCategoryType,
  AutomationDocument,
  SavingsTopUpAutomationDocument,
  TopUpAutomationDocument
} from "../../models/Automation";
import InfoModal from "../components/modals/infoModal";
import ConfirmationModal from "../components/modals/confirmationModal";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { LinkedBankAccount } from "../types/bank";
import Decimal from "decimal.js";
import { BankAccountDocument } from "../../models/BankAccount";
import { formatCurrency } from "../utils/currencyUtil";
import { getMonthlyOnTheXthDateString } from "../utils/dateUtil";
import { isVerifying } from "../utils/userUtil";
import VirtualToRealBannerCTA from "../components/virtualToRealBannerCTA";
import IntercomUtil from "../utils/intercomUtil";
import ConfigUtil from "../../utils/configUtil";
import LegalDocumentUtil from "../utils/legalDocumentUtil";
import AutomationEntry from "../components/automationEntry";
import { UserSavingsItemType } from "../types/savings";
import { savingsUniverseConfig } from "@wealthyhood/shared-configs";

export type AutopilotPropsType = {
  activeOrPendingMandates: MandateDocument[];
  activeTopUpAutomation: TopUpAutomationDocument;
  activeSavingsTopUpAutomations: SavingsTopUpAutomationDocument[];
  rebalanceAutomation: AutomationDocument;
  linkedBankAccounts: LinkedBankAccount[];
  userSavings: UserSavingsItemType[];
} & PagePropsType;

type StateType = {
  showRepeatingInvestmentInfoModal: boolean;
  showRepeatingSavingsInfoModal: boolean;
  showAutomatedRebalancingInfoModal: boolean;
  showTerminateAutomationModal: boolean;
  showDetailsAreBeingVerifiedInfoModal: boolean;
  showVerifyYourDetailsModal: boolean;
  showSetupAutomatedRebalancingModal: boolean;
  showLinkBankAccountModal: boolean;
  showSetupTargetPortfolioInfoModal: boolean;
  showRepeatingInvestmentPrerequisiteInfoModal: boolean;
  savingsAutomationToTerminate?: string;
  showTerminateSavingsAutomationModal: boolean;
};

type AutomationIconConfigType = {
  enabled: string;
  disabled: string;
};

type SavingsTopUpAutomationConfigType = Record<savingsUniverseConfig.SavingsProductType, AutomationIconConfigType>;
type NonSavingsAutomationCategoryType = Exclude<AutomationCategoryType, "SavingsTopUpAutomation">;

const SAVINGS_AUTOMATION_ICON_CONFIG: SavingsTopUpAutomationConfigType = {
  mmf_dist_gbp: {
    enabled: "/images/icons/gbp_topup_enabled.svg",
    disabled: "/images/icons/gbp_topup_disabled.svg"
  },
  mmf_dist_eur: {
    enabled: "/images/icons/eur_topup_enabled.svg",
    disabled: "/images/icons/eur_topup_disabled.svg"
  }
};

const AUTOMATION_ICON_CONFIG: Record<NonSavingsAutomationCategoryType, AutomationIconConfigType> = {
  RebalanceAutomation: {
    enabled: "/images/icons/automated_rebalancing_enabled.svg",
    disabled: "/images/icons/automated_rebalancing_disabled.svg"
  },
  TopUpAutomation: {
    enabled: "/images/icons/topup_enabled.svg",
    disabled: "/images/icons/topup_disabled.svg"
  }
};

class AutopilotPage extends React.Component<AutopilotPropsType, StateType> {
  constructor(props: AutopilotPropsType) {
    super(props);
    this.state = {
      showRepeatingInvestmentInfoModal: false,
      showRepeatingSavingsInfoModal: false,
      showAutomatedRebalancingInfoModal: false,
      showTerminateAutomationModal: false,
      showDetailsAreBeingVerifiedInfoModal: false,
      showVerifyYourDetailsModal: false,
      showSetupAutomatedRebalancingModal: false,
      showLinkBankAccountModal: false,
      showTerminateSavingsAutomationModal: false,
      showSetupTargetPortfolioInfoModal: false,
      showRepeatingInvestmentPrerequisiteInfoModal: false
    };
  }

  private _setShowRepeatingInvestmentInfoModal(showRepeatingInvestmentInfoModal: boolean) {
    this.setState({ showRepeatingInvestmentInfoModal });
  }

  private _setShowRepeatingSavingsInfoModal(showRepeatingSavingsInfoModal: boolean) {
    this.setState({ showRepeatingSavingsInfoModal });
  }

  private _setShowAutomatedRebalancingInfoModal(showAutomatedRebalancingInfoModal: boolean) {
    this.setState({ showAutomatedRebalancingInfoModal });
  }

  private _setShowDetailsAreBeingVerifiedInfoModal(showDetailsAreBeingVerifiedInfoModal: boolean) {
    this.setState({ showDetailsAreBeingVerifiedInfoModal });
  }

  private _setShowVerifyYourDetailsModal(showVerifyYourDetailsModal: boolean) {
    this.setState({ showVerifyYourDetailsModal });
  }

  private _setShowTerminateAutomationModal(showTerminateAutomationModal: boolean) {
    this.setState({ showTerminateAutomationModal });
  }

  private _setShowSetupAutomatedRebalancingModal(showSetupAutomatedRebalancingModal: boolean) {
    this.setState({ showSetupAutomatedRebalancingModal });
  }

  private _setShowSetupTargetPortfolioInfoModal(showSetupTargetPortfolioInfoModal: boolean) {
    this.setState({ showSetupTargetPortfolioInfoModal });
  }

  private _setShowRepeatingInvestmentPrerequisiteInfoModal(showRepeatingInvestmentPrerequisiteInfoModal: boolean) {
    this.setState({ showRepeatingInvestmentPrerequisiteInfoModal });
  }

  private _getLinkedBankAccountForActiveAutomation() {
    const { activeTopUpAutomation, activeOrPendingMandates, linkedBankAccounts } = this.props;

    if (!activeTopUpAutomation || !activeOrPendingMandates || activeOrPendingMandates.length === 0) {
      return null;
    }

    return linkedBankAccounts.find(
      (bankAccount) =>
        bankAccount.id ===
        (
          activeOrPendingMandates.find(
            (mandate) => mandate.id === (activeTopUpAutomation.mandate as MandateDocument).id
          ).bankAccount as BankAccountDocument
        )._id
    );
  }

  private _getLinkedBankAccountForSavingsAutomation(automation: SavingsTopUpAutomationDocument) {
    const { activeOrPendingMandates, linkedBankAccounts } = this.props;

    if (!activeOrPendingMandates || activeOrPendingMandates.length === 0) {
      return null;
    }

    return linkedBankAccounts.find(
      (bankAccount) =>
        bankAccount.id ===
        (
          activeOrPendingMandates.find((mandate) => mandate.id === (automation.mandate as MandateDocument)?.id)
            .bankAccount as BankAccountDocument
        )._id
    );
  }

  private _enableSavingsTerminationModal(automationId: string) {
    this.setState({ showTerminateSavingsAutomationModal: true, savingsAutomationToTerminate: automationId });
  }

  private _closeSavingsTerminationModal() {
    this.setState({ showTerminateSavingsAutomationModal: false, savingsAutomationToTerminate: null });
  }

  private async _terminateAutomation() {
    const { activeTopUpAutomation } = this.props;

    try {
      await axios.post(`/automations/${activeTopUpAutomation._id}/cancel`);
      window.location.reload();
    } catch (err) {
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private async _setupAutomatedRebalancing() {
    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");
      await axios.post("setup-automated-rebalancing", {});
      window.location.reload();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
      throw err;
    }
  }

  private async _terminateAutomatedRebalancing() {
    const { rebalanceAutomation } = this.props;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");
      await axios.post(`/automations/${rebalanceAutomation._id}/cancel`);
      window.location.reload();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private async _terminateSavingsTopupAutomation() {
    const { savingsAutomationToTerminate } = this.state;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");
      await axios.post(`/automations/${savingsAutomationToTerminate}/cancel`);
      window.location.reload();
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  render(): JSX.Element {
    const { user, activeTopUpAutomation, activeSavingsTopUpAutomations, rebalanceAutomation, userSavings } =
      this.props;
    const {
      showRepeatingInvestmentInfoModal,
      showRepeatingSavingsInfoModal,
      showAutomatedRebalancingInfoModal,
      showDetailsAreBeingVerifiedInfoModal,
      showVerifyYourDetailsModal,
      showTerminateAutomationModal,
      showSetupAutomatedRebalancingModal,
      showSetupTargetPortfolioInfoModal,
      showRepeatingInvestmentPrerequisiteInfoModal,
      showTerminateSavingsAutomationModal
    } = this.state;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const portfolio = user.portfolios?.[0];

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <ModalsWrapper user={user} activePage={this.props.activePage} />

        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h2 className="fw-bolder p-0 mb-4">Autopilot</h2>
            <p className="text-muted">
              Set your portfolio on autopilot to boost your wealth building. Schedule repeating investments,
              automated rebalancing and monthly deposits.
            </p>
          </div>
        </div>
        {/* End About Page */}

        <MainCard>
          {/* Investments */}
          <div className={"mb-5"}>
            <h6 className={"mb-3"} style={{ fontSize: "18px" }}>
              Investments
            </h6>
            {/* Recurring Top-Up */}
            <AutomationEntry
              title={"Repeating investment"}
              className={`automation-entry ${activeTopUpAutomation ? "active" : ""}`}
              subtitleContainer={
                <div className="text-muted font-weight-bold">
                  {activeTopUpAutomation ? (
                    <div className={"justify-content-center align-items-center"}>
                      <img
                        className="align-self-center me-2"
                        style={{ height: "20px", width: "20px" }}
                        src={this._getLinkedBankAccountForActiveAutomation()?.bankIconURL}
                      />
                      <span className="me-2 t-875" style={{ color: "#536AE3" }}>
                        {formatCurrency(
                          Decimal.div(activeTopUpAutomation.consideration.amount, 100).toNumber(),
                          activeTopUpAutomation.consideration.currency,
                          locale,
                          0,
                          0
                        )}{" "}
                        {getMonthlyOnTheXthDateString({ capitalFirst: false }, activeTopUpAutomation.dayOfMonth)}
                      </span>
                    </div>
                  ) : (
                    <span className="me-2 t-875 text-muted">Set up a monthly investment</span>
                  )}
                </div>
              }
              active={!!activeTopUpAutomation}
              icon={AUTOMATION_ICON_CONFIG.TopUpAutomation[activeTopUpAutomation ? "enabled" : "disabled"]}
              onInfoClick={(event: any) => {
                this._setShowRepeatingInvestmentInfoModal(true);
                event.stopPropagation();
              }}
              onContainerClick={() => {
                if (activeTopUpAutomation) {
                  eventEmitter.emit(EVENTS.setupRepeatingInvestmentModal);
                }
              }}
              onToggleSwitch={async (event) => {
                if (!user.submittedRequiredInfo) {
                  this._setShowVerifyYourDetailsModal(true);
                } else if (isVerifying(user)) {
                  this._setShowDetailsAreBeingVerifiedInfoModal(true);
                } else if (!portfolio?.isTargetAllocationSetup && !portfolio?.holdings?.length) {
                  this._setShowRepeatingInvestmentPrerequisiteInfoModal(true);
                } else if (activeTopUpAutomation) {
                  this._setShowTerminateAutomationModal(true);
                  event.stopPropagation();
                } else {
                  eventEmitter.emit(EVENTS.setupRepeatingInvestmentModal);
                }
              }}
            />
            {/* End Recurring Top-Up */}

            {/* Automated Rebalancing */}
            <AutomationEntry
              title={"Automated rebalancing"}
              className={`automation-entry ${rebalanceAutomation?.status === "Active" ? "active" : ""}`}
              subtitleContainer={
                <span
                  className="me-2 t-875 text-muted"
                  style={{
                    color:
                      rebalanceAutomation && rebalanceAutomation?.status === "Active" ? "#536AE3 !important" : ""
                  }}
                >
                  Every month
                </span>
              }
              active={rebalanceAutomation && rebalanceAutomation?.status === "Active"}
              icon={
                AUTOMATION_ICON_CONFIG.RebalanceAutomation[
                  rebalanceAutomation && rebalanceAutomation?.status === "Active" ? "enabled" : "disabled"
                ]
              }
              onInfoClick={(event: any) => {
                this._setShowAutomatedRebalancingInfoModal(true);
                event.stopPropagation();
              }}
              onContainerClick={() => {
                return;
              }}
              onToggleSwitch={async () => {
                if (!user.submittedRequiredInfo) {
                  this._setShowVerifyYourDetailsModal(true);
                } else if (isVerifying(user)) {
                  this._setShowDetailsAreBeingVerifiedInfoModal(true);
                } else if (!portfolio?.isTargetAllocationSetup) {
                  this._setShowSetupTargetPortfolioInfoModal(true);
                } else if (rebalanceAutomation && rebalanceAutomation?.status === "Active") {
                  await this._terminateAutomatedRebalancing();
                } else if (rebalanceAutomation && rebalanceAutomation?.status !== "Active") {
                  // If user has already set up a rebalance automation in the past but is currently inactive,
                  // we don't have to show them the rebalance info modal again.
                  await this._setupAutomatedRebalancing();
                } else if (!rebalanceAutomation) {
                  // Instead, if they have never setup an automated rebalance in the past, we show them an info
                  // modal first.
                  this._setShowSetupAutomatedRebalancingModal(true);
                }
              }}
            />
          </div>
          {/* End Investments */}

          {/* Interest */}
          <div>
            <h6 className={"mb-3"} style={{ fontSize: "18px" }}>
              Interest
            </h6>

            {userSavings.map((savingsItem) => {
              const automation = activeSavingsTopUpAutomations.find(
                (automation) => automation.savingsProduct === savingsItem.savingsProductId
              );
              const isActive = automation && automation?.active;

              return (
                <AutomationEntry
                  key={savingsItem.savingsProductId}
                  title={`Earn ${savingsItem.netInterestRate} interest`}
                  className={`automation-savings-entry ${isActive ? "active" : ""}`}
                  subtitleContainer={
                    <div className="text-muted font-weight-bold automation-entry-subtitle">
                      {isActive ? (
                        <div className={"justify-content-center align-items-center"}>
                          <img
                            className="align-self-center me-2"
                            style={{ height: "20px", width: "20px" }}
                            src={this._getLinkedBankAccountForSavingsAutomation(automation)?.bankIconURL}
                          />
                          <span className="me-2 t-875">
                            {formatCurrency(
                              Decimal.div(automation.consideration.amount, 100).toNumber(),
                              automation.consideration.currency,
                              locale,
                              0,
                              0
                            )}{" "}
                            {getMonthlyOnTheXthDateString({ capitalFirst: false }, automation.dayOfMonth)}
                          </span>
                        </div>
                      ) : (
                        <span className="me-2 t-875 text-muted">Set up a monthly deposit</span>
                      )}
                    </div>
                  }
                  active={isActive}
                  icon={
                    SAVINGS_AUTOMATION_ICON_CONFIG[savingsItem.savingsProductId][isActive ? "enabled" : "disabled"]
                  }
                  onInfoClick={(event: any) => {
                    event.stopPropagation();
                    this._setShowRepeatingSavingsInfoModal(true);
                  }}
                  onContainerClick={() => {
                    if (isActive) {
                      eventEmitter.emit(
                        EVENTS.setupRepeatingSavingsModal,
                        savingsItem.savingsProductId,
                        savingsItem.netInterestRate
                      );
                    }
                  }}
                  onToggleSwitch={async (event) => {
                    if (!user.submittedRequiredInfo) {
                      this._setShowVerifyYourDetailsModal(true);
                    } else if (isVerifying(user)) {
                      this._setShowDetailsAreBeingVerifiedInfoModal(true);
                    } else if (isActive) {
                      event.stopPropagation();
                      this._enableSavingsTerminationModal(automation.id);
                    } else if (!isActive) {
                      eventEmitter.emit(
                        EVENTS.setupRepeatingSavingsModal,
                        savingsItem.savingsProductId,
                        savingsItem.netInterestRate
                      );
                    }
                  }}
                />
              );
            })}
          </div>
          {/* End Interest */}
        </MainCard>

        {/* Virtual to Real Banner CTA */}
        {!user.kycPassed && (
          <div className="row p-0 m-0">
            <div className="col p-0 m-0">
              <VirtualToRealBannerCTA user={user} />
            </div>
          </div>
        )}
        {/* End Virtual to Real Banner CTA */}

        {/* Repeating Investment Info Modal */}
        <InfoModal
          title={"Setting up a repeating investment"}
          show={showRepeatingInvestmentInfoModal}
          handleClose={() => this._setShowRepeatingInvestmentInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <div className="d-flex flex-column justify-content-center">
            <p className="text-muted mt-4">
              Repeating investments help you automate investing in your portfolio every month. This process (aka
              dollar-cost averaging - DCA) allows you to consistently build wealth and navigate the ups and downs
              of the markets.
            </p>
            <p className="text-muted">
              When activated, a repeating investment is placed once a month as a “Portfolio Buy” order. You simply
              decide how much you want to invest and the bank account you want to use.
            </p>
            <p className="text-muted">
              The repeating investment takes place on the date you define. For example, if you set it up for the
              15th of the month, the repeating investment will occur on the 15th of each subsequent month for as
              long as it is activated.
            </p>
            <p className="text-muted">
              You can always pause your repeating investment by switching the toggle off.
            </p>
          </div>
        </InfoModal>
        {/* End Repeating Investment Info Modal */}

        {/* Repeating Savings Info Modal */}
        <InfoModal
          title={"Setting up a monthly deposit"}
          show={showRepeatingSavingsInfoModal}
          handleClose={() => this._setShowRepeatingSavingsInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <div className="d-flex flex-column justify-content-center">
            <p className="text-muted mt-4">
              Money market funds are an alternative to traditional savings accounts, typically offering higher
              interest rates with less risk than stocks or long-term investments.
            </p>
            <p className="text-muted">
              Repeating deposits on interest paying MMFs allow you to consistently build wealth and navigate the
              ups and downs of the markets.
            </p>
            <p className="text-muted">
              When activated, a repeating deposit is placed once a month as an “Add money” order to the MMF. You
              simply decide how much you want to deposit and the bank account you want to use.
            </p>
            <p className="text-muted">
              The repeating deposit takes place on the date you define. For example, if you set it up for the 15th
              of the month, the repeating deposit will occur on the 15th of each subsequent month for as long as it
              is activated.
            </p>
            <p className="text-muted">You can always pause your repeating deposit by switching the toggle off.</p>
          </div>
        </InfoModal>
        {/* End Repeating Savings Info Modal */}

        {/* Automated Rebalancing Info Modal */}
        <InfoModal
          title={"Setting up automated rebalancing"}
          show={showAutomatedRebalancingInfoModal}
          handleClose={() => this._setShowAutomatedRebalancingInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <h1 />
          <p className={"text-muted"}>
            By setting up automated rebalancing, you help maintain the initial asset allocation you defined for
            your target portfolio.
          </p>
          <h6 className={"mb-3 mt-4"}>How it works?</h6>
          <p className={"text-muted"}>
            If activated, automated rebalancing will take place on the first Monday of the month, placing buy and
            sell orders to get your holdings back to your target portfolio.
          </p>
          <p className={"text-muted"}>
            The rebalancing process lasts for two consecutive trading sessions. First, overweight assets are sold,
            and then, the proceedings are used to buy underweight assets.
          </p>
          <p className={"text-muted"}>
            Keep in mind that you cannot sell any of your holdings until the process is completed.
          </p>
          <p className={"text-muted"}>
            You can always change your target portfolio allocation from your Target tab.
          </p>
          <h6 className={"mb-3 mt-4"}>Example</h6>
          <p className={"text-muted"}>
            Suppose your target portfolio allocation is:
            <ul>
              <li>60% stocks</li>
              <li>30% bonds</li>
              <li>10% gold</li>
            </ul>
          </p>
          <p className={"text-muted"}>
            After market fluctuations, your allocation shifts to 70% stocks, 20% bonds, and 10% gold, with a total
            portfolio value of {formatCurrency(10000, user.currency, locale)}.
          </p>
          <p className={"text-muted"}>
            The automated rebalancing process starts on the first Monday of the month:
            <ul>
              <li>
                It sells {formatCurrency(1000, user.currency, locale)} worth of stocks, reducing their allocation
                to 60%.
              </li>
              <li>Then it uses the proceeds to buy bonds, increasing their allocation to 30%.</li>
              <li>The gold allocation remains at 10%.</li>
            </ul>
          </p>
          <h6 className={"mb-3 mt-4"}>Disclaimer</h6>
          <p className={"text-muted"}>
            Automated rebalancing does not take into account the market conditions at the time of rebalancing and
            you may experience a capital gain or loss during the process. Automated rebalancing will only take
            place if you have holdings in your portfolio. You can read more on our{" "}
            <a
              className="fw-bolder text-decoration-none"
              href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
              target="_blank"
              rel="noreferrer"
            >
              Investor Terms
            </a>
            .
          </p>
        </InfoModal>
        {/* End Automated Rebalancing Info Modal */}

        {/* Details Are Being Verified Info Modal */}
        <InfoModal
          title={
            user.hasFailedKyc
              ? "We need additional details to verify your account!"
              : "Your details are being verified..."
          }
          show={showDetailsAreBeingVerifiedInfoModal}
          handleClose={() => this._setShowDetailsAreBeingVerifiedInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          {user.hasFailedKyc ? (
            <div className="d-flex flex-column justify-content-center">
              <p className="text-muted mt-4 mb-5">
                We have reviewed your details, but we require some additional supporting documents to proceed with
                verifying your account.
              </p>
              <button
                className="btn btn-primary fw-100"
                onClick={(event) => {
                  event.stopPropagation();
                  IntercomUtil.showMessages();
                  this._setShowDetailsAreBeingVerifiedInfoModal(false);
                }}
              >
                Upload additional info
              </button>
            </div>
          ) : (
            <div className="d-flex flex-column justify-content-center">
              <p className="text-muted mt-4">
                Once your account has been verified you will be able to use our auto-pilot mode. Don{"'"}t worry,
                it doesn{"'"}t take long!
              </p>
            </div>
          )}
        </InfoModal>
        {/* End Details Are Being Verified Info Modal */}

        {/* Setup Target Portfolio Info Modal */}
        <InfoModal
          title="Set up your target portfolio"
          show={showSetupTargetPortfolioInfoModal}
          handleClose={() => this._setShowSetupTargetPortfolioInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <div className="d-flex flex-column justify-content-center">
            <p className="text-muted">
              In order to use automated rebalancing, you need to set up your target portfolio. You can do so by
              accessing the ‘Target Portfolio’ section from your Dashboard.
            </p>
          </div>
        </InfoModal>
        {/* Setup Target Portfolio Info Modal */}

        {/* Repating investment prerequisite Info Modal */}
        <InfoModal
          title="Set up your portfolio"
          show={showRepeatingInvestmentPrerequisiteInfoModal}
          handleClose={() => this._setShowRepeatingInvestmentPrerequisiteInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <div className="d-flex flex-column justify-content-center">
            <p className="text-muted">
              In order to activate a repeating investment, you need to have active holdings or set up your target
              portfolio. You can do so by accessing the ‘Target Portfolio’ section from your Dashboard.
            </p>
          </div>
        </InfoModal>
        {/* Repating investment prerequisite Info Modal */}

        {/* Verify Your Account Modal */}
        <ConfirmationModal
          title={"First, verify your account!"}
          show={showVerifyYourDetailsModal}
          dialogClassName={"max-w-600px"}
          handleConfirmation={async () => {
            window.location.href = "/investor/collect-personal-details";
          }}
          handleClose={() => this._setShowVerifyYourDetailsModal(false)}
          size={"lg"}
          onlyYesButton={true}
          yesButtonLabel={"Let’s go!"}
        >
          <p className={"text-muted"}>
            You need to verify your account details to be able to use our auto-pilot mode. Don{"'"}t worry, it won
            {"'"}t take long!
          </p>
        </ConfirmationModal>
        {/* Verify Your Account Modal */}

        {/* Terminate Automation Info Modal */}
        <ConfirmationModal
          title={"Terminate this repeating investment?"}
          show={showTerminateAutomationModal}
          dialogClassName={"max-w-600px"}
          handleConfirmation={async () => this._terminateAutomation()}
          handleClose={() => this._setShowTerminateAutomationModal(false)}
          size={"lg"}
          noButtonLabel={"Back"}
          yesButtonLabel={"Terminate"}
        >
          <p className={"text-muted"}>Are you sure that you want to terminate this repeating investment?</p>
        </ConfirmationModal>
        {/* Terminate Automation Info Modal */}

        {/* Terminate Savings Automation Info Modal */}
        <ConfirmationModal
          title={"Terminate this monthly deposit?"}
          show={showTerminateSavingsAutomationModal}
          dialogClassName={"max-w-600px"}
          handleConfirmation={async () => this._terminateSavingsTopupAutomation()}
          handleClose={() => this._closeSavingsTerminationModal()}
          size={"lg"}
          noButtonLabel={"Back"}
          yesButtonLabel={"Terminate"}
        >
          <p className={"text-muted"}>Are you sure that you want to terminate this monthly deposit?</p>
        </ConfirmationModal>
        {/* Terminate Savings  Automation Info Modal */}

        {/* Setup Automated Rebalancing Info Modal */}
        <ConfirmationModal
          title={"Setting up automated rebalancing"}
          show={showSetupAutomatedRebalancingModal}
          dialogClassName={"max-w-600px"}
          handleConfirmation={async () => this._setupAutomatedRebalancing()}
          handleClose={() => this._setShowSetupAutomatedRebalancingModal(false)}
          size={"lg"}
          yesButtonLabel={"Got it!"}
          onlyYesButton={true}
        >
          <h1 />
          <p className={"text-muted"}>
            By setting up automated rebalancing, you help maintain the initial asset allocation you defined for
            your target portfolio.
          </p>
          <h6 className={"mb-3 mt-4"}>How it works?</h6>
          <p className={"text-muted"}>
            If activated, automated rebalancing will take place on the first Monday of the month, placing buy and
            sell orders to get your holdings back to your target portfolio.
          </p>
          <p className={"text-muted"}>
            The rebalancing process lasts for two consecutive trading sessions. First, overweight assets are sold,
            and then, the proceedings are used to buy underweight assets.
          </p>
          <p className={"text-muted"}>
            Keep in mind that you cannot sell any of your holdings until the process is completed.
          </p>
          <p className={"text-muted"}>
            You can always change your target portfolio allocation from your Target tab.
          </p>
          <h6 className={"mb-3 mt-4"}>Example</h6>
          <p className={"text-muted"}>
            Suppose your target portfolio allocation is:
            <ul>
              <li>60% stocks</li>
              <li>30% bonds</li>
              <li>10% gold</li>
            </ul>
          </p>
          <p className={"text-muted"}>
            After market fluctuations, your allocation shifts to 70% stocks, 20% bonds, and 10% gold, with a total
            portfolio value of {formatCurrency(10000, user.currency, locale)}.
          </p>
          <p className={"text-muted"}>
            The automated rebalancing process starts on the first Monday of the month:
            <ul>
              <li>
                It sells {formatCurrency(1000, user.currency, locale)} worth of stocks, reducing their allocation
                to 60%.
              </li>
              <li>Then it uses the proceeds to buy bonds, increasing their allocation to 30%.</li>
              <li>The gold allocation remains at 10%.</li>
            </ul>
          </p>
          <h6 className={"mb-3 mt-4"}>Disclaimer</h6>
          <p className={"text-muted"}>
            Automated rebalancing does not take into account the market conditions at the time of rebalancing and
            you may experience a capital gain or loss during the process. Automated rebalancing will only take
            place if you have holdings in your portfolio. You can read more on our{" "}
            <a
              className="fw-bolder text-decoration-none"
              href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
              target="_blank"
              rel="noreferrer"
            >
              Investor Terms
            </a>
            .
          </p>
        </ConfirmationModal>
        {/* Terminate Automation Info Modal */}
      </MainLayout>
    );
  }
}

export default AutopilotPage;
