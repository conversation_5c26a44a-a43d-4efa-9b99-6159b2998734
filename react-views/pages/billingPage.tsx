import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import ModalsWrapper from "../components/modalsWrapper";
import { ChargeTransactionDocument } from "../../models/Transaction";
import InvestorTransactionRow from "../components/investorTransactionRow";
import InfoModal from "../components/modals/infoModal";
import { SubscriptionDocument } from "../../models/Subscription";
import MainCard from "../layouts/mainCard";
import { ProviderType } from "../../services/truelayerService";
import { PortfolioModeEnum } from "../../models/Portfolio";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { entitiesConfig, plansConfig } from "@wealthyhood/shared-configs";
import { formatDateToDDMONYYYY } from "../utils/dateUtil";
import ConfigUtil from "../../utils/configUtil";
import { captureException } from "@sentry/react";
import { Modal, ModalBody } from "react-bootstrap";
import SelectedSubscriptionPaymentMethod from "../components/selectedSubscriptionPaymentMethod";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import CardPaymentSubscriptionPaymentMethodUpdateModal from "../components/cardSubscriptionPaymentMethodUpdateModal";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import { PLAN_BG_IMAGE_CONFIG, PLAN_ICON_IMAGE_CONFIG } from "../configs/plansConfig";

export type BillingPropsType = {
  chargeTransactions: ChargeTransactionDocument[];
  truelayerProviders: ProviderType[];
  paymentMethods: PaymentMethodDocument[];
  subscription: SubscriptionDocument;
  initialShowCardPaymentModal: boolean;
} & PagePropsType;

type StateType = {
  showChargesInfoModal: boolean;
  showPaymentMethodSelectModal: boolean;
  showCardPaymentModal: boolean;
  linkExists: boolean;
  planRenewalSuccess: boolean;
  showAllBillingTransactions: boolean;
  allChargeTransactions: ChargeTransactionDocument[];
};

const LIMIT = 5;

class BillingPage extends React.Component<BillingPropsType, StateType> {
  constructor(props: BillingPropsType) {
    super(props);
    this.state = {
      showPaymentMethodSelectModal: false,
      showChargesInfoModal: false,
      showCardPaymentModal: this.props.initialShowCardPaymentModal ?? false,
      linkExists: false,
      planRenewalSuccess: false,
      showAllBillingTransactions: false,
      allChargeTransactions: this.props.chargeTransactions
    };
  }

  private async _fetchBillingActivity() {
    try {
      const response = await axios({
        method: "GET",
        url: "/investor/billing-activity",
        headers: { "Content-Type": "application/json" }
      });

      this.setState({ allChargeTransactions: response.data });
    } catch (err) {
      captureException(err);
      emitToast({
        content: "An error occurred while fetching data. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  async componentDidMount() {
    try {
      this._fetchBillingActivity();
      // we check that costs and charges link exists for user
      // we use our server as proxy cause direct access to resource causes cors issues
      const link = `/check-file?link=${this._getCostAndChargesLink()}`;
      await axios.get(link);
      this.setState({ linkExists: true });
    } catch (err) {
      this.setState({ linkExists: false });
    }
  }

  private _showAllBillingActivity() {
    this.setState({ showAllBillingTransactions: true });
  }

  private _hideAllBillingActivity() {
    this.setState({ showAllBillingTransactions: false });
  }

  private async _renewSubscription() {
    const { user } = this.props;

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      await axios.post(`subscriptions/${user.subscription._id}/renew`);
      this.setState({ planRenewalSuccess: true }, () => {
        eventEmitter.emit(EVENTS.loadingSplashMask, "");
        emitToast({
          toastType: ToastTypeEnum.success,
          content: `Your ${
            PLAN_CONFIG[PRICE_CONFIG[user.subscription.price]?.plan].name
          } plan renewed successfully!`
        });
      });
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _getCostAndChargesLink(): string {
    const { user } = this.props;
    const wkPortfolioId = user.portfolios?.find(({ mode }) => mode == PortfolioModeEnum.REAL)?.providers
      ?.wealthkernel?.id;

    const urlTemplate = process.env.COSTS_AND_CHARGES_URL_TEMPLATE;
    return `${urlTemplate.replace("${wkPortfolioId}", wkPortfolioId)}`;
  }

  private _setShowChargesInfoModal(showChargesInfoModal: boolean) {
    this.setState({ showChargesInfoModal });
  }

  private _setShowCardPaymentSubscriptionPaymentMethodSelectModal(showCardPaymentModal: boolean) {
    this.setState({ showCardPaymentModal });
  }

  private _getPriceConfig(): plansConfig.PriceConfigType {
    const { user } = this.props;
    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    return PRICE_CONFIG[user.subscription.price];
  }

  private _getPlanConfig(): plansConfig.PlanConfigType {
    const priceConfig = this._getPriceConfig();
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);
    return PLAN_CONFIG[priceConfig.plan];
  }

  private _getPlanCardElement(): JSX.Element {
    const { user } = this.props;
    const { planRenewalSuccess } = this.state;

    // If user hasn't a subscription yet
    // do not generate plan card
    if (!user.subscription) {
      return <></>;
    }

    // If user just renewed subscription, we ignore expiration object
    const hasValidDowngrade = !planRenewalSuccess && !!user.subscription.expiration?.date;

    const priceConfig = this._getPriceConfig();
    const planConfig = this._getPlanConfig();

    const hasFreePlan = priceConfig.keyName == "free_monthly";
    const hasLifetimePlan = priceConfig.recurrence === "lifetime";
    const hasActiveRecurrentPlan = ["monthly", "yearly"].includes(priceConfig.recurrence) && !hasValidDowngrade;
    const hasExpiringRecurrentPlan = ["monthly", "yearly"].includes(priceConfig.recurrence) && hasValidDowngrade;

    return (
      <div
        className={"card card-body wh-simple-card border-0 mb-5 text-white"}
        style={{ backgroundImage: PLAN_BG_IMAGE_CONFIG[priceConfig.plan], backgroundSize: "cover" }}
      >
        <div className="d-flex flex-row">
          <div className="d-flex align-self-center flex-row w-100 p-0 justify-content-start">
            <div className="text-start align-self-center text-center p-0 me-md-4 me-1">
              <img
                alt="icon"
                src={PLAN_ICON_IMAGE_CONFIG[priceConfig.plan]}
                style={{ width: "48px", height: "48px" }}
              />
            </div>
            <div className="p-0 d-flex flex-column justify-content-center">
              <div className="d-flex mb-1">
                <h5 className="fw-bold align-self-center m-0 me-2">{planConfig.name}</h5>
                <i
                  className="material-symbols-outlined align-self-center cursor-pointer text-white"
                  style={{ fontSize: "20px" }}
                  onClick={(event) => {
                    event.stopPropagation();
                    window.location.href = `/investor/change-plan?select=${priceConfig.keyName}`;
                  }}
                >
                  info
                </i>
              </div>
              {hasFreePlan && (
                <span className="align-self-center t-75 fw-light">
                  Since {formatDateToDDMONYYYY(user.subscription.createdAt)}
                </span>
              )}
              {hasLifetimePlan && <span className="align-self-center t-75 fw-light">Lifetime</span>}
              {hasActiveRecurrentPlan && (
                <span className="align-self-center t-75 fw-light">
                  Renews {formatDateToDDMONYYYY(user.subscription.nextChargeAt)}
                </span>
              )}
              {hasExpiringRecurrentPlan && (
                <span className="align-self-center t-75 fw-light">
                  Expires {formatDateToDDMONYYYY(user.subscription.expiration.date)}
                </span>
              )}
            </div>
          </div>
          {this._getButtonsConfig()?.planCardButton && (
            <div className="d-flex align-self-center">{this._getButtonsConfig().planCardButton}</div>
          )}
        </div>
      </div>
    );
  }

  private async _switchToYearlySubscription() {
    const { user, subscription } = this.props;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      const targetPrice = this._getEquivalentYearlyPrice();

      await axios.post(`subscriptions/${user.subscription._id}`, {
        category: "CardPaymentSubscription",
        price: targetPrice
      });
      window.location.href = `/investor/plan-update-success?from=${subscription.price}&to=${targetPrice}`;
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _getButtonsConfig(): { planCardButton?: JSX.Element; bottomCardButton?: JSX.Element } {
    const { user, subscription } = this.props;
    const { planRenewalSuccess } = this.state;

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);

    const priceConfig = this._getPriceConfig();
    const hasFreePlan = priceConfig.keyName == "free_monthly";
    const hasValidDowngrade = !planRenewalSuccess && !!user.subscription.expiration?.date;
    const hasExpiringRecurrentPlan = ["monthly", "yearly"].includes(priceConfig.recurrence) && hasValidDowngrade;
    const hasMonthlyPlan = priceConfig.recurrence === "monthly";
    const doesNotHaveDirectDebitPlan = subscription.category !== "DirectDebitSubscription";

    if (hasFreePlan) {
      return {
        planCardButton: (
          <a href="/investor/change-plan?select=paid_low_yearly" className="btn btn-plan align-self-center">
            Upgrade
          </a>
        ),
        bottomCardButton: (
          <button
            type="button"
            className="btn btn-primary"
            onClick={() => {
              window.location.href = "/investor/change-plan?select=paid_low_yearly";
            }}
          >
            Upgrade your plan
          </button>
        )
      };
    }

    if (hasExpiringRecurrentPlan && doesNotHaveDirectDebitPlan) {
      return {
        planCardButton: (
          <button className="btn btn-plan align-self-center" onClick={() => this._renewSubscription()}>
            Renew
          </button>
        ),
        bottomCardButton: (
          <button className="btn btn-plan-reverse align-self-center" onClick={() => this._renewSubscription()}>
            Renew {PLAN_CONFIG[priceConfig.plan].name}
          </button>
        )
      };
    }

    if (hasMonthlyPlan && doesNotHaveDirectDebitPlan) {
      return {
        bottomCardButton: (
          <>
            <button
              type="button"
              className="btn btn-secondary me-4"
              onClick={() =>
                (window.location.href = `/investor/change-plan?select=${this._getEquivalentYearlyPrice()}`)
              }
            >
              Learn more
            </button>
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-primary"
              customonclick={async () => {
                await this._switchToYearlySubscription();
              }}
            >
              Save {PRICE_CONFIG[this._getEquivalentYearlyPrice()].savePercentage} with annual plan
            </LoadingOnSubmitButton>
          </>
        )
      };
    }
  }

  private _getCurrentlyUsedPaymentMethod(): PaymentMethodDocument {
    const { subscription, paymentMethods } = this.props;

    return paymentMethods.find(({ id }) => subscription.paymentMethod.toString() === id);
  }

  private _hasCardSubscription(): boolean {
    const { user } = this.props;

    return user.subscription?.category === "CardPaymentSubscription";
  }

  private _getUpdatePaymentMethodElement(): JSX.Element {
    const { user } = this.props;

    // If user hasn't a subscription yet do not generate plan card
    if (!this._hasCardSubscription()) {
      return <></>;
    }

    return (
      <div className="row p-0 px-md-0 px-3 m-0 mb-5">
        <div className="col p-0 m-0">
          <h5 className="fw-bolder mb-4">Payment method</h5>
          <SelectedSubscriptionPaymentMethod
            onClick={() => this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(true)}
            selectedPaymentMethod={this._getCurrentlyUsedPaymentMethod()}
            user={user}
          />
        </div>
      </div>
    );
  }

  private _getEquivalentYearlyPrice(): plansConfig.PriceType {
    const { subscription } = this.props;

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);

    return Object.values(PRICE_CONFIG).find(
      (priceConfig) =>
        priceConfig.plan === PRICE_CONFIG[subscription.price].plan && priceConfig.recurrence === "yearly"
    ).keyName;
  }

  render(): JSX.Element {
    const { user, truelayerProviders, paymentMethods } = this.props;
    const {
      showChargesInfoModal,
      showCardPaymentModal,
      allChargeTransactions,
      linkExists,
      showAllBillingTransactions
    } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        <ModalsWrapper user={user} />

        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Billing</h5>
            <p className="text-muted">Review your payment method and check your subscriptions’ history.</p>
          </div>
        </div>
        {/* End About Page */}

        <MainCard marginBottom={"160px"}>
          {this._getPlanCardElement()}
          {this._getUpdatePaymentMethodElement()}

          {/* Transactions */}
          <div className="row m-0 p-0 px-md-0 px-3" style={{ paddingBottom: "2rem !important" }}>
            <div className="col p-0 m-0">
              <div className="d-flex align-items-baseline justify-content-between mb-4">
                <div className="d-flex flex-row">
                  <h5 className="fw-bolder align-self-center mb-0">Charges history</h5>
                  <i
                    className="material-symbols-outlined text-primary cursor-pointer ms-1 align-self-center"
                    onClick={(event) => {
                      event.stopPropagation();
                      this._setShowChargesInfoModal(true);
                    }}
                    style={{
                      fontSize: "18px"
                    }}
                  >
                    info
                  </i>
                </div>
                {allChargeTransactions.length > 0 ? (
                  <div className="d-flex">
                    <h6
                      className="fw-bolder text-end text-primary cursor-pointer"
                      onClick={() => this._showAllBillingActivity()}
                    >
                      See all
                    </h6>
                  </div>
                ) : null}
              </div>
              <div className="row m-0">
                <div className="col p-0">
                  {allChargeTransactions.length > 0 ? (
                    allChargeTransactions
                      .slice(0, LIMIT)
                      .map((transaction, index) => (
                        <InvestorTransactionRow
                          onClick={() => null}
                          transaction={transaction}
                          truelayerProviders={truelayerProviders}
                          investmentProducts={[]}
                          key={`transaction_${index}`}
                        />
                      ))
                  ) : (
                    <p className="text-muted">No transactions made yet.</p>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* End Transactions */}

          {/* Cost and charges link */}
          {linkExists && user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK && (
            <div className="row m-0 p-0 mt-5">
              <div className="col p-0">
                <a
                  href={this._getCostAndChargesLink()}
                  target="_blank"
                  className="wh-side-option cursor-pointer text-decoration-none text-dark w-100 p-0"
                  rel="noreferrer"
                >
                  <div className="d-flex">
                    <div className="d-flex">
                      <i className="wh-card material-symbols-outlined align-self-center text-center me-2">
                        description
                      </i>
                      <span className="align-self-center text-nowrap me-4">Costs and charges report</span>
                    </div>
                    <i
                      className="material-symbols-outlined align-self-center w-100 text-end fw-bolder"
                      style={{ fontSize: "14px" }}
                    >
                      arrow_forward_ios
                    </i>
                  </div>
                </a>
              </div>
            </div>
          )}
          {/* End Cost and charges link */}

          {/* Bottom Buttons */}
          {this._getButtonsConfig()?.bottomCardButton && (
            <div className="row fixed-bottom p-0 m-0 justify-content-end">
              <div className="col-md-3 col-xxl-4 col-sm-1" />
              <div className="col-xxl-4 col-md-6 col-sm-10 mb-4 bg-white wh-lab-bottom-buttons">
                <div className="d-flex p-0 justify-content-end align-self-center mb-0">
                  <div className="my-4 d-none d-sm-block px-5">
                    <div className="d-flex align-self-center">{this._getButtonsConfig().bottomCardButton}</div>
                  </div>
                </div>
              </div>
              <div className="col-md-3 col-xxl-4 col-sm-1" />
            </div>
          )}
        </MainCard>

        {/* Charges Info Modal */}
        <InfoModal
          title={"Charges history"}
          show={showChargesInfoModal}
          handleClose={() => this._setShowChargesInfoModal(false)}
          dialogClassName={"max-w-600px"}
        >
          <div className="d-flex flex-column justify-content-center">
            <p className="text-muted mt-4 mb-5">
              Charges history includes custody fees (for our Basic plan) and any subscription payments you’ve made.
              They don’t include order charges and FX fees.
            </p>
          </div>
        </InfoModal>
        {/* End Charges Info Modal */}

        {/* Setup Card Payments Modal */}
        {this._hasCardSubscription() && (
          <CardPaymentSubscriptionPaymentMethodUpdateModal
            user={user}
            show={showCardPaymentModal}
            handleClose={() => this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(false)}
            paymentMethods={paymentMethods}
            initialSelectedPaymentMethod={this._getCurrentlyUsedPaymentMethod()}
          />
        )}
        {/* End Setup Card Payments Modal */}

        {/* See all billing transactions modal */}
        <Modal onHide={() => this._hideAllBillingActivity()} size={"lg"} show={showAllBillingTransactions}>
          <Modal.Header className="border-bottom-0" closeButton>
            <Modal.Title />
          </Modal.Header>
          {/* Action Title */}
          <ModalBody className={"mb-5"}>
            <div className="row m-0 px-md-5 px-2">
              <div className="col p-0">
                {allChargeTransactions.map((transaction, index) => (
                  <InvestorTransactionRow
                    onClick={() => null}
                    transaction={transaction}
                    truelayerProviders={truelayerProviders}
                    investmentProducts={[]}
                    key={`transaction_${index}`}
                  />
                ))}
              </div>
            </div>
          </ModalBody>
        </Modal>
        {/* End billing transactions Modal */}
      </MainLayout>
    );
  }
}

export default BillingPage;
