import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import axios from "axios";
import { ToastTypeEnum } from "../configs/toastConfig";
import MainLayout from "../layouts/mainLayout";
import { PagePropsType } from "../types/page";
import ModalsWrapper from "../components/modalsWrapper";
import MainCard from "../layouts/mainCard";
import PlanSelection from "../components/planSelection";
import PlanSelectionButton from "../components/planSelectionButton";
import ConfigUtil from "../../utils/configUtil";
import CardSubscriptionPaymentMethodSelectModal from "../components/cardSubscriptionPaymentMethodSelectModal";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import { SubscriptionDocument } from "../../models/Subscription";
import { SavingsProductFeeDetailsWithIdType } from "../types/savings";
import { PlansContext } from "../contexts/plansContext";

export type ChangePlanPropsType = {
  paymentMethods: PaymentMethodDocument[];
  initialSelectedPrice?: plansConfig.PriceType;
  initialShowCardPaymentModal?: boolean;
  promotionalSavingsProductData: SavingsProductFeeDetailsWithIdType;
} & PagePropsType;

type StateType = {
  selectedPrice: plansConfig.PriceType;
  selectedRecurrence: plansConfig.PriceRecurrenceType;
  showCardPaymentModal: boolean;
  planRenewalSuccess: boolean;
};

class ChangePlanPage extends React.Component<ChangePlanPropsType, StateType> {
  private _PRICE_CONFIG;

  constructor(props: ChangePlanPropsType) {
    super(props);
    this._PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    this.state = {
      selectedPrice: this._getCurrentPriceIfActiveOrDefaultToFree(),
      selectedRecurrence:
        this._PRICE_CONFIG[this._getCurrentPriceIfActiveOrDefaultToFree()].recurrence || "yearly",
      showCardPaymentModal: this.props.initialShowCardPaymentModal ?? false,
      planRenewalSuccess: false
    };
  }

  private _getCurrentPriceIfActiveOrDefaultToFree(): plansConfig.PriceType {
    const currentPrice = this.props.initialSelectedPrice || this.props.user.subscription.price;

    return this._PRICE_CONFIG[currentPrice].active ? this._PRICE_CONFIG[currentPrice].keyName : "free_monthly";
  }

  private async _renewSubscription() {
    const { user } = this.props;

    const PRICE_CONFIG = ConfigUtil.getPricing(user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(user.companyEntity);

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      await axios.post(`subscriptions/${user.subscription._id}/renew`);
      this.setState({ planRenewalSuccess: true }, () => {
        eventEmitter.emit(EVENTS.loadingSplashMask, "");
        emitToast({
          toastType: ToastTypeEnum.success,
          content: `Your ${
            PLAN_CONFIG[PRICE_CONFIG[user.subscription.price]?.plan].name
          } plan renewed successfully!`
        });
      });
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private async _updateSubscription() {
    const { user } = this.props;
    const { selectedPrice } = this.state;

    try {
      eventEmitter.emit(EVENTS.loadingSplashMask, "Please wait...");

      const selectedPriceConfig = this._PRICE_CONFIG[selectedPrice];

      let category;
      if (["monthly", "yearly"].includes(selectedPriceConfig.recurrence) && selectedPrice !== "free_monthly") {
        category = "CardPaymentSubscription";
      } else {
        category = "FeeBasedSubscription";
      }

      await axios.post(`subscriptions/${user.subscription._id}`, {
        category,
        price: selectedPrice
      });
      window.location.href = `/investor/plan-update-success?from=${user.subscription.price}&to=${selectedPrice}`;
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private _setActivePlan = (price: plansConfig.PriceType): void => this.setState({ selectedPrice: price });

  private _setActiveRecurrence = (recurrence: plansConfig.PriceRecurrenceType): void =>
    this.setState({ selectedRecurrence: recurrence });

  private _setShowCardPaymentSubscriptionPaymentMethodSelectModal(showCardPaymentModal: boolean) {
    this.setState({ showCardPaymentModal });
  }

  private _getSubscriptionExpiration(): {
    date: Date;
    downgradesTo: plansConfig.PriceType;
  } {
    const { user } = this.props;

    // If user renews plan successfully we ignore expiration object
    return user.subscription.expiration && !this.state.planRenewalSuccess
      ? {
          date: user.subscription.expiration.date,
          downgradesTo: ConfigUtil.getPricing(this.props.user.companyEntity)[
            user.subscription.expiration.downgradesTo
          ].keyName
        }
      : undefined;
  }

  private _getButton(): JSX.Element {
    const { selectedPrice } = this.state;
    const { user } = this.props;

    const subscription = user.subscription;

    const isFreePlanSelected = selectedPrice == "free_monthly";
    const isCardPlanSelected = ["monthly", "yearly"].includes(this._PRICE_CONFIG[selectedPrice].recurrence);

    const isOnCardSubscription = subscription.category === "CardPaymentSubscription";

    const expiration = this._getSubscriptionExpiration();

    return (
      <PlanSelectionButton
        selectedPrice={selectedPrice}
        expiration={expiration}
        currentSubscription={subscription}
        onButtonClick={async () => {
          if (isFreePlanSelected) {
            await this._updateSubscription();
          } else if (!!subscription.expiration && subscription.price === selectedPrice && isCardPlanSelected) {
            await this._renewSubscription();
          } else if (isOnCardSubscription && isCardPlanSelected && subscription.price !== selectedPrice) {
            await this._updateSubscription();
          } else {
            this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(true);
          }
        }}
      />
    );
  }

  render(): JSX.Element {
    const { user, paymentMethods, promotionalSavingsProductData } = this.props;
    const { selectedPrice, selectedRecurrence, showCardPaymentModal } = this.state;

    const buttonEl = this._getButton();

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <ModalsWrapper user={user} lazyLoadData={true} />

        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Choose your plan</h5>
            <p className="text-muted">
              Pick the plan that’s right for you. You’ll still be able to switch whenever you want.
            </p>
          </div>
        </div>
        {/* End About Page */}

        <MainCard>
          <PlansContext.Provider
            value={{
              user,
              locale: ConfigUtil.getDefaultUserLocale(user.residencyCountry),
              promotionalSavingsProductData
            }}
          >
            <PlanSelection
              selectedPrice={selectedPrice}
              selectedRecurrence={selectedRecurrence}
              handlePriceSelection={this._setActivePlan}
              handleRecurrenceSelection={this._setActiveRecurrence}
              userCanGetFreeTrial={!this.props.user.subscription.hasUsedFreeTrial}
            />
          </PlansContext.Provider>

          {/* Bottom Buttons */}
          <div className="row fixed-bottom p-0 m-0 justify-content-end">
            <div className="col-xxl-4 col-md-6 col-sm-10 mb-4 bg-white wh-lab-bottom-buttons">
              <div className="my-4 d-none d-sm-block px-5">
                <div className="d-flex p-0 justify-content-end align-self-center mb-0"> {buttonEl}</div>
              </div>
            </div>
            <div className="col-md-3 col-xxl-4 col-sm-1" />
          </div>

          <div className="px-md-5 px-3 fixed-bottom bg-white w-100 d-block d-sm-none">
            <div className="d-flex p-0 justify-content-end align-self-center mb-0">
              <div className="mb-4 mt-3">{buttonEl}</div>
            </div>
          </div>
          {/* End Bottom Buttons */}
        </MainCard>

        {/* Setup Card Payments Modal */}
        <CardSubscriptionPaymentMethodSelectModal
          user={user}
          show={showCardPaymentModal}
          handleClose={() => this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(false)}
          paymentMethods={paymentMethods}
          newPrice={selectedPrice}
          subscription={user.subscription as SubscriptionDocument}
          source={"change-plan"}
        />
        {/* End Setup Card Payments Modal */}
      </MainLayout>
    );
  }
}

export default ChangePlanPage;
