import React from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "../layouts/baseLayout";

export type ClosingAccountPropsType = PagePropsType;

class ClosingAccountPage extends React.Component<ClosingAccountPropsType> {
  render(): JSX.Element {
    return (
      <BaseLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="container-fluid p-0 vh-100 bg-primary">
          <div className="row mt-md-5 ms-md-5 mb-md-0 mt-4 mb-4">
            <span className="d-none d-sm-block">
              <img alt="" src="/images/icons/wealthyhood-logo.svg" />
            </span>
            <span className="d-flex justify-content-center align-self-center d-block d-sm-none">
              <img alt="" src="/images/icons/wealthyhood-logo.svg" />
            </span>
          </div>
          <div className="row justify-content-center m-0 h-100 w-100">
            <div className="d-flex justify-content-center align-self-center text-center p-md-0 p-3 mb-5">
              <div className="text-light onboarding-container mb-5">
                <iframe
                  style={{ border: "none" }}
                  width="88"
                  height="88"
                  src="https://rive.app/s/fJx9T3kO90ap0O_0nhgq5A/embed"
                  allowFullScreen
                />
                <h1 className="d-none d-sm-block p-0 mt-3 text-nowrap">Your account is being closed...</h1>
                <h1 className="d-block d-sm-none p-0 mt-3">Your account is being closed...</h1>
                <a href="/logout" className="btn btn-secondary mt-5">
                  Sign out
                </a>
              </div>
            </div>
          </div>
        </div>
      </BaseLayout>
    );
  }
}

export default ClosingAccountPage;
