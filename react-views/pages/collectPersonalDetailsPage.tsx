import React from "react";

import VerificationWizard from "../components/verificationWizard";
import { PagePropsType } from "../types/page";
import { AddressInterface } from "../../models/Address";
import { countriesConfig } from "@wealthyhood/shared-configs";
import OnboardingLayoutNew from "../layouts/onboardingLayoutNew";
import { getStepsConfig } from "../configs/personalDetailsWizardConfig";
import WizardStepper from "../components/wizardStepper";
import { EmploymentInfoConfiguration, EmploymentInfoType } from "./../types/employmentConfig";

export type CollectPersonalDetailsPagePropsType = {
  address: AddressInterface;
  taxResidencyProofValue: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  nationality: countriesConfig.CountryCodesType | "";
  employmentConfig: EmploymentInfoConfiguration;
  employmentInfo?: EmploymentInfoType;
} & PagePropsType;

type StateType = {
  currentStep: number;
};

class CollectPersonalDetailsPage extends React.Component<CollectPersonalDetailsPagePropsType, StateType> {
  constructor(props: CollectPersonalDetailsPagePropsType) {
    super(props);
    this.state = {
      currentStep: 1
    };
  }

  private _setCurrentStep = (currentStep: number): void => {
    this.setState({ currentStep });
  };

  render(): JSX.Element {
    const {
      address,
      taxResidencyProofValue,
      firstName,
      lastName,
      dateOfBirth,
      nationality,
      employmentConfig,
      employmentInfo,
      user
    } = this.props;
    const { currentStep } = this.state;

    return (
      <OnboardingLayoutNew
        activePage={this.props.activePage}
        user={this.props.user}
        alignment="center"
        leftSideChild={
          <WizardStepper
            currentStep={currentStep}
            maxStepsDisplayed={4}
            stepsConfig={getStepsConfig(user.residencyCountry)}
          />
        }
      >
        <div className="row justify-content-center">
          <VerificationWizard
            residencyCountry={user.residencyCountry}
            address={address}
            taxResidencyProofValue={taxResidencyProofValue}
            firstName={firstName}
            lastName={lastName}
            nationality={nationality}
            dateOfBirth={dateOfBirth}
            onStepChange={(step) => this._setCurrentStep(step)}
            employmentConfig={employmentConfig}
            employmentInfo={employmentInfo}
          />
        </div>
      </OnboardingLayoutNew>
    );
  }
}

export default CollectPersonalDetailsPage;
