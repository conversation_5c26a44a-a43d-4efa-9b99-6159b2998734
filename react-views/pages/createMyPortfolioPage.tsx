import React from "react";
import { PagePropsType } from "../types/page";
import SuccessLayout from "../layouts/successLayout";

export type CreateMyPortfolioPropsType = PagePropsType;

class CreateMyPortfolioPage extends React.Component<CreateMyPortfolioPropsType> {
  private _getContent(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-1">Hooray!</h3>
        <h3 className="text-center fw-bold mb-md-5 mb-4">Let&rsquo;s start investing!</h3>
        <p className="p-0 text-center text-muted mb-5">
          We&rsquo;ll help you navigate the stock markets with no jargon or complexity.
        </p>

        <div className={"d-flex flex-column p-0"}>
          <h5 className={"fw-bold text-center mb-4"}>What’s next?</h5>
          <div className={"position-relative"}>
            <ul className="step mx-0 mb-0 text-muted">
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>1</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">
                      Create your top-class portfolio.
                    </span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>2</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Make your first investment!</span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-last-step-icon"}>3</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Automate your wealth-building!</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </>
    );
  }

  private _getIcon(): string {
    return "/images/icons/astronaut-dancing.png";
  }

  private _getActionElement(): JSX.Element {
    return (
      <a href="/portfolios/creation" className="btn btn-primary w-100" style={{ maxWidth: "100% !important" }}>
        Create my portfolio!
      </a>
    );
  }

  render(): JSX.Element {
    const { user } = this.props;

    return (
      <SuccessLayout user={user} imageUrl={this._getIcon()} actionElement={this._getActionElement()}>
        {this._getContent()}
      </SuccessLayout>
    );
  }
}

export default CreateMyPortfolioPage;
