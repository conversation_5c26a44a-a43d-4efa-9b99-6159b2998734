import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import { investmentUniverseConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import DailySummaryPortfolioCardCarousel from "../components/dailySummaryPortfolioCardCarousel";
import DailySummarySentimentScore from "../components/dailySummarySentimentScore";
import DailySummaryMarketSummaryExpanded from "../components/dailySummaryMarketSummaryExpanded";
import DailySummaryMarketSummaryCollapsed from "../components/dailySummaryMarketSummaryCollapsed";
import DailySummaryTodayMarkets from "../components/dailySummaryTodayMarkets";
import DailySummaryTopMovers from "../components/dailySummaryTopMovers";
import DailySummaryPortfolioChart from "../components/dailySummaryPortfolioChart";
import InfoModal from "../components/modals/infoModal";
import DailySummaryDateNavigation from "../components/dailySummaryDateNavigation";
import ModalsWrapper from "../components/modalsWrapper";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { SavingsProductFeeDetailsType } from "../types/savings";
import { BannerDataType } from "../configs/bannerConfig";
import Banners from "../components/banners";
import DailySummaryBackfillDisclaimer from "../components/dailySummaryBackfillDisclaimer";

export type ReturnsType = { upBy?: string; downBy?: string };

export type SavingsInterestDetailsType = {
  estimated?: boolean;
  unrealisedMonthlyInterest: string;
  dailyInterest: string;
} | null;

export type SentimentScoreWithLabelsType = {
  total: { score: number; label: SentimentLabelEnum };
  news?: { score: number; label: SentimentLabelEnum };
  analyst?: { score: number; label: SentimentLabelEnum };
  priceMomentum?: { score: number; label: SentimentLabelEnum };
};

export enum SentimentLabelEnum {
  OPTIMAL = "optimal",
  SUBOPTIMAL = "suboptimal",
  UNDERPERFORMING = "underperforming"
}

export type SundownDigestType = string;
export type TodayMarketsEntryType = { label: string; returns: ReturnsType };
export type TodayMarketsType = TodayMarketsEntryType[];

export type InvestmentWithPerformanceType = {
  assetId: investmentUniverseConfig.AssetType;
  value: string;
  weight: string;
} & ReturnsType;

export type InvestmentsWithPerformanceType = InvestmentWithPerformanceType[];

type SnapshotPortfolioComponentType = {
  key: string;
  displayValue: string;
  value: number;
};

export type DailySummaryWithDataType = {
  timestamp: number;
  chartLabel: string;
  shortDateLabel: string;
  fullDateLabel: string;
  portfolio: DailySummaryPortfolioType;
  sentimentScore?: SentimentScoreWithLabelsType;
  todayMarkets?: TodayMarketsType;
  topPerformers?: InvestmentsWithPerformanceType;
  performers: PerformersType;
  sundownDigest?: SundownDigestType;
  marketSummary?: MarketSummaryType;
  isUninvested?: boolean;
  isOnlyForChartLabel?: boolean;
  seeAllTopPerformersEnabled?: boolean;
};

export type MarketSummaryType = {
  overview?: string;
  sections?: {
    title?: string; // Concatenation of asset name and ticker
    tickerSymbol?: string; // Stored section.ticker
    assetReturns?: ReturnsType; // Only if assetId exists
    content?: string; // Stored section.content
    assetId?: publicInvestmentUniverseConfig.PublicAssetType;
    tag?: string; // Section category tag when no ticker is available
  }[];
};

export type PerformersType = {
  all: InvestmentsWithPerformanceType;
  best: InvestmentsWithPerformanceType;
  worst: InvestmentsWithPerformanceType;
};

export type DailySummaryPortfolioType = {
  cash: SnapshotPortfolioComponentType;
  savings: SnapshotPortfolioComponentType & SavingsInterestDetailsType;
  total: SnapshotPortfolioComponentType;
  holdings: SnapshotPortfolioComponentType & ReturnsType;
};

export type PortfolioKeyType = keyof DailySummaryPortfolioType;

export type DailySummaryPagePropsType = {
  dailySummaries: DailySummaryWithDataType[];
  savingsProductFeeDetails: SavingsProductFeeDetailsType;
  banners: BannerDataType[];
} & PagePropsType;

type ViewModeType = "SUMMARY" | "SUNDOWN_DIGEST" | "TOP_PERFORMERS";

type StateType = {
  viewMode: ViewModeType;
  selectedSummary: DailySummaryWithDataType;
  selectedCard: PortfolioKeyType;
  showSentimentScoreInfoModal: boolean;
  showDailyMarketSummaryInfoModal: boolean;
  showNewsSentimentScoreInfoModal: boolean;
  showAnalystSentimentScoreInfoModal: boolean;
  showPriceMomentumSentimentScoreInfoModal: boolean;
};

class DailySummaryPage extends React.Component<DailySummaryPagePropsType, StateType> {
  constructor(props: DailySummaryPagePropsType) {
    super(props);

    this.state = {
      viewMode: "SUMMARY",
      selectedSummary: this.getInitialSummary(),
      selectedCard: "holdings",
      showSentimentScoreInfoModal: false,
      showDailyMarketSummaryInfoModal: false,
      showNewsSentimentScoreInfoModal: false,
      showAnalystSentimentScoreInfoModal: false,
      showPriceMomentumSentimentScoreInfoModal: false
    };
  }

  private _setShowSentimentScoreInfoModal(showSentimentScoreInfoModal: boolean) {
    this.setState({ showSentimentScoreInfoModal });
  }

  private _setShowNewsSentimentScoreInfoModal(showNewsSentimentScoreInfoModal: boolean) {
    this.setState({ showNewsSentimentScoreInfoModal });
  }

  private _setShowAnalystSentimentScoreInfoModal(showAnalystSentimentScoreInfoModal: boolean) {
    this.setState({ showAnalystSentimentScoreInfoModal });
  }

  private _setShowPriceMomentumSentimentScoreInfoModal(showPriceMomentumSentimentScoreInfoModal: boolean) {
    this.setState({ showPriceMomentumSentimentScoreInfoModal });
  }

  private _setShowDailyMarketSummaryInfoModal(showDailyMarketSummaryInfoModal: boolean) {
    this.setState({ showDailyMarketSummaryInfoModal });
  }

  private _setViewMode(viewMode: ViewModeType) {
    this.setState({ viewMode });
  }

  private _setSelectedSummary(selectedSummary: DailySummaryWithDataType) {
    this.setState({ selectedSummary });
  }

  private _setSelectedCard(selectedCard: PortfolioKeyType) {
    this.setState({ selectedCard });
  }

  private _getChartData() {
    const { dailySummaries } = this.props;

    const cashData = dailySummaries.map((summary) => {
      return {
        value: summary.portfolio?.cash?.value ?? 0,
        label: summary.chartLabel,
        isOnlyForChartLabel: summary.isOnlyForChartLabel
      };
    });
    const holdingsData = dailySummaries.map((summary) => {
      return {
        value: summary.portfolio?.holdings?.value ?? 0,
        label: summary.chartLabel,
        isOnlyForChartLabel: summary.isOnlyForChartLabel
      };
    });
    const totalValueData = dailySummaries.map((summary) => {
      return {
        value: summary.portfolio?.total?.value ?? 0,
        label: summary.chartLabel,
        isOnlyForChartLabel: summary.isOnlyForChartLabel
      };
    });
    const savingsData = dailySummaries.map((summary) => {
      return {
        value: summary.portfolio?.savings?.value ?? 0,
        label: summary.chartLabel,
        isOnlyForChartLabel: summary.isOnlyForChartLabel
      };
    });

    return { cashData, holdingsData, savingsData, totalValueData };
  }

  /**
   * Returns second latest data point (i.e. yesterday), unless the summaries array only contains one data point
   * (i.e. today), in which case we return that.
   * @private
   */
  private getInitialSummary(): DailySummaryWithDataType {
    const { dailySummaries } = this.props;

    const realSummaries = dailySummaries.filter((summary) => !summary.isOnlyForChartLabel);

    if (realSummaries.length > 1) {
      return realSummaries?.at(-1);
    } else return realSummaries?.at(0);
  }

  render(): JSX.Element {
    const { activePage, title, subtitle, user, featureFlags, dailySummaries, savingsProductFeeDetails, banners } =
      this.props;
    const {
      selectedSummary,
      selectedCard,
      viewMode,
      showSentimentScoreInfoModal,
      showDailyMarketSummaryInfoModal,
      showNewsSentimentScoreInfoModal,
      showAnalystSentimentScoreInfoModal,
      showPriceMomentumSentimentScoreInfoModal
    } = this.state;

    const currentIndex = dailySummaries.findIndex((s) => s.timestamp === this.state.selectedSummary.timestamp);
    const { cashData, holdingsData, totalValueData, savingsData } = this._getChartData();
    const portfolio = user.portfolios?.[0];

    return (
      <MainLayout
        activePage={activePage}
        subtitle={subtitle}
        title={title}
        user={user}
        featureFlags={featureFlags}
      >
        <ModalsWrapper user={user} activePage={activePage} />
        {viewMode === "SUMMARY" && (
          <>
            <div className="d-flex align-items-center justify-content-between mb-5 p-0">
              <h2 className="m-0">Home</h2>
              <a className="earn-interest-cta shadow-sm" href="/investor/cash?selectedAccount=savings">
                {`🤑 Earn ${savingsProductFeeDetails.netInterestRateOfCurrentPlan} interest`}
              </a>
            </div>

            <div className={"border-radius-xl my-5 border-radius-xxl overflow-hidden bg-white"}>
              {!selectedSummary.isUninvested ? (
                <div
                  className={"daily-summary-gradient pt-5"}
                  style={{
                    maxWidth: "100%"
                  }}
                >
                  <DailySummaryDateNavigation
                    summaries={dailySummaries}
                    currentIndex={currentIndex}
                    onSelectSummary={(selectedSummary) => this._setSelectedSummary(selectedSummary)}
                  />
                  <DailySummaryPortfolioCardCarousel
                    activeCard={selectedCard ?? "holdings"}
                    portfolio={selectedSummary.portfolio}
                    onChangeActiveCard={(activeCard) => this._setSelectedCard(activeCard)}
                  />
                  <DailySummaryPortfolioChart
                    active={selectedCard === "cash"}
                    data={cashData}
                    activeIndex={currentIndex}
                  />
                  <DailySummaryPortfolioChart
                    active={selectedCard === "holdings"}
                    data={holdingsData}
                    activeIndex={currentIndex}
                  />
                  <DailySummaryPortfolioChart
                    active={selectedCard === "savings"}
                    data={savingsData}
                    activeIndex={currentIndex}
                  />
                  <DailySummaryPortfolioChart
                    active={selectedCard === "total"}
                    data={totalValueData}
                    activeIndex={currentIndex}
                  />
                </div>
              ) : portfolio.isTargetAllocationSetup ? (
                <div className={"position-relative d-flex justify-content-center"}>
                  <div className={"position-absolute"} style={{ maxWidth: "80%", bottom: "140px" }}>
                    <h3 className={"pb-5 text-center"}>You’re now ready to make your first investment!</h3>
                    <button
                      style={{ maxWidth: "100% !important" }}
                      type="button"
                      className="btn btn-primary fw-100"
                      onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
                    >
                      Make your first investment
                    </button>
                  </div>
                  <img src={"/images/summary/uninvested-summary.png"} style={{ maxWidth: "100%" }} />
                </div>
              ) : (
                <div className={"position-relative d-flex justify-content-center"}>
                  <div
                    className={"position-absolute d-flex flex-column align-items-center"}
                    style={{ maxWidth: "80%", bottom: "140px" }}
                  >
                    <h3 className={"pb-5 text-center"}>You’re now ready to make your first investment!</h3>
                    <button
                      className="btn btn-primary w-100 mb-4"
                      style={{ maxWidth: "100% !important" }}
                      onClick={() => (window.location.href = "/portfolios/asset-discovery?fromPage=home")}
                    >
                      Discover stocks & ETFs
                    </button>
                    <a
                      href="/portfolios/creation?shouldSetupTargetAllocation=true"
                      className="btn btn-secondary w-100"
                      style={{ maxWidth: "100% !important" }}
                    >
                      Set up a target portfolio
                    </a>
                  </div>
                  <img src={"/images/summary/uninvested-summary.png"} style={{ maxWidth: "100%" }} />
                </div>
              )}

              <div className={"px-4 bg-white"}>
                <Banners banners={banners} userHasSubscription={!!user.subscription} />
                {selectedSummary.sentimentScore && !!selectedSummary.sentimentScore?.total && (
                  <DailySummarySentimentScore
                    sentimentScore={selectedSummary.sentimentScore}
                    onInfoClick={() => this._setShowSentimentScoreInfoModal(true)}
                    onClickAnalyst={() => this._setShowAnalystSentimentScoreInfoModal(true)}
                    onClickNews={() => this._setShowNewsSentimentScoreInfoModal(true)}
                    onClickPriceMomentum={() => this._setShowPriceMomentumSentimentScoreInfoModal(true)}
                  />
                )}
                {selectedSummary.todayMarkets && (
                  <DailySummaryTodayMarkets todayMarkets={selectedSummary.todayMarkets} />
                )}
                {selectedSummary.marketSummary && (
                  <DailySummaryMarketSummaryCollapsed
                    onExpand={() => this._setViewMode("SUNDOWN_DIGEST")}
                    content={selectedSummary.marketSummary}
                    onInfoClick={() => this._setShowDailyMarketSummaryInfoModal(true)}
                  />
                )}
                {selectedSummary.performers?.all?.length > 0 && (
                  <DailySummaryTopMovers
                    onExpand={() => this._setViewMode("TOP_PERFORMERS")}
                    expanded={false}
                    allInvestments={selectedSummary.performers.all}
                    topPerformers={selectedSummary.performers.best}
                    worstPerformers={selectedSummary.performers.worst}
                  />
                )}
                <DailySummaryBackfillDisclaimer timestamp={selectedSummary.timestamp} />
              </div>
            </div>
          </>
        )}
        {viewMode === "SUNDOWN_DIGEST" && (
          <MainCard>
            <DailySummaryMarketSummaryExpanded
              onBack={() => this._setViewMode("SUMMARY")}
              content={selectedSummary.marketSummary}
              fullDate={selectedSummary.fullDateLabel}
            />
          </MainCard>
        )}
        {viewMode === "TOP_PERFORMERS" && (
          <MainCard>
            <DailySummaryTopMovers
              onBack={() => this._setViewMode("SUMMARY")}
              expanded={true}
              allInvestments={selectedSummary.performers.all}
              topPerformers={selectedSummary.performers.best}
              worstPerformers={selectedSummary.performers.worst}
            />
          </MainCard>
        )}

        <InfoModal
          title={"About Wealthyhood’s sentiment score"}
          show={showSentimentScoreInfoModal}
          handleClose={() => this._setShowSentimentScoreInfoModal(false)}
        >
          {user.isPriceMomentumSentimentEnabled ? (
            <>
              <p className={"text-muted"}>
                The Wealthyhood sentiment score gives you a daily snapshot of how favourable the outlook is for
                your investments, rated from 0-100. Think of it as a health check-up that combines three key
                signals about your portfolio's condition.
              </p>
              <p className={"text-muted"}>
                First, it analyses <strong>recent news coverage</strong> about your investments – are companies in
                your portfolio making headlines for good reasons (like strong earnings) or concerning ones (like
                management issues)?
              </p>
              <p className={"text-muted"}>
                Second, it factors in what <strong>professional analysts</strong> are saying – are they
                recommending people buy, hold, or sell the assets you own?
              </p>
              <p className={"text-muted"}>
                Third, it looks at <strong>recent price movements</strong> of your investments – are they showing
                positive momentum or facing downward pressure?
              </p>
              <p className={"text-muted"}>
                By combining these three important factors into a single score, we help you quickly understand the
                overall sentiment around your portfolio without having to constantly monitor multiple sources
                yourself.
              </p>
            </>
          ) : (
            <>
              <p className={"text-muted"}>
                The Wealthyhood sentiment score gives you a daily snapshot of how favourable the outlook is for
                your investments, rated from 0-100. Think of it as a health check-up that combines two key signals
                about your portfolio's condition.
              </p>
              <p className={"text-muted"}>
                First, it analyses <strong>recent news coverage</strong> about your investments – are companies in
                your portfolio making headlines for good reasons (like strong earnings) or concerning ones (like
                management issues)?
              </p>
              <p className={"text-muted"}>
                Second, it factors in what <strong>professional analysts</strong> are saying – are they
                recommending people buy, hold, or sell the assets you own? The weighted average of these
                recommendations provide a score between 0-1.
              </p>
              <p className={"text-muted"}>
                By combining these two important factors into a single score, as their average, we help you quickly
                understand the overall sentiment around your portfolio without having to constantly monitor
                multiple sources yourself.
              </p>
              <p className={"text-muted"}>
                Note: Data displayed is indicative only and its accuracy is not guaranteed. Past performance is not
                an indication of future performance. The data for the two individual sentiment scores is sourced
                from EOD Historical Data.
              </p>
            </>
          )}
        </InfoModal>
        <InfoModal
          title={"News sentiment"}
          show={showNewsSentimentScoreInfoModal}
          handleClose={() => this._setShowNewsSentimentScoreInfoModal(false)}
        >
          <p className={"text-muted"}>
            The News sentiment score shows how recent news coverage could affect your investment portfolio's
            outlook. Our system continuously scans trusted financial news sources for stories about your
            investments, analysing both the content and tone of the coverage.{" "}
          </p>
          <p className={"text-muted"}>
            For each asset you own, we evaluate important news like earnings reports, company announcements, and
            market developments. Recent news has more impact than older stories, and positive news (like strong
            growth) boosts the score while negative coverage (like missed targets) lowers it.{" "}
          </p>
          <p className={"text-muted"}>
            The score ranges from 0-100:{" "}
            <ul className={"text-muted"}>
              <li>
                <strong>Poor</strong> means mostly negative coverage.
              </li>
              <li>
                <strong>Fair</strong> shows mixed news.
              </li>
              <li>
                <strong>Optimal</strong> reflects positive coverage.
              </li>
            </ul>
          </p>
          <p className={"text-muted"}>
            This helps you stay informed about important news affecting your investments without reading every
            article yourself – though remember that news sentiment is just one factor among many to consider for
            investment decisions.
          </p>
        </InfoModal>
        <InfoModal
          title={"Analyst sentiment"}
          show={showAnalystSentimentScoreInfoModal}
          handleClose={() => this._setShowAnalystSentimentScoreInfoModal(false)}
        >
          <p className={"text-muted"}>
            The Analyst sentiment score reflects what professional financial analysts are currently saying about
            the investments in your portfolio. Our system tracks recommendations and analysis from established
            financial institutions and market experts who closely study these assets.
          </p>
          <p className={"text-muted"}>
            For each investment you hold, we monitor how analysts rate the asset (typically as{" "}
            <strong>"Buy"</strong>, <strong>"Hold"</strong> or <strong>"Sell"</strong>), along with their price
            targets and detailed research.
          </p>
          <p className={"text-muted"}>
            When more analysts are recommending buying an asset or raising their price targets, this improves the
            sentiment score. Conversely, if analysts are downgrading their recommendations or lowering price
            targets, this reduces the score.
          </p>
          <p className={"text-muted"}>
            The score ranges from 0-100:
            <ul className={"text-muted"}>
              <li>
                <strong>Poor</strong> indicates that analysts generally have cautious or negative views about your
                holdings.
              </li>
              <li>
                <strong>Fair</strong> suggests mixed or neutral analyst opinions.
              </li>
              <li>
                <strong>Optimal</strong> means analysts are predominantly positive about your investments.
              </li>
            </ul>
          </p>
          <p className={"text-muted"}>
            This score helps you understand how market professionals view your portfolio's potential – though
            remember that even experts can sometimes be wrong, and analyst sentiment is just one of several factors
            to consider in your investment strategy.
          </p>
        </InfoModal>
        <InfoModal
          title={"Price momentum"}
          show={showPriceMomentumSentimentScoreInfoModal}
          handleClose={() => this._setShowPriceMomentumSentimentScoreInfoModal(false)}
        >
          <p className={"text-muted"}>
            The Price momentum score measures the recent price movements of investments in your portfolio, helping
            you understand their current market strength or weakness. Our system analyses various technical
            indicators to evaluate how strongly and in which direction your investments' prices are trending.
          </p>
          <p className={"text-muted"}>
            For each asset you own, we examine both short-term and medium-term price movements. We look at factors
            like how the current price compares to recent averages, the strength of price trends, and the
            consistency of price movements.
          </p>
          <p className={"text-muted"}>
            Strong upward price movements with steady progression contribute to a higher score, while declining
            prices or erratic movements result in a lower score.
          </p>
          <p className={"text-muted"}>
            The score ranges from 0-100:{" "}
            <ul className={"text-muted"}>
              <li>
                <strong>Poor</strong> suggests your investments are showing negative price momentum or weakness in
                their price trends.
              </li>
              <li>
                <strong>Fair</strong> indicates mixed or sideways price movement with no clear direction.
              </li>
              <li>
                <strong>Optimal</strong> means your investments are displaying positive price momentum with strong
                upward trends.
              </li>
            </ul>
          </p>
          <p className={"text-muted"}>
            This score helps you understand the current market dynamics affecting your portfolio – though remember
            that past price movements don't guarantee future performance, and price momentum is just one of several
            factors to consider in your investment strategy.
          </p>
        </InfoModal>
        <InfoModal
          title={"Daily market summary"}
          show={showDailyMarketSummaryInfoModal}
          handleClose={() => this._setShowDailyMarketSummaryInfoModal(false)}
        >
          <p className={"text-muted"}>
            The Daily Market Summary is your daily evening briefing that wraps up the most important market events
            and news that could affect your investments.
          </p>
          <p className={"text-muted"}>
            Think of it as having a knowledgeable friend summarise the day's key financial stories for you, but in
            a clear, engaging way that cuts through the noise.
          </p>
          <p className={"text-muted"}>
            We carefully curate the most relevant news, market movements, and significant developments, then
            present them in an easy-to-read format that helps you stay informed without getting overwhelmed.
          </p>
          <p className={"text-muted"}>
            Whether you're too busy to follow the markets during the day or just want to understand what really
            mattered, the Daily Market Summary helps you catch up on essential updates that could impact your
            investment journey.
          </p>
        </InfoModal>
      </MainLayout>
    );
  }
}

export default DailySummaryPage;
