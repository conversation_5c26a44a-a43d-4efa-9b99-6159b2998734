import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import { entitiesConfig, rewardsConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";

export type EarnFreeSharesPropsType = PagePropsType;

class EarnFreeSharesPage extends React.Component<EarnFreeSharesPropsType> {
  private _getRewardCopy = (): string => {
    const { user } = this.props;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    const formatedMinRewardAmount = formatCurrency(
      rewardsConfig.MIN_REWARD_AMOUNT_COPY,
      user.currency,
      locale,
      0,
      0
    );
    const formatedMaxRewardAmount = formatCurrency(
      rewardsConfig.MAX_REWARD_AMOUNT_COPY,
      user.currency,
      locale,
      0,
      0
    );

    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      const formatedMinInvestAmount = formatCurrency(
        rewardsConfig.MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY,
        user.currency,
        locale,
        0,
        0
      );
      return `When your friend invests at least ${formatedMinInvestAmount}, you'll both get 
      a free share between ${formatedMinRewardAmount} and ${formatedMaxRewardAmount}!`;
    } else {
      const formatedMinDepositAmount = formatCurrency(
        rewardsConfig.MIN_DEPOSIT_AMOUNT_FOR_REWARD_ELIGIBILITY,
        user.currency,
        locale,
        0,
        0
      );
      return `When your friend deposits at least ${formatedMinDepositAmount}, you'll both get 
      a free share between ${formatedMinRewardAmount} and ${formatedMaxRewardAmount}!`;
    }
  };
  render(): JSX.Element {
    const { user } = this.props;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <img
              className="mb-5 w-100"
              style={{ borderRadius: "18px", maxHeight: "340px" }}
              alt=""
              src={"/images/referral/refer-friend-astronauts.png"}
            />
            <h4 className="fw-bolder mb-4">Earn free shares</h4>
            <p className="text-muted">
              Invite a friend to join Wealthyhood, and you’ll both get a free share worth up to{" "}
              {formatCurrency(rewardsConfig.MAX_REWARD_AMOUNT_COPY, user.currency, locale, 0, 0)}!
            </p>
          </div>
        </div>
        {/* End About Page */}

        <div className="wh-card-body bg-white p-md-5 py-5 px-3" style={{ marginBottom: "160px" }}>
          <div className="col-md-12 d-flex flex-column align-self-center justify-content-center">
            <div>
              <h5 className={"mb-5"}>Earn your free shares in 4 simple steps:</h5>
              <div className={"row"}>
                <div className={"col-1"}>
                  <div
                    className={"d-flex justify-content-center align-items-center"}
                    style={{ backgroundColor: "#F1F3FD", borderRadius: "32px", width: "32px", height: "32px" }}
                  >
                    <p className={"mb-0"}>1</p>
                  </div>
                </div>
                <div className={"col-11"}>
                  <div className={"d-flex flex-column"}>
                    <h6>Get started</h6>
                    <p className={"text-muted t-875"}>Sign up and create your portfolio.</p>
                  </div>
                </div>
              </div>
              <div className={"row"}>
                <div className={"col-1"}>
                  <div
                    className={"d-flex justify-content-center align-items-center"}
                    style={{ backgroundColor: "#F1F3FD", borderRadius: "32px", width: "32px", height: "32px" }}
                  >
                    <p className={"mb-0"}>2</p>
                  </div>
                </div>
                <div className={"col-11"}>
                  <div className={"d-flex flex-column"}>
                    <h6>Invest</h6>
                    <p className={"text-muted t-875"}>Make your first investment and kick off your journey.</p>
                  </div>
                </div>
              </div>
              <div className={"row"}>
                <div className={"col-1"}>
                  <div
                    className={"d-flex justify-content-center align-items-center"}
                    style={{ backgroundColor: "#F1F3FD", borderRadius: "32px", width: "32px", height: "32px" }}
                  >
                    <p className={"mb-0"}>3</p>
                  </div>
                </div>
                <div className={"col-11"}>
                  <div className={"d-flex flex-column"}>
                    <h6>Share</h6>
                    <p className={"text-muted t-875"}>
                      Invite your friends with their email or share your referral code or link.
                    </p>
                  </div>
                </div>
              </div>
              <div className={"row"}>
                <div className={"col-1"}>
                  <div
                    className={"d-flex justify-content-center align-items-center"}
                    style={{ backgroundColor: "#F1F3FD", borderRadius: "32px", width: "32px", height: "32px" }}
                  >
                    <p className={"mb-0"}>4</p>
                  </div>
                </div>
                <div className={"col-11"}>
                  <div className={"d-flex flex-column"}>
                    <h6>Get your free share 🎉</h6>
                    <p className={"text-muted t-875"}>{this._getRewardCopy()}</p>
                  </div>
                </div>
              </div>
            </div>
            <a
              href="https://www.wealthyhood.com/help-centre/referrals-faq"
              target="_blank"
              className="btn btn-ghost fw-bold align-self-center mt-4 mb-5"
            >
              <div className="d-flex align-self-center justify-content-center">Read more</div>
            </a>
            {/* Fixed Bottom Button */}
            <div className="row fixed-bottom p-0 m-0 justify-content-end">
              <div className="col-md-3 col-xxl-4 col-sm-1" />
              <div className="col-xxl-4 col-md-6 col-sm-10 mb-4 bg-white wh-lab-bottom-buttons">
                <div className="d-flex p-0 justify-content-end align-self-center mb-0">
                  <div className="my-4 px-3">
                    <a
                      href="/investor/invite-friend"
                      className="btn btn-primary btn-nmw"
                      style={{ maxWidth: "300px" }}
                    >
                      Invite a friend to Wealthyhood
                    </a>
                  </div>
                </div>
              </div>
              <div className="col-md-3 col-xxl-4 col-sm-1" />
            </div>
            {/* End Fixed Bottom Button */}
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default EarnFreeSharesPage;
