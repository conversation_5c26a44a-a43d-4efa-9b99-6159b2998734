import React from "react";
import OnboardingLayout from "../layouts/onboardingLayout";
import { PagePropsType } from "../types/page";

export type EmailDisposablePropsType = PagePropsType;

class EmailDisposablePage extends React.Component<EmailDisposablePropsType> {
  render(): JSX.Element {
    return (
      <OnboardingLayout title={""} user={this.props.user}>
        <div className="row justify-content-center mb-12 mt-10">
          <div className="col-md-3 d-flex justify-content-center">
            <img src="/images/icons/unavailable-100.png" />
          </div>
        </div>
        <div className="row mb-12">
          <div className="col-12 d-flex flex-column justify-content-center text-center">
            <h2 className="font-weight-bolder text-center mb-8">Disposable Email Found</h2>
            <p className="text-center h4 font-weight-normal mb-8">
              You seem to be using a disposable email address.
            </p>
            <a href="/logout">Please create an account with your personal email address</a>
          </div>
        </div>
      </OnboardingLayout>
    );
  }
}

export default EmailDisposablePage;
