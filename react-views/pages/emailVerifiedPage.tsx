import React from "react";
import { PagePropsType } from "../types/page";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import SuccessLayout from "../layouts/successLayout";
import InfoModal from "../components/modals/infoModal";
import { formatDateToDDMONYY } from "../utils/dateUtil";
import { countriesConfig } from "@wealthyhood/shared-configs";
import LegalDocumentUtil from "../utils/legalDocumentUtil";

export type EmailVerifiedPropsType = PagePropsType;

type StateType = {
  acceptedInvestorTC: boolean;
  confirmedNonUsPerson: boolean;
  comfirmedW8BenForm: boolean;
  showInfoModal: boolean;
  info?: "non-us" | "w8-ben-form";
};

class EmailVerifiedPage extends React.Component<EmailVerifiedPropsType, StateType> {
  constructor(props: EmailVerifiedPropsType) {
    super(props);
    this.state = {
      acceptedInvestorTC: false,
      confirmedNonUsPerson: false,
      comfirmedW8BenForm: false,
      showInfoModal: false
    };
  }

  private _acceptTerms = async () => {
    try {
      await axios.post("/investor/accept-terms");
      window.location.href = "/investor/verification-initiated";
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _getRadioBtn(value: boolean, onToggle: () => void): JSX.Element {
    return (
      <>
        {value ? (
          <span
            className="material-icons icon-primary align-self-center align-self-center cursor-pointer"
            style={{
              height: "24px",
              width: "24px"
            }}
            onClick={onToggle}
          >
            check_circle
          </span>
        ) : (
          <span
            className="material-symbols-outlined text-secondary align-self-center align-self-center cursor-pointer"
            style={{
              height: "24px",
              width: "24px"
            }}
            onClick={onToggle}
          >
            radio_button_unchecked
          </span>
        )}
      </>
    );
  }

  private _setShowInfoModal(showInfoModal: boolean, info?: "non-us" | "w8-ben-form") {
    this.setState({ showInfoModal, info });
  }

  private _getW8BenFormInfo(): JSX.Element {
    const { user } = this.props;
    return (
      <div>
        <h2>W-8 BEN form</h2>
        <br />
        <h4>Your personal details</h4>
        <br />
        <div className="row m-0 p-0 mb-5">
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Full name</div>
            <div className="col p-0 fw-bold text-end">{user.firstName + " " + user.lastName}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Date of birth</div>
            <div className="col p-0 fw-bold text-end">{formatDateToDDMONYY(user.dateOfBirth)}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Citizenship</div>
            <div className="col p-0 fw-bold text-end">{user.nationalities[0]}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Email</div>
            <div className="col p-0 fw-bold text-end">{user.email}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Address</div>
            <div className="col p-0 fw-bold text-end">{`${user.addresses[0].line1}`}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Postcode</div>
            <div className="col p-0 fw-bold text-end">{`${user.addresses[0].postalCode}`}</div>
          </div>
          <div className="row py-3 m-0 border-bottom">
            <div className="col p-0 text-muted text-start">Country</div>
            <div className="col p-0 fw-bold text-end">
              {countriesConfig.countries.find((config) => config.code == user.addresses[0].countryCode)?.name}
            </div>
          </div>
          {user.taxResidency?.value && (
            <div className="row py-3 m-0 border-bottom">
              <div className="col p-0 text-muted text-start">TIN</div>
              <div className="col p-0 fw-bold text-end">{user.taxResidency?.value ?? "-"}</div>
            </div>
          )}
        </div>
        <h4 className="mb-4">Certification</h4>
        <p className="text-muted m-0">
          I certify that the beneficial owner is a resident of the aforementioned ‘Country’ within the meaning of
          the income tax treaty between the United States and that country.
        </p>
        <br />
        <p className="text-muted m-0">
          Under penalties of perjury, I declare that I have examined the information on this form and to the best
          of my knowledge and belief it is true, correct, and complete. I further certify under penalties of
          perjury that:
        </p>
        <br />
        <p className="text-muted m-0">
          • I am the individual that is the beneficial owner (or am authorized to sign for the individual that is
          the beneficial owner) of all the income to which this form relates or am using this form to document
          myself for chapter 4 purposes,
        </p>
        <br />
        <p className="text-muted m-0">• The person named above is not a U.S. person</p>
        <br />
        <p className="text-muted m-0">
          • The income to which this form relates is: (a) not effectively connected with the conduct of a trade or
          business in the United States, (b) effectively connected but is not subject to tax under an applicable
          income tax treaty, or (c) the partner's share of a partnership's effectively connected income,
        </p>
        <br />
        <p className="text-muted m-0">
          • The person named above is a resident of the treaty country listed above (if any) within the meaning of
          the income tax treaty between the United States and that country, and
        </p>
        <br />
        <p className="text-muted m-0">
          • For broker transactions or barter exchanges, the beneficial owner is an exempt foreign person as
          defined in the instructions.
        </p>
        <br />
        <p className="text-muted m-0">
          Furthermore, I authorise this form to be provided to any withholding agent that has control, receipt, or
          custody of the income of which I am the beneficial owner or any withholding agent that can disburse or
          make payments of the income of which I am the beneficial owner. I agree that I will submit a new form
          within 30 days if any certification made on this form becomes incorrect.
        </p>
        <br />
        <p className="text-muted m-0">
          The Internal Revenue Service does not require your consent to any provisions of this document other than
          the certifications required to establish your status as a non-US individual and, if applicable, obtain a
          reduced rate of withholding.
        </p>
        <br />
      </div>
    );
  }

  private _getNonUsPersonInfo(): JSX.Element {
    return (
      <div>
        <h3>Who is a US person?</h3>
        <br />
        <p className="text-muted">
          A US person is any US citizen, permanent resident alien, entity organised under the laws of the United
          States or any jurisdiction within the United States (including foreign branches), or any person in the
          United States​​.
        </p>
      </div>
    );
  }

  render(): JSX.Element {
    const { user } = this.props;
    const { acceptedInvestorTC, confirmedNonUsPerson, comfirmedW8BenForm, showInfoModal, info } = this.state;

    return (
      <SuccessLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        imageUrl={"/images/icons/email-verify.png"}
        actionElement={
          <button
            className={`btn btn-primary fw-100 ${
              acceptedInvestorTC && confirmedNonUsPerson && comfirmedW8BenForm ? "" : "disabled"
            }`}
            onClick={this._acceptTerms}
          >
            Accept and proceed
          </button>
        }
      >
        <div className="row m-0 p-0">
          <h3 className="fw-bolder text-center">Almost there!</h3>
        </div>
        <div className="row m-0 p-0 mt-3 mb-4">
          <p className="text-muted mb-5 text-center">
            Please review and confirm the following information before opening your Wealthyhood account.
          </p>
          <div className="d-flex align-items-center mb-3">
            {this._getRadioBtn(acceptedInvestorTC, () =>
              this.setState((prevState) => ({ ...prevState, acceptedInvestorTC: !prevState.acceptedInvestorTC }))
            )}
            <span className="m-0 ms-2">
              I confirm that I have read and understand the{" "}
              <a
                className="fw-bolder text-decoration-none"
                href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
                target="_blank"
                rel="noreferrer"
              >
                Investor Terms & Conditions.
              </a>
            </span>
          </div>
          <div className="d-flex align-items-center mb-3">
            {this._getRadioBtn(confirmedNonUsPerson, () =>
              this.setState((prevState) => ({
                ...prevState,
                confirmedNonUsPerson: !prevState.confirmedNonUsPerson
              }))
            )}
            <span className="m-0 ms-2">I confirm that I am not a US person.</span>{" "}
            <span
              className="material-symbols-outlined cursor-pointer align-self-center text-primary ms-1"
              style={{
                fontSize: "16px"
              }}
              onClick={() => this._setShowInfoModal(true, "non-us")}
            >
              info
            </span>
          </div>
          <div className="d-flex align-items-center">
            {this._getRadioBtn(comfirmedW8BenForm, () =>
              this.setState((prevState) => ({
                ...prevState,
                comfirmedW8BenForm: !prevState.comfirmedW8BenForm
              }))
            )}
            <span className="m-0 ms-2">
              I confirm that I have read and certify the{" "}
              <a
                className="fw-bolder text-decoration-none cursor-pointer"
                onClick={() => this._setShowInfoModal(true, "w8-ben-form")}
                rel="noreferrer"
              >
                W-8 BEN form
              </a>{" "}
              as true and accurate.
            </span>
          </div>
        </div>

        <InfoModal title={null} show={showInfoModal} handleClose={() => this._setShowInfoModal(false)}>
          {info === "non-us" && this._getNonUsPersonInfo()}
          {info === "w8-ben-form" && this._getW8BenFormInfo()}
        </InfoModal>
      </SuccessLayout>
    );
  }
}

export default EmailVerifiedPage;
