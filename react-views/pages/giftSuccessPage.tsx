import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";

export type GiftSuccessPropsType = PagePropsType;

class GiftSuccessPage extends React.Component<GiftSuccessPropsType> {
  render(): JSX.Element {
    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">Your gift is on the way!</h5>
              <p className="text-center text-muted">
                If your friend hasn’t yet invested with Wealthyhood and has no pending gifts, we’ll notify them to
                accept your gift! Make sure to let them know!
              </p>
              <p className="text-center">
                <a href="/" className="btn btn-primary fw-100 mt-5">
                  Got it!
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default GiftSuccessPage;
