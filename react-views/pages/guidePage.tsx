import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import HorizontalScroller from "../components/horizontalScroller";
import MainCard from "../layouts/mainCard";
import LoadingSpinner from "../components/loadingSpinner";
import { LearningGuideChapterType, LearningGuideWithChaptersDataType } from "../configs/learningHubConfig";
import LearningHubItemPaywall from "../components/learningHubItemPaywall";
import ScopedHtmlComponent from "../components/scopedHtmlComponent";

export type GuidePropsType = {
  learningGuide: LearningGuideWithChaptersDataType;
} & PagePropsType;

type StateType = {
  selectedChapter: number;
};

class GuidePage extends React.Component<GuidePropsType, StateType> {
  constructor(props: GuidePropsType) {
    super(props);
    this.state = {
      selectedChapter: 0
    };
  }

  private _setSelectedChapter(selectedChapter: number) {
    this.setState({ selectedChapter });
  }

  private _shouldActivatePaywall() {
    const { user } = this.props;
    const subscription = user.subscription;

    if (!subscription) {
      return true;
    }

    if (subscription.isPaidPlan) {
      return false;
    }

    return true;
  }

  render(): JSX.Element {
    const { learningGuide } = this.props;
    const { selectedChapter } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="px-md-0 px-3">
          {/* Back Button */}
          <div className="row p-0 m-0 mb-3">
            <div className="col p-0">
              <span
                className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                onClick={() => (window.location.href = "/investor/learning-hub?tab=learn")}
                style={{
                  fontSize: "24px"
                }}
              >
                arrow_back
              </span>
            </div>
          </div>
          {/* End Back Button*/}
          <div className="position-relative mb-4">
            <img className="w-100 wh-card-body" src={learningGuide.webCoverImageURL} />
            <img
              className="position-absolute start-50 top-50 translate-middle"
              style={{ height: "200px" }}
              src={learningGuide.guideIconURL}
            />
          </div>
          <h2 className="mb-2">{learningGuide.title}</h2>
          <p className="text-primary mb-5">{learningGuide.chapters.length + " chapters"}</p>
          <p className="text-muted mb-5">{learningGuide.description}</p>
        </div>
        <MainCard className={"px-md-0 px-0"}>
          {learningGuide.chapters.length > 0 ? (
            <>
              <HorizontalScroller id={"guide-scroller"} className="p-0 px-lg-0 px-md-3 px-0 mb-5">
                {learningGuide.chapters.map((chapter: LearningGuideChapterType, index) => (
                  <div
                    key={`tenor-nav-${index}`}
                    className={
                      "cursor-pointer col py-2 px-3 text-center align-self-center text-center text-nowrap " +
                      (index === selectedChapter ? "fw-bold active-sector" : "")
                    }
                    onClick={() => this._setSelectedChapter(index)}
                  >
                    {index + 1 + ". " + chapter.title}
                  </div>
                ))}
              </HorizontalScroller>
              <ScopedHtmlComponent
                className={`px-md-5 px-0 ${this._shouldActivatePaywall() ? "paywalled" : ""}`}
                htmlContent={this.props.learningGuide.chapters[this.state.selectedChapter].body}
              />
              {this._shouldActivatePaywall() && <LearningHubItemPaywall />}
            </>
          ) : (
            <LoadingSpinner />
          )}
        </MainCard>
      </MainLayout>
    );
  }
}

export default GuidePage;
