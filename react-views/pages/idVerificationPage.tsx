import React, { Component } from "react";
import axios from "axios";
import { PagePropsType } from "../types/page";
import BaseLayout from "../layouts/baseLayout";
import snsWebSdk from "@sumsub/websdk";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

export type IdVerificationPropsType = { token: string } & PagePropsType;

const ID_VERIFICATION_REDIRECT_DELAY = 1000;

export default class IdVerificationPage extends Component<IdVerificationPropsType> {
  private _generateNewToken = async (): Promise<string> => {
    try {
      const { sdkToken } = (await axios.get("/investor/id-verification/token")) as { sdkToken: string };

      return sdkToken;
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  };

  componentDidMount() {
    const { token, user } = this.props;

    const snsWebSdkInstance = snsWebSdk
      .init(token, this._generateNewToken)
      .withConf({
        lang: "en",
        email: user.email,
        theme: "light",
        controlledNavigationBack: true
      })
      .withOptions({ addViewportTag: false, adaptIframeHeight: true })
      .on("idCheck.onApplicantStatusChanged", (payload: any) => {
        if (["completed", "pending"].includes(payload.reviewStatus)) {
          setTimeout(
            () => (window.location.href = "/investor/id-verification/pending"),
            ID_VERIFICATION_REDIRECT_DELAY
          );
        }
      })
      .build();

    snsWebSdkInstance.launch("#sumsub-websdk-container");
  }

  render() {
    const { user } = this.props;

    return (
      <BaseLayout user={user}>
        <div id="sumsub-websdk-container" />
      </BaseLayout>
    );
  }
}
