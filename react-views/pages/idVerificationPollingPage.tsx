import React, { Component } from "react";
import { PagePropsType } from "../types/page";
import BaseLayout from "../layouts/baseLayout";
import axios from "axios";
import { captureException } from "@sentry/react";
import { KycOperationDocument } from "../../models/KycOperation";

export type IdVerificationPollingPropsType = PagePropsType;

const POLL_INTERVAL = 4000;

export default class IdVerificationPollingPage extends Component<IdVerificationPollingPropsType> {
  private _pollingTimer: NodeJS.Timeout;

  componentDidMount(): void {
    this._pollingTimer = setTimeout(() => this._triggerPolling(), POLL_INTERVAL);
  }

  componentWillUnmount() {
    this._stopPolling();
  }

  private _stopPolling = () => {
    clearInterval(this._pollingTimer);
    this._pollingTimer = null;
  };

  private _triggerPolling = async () => {
    try {
      const res = await axios.post<KycOperationDocument>("/investor/sync-id-verification");
      if (res?.data?.isProcessed) {
        this._stopPolling();
        window.location.href = "/investor/collect-personal-details";
        return;
      }
    } catch (err) {
      captureException(err);
    }

    this._pollingTimer = setTimeout(() => this._triggerPolling(), POLL_INTERVAL);
  };

  render() {
    const { user } = this.props;

    return (
      <BaseLayout user={user}>
        <div className="d-flex flex-column justify-content-center align-items-center p-0 vh-100">
          <div id="success-spinner" className="mb-5" />
          <h4 className={"mb-3"}>We are processing your details...</h4>
          <p className="text-muted">This may take up to 30 seconds.</p>
        </div>
      </BaseLayout>
    );
  }
}
