import React, { Component } from "react";
import { PagePropsType } from "../types/page";
import SuccessLayout from "../layouts/successLayout";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";

export type IdVerificationResumePropsType = PagePropsType;

export default class IdVerificationResumePage extends Component<IdVerificationResumePropsType> {
  render() {
    return (
      <SuccessLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        enableModals={true}
        imageUrl={"/images/icons/verification.png"}
        actionElement={
          <div className="p-0 fade-in">
            <div className="row m-0 mb-4 w-100 text-center">
              <LoadingOnSubmitButton
                style={{ maxWidth: "100% !important" }}
                type="button"
                className="btn btn-primary fw-100"
                customonclick={async () => window.location.replace("/investor/id-verification")}
              >
                Continue account verification
              </LoadingOnSubmitButton>
            </div>
          </div>
        }
      >
        <div className="row m-0 p-0 mb-4 mt-4 text-center">
          <h3 className="fade-in fw-bolder">Please continue the verification process.</h3>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="p-0 fade-in">
            <p className="text-center text-muted px-3">
              You need to complete the verification process before opening your Wealthyhood account.
            </p>
          </div>
        </div>
      </SuccessLayout>
    );
  }
}
