import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import ModalsWrapper from "../components/modalsWrapper";
import { TransactionActivityItemType } from "../types/transaction";
import InvestmentActivity from "../components/investmentActivity";
import { ProviderType } from "../../services/truelayerService";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import {
  AssetTransactionDocument,
  CashbackTransactionDocument,
  DepositCashTransactionDocument,
  DividendTransactionDocument,
  RebalanceTransactionDocument,
  SavingsTopupTransactionDocument,
  TransactionDocument,
  WealthyhoodDividendTransactionDocument,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import { RewardDocument } from "../../models/Reward";
import CashReceiptModal from "../components/modals/cashReceiptModal";
import InvestmentReceiptModal from "../components/modals/investmentReceiptModal";
import RepeatingInvestmentReceiptModal from "../components/modals/repeatingInvestmentReceiptModal";
import RepeatingSavingsReceiptModal from "../components/modals/repeatingSavingsReceiptModal";
import RewardReceiptModal from "../components/modals/rewardReceiptModal";
import DividendReceiptModal from "../components/modals/dividendReceiptModal";
import RebalanceReceiptModal from "../components/modals/rebalanceReceiptModal";
import CashbackReceiptModal from "../components/modals/cashbackReceiptModal";
import WealthyhoodDividendReceiptModal from "../components/modals/wealthyhoodDividendReceiptModal";
import { SavingsTopUpAutomationDocument, TopUpAutomationDocument } from "../../models/Automation";

export type InvestmentActivityPropsType = {
  activity?: TransactionActivityItemType[];
  truelayerProviders: ProviderType[];
  investmentProducts: InvestmentProductDocument[];
  topUpAutomation: TopUpAutomationDocument;
  savingsTopUpAutomation: SavingsTopUpAutomationDocument;
} & PagePropsType;

type StateType = {
  selectedTransaction: TransactionDocument;
  selectedReward: RewardDocument;
};

class InvestmentActivityPage extends React.Component<InvestmentActivityPropsType, StateType> {
  constructor(props: InvestmentActivityPropsType) {
    super(props);
    this.state = {
      selectedTransaction: null,
      selectedReward: null
    };

    this._clearTransactionSelection = this._clearTransactionSelection.bind(this);
  }

  private _setSelectedReward(selectedReward: RewardDocument) {
    this.setState({ selectedReward });
  }

  private _clearRewardSelection = () => {
    this._setSelectedReward(null);
  };

  private _setSelectedTransaction(selectedTransaction: TransactionDocument) {
    this.setState({ selectedTransaction });
  }

  private _clearTransactionSelection() {
    this._setSelectedTransaction(null);
  }

  private _openTransactionModal(options: { transaction?: TransactionDocument; reward?: RewardDocument }) {
    if (options.transaction) {
      this._setSelectedTransaction(options.transaction);
    } else if (options.reward) {
      this._setSelectedReward(options.reward);
    }
  }

  render(): JSX.Element {
    const { user, activity, truelayerProviders, investmentProducts, topUpAutomation, savingsTopUpAutomation } =
      this.props;
    const { selectedReward, selectedTransaction } = this.state;

    const showRewardReceiptModal = !!selectedReward;
    const showDividendReceiptModal = selectedTransaction?.category == "DividendTransaction";
    const showInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRepeatingInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !!(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRepeatingSavingsReceiptModal =
      selectedTransaction?.category == "SavingsTopupTransaction" &&
      (selectedTransaction as SavingsTopupTransactionDocument).displayStatus === "PendingDeposit" &&
      !!(selectedTransaction as SavingsTopupTransactionDocument).linkedAutomation;
    const showRebalanceReceiptModal = selectedTransaction?.category == "RebalanceTransaction";
    const showCashbackReceiptModal = selectedTransaction?.category == "CashbackTransaction";
    const showWhDividendReceiptModal = selectedTransaction?.category == "WealthyhoodDividendTransaction";
    const showCashReceiptModal = ["DepositCashTransaction", "WithdrawalCashTransaction"].includes(
      selectedTransaction?.category
    );

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        <ModalsWrapper user={this.props.user} activePage={this.props.activePage} />

        {/* Back Button */}
        <div className="row p-0 m-0 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
              onClick={() => (window.location.href = "/investor/investments")}
              style={{
                fontSize: "24px"
              }}
            >
              arrow_back
            </span>
          </div>
        </div>

        <MainCard>
          <InvestmentActivity
            truelayerProviders={truelayerProviders}
            activity={activity}
            investmentProducts={investmentProducts}
            openTransactionModal={(options) => this._openTransactionModal(options)}
          />
        </MainCard>

        {/* Receipt Modal */}
        {showCashReceiptModal && (
          <CashReceiptModal
            transaction={selectedTransaction as WithdrawalCashTransactionDocument | DepositCashTransactionDocument}
            truelayerProviders={truelayerProviders}
            show={showCashReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showInvestmentReceiptModal && (
          <InvestmentReceiptModal
            user={user}
            transaction={selectedTransaction as AssetTransactionDocument}
            investmentProducts={investmentProducts}
            show={showInvestmentReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRepeatingInvestmentReceiptModal && (
          <RepeatingInvestmentReceiptModal
            user={user}
            transaction={selectedTransaction as AssetTransactionDocument}
            automation={topUpAutomation}
            investmentProducts={investmentProducts}
            show={showRepeatingInvestmentReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRepeatingSavingsReceiptModal && (
          <RepeatingSavingsReceiptModal
            transaction={selectedTransaction as SavingsTopupTransactionDocument}
            automation={savingsTopUpAutomation}
            show={showRepeatingSavingsReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRewardReceiptModal && (
          <RewardReceiptModal
            reward={selectedReward}
            show={showRewardReceiptModal}
            handleClose={this._clearRewardSelection}
          />
        )}
        {showDividendReceiptModal && (
          <DividendReceiptModal
            transaction={selectedTransaction as DividendTransactionDocument}
            show={showDividendReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRebalanceReceiptModal && (
          <RebalanceReceiptModal
            investmentProducts={investmentProducts}
            transaction={selectedTransaction as RebalanceTransactionDocument}
            show={showRebalanceReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showCashbackReceiptModal && (
          <CashbackReceiptModal
            transaction={selectedTransaction as CashbackTransactionDocument}
            show={showCashbackReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showWhDividendReceiptModal && (
          <WealthyhoodDividendReceiptModal
            whDividend={selectedTransaction as WealthyhoodDividendTransactionDocument}
            show={showWhDividendReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
      </MainLayout>
    );
  }
}

export default InvestmentActivityPage;
