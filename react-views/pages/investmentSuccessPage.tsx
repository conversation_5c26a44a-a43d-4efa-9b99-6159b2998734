import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";

export type InvestmentSuccessPropsType = PagePropsType;

class InvestmentSuccessPage extends React.Component<InvestmentSuccessPropsType> {
  render(): JSX.Element {
    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">Yay! Your order has been placed successfully!</h5>
              <p className="text-center text-muted">
                You can check the status of your orders in {"'"}Investments{"'"}.
              </p>
              <p className="text-center text-muted">
                We{"'"}ll notify you by email when your investment has been executed.
              </p>
              <p className="text-center text-muted">
                <a href="/investor/investments" className="btn btn-primary w-100 mt-5">
                  Got it!
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default InvestmentSuccessPage;
