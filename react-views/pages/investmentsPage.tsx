import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import DashboardPortfolioBreakdown from "../components/dashboardPortfolioBreakdown";
import PortfolioValueChart from "../components/portfolioValueChart";
import PortfolioValueAndReturn from "../components/portfolioValueAndReturn";
import { PagePropsType } from "../types/page";
import { HoldingsType, PortfolioReturnRates } from "../../models/Portfolio";
import { eventEmitter, EVENTS } from "../utils/eventService";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import ModalsWrapper from "../components/modalsWrapper";
import ActionButtons from "../components/actionButtons";
import GroupBy, { GroupByMethodEnum } from "../components/groupBy";
import { AssetTransactionDocument } from "../../models/Transaction";
import { PortfolioPricesByTenorType } from "../types/portfolio";
import { TenorEnum } from "../configs/durationConfig";

export type DashboardPagePropsType = {
  availableCash: number;
  currency: string;
  portfolioPricesByTenor: PortfolioPricesByTenorType;
  holdingsWithReturns: (HoldingsType & { sinceBuyReturns?: number })[];
  portfolioReturnsValues: PortfolioReturnRates;
  portfolioValue: number;
  portfolioId: string;
  upByValues: PortfolioReturnRates;
  hasPendingRebalanceTransactions: boolean;
  showSuccessOrderModal: boolean;
  successOrderDocument: AssetTransactionDocument | null;
} & PagePropsType;

type StateType = {
  activeChartDuration: TenorEnum;
  groupByMethod: GroupByMethodEnum;
};

const dashboardGroupByMethods: GroupByMethodEnum[] = [
  GroupByMethodEnum.AssetClassAndSector,
  GroupByMethodEnum.AssetType,
  GroupByMethodEnum.SingleList
];

class InvestmentsPage extends React.Component<DashboardPagePropsType, StateType> {
  constructor(props: DashboardPagePropsType) {
    super(props);
    this.state = {
      activeChartDuration: TenorEnum.ALL_TIME,
      groupByMethod: GroupByMethodEnum.AssetType
    };
    this._handleOnTenorChange = this._handleOnTenorChange.bind(this);
  }

  private _handleOnTenorChange(activeChartDuration: TenorEnum) {
    this.setState({ activeChartDuration });
  }

  private _onSelectGroupByMethod(selectedGroupByMethod: GroupByMethodEnum) {
    this.setState({ groupByMethod: selectedGroupByMethod });
  }

  componentDidMount(): void {
    if (this.props.showSuccessOrderModal && this.props.successOrderDocument) {
      eventEmitter.emit(EVENTS.orderSuccessModal, this.props.successOrderDocument);
      // we update the path and remove order success related params, since we no longer need them
      const currentURL = new URL(window.location.href);
      const params = new URLSearchParams(currentURL.search);
      params.delete("showSuccessOrder");
      params.delete("assetTransactionId");
      params.delete("depositId");

      window.history.replaceState(
        null,
        null,
        String(params) == "" ? `${currentURL.origin}/` : `${currentURL.origin}/?${params}`
      );
    } else {
      eventEmitter.emit(EVENTS.pendingWhDividendModal);
    }
  }

  render(): JSX.Element {
    const {
      portfolioPricesByTenor,
      holdingsWithReturns,
      hasPendingRebalanceTransactions,
      user,
      title,
      subtitle,
      activePage,
      featureFlags,
      portfolioValue,
      upByValues,
      portfolioReturnsValues
    } = this.props;

    const portfolio = this.props.user.portfolios[0];

    return (
      <MainLayout
        activePage={activePage}
        subtitle={subtitle}
        title={title}
        user={user}
        featureFlags={featureFlags}
      >
        <ModalsWrapper user={this.props.user} activePage={this.props.activePage} />
        <div className="d-flex align-items-center justify-content-between mb-5 p-0">
          <h2 className="m-0">Investments</h2>
          <a
            href="/portfolios/asset-discovery"
            className="d-flex align-items-center justify-content-center text-muted text-decoration-none text-start btn btn-light btn-small me-3 account-card-btn"
          >
            <span
              className="mx-auto material-symbols-outlined align-self-center px-3"
              style={{ fontSize: "20px", color: "#000" }}
            >
              search
            </span>
          </a>
        </div>

        <>
          {user.portfolioConversionStatus === "completed" ? (
            <MainCard>
              <div className="row m-0 p-0">
                <div className="col p-0 m-0">
                  <PortfolioValueAndReturn
                    upByValues={upByValues}
                    portfolioReturnsValues={portfolioReturnsValues}
                    activeTenor={this.state.activeChartDuration}
                    portfolioValue={portfolioValue}
                  />
                </div>
              </div>
              <div className="row justify-content-center m-0">
                <div className="col p-0">
                  <PortfolioValueChart
                    onTenorChange={this._handleOnTenorChange}
                    portfolioPricesByTenor={portfolioPricesByTenor}
                    activeTenor={this.state.activeChartDuration}
                  />
                </div>
              </div>
            </MainCard>
          ) : portfolio.isTargetAllocationSetup ? (
            <>
              <PortfolioValueAndReturn
                upByValues={upByValues}
                portfolioReturnsValues={portfolioReturnsValues}
                activeTenor={this.state.activeChartDuration}
                portfolioValue={portfolioValue}
              />
              <div className={"border-radius-xl my-5 border-radius-xxl overflow-hidden bg-white"}>
                <div className={"position-relative d-flex justify-content-center"}>
                  <div className={"position-absolute"} style={{ maxWidth: "80%", bottom: "140px" }}>
                    <h3 className={"pb-5 text-center"}>Nice, you’re all set to start investing!</h3>
                    <button
                      style={{ maxWidth: "100% !important" }}
                      type="button"
                      className="btn btn-primary fw-100"
                      onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
                    >
                      Make your first investment
                    </button>
                  </div>
                  <img
                    src={"/images/backgrounds/uninvested-investments-background.png"}
                    style={{ maxWidth: "100%", borderRadius: "32px" }}
                  />
                </div>
              </div>
            </>
          ) : (
            <>
              <PortfolioValueAndReturn
                upByValues={upByValues}
                portfolioReturnsValues={portfolioReturnsValues}
                activeTenor={this.state.activeChartDuration}
                portfolioValue={portfolioValue}
              />

              <div className={"border-radius-xl my-5 border-radius-xxl overflow-hidden bg-white"}>
                <div className={"position-relative d-flex justify-content-center"}>
                  <div className={"position-absolute"} style={{ maxWidth: "80%", bottom: "140px" }}>
                    <h3 className={"pb-5 text-center"}>Nice, you’re all set to start investing!</h3>
                    <button
                      className="btn btn-primary w-100 mb-4"
                      style={{ maxWidth: "100% !important" }}
                      onClick={() => (window.location.href = "/portfolios/asset-discovery")}
                    >
                      Discover stocks & ETFs
                    </button>
                    <a
                      href="/portfolios/creation?shouldSetupTargetAllocation=true"
                      className="btn btn-secondary w-100"
                      style={{ maxWidth: "100% !important" }}
                    >
                      Set up a target portfolio
                    </a>
                  </div>
                  <img
                    src={"/images/backgrounds/uninvested-investments-background.png"}
                    style={{ maxWidth: "100%", borderRadius: "32px" }}
                  />
                </div>
              </div>
            </>
          )}
        </>

        {/* Action Buttons */}
        <ActionButtons
          disableBuyButton={!portfolio?.isTargetAllocationSetup && portfolio?.holdings?.length === 0}
          disableActivityButton={user.portfolioConversionStatus === "notStarted"}
        />
        {/* End Action Buttons */}

        {/* Allocation Breakdown */}
        {holdingsWithReturns?.length > 0 && (
          <MainCard>
            <GroupBy
              title="Your investments"
              activeGroupByMethod={this.state.groupByMethod}
              groupByMethods={dashboardGroupByMethods}
              onSelectGroupByMethodClick={(selectedGroupByMethod) =>
                this._onSelectGroupByMethod(selectedGroupByMethod)
              }
            />
            <div className="row m-0">
              <div className="col p-0">
                <DashboardPortfolioBreakdown
                  holdingsArray={holdingsWithReturns}
                  onAssetClick={async (assetCommonId: investmentUniverseConfig.AssetType): Promise<void> => {
                    eventEmitter.emit(EVENTS.investmentProductModal, assetCommonId);
                  }}
                  groupByMethod={this.state.groupByMethod}
                />
              </div>
            </div>
          </MainCard>
        )}
        {/* End Allocation Breakdown */}
      </MainLayout>
    );
  }
}

export default InvestmentsPage;
