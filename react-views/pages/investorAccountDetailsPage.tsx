import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import CloseAccountModal from "../components/modals/closeAccountModal";
import { countriesConfig } from "@wealthyhood/shared-configs";
import ModalsWrapper from "../components/modalsWrapper";
import IntercomUtil from "../utils/intercomUtil";

type StateType = {
  showCloseAccountModal: boolean;
  modalKey: number;
};

class InvestorAccountDetailsPage extends React.Component<PagePropsType, StateType> {
  constructor(props: PagePropsType) {
    super(props);
    this.state = {
      showCloseAccountModal: false,
      modalKey: 0
    };
  }

  private _setShowCloseAcountModal(showCloseAccountModal: boolean): void {
    this.setState((prevState) => {
      return {
        showCloseAccountModal,
        /**
         * Only update modalKey when we set showCloseAccountModal -> true
         * This will recreate modal instead of update it, in order to reset it's state
         */
        modalKey: showCloseAccountModal ? prevState.modalKey + 1 : prevState.modalKey
      };
    });
    this.setState({ showCloseAccountModal });
  }

  render(): JSX.Element {
    const { user } = this.props;
    const { showCloseAccountModal, modalKey } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <ModalsWrapper user={user} />

        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Account details</h5>
            <p className="text-muted">
              Here, you can review your personal details and request an edit if something is incorrect.
            </p>
          </div>
        </div>
        {/* End About Page */}

        {/* User Info */}
        <div className="row p-0 m-0">
          <div className="col p-0 m-0">
            <MainCard>
              <h5 className="fw-bolder p-0 mb-4">Your personal details</h5>
              <div className="row py-3 m-0 border-bottom">
                <div className="col p-0 text-muted text-start">Name</div>
                <div className="col p-0 fw-bold text-end">{user.firstName}</div>
              </div>
              <div className="row py-3 m-0 border-bottom">
                <div className="col p-0 text-muted text-start">Surname</div>
                <div className="col p-0 fw-bold text-end">{user.lastName}</div>
              </div>
              <div className="row py-3 m-0 border-bottom">
                <div className="col p-0 text-muted text-start">Date of birth</div>
                <div className="col p-0 fw-bold text-end">
                  {user.dateOfBirth &&
                    new Date(user.dateOfBirth).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric"
                    })}
                </div>
              </div>
              <div className="row py-3 m-0 border-bottom">
                <div className="col p-0 text-muted text-start">TIN</div>
                <div className="col p-0 fw-bold text-end">{user.taxResidency?.value ?? "-"}</div>
              </div>
              <div className="row py-3 m-0 border-bottom">
                <div className="col p-0 text-muted text-start">Email</div>
                <div className="col p-0 fw-bold text-end">{user.email}</div>
              </div>
              {user.addresses.length > 0 && (
                <>
                  <div className="row py-3 m-0 border-bottom">
                    <div className="col p-0 text-muted text-start">Address</div>
                    <div className="col p-0 fw-bold text-end">{`${user.addresses?.[0]?.line1}, ${user.addresses?.[0]?.city}`}</div>
                  </div>
                  <div className="row py-3 m-0 border-bottom">
                    <div className="col p-0 text-muted text-start">Postcode</div>
                    <div className="col p-0 fw-bold text-end">{user.addresses?.[0]?.postalCode}</div>
                  </div>
                </>
              )}
              <div className="row py-3 m-0 mb-5">
                <div className="col p-0 text-muted text-start">Country</div>
                <div className="col p-0 fw-bold text-end">
                  {countriesConfig.countries.find((config) => config.code == user.taxResidency?.countryCode)?.name}
                </div>
              </div>
              {/* Request Edit Button */}
              <div className="row m-0 mb-4 p-0 justify-content-center">
                <div
                  className="btn btn-ghost fw-bold"
                  onClick={(event) => {
                    event.stopPropagation();
                    IntercomUtil.showNewMessage();
                  }}
                >
                  Request an edit
                </div>
              </div>
              {/* End Request Edits Button */}
              {/* Close Account Button */}
              <div className="row m-0 p-0 justify-content-center">
                <div
                  className="btn btn-clean btn-wh-clean text-danger fw-bold"
                  onClick={() => this._setShowCloseAcountModal(true)}
                >
                  Close your account
                </div>
              </div>
              {/* End Close Account Button */}
            </MainCard>
          </div>
        </div>
        {/* End User Info */}

        <CloseAccountModal
          user={user}
          show={showCloseAccountModal}
          handleClose={() => this._setShowCloseAcountModal(false)}
          key={modalKey}
        />
      </MainLayout>
    );
  }
}

export default InvestorAccountDetailsPage;
