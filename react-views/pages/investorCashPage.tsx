import React from "react";
import {
  AssetTransactionDocument,
  CashbackTransactionDocument,
  DepositCashTransactionDocument,
  DividendTransactionDocument,
  RebalanceTransactionDocument,
  SavingsTopupTransactionDocument,
  TransactionDocument,
  WealthyhoodDividendTransactionDocument,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import { ProviderType } from "../../services/truelayerService";
import CashBalanceCardWithActions from "../components/cashBalanceCardWithActions";
import VirtualToRealBannerCTA from "../components/virtualToRealBannerCTA";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import ModalsWrapper from "../components/modalsWrapper";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { isVerified } from "../utils/userUtil";
import { SavingsTopUpAutomationDocument, TopUpAutomationDocument } from "../../models/Automation";
import { TransactionActivityTransactionItemType } from "../types/transaction";
import InvestorSavingsActivity from "../components/investorSavingsActivity";
import DividendReceiptModal from "../components/modals/dividendReceiptModal";
import RebalanceReceiptModal from "../components/modals/rebalanceReceiptModal";
import WealthyhoodDividendReceiptModal from "../components/modals/wealthyhoodDividendReceiptModal";
import CashbackReceiptModal from "../components/modals/cashbackReceiptModal";
import InvestmentReceiptModal from "../components/modals/investmentReceiptModal";
import CashReceiptModal from "../components/modals/cashReceiptModal";
import { SavingsProductActivityItemType, UserSavingsItemType } from "../types/savings";
import SavingsCardWithActions from "../components/savingsCardWithActions";
import { formatCurrency } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";
import RepeatingInvestmentReceiptModal from "../components/modals/repeatingInvestmentReceiptModal";
import RepeatingSavingsReceiptModal from "../components/modals/repeatingSavingsReceiptModal";
import InvestorCashActivity from "../components/investorCashActivity";
import { entitiesConfig } from "@wealthyhood/shared-configs";

export type InvestorCashPagePropsType = {
  savingsActivityItems: SavingsProductActivityItemType[];
  cashActivityItems: TransactionActivityTransactionItemType[];
  pendingIncomingCashFlowsTransactions: TransactionDocument[];
  availableCash: number;
  truelayerProviders: ProviderType[];
  portfolioValue: number;
  investmentProducts: InvestmentProductDocument[];
  topUpAutomation: TopUpAutomationDocument;
  savingsTopUpAutomation: SavingsTopUpAutomationDocument;
  userSavings: UserSavingsItemType[];
  initialSelectedAccount?: InvestorAccountType;
} & PagePropsType;

export type InvestorAccountType = "cash" | "savings";

type StateType = {
  selectedTransaction: TransactionDocument;
  selectedAccount: InvestorAccountType;
};

class InvestorCashPage extends React.Component<InvestorCashPagePropsType, StateType> {
  constructor(props: InvestorCashPagePropsType) {
    super(props);
    this.state = {
      selectedTransaction: null,
      selectedAccount: this.props.initialSelectedAccount ?? "cash"
    };

    this._clearTransactionSelection = this._clearTransactionSelection.bind(this);
  }

  private _setSelectedTransaction(selectedTransaction: TransactionDocument) {
    this.setState({ selectedTransaction });
  }

  private _clearTransactionSelection() {
    this._setSelectedTransaction(null);
  }

  private _setSelectedAccount(selectedAccount: InvestorAccountType) {
    this.setState({ selectedAccount });
  }

  private _getSavingsTabTitle() {
    const { user } = this.props;
    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return `${this.props.userSavings[0].currency} interest`;
    }
    return `${this.props.userSavings[0].currency} savings`;
  }

  public render(): JSX.Element {
    const {
      availableCash,
      truelayerProviders,
      user,
      investmentProducts,
      cashActivityItems,
      userSavings,
      pendingIncomingCashFlowsTransactions,
      savingsActivityItems,
      topUpAutomation,
      savingsTopUpAutomation
    } = this.props;
    const { selectedTransaction, selectedAccount } = this.state;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    const isUserVerified = isVerified(user);

    const showDividendReceiptModal = selectedTransaction?.category == "DividendTransaction";
    const showInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRepeatingInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !!(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRepeatingSavingsReceiptModal =
      selectedTransaction?.category == "SavingsTopupTransaction" &&
      (selectedTransaction as SavingsTopupTransactionDocument).displayStatus === "PendingDeposit" &&
      !!(selectedTransaction as SavingsTopupTransactionDocument).linkedAutomation;
    const showRebalanceReceiptModal = selectedTransaction?.category == "RebalanceTransaction";
    const showCashbackReceiptModal = selectedTransaction?.category == "CashbackTransaction";
    const showWhDividendReceiptModal = selectedTransaction?.category == "WealthyhoodDividendTransaction";
    const showCashReceiptModal = ["DepositCashTransaction", "WithdrawalCashTransaction"].includes(
      selectedTransaction?.category
    );

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        expand={true}
      >
        <ModalsWrapper user={this.props.user} activePage={this.props.activePage} />
        {/* Navigation */}
        <div className="col-xxl-6 col-md-8 col-sm-10 p-0 m-0 d-flex align-items-center justify-content-between mb-5">
          <h2 className="fw-bolder p-0 px-md-0 px-3 m-0 d-none d-sm-block">Cash</h2>
          <div className="account-tab-container">
            <span
              className={`small account-tab ${selectedAccount === "cash" ? "account-tab-active" : ""}`}
              onClick={() => this._setSelectedAccount("cash")}
            >
              Cash
            </span>
            <span
              className={`small account-tab ${selectedAccount === "savings" ? "account-tab-active" : ""}`}
              onClick={() => this._setSelectedAccount("savings")}
            >
              {this._getSavingsTabTitle()}
            </span>
          </div>
        </div>
        {/* End Navigation */}

        {/* Cash balance / Savings cards */}
        {isUserVerified && (
          <>
            <div className="col-xxl-6 col-md-8 col-sm-10 p-0 m-0 mb-5">
              <div className="d-flex justify-content-center m-0">
                {selectedAccount === "cash" && (
                  <CashBalanceCardWithActions
                    canUserDeposit={isUserVerified}
                    investmentProducts={investmentProducts}
                    truelayerProviders={truelayerProviders}
                    cashBalance={formatCurrency(availableCash, user.currency, locale)}
                    incomingCashflowTransactions={pendingIncomingCashFlowsTransactions}
                    openTransactionModal={(transaction) => this._setSelectedTransaction(transaction)}
                  />
                )}

                {selectedAccount === "savings" && (
                  <SavingsCardWithActions
                    savingsProductId={userSavings[0].savingsProductId}
                    netInterestRate={userSavings[0].netInterestRate}
                    displaySavingsAmount={userSavings[0].displaySavingsAmount}
                    displayUnrealisedInterest={userSavings[0].displayUnrealisedInterest}
                    savingsAmount={userSavings[0].savingsAmount}
                    currency={userSavings[0].currency}
                  />
                )}
              </div>
            </div>
          </>
        )}
        {/* End Cash balance / Savings cards */}

        <div className="row justify-content-auto m-0 p-0 mb-5">
          <div className="col-xxl-6 col-md-8 col-sm-10 p-0 m-0">
            <div className="row m-0 p-0 justify-content-auto">
              {!isUserVerified && (
                <div className="row p-0 m-0">
                  <div className="col p-0 m-0">
                    <VirtualToRealBannerCTA user={user} />
                  </div>
                </div>
              )}

              {/* Transaction Activity */}
              {selectedAccount === "cash" && (
                <InvestorCashActivity
                  truelayerProviders={truelayerProviders}
                  activity={cashActivityItems}
                  investmentProducts={investmentProducts}
                  openTransactionModal={(transaction) => this._setSelectedTransaction(transaction)}
                />
              )}

              {/* Savings Activity */}
              {selectedAccount === "savings" && (
                <InvestorSavingsActivity
                  savingsProductId={userSavings[0].savingsProductId}
                  truelayerProviders={truelayerProviders}
                  initialSavingsActivityItems={savingsActivityItems}
                  investmentProducts={investmentProducts}
                  openTransactionModal={(transaction) => this._setSelectedTransaction(transaction)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Receipt Modal */}
        {showCashReceiptModal && (
          <CashReceiptModal
            transaction={selectedTransaction as WithdrawalCashTransactionDocument | DepositCashTransactionDocument}
            truelayerProviders={truelayerProviders}
            show={showCashReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showInvestmentReceiptModal && (
          <InvestmentReceiptModal
            user={user}
            transaction={selectedTransaction as AssetTransactionDocument}
            investmentProducts={investmentProducts}
            show={showInvestmentReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRepeatingInvestmentReceiptModal && (
          <RepeatingInvestmentReceiptModal
            user={user}
            transaction={selectedTransaction as AssetTransactionDocument}
            automation={topUpAutomation}
            investmentProducts={investmentProducts}
            show={showRepeatingInvestmentReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRepeatingSavingsReceiptModal && (
          <RepeatingSavingsReceiptModal
            transaction={selectedTransaction as SavingsTopupTransactionDocument}
            automation={savingsTopUpAutomation}
            show={showRepeatingSavingsReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showDividendReceiptModal && (
          <DividendReceiptModal
            transaction={selectedTransaction as DividendTransactionDocument}
            show={showDividendReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showRebalanceReceiptModal && (
          <RebalanceReceiptModal
            investmentProducts={investmentProducts}
            transaction={selectedTransaction as RebalanceTransactionDocument}
            show={showRebalanceReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showCashbackReceiptModal && (
          <CashbackReceiptModal
            transaction={selectedTransaction as CashbackTransactionDocument}
            show={showCashbackReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
        {showWhDividendReceiptModal && (
          <WealthyhoodDividendReceiptModal
            whDividend={selectedTransaction as WealthyhoodDividendTransactionDocument}
            show={showWhDividendReceiptModal}
            handleClose={this._clearTransactionSelection}
          />
        )}
      </MainLayout>
    );
  }
}

export default InvestorCashPage;
