import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import HorizontalScroller from "../components/horizontalScroller";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import { rewardsConfig } from "@wealthyhood/shared-configs";
import { formatCurrency } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";
import LegalDocumentUtil from "../utils/legalDocumentUtil";

export type InviteFriendPropsType = {
  initialReferralCode: string;
} & PagePropsType;

type StateType = {
  referralCode: string;
  email: string;
};

class InviteFriendPage extends React.Component<InviteFriendPropsType, StateType> {
  constructor(props: InviteFriendPropsType) {
    super(props);
    this.state = {
      email: undefined,
      referralCode: this.props.initialReferralCode
    };
  }

  private _inviteFriend = async () => {
    const { email } = this.state;

    try {
      await axios.post("/investor/invite-friend", {
        targetUserEmail: email
      });
      emitToast({
        content: "Your invite was successfully sent to your friend!",
        toastType: ToastTypeEnum.success
      });
      this.setState({ email: "" });
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _isEmailFilled = () => {
    const { email } = this.state;

    return email?.length > 0;
  };

  private _resetReferralCode = async (options: { fromReferralLink: boolean } = { fromReferralLink: false }) => {
    try {
      const res = await axios.post("/investor/reset-referral-code");
      if (res.data.referralCode) {
        emitToast({
          content: options.fromReferralLink ? "Referral link regenerated!" : "Referral code regenerated!",
          toastType: ToastTypeEnum.success
        });
        this.setState({ referralCode: res.data.referralCode });
      }
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  render(): JSX.Element {
    const { email, referralCode } = this.state;
    const { user } = this.props;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* Back Button */}
        <div className="row p-0 m-0 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
              onClick={() => (window.location.href = "/investor/earn-free-shares")}
              style={{
                fontSize: "24px"
              }}
            >
              arrow_back
            </span>
          </div>
        </div>
        {/* End Back Button*/}

        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h4 className="fw-bolder mb-4">Invite a friend to Wealthyhood</h4>
            <p className="text-muted">
              Invite a friend to join Wealthyhood and you’ll both earn a free share up to{" "}
              {formatCurrency(rewardsConfig.MAX_REWARD_AMOUNT_COPY, user.currency, locale, 0, 0)}! Enter their
              email below or share your referral code or link.
            </p>
          </div>
        </div>
        {/* End About Page */}

        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="col-md-12 d-flex flex-column align-self-center justify-content-center">
            <div>
              <HorizontalScroller
                id={"invite-friend-methods-scroller"}
                className="p-md-0 px-3 m-0 mb-md-5"
                showScrollAfterNElements={3}
                showScrollDots={true}
              >
                <div className={"wh-invite-card ms-2 d-flex justify-content-center"}>
                  <div className={"position-relative w-100"}>
                    <img
                      className={"wh-invite-card d-flex justify-content-center position-absolute"}
                      src={"/images/referral/email-referral-card.png"}
                      alt={""}
                    />
                    <div className={"position-absolute py-4 px-4 h-100 d-flex flex-column"}>
                      <h5 className={"text-white"}>Enter a friend’s email</h5>
                      <div
                        className="wh-primary-label fw-bold d-inline-block t-875 text-white mt-1"
                        style={{
                          backgroundColor: "#E6E6E61A",
                          width: "fit-content"
                        }}
                      >
                        Recommended
                      </div>
                      <p className={"text-white t-875 pt-3"}>
                        Enter a friend’s email to send your invite! You can invite as many friends as you want
                        using their email!
                      </p>
                      <div className={"d-flex mt-auto pt-1 justify-content-between"}>
                        <div className="form-group" style={{ width: "84%" }}>
                          <input
                            type="text"
                            className="referral-email-input"
                            name="email"
                            placeholder="Your friend’s email"
                            value={email}
                            onChange={(event: any) => {
                              this.setState({ email: event?.target.value });
                            }}
                            style={{ borderRadius: "10px" }}
                            required
                          />
                        </div>
                        <div className={"d-flex justify-content-center"} style={{ width: "40px", height: "40px" }}>
                          {this._isEmailFilled() ? (
                            <LoadingOnSubmitButton
                              style={{ minWidth: "unset", width: "100%", height: "100%", borderRadius: "10px" }}
                              type="button"
                              className={
                                "btn btn-primary btn-nmw d-flex align-items-center justify-content-center p-0"
                              }
                              customonclick={this._inviteFriend}
                            >
                              <span
                                className="material-icons icon-primary cursor-pointer align-self-center text-white"
                                style={{
                                  marginLeft: "2px",
                                  fontSize: "18px"
                                }}
                              >
                                arrow_forward_ios
                              </span>
                            </LoadingOnSubmitButton>
                          ) : (
                            <button
                              type="button"
                              className="btn btn-primary btn-nmw d-flex align-items-center justify-content-center"
                              disabled={true}
                              style={{ minWidth: "unset", width: "100%", height: "100%", borderRadius: "10px" }}
                            >
                              <span
                                className="material-icons icon-primary cursor-pointer align-self-center text-white"
                                style={{
                                  marginLeft: "2px",
                                  fontSize: "18px"
                                }}
                              >
                                arrow_forward_ios
                              </span>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={"wh-invite-card ms-2 d-flex justify-content-center"}>
                  <div className={"position-relative w-100"}>
                    <img
                      className={"wh-invite-card d-flex justify-content-center position-absolute"}
                      src={"/images/referral/code-referral-card.png"}
                      alt={""}
                    />
                    <div className={"position-absolute py-4 px-4 h-100 d-flex flex-column"}>
                      <h5>Copy your referral code</h5>
                      <p className={"text-muted t-875 pt-3"}>
                        Share your code with a friend to use when signing up! It’s valid for one invite and voided
                        when redeemed. You can then generate a new code and repeat!
                      </p>
                      <div className={"d-flex pt-1 justify-content-between mt-auto"}>
                        <button
                          type="button"
                          className="btn btn-primary btn-nmw d-flex align-items-center justify-content-center w-100"
                          onClick={async () => {
                            await navigator.clipboard.writeText(referralCode);
                            emitToast({
                              content: "Referral code copied to clipboard",
                              toastType: ToastTypeEnum.success
                            });
                          }}
                          style={{ borderRadius: "10px", maxWidth: "unset", height: "40px" }}
                        >
                          {referralCode}
                          <span
                            className="material-icons icon-primary cursor-pointer align-self-center text-white ms-2"
                            style={{
                              fontSize: "18px"
                            }}
                          >
                            content_copy
                          </span>
                        </button>
                        <span
                          className="material-icons icon-primary cursor-pointer align-self-center ms-2 p-2"
                          onClick={() => this._resetReferralCode()}
                          style={{
                            fontSize: "26px",
                            color: "#4957D5"
                          }}
                        >
                          cached
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={"wh-invite-card ms-2 d-flex justify-content-center"}>
                  <div className={"position-relative w-100"}>
                    <img
                      className={"wh-invite-card d-flex justify-content-center position-absolute"}
                      src={"/images/referral/code-referral-card.png"}
                      alt={""}
                    />
                    <div className={"position-absolute py-4 px-4 h-100 d-flex flex-column"}>
                      <h5>Copy your referral link</h5>
                      <p className={"text-muted t-875 pt-3"}>
                        Share your referral link with a friend to sign up! It’s valid for only one invite and
                        voided when redeemed. You can then generate a new link and repeat!
                      </p>
                      <div className={"d-flex pt-1 justify-content-between mt-auto"}>
                        <button
                          type="button"
                          className="btn btn-primary btn-nmw d-flex align-items-center justify-content-center w-100"
                          onClick={async () => {
                            await navigator.clipboard.writeText(`https://wealthyhood.com?wlthd=${referralCode}`);
                            emitToast({
                              content: "Referral link copied to clipboard",
                              toastType: ToastTypeEnum.success
                            });
                          }}
                          style={{ borderRadius: "10px", maxWidth: "unset", height: "40px" }}
                        >
                          Copy your referral link
                          <span
                            className="material-icons icon-primary cursor-pointer align-self-center text-white ms-2"
                            style={{
                              fontSize: "18px"
                            }}
                          >
                            content_copy
                          </span>
                        </button>
                        <span
                          className="material-icons icon-primary cursor-pointer align-self-center ms-2 p-2"
                          onClick={() => this._resetReferralCode({ fromReferralLink: true })}
                          style={{
                            fontSize: "26px",
                            color: "#4957D5"
                          }}
                        >
                          cached
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </HorizontalScroller>
            </div>
          </div>
          <p className={"text-muted t-875 m-0 mt-5 text-center"}>
            By sharing your link, you confirm that you have read & agree to our
            <a
              className="ms-1 text-decoration-none text-primary fw-bold"
              href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms & Conditions
            </a>
            .
          </p>
          <p className={"text-muted t-875 text-center"}>Capital at risk.</p>
        </div>
      </MainLayout>
    );
  }
}

export default InviteFriendPage;
