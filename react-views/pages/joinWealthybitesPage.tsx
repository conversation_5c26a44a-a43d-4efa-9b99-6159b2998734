import React from "react";
import { PagePropsType } from "../types/page";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import SuccessLayout from "../layouts/successLayout";
import axios from "axios";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";

export type Join<PERSON>ealthybitesPropsType = PagePropsType;

class JoinWealthybitesPage extends React.Component<JoinWealthybitesPropsType> {
  constructor(props: JoinWealthybitesPropsType) {
    super(props);
  }

  private _subscribeToWealthybites = async (didSubscribe: boolean) => {
    try {
      await axios.post("/investor/subscribe-wealthybites", {
        didSubscribe
      });

      window.location.href = "/";
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  render(): JSX.Element {
    return (
      <SuccessLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        enableModals={true}
        imageUrl={"/images/icons/wealthybites.png"}
        actionElement={
          <div className="p-0 fade-in">
            <div className="row m-0 mb-4 w-100 text-center">
              <LoadingOnSubmitButton
                style={{ maxWidth: "100% !important" }}
                type="button"
                className="btn btn-primary fw-100"
                customonclick={async () => this._subscribeToWealthybites(true)}
              >
                Start your morning informed!
              </LoadingOnSubmitButton>
            </div>
            <div className="row m-0 w-100 text-center">
              <LoadingOnSubmitButton
                className="text-primary text-decoration-none fw-semibold"
                style={{ border: "none", background: "#fff" }}
                customonclick={async () => this._subscribeToWealthybites(false)}
              >
                No thanks
              </LoadingOnSubmitButton>
            </div>
          </div>
        }
      >
        <div className="row m-0 p-0 mb-4 mt-4 text-center">
          <h3 className="fade-in fw-bolder">Join the Community!</h3>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="p-0 fade-in">
            <p className="text-center text-muted px-3">
              Start your day with the top stories about the markets! Get the latest market news and insights from
              our analysts delivered straight to your inbox every morning!
            </p>
          </div>
        </div>
      </SuccessLayout>
    );
  }
}

export default JoinWealthybitesPage;
