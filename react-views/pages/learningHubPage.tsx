import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import LearningHubLearningGuides from "../components/learningHubLearningGuides";
import LearningHubNews from "../components/learningHubNews";
import LearningHubGlossary, { GlossaryItemType } from "../components/learningHubGlossary";
import LearningHubAnalystInsights from "../components/learningHubAnalystInsights";
import { ArticleDataType, LearningGuideDataType } from "../configs/learningHubConfig";
import { PaginatedApiResponse } from "apiResponse";
import axios from "axios";
import { captureException } from "@sentry/react";
import { isFeatureDisabled } from "../utils/featureFlagUtil";
import { ConfigCatFeatureFlags } from "../../config/featuresConfig";

const TabTypeArray = ["Analyst Insights", "Learning Guides", "News", "Glossary"] as const;
export type TabType = typeof TabTypeArray[number];
export const TABS_QUERY_MAP: Record<TabType, string> = {
  News: "news",
  "Analyst Insights": "insights",
  "Learning Guides": "learn",
  Glossary: "glossary"
};

export type LearningHubPropsType = {
  initialActiveTab: TabType;
  news: ArticleDataType[];
  analystInsights: PaginatedApiResponse<ArticleDataType>;
  learningGuides: LearningGuideDataType[];
  glossaryItems: GlossaryItemType[];
} & PagePropsType;

type StateType = {
  activeTab: TabType;
  loadedImagesCount: number;
  analystInsightsToDisplay: ArticleDataType[];
  lastAnalystInsightsPage: number;
};

class LearningHubPage extends React.Component<LearningHubPropsType, StateType> {
  private isAnalystInsightsRequestLoading = false;

  constructor(props: LearningHubPropsType) {
    super(props);
    this.state = {
      activeTab: this.props.initialActiveTab ?? "Analyst Insights",
      loadedImagesCount: 0,
      analystInsightsToDisplay: this.props.analystInsights.data,
      lastAnalystInsightsPage: this.props.analystInsights?.pagination?.page ?? 1
    };

    this._increaseAnalystInsights = this._increaseAnalystInsights.bind(this);
  }

  private _setActiveTab(activeTab: TabType): void {
    this.setState({ activeTab });
  }

  private _isPayingSubscriber(): boolean {
    const { user } = this.props;
    const subscription = user.subscription;

    if (!subscription) {
      return false;
    }

    if (subscription.isPaidPlan) {
      return true;
    }

    return false;
  }

  private _setTabQueryParam(activeTab: TabType) {
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("tab", TABS_QUERY_MAP[activeTab]);

    const newUrl = `${window.location.pathname}?${searchParams.toString()}`;

    window.history.pushState({}, "", newUrl);
  }

  private async _increaseAnalystInsights(): Promise<void> {
    if (this.isAnalystInsightsRequestLoading) return;

    this.isAnalystInsightsRequestLoading = true;

    try {
      const { lastAnalystInsightsPage } = this.state;

      const response = await axios.get(
        `/investor/learning-hub/analyst-insights?page=${lastAnalystInsightsPage + 1}`
      );

      const newAnalystInsights = (response.data as PaginatedApiResponse<ArticleDataType>).data ?? [];

      this.setState((prevState) => ({
        analystInsightsToDisplay: [...prevState.analystInsightsToDisplay, ...newAnalystInsights],
        lastAnalystInsightsPage: lastAnalystInsightsPage + 1
      }));
    } catch (err) {
      captureException(err);
    } finally {
      this.isAnalystInsightsRequestLoading = false;
    }
  }

  private _areAllAnalystInsightsFetched(): boolean {
    // If the feature is disabled, return true
    if (isFeatureDisabled(ConfigCatFeatureFlags.ANALYST_INSIGHT_MIGRATION, this.props.featureFlags)) {
      return true;
    }

    return this.state.lastAnalystInsightsPage === this.props.analystInsights.pagination.pages;
  }

  render(): JSX.Element {
    const { news, glossaryItems, learningGuides } = this.props;
    const { activeTab, analystInsightsToDisplay } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        <div className="px-md-0 px-3">
          {/* About Page */}
          <div className="row p-0 m-0 mb-4">
            <div className="col p-0">
              <h2 className="fw-bolder mb-4">Learning</h2>
              <p className="text-muted">
                Invest in yourself along with your money! We have partnered with Finimize to provide you the best
                learning guides and help you navigate the stock markets with ease.{" "}
              </p>
            </div>
          </div>
          {/* End About Page */}
          <div className="wh-card-body bg-white py-md-5 py-4 mb-md-5 mb-0">
            <div className="row px-lg-5 px-md-3 px-0 p-0 m-0 mb-5">
              <div className="d-flex align-self-center p-0">
                <div className="d-flex flex-row flex-nowrap overflow-auto no-scroll-bar">
                  {TabTypeArray.map((tab: TabType, index) => (
                    <div
                      key={`tenor-nav-${index}`}
                      className={
                        "cursor-pointer col py-2 px-3 text-center align-self-center text-center text-nowrap " +
                        (activeTab === tab ? "active-sector" : "")
                      }
                      onClick={() => {
                        this._setTabQueryParam(tab);
                        this._setActiveTab(tab);
                      }}
                    >
                      {tab}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            {activeTab == "News" && (
              <div className="d-flex flex-column px-xl-5 px-md-4 px-sm-3">
                <LearningHubNews news={news} isPayingSubscriber={this._isPayingSubscriber()} />
              </div>
            )}
            {activeTab == "Learning Guides" && (
              <div className="d-flex flex-column px-xl-5 px-md-4 px-sm-3">
                <LearningHubLearningGuides
                  learningGuides={learningGuides}
                  isPayingSubscriber={this._isPayingSubscriber()}
                />
              </div>
            )}
            {activeTab == "Analyst Insights" && (
              <div className="d-flex flex-column px-xl-5 px-md-4 px-sm-3">
                <LearningHubAnalystInsights
                  data={analystInsightsToDisplay}
                  showLoadMore={!this._areAllAnalystInsightsFetched()}
                  onLoadMoreClicked={this._increaseAnalystInsights}
                  isPayingSubscriber={this._isPayingSubscriber()}
                />
              </div>
            )}
            {activeTab == "Glossary" && (
              <div className="d-flex flex-column px-xl-5 px-md-4 px-sm-3">
                <LearningHubGlossary glossaryItems={glossaryItems} />
              </div>
            )}
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default LearningHubPage;
