import React from "react";
import OnboardingLayout from "../layouts/onboardingLayout";
import { PagePropsType } from "../types/page";

class MaintenancePage extends React.Component<PagePropsType> {
  render(): JSX.Element {
    return (
      <OnboardingLayout title="" user={this.props.user}>
        <div className="row justify-content-center mb-12 mt-10">
          <div className="col-md-3 d-flex justify-content-center">
            <img src="/images/icons/maintenance-100.png" />
          </div>
        </div>
        <div className="row mb-12">
          <div className="col-12 d-flex flex-column justify-content-center text-center">
            <h1 className="font-weight-bolder text-center mb-8">Bear with us</h1>
            <h2 className="text-center font-weight-normal">
              We{"'"}re currently performing some scheduled maintenance. {"💻"}
            </h2>
            <h2 className="text-center font-weight-normal">We{"'"}ll be back up shortly.</h2>
          </div>
        </div>
      </OnboardingLayout>
    );
  }
}

export default MaintenancePage;
