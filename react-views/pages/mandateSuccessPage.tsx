import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";
import { getOnTheXthDateString } from "../utils/dateUtil";
import { entitiesConfig } from "@wealthyhood/shared-configs";

export type MandateSuccessPropsType = {
  target: "autopilot" | "myaccount";
  dayOfMonth: number;
} & PagePropsType;

class MandateSuccessPage extends React.Component<MandateSuccessPropsType> {
  private _getAmountOfDaysToReachWealthyhood() {
    const { user } = this.props;
    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return "3 to 4";
    }
    return "4 to 5";
  }

  render(): JSX.Element {
    const { target, dayOfMonth } = this.props;

    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">Your Direct Debit was set up successfully.</h5>
              <p className="text-center text-muted">
                You will receive a separate email within 3 business days confirming that the mandate has been set
                up.
              </p>
            </div>
          </div>
          <div className="row m-0 mt-4">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <div className="direct-debit-schedule">
                <h5 className="fw-bolder mb-4">Your Direct Debit schedule</h5>
                <p className="text-muted">Here’s what to expect in terms of your Direct Debit timing.</p>
                <ol className="timeline">
                  <li className="timeline-item">
                    <span className="timeline-item-icon | faded-icon">
                      <i className="material-symbols-outlined text-primary align-self-center">assured_workload</i>
                    </span>
                    <div className="timeline-item-description">
                      <h6 className="fw-bold">Bank account charged</h6>
                      <p className="text-muted">
                        {getOnTheXthDateString({ capitalFirst: true }, dayOfMonth)} of every month.
                      </p>
                    </div>
                  </li>
                  <li className="timeline-item">
                    <span className="timeline-item-icon | faded-icon">
                      <i className="material-symbols-outlined text-primary align-self-center">schedule</i>
                    </span>
                    <div className="timeline-item-description">
                      <p className="text-muted">
                        Due to Direct Debit timings, it usually takes {this._getAmountOfDaysToReachWealthyhood()}{" "}
                        business days for your money to land at Wealthyhood after it leaves your bank accounts.
                      </p>
                    </div>
                  </li>
                  <li className="timeline-item">
                    <span className="timeline-item-icon | faded-icon">
                      <i className="material-symbols-outlined text-primary align-self-center">payments</i>
                    </span>
                    <div className="timeline-item-description">
                      <h6 className="fw-bold">Money received</h6>
                      <p className="text-muted">Your money lands at Wealthyhood.</p>
                    </div>
                  </li>
                  <li className="timeline-item">
                    <span className="timeline-item-icon | faded-icon">
                      <i className="material-symbols-outlined text-primary align-self-center">receipt_long</i>
                    </span>
                    <div className="timeline-item-description">
                      <h6 className="fw-bold">Order placed</h6>
                      <p className="text-muted">
                        Once your deposit is reconciled (up to one day), your order will be placed.
                      </p>
                    </div>
                  </li>
                </ol>

                <p className="text-muted small mt-4">
                  <strong>Note:</strong> Your first investment may take a bit longer to complete as we’re setting
                  up your Direct Debit mandate.
                </p>
              </div>
            </div>
          </div>
          {target && (
            <div className="row m-0">
              <p className="text-center">
                <a
                  href={target === "autopilot" ? "/investor/autopilot" : "/"}
                  className="btn btn-primary w-100 mt-5"
                >
                  Got it!
                </a>
              </p>
            </div>
          )}
        </div>
      </MainLayout>
    );
  }
}

export default MandateSuccessPage;
