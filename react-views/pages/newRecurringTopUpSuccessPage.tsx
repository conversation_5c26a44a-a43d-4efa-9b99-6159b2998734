import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";

export type NewRecurringTopUpSuccessPropsType = {
  target: "autopilot" | "myaccount";
} & PagePropsType;

class NewRecurringTopUpSuccessPage extends React.Component<NewRecurringTopUpSuccessPropsType> {
  render(): JSX.Element {
    const { target } = this.props;

    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">Your Direct Debit was set up successfully.</h5>
              <p className="text-center">
                {target === "autopilot" ? (
                  <a href="/investor/autopilot" className="btn btn-primary w-100 mt-5">
                    Go to Autopilot
                  </a>
                ) : (
                  <a href="/" className="btn btn-primary w-100 mt-5">
                    Got it!
                  </a>
                )}
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default NewRecurringTopUpSuccessPage;
