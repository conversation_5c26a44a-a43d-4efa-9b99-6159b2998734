import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import NotificationItem from "../components/notificationItem";
import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettingsCategory
} from "../types/notifications";

export type NotificationSettingsPropsType = {
  app?: NotificationSettingsCategory<AppNotificationSettingEnum>[];
  email?: NotificationSettingsCategory<EmailNotificationSettingEnum>[];
} & PagePropsType;

class NotificationSettingsPage extends React.Component<NotificationSettingsPropsType> {
  render(): JSX.Element {
    const { email } = this.props;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Email settings</h5>
            <p className="text-muted">Choose which emails you want to receive from Wealthyhood.</p>
          </div>
        </div>
        {/* End About Page */}

        <MainCard marginBottom={"160px"}>
          <div className="p-0 m-0 mb-4">
            {email?.map((emailNotifications) => (
              <div key={emailNotifications.category} className="mb-5">
                <h5 className="col-2 pb-4 mb-4 border-bottom w-100">{emailNotifications.category}</h5>
                {emailNotifications.notifications.map((notification) => (
                  <NotificationItem key={notification.id} notification={notification} />
                ))}
              </div>
            ))}
          </div>
        </MainCard>
      </MainLayout>
    );
  }
}

export default NotificationSettingsPage;
