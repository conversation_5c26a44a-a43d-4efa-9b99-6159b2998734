import React from "react";
import { PagePropsType } from "../types/page";
import SuccessLayout from "../layouts/successLayout";
import { GiftDocument } from "../../models/Gift";
import { UserDocument } from "../../models/User";
import { getUserInitials } from "../utils/userUtil";
import { localeConfig, rewardsConfig } from "@wealthyhood/shared-configs";
import { formatCurrency, getUserCurrencyWithLocaleFallback } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";
import Decimal from "decimal.js";

type ViewModeType = "welcome" | "gift" | "referred";

export type OpenAccountPagePropsType = {
  notSeenGift?: GiftDocument;
} & PagePropsType;

class OpenAccountPage extends React.Component<OpenAccountPagePropsType> {
  // To retrieve the user's locale, we default to the context locale (which is based on the residency country) and
  // fallback to the request locale (from the headers) if that's not available.
  private _getLocale(): localeConfig.LocaleType {
    const { requestLocale, user } = this.props;

    return ConfigUtil.getDefaultUserLocale(user.residencyCountry) ?? requestLocale;
  }

  private _getViewMode(): ViewModeType {
    const { notSeenGift, user } = this.props;

    if (notSeenGift) {
      return "gift";
    } else if (user?.canUnlockFreeShare) {
      return "referred";
    } else {
      return "welcome";
    }
  }

  private _getContentForOpenAccountView(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">Open your account!</h3>
        <p className="p-0 text-muted mb-4">
          Let’s get to know you a bit better. Verify your personal details to open your Wealthyhood account and put
          your skin in the game.
        </p>
      </>
    );
  }

  private _getContentForGiftView(): JSX.Element {
    const { notSeenGift } = this.props;

    const gifter = notSeenGift?.gifter as UserDocument;

    return (
      <>
        <h3 className="fw-bold mb-md-5 mb-4 text-center">Welcome on board!</h3>
        {gifter.firstName && gifter.lastName ? (
          <>
            <div className={"d-flex align-items-center justify-content-center"} style={{ gap: "2rem" }}>
              <div className="wh-avatar-img-lg me-2 text-white">{getUserInitials(gifter)}</div>
              <i className="fas fa-arrow-right fa-lg text-primary" />
              <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift.png"} />
            </div>
            <div className={"d-flex flex-column text-center mt-4 pt-2"}>
              <p className={"mb-0 fw-bolder"}>
                {gifter.firstName} {gifter.lastName}
              </p>
              <p>
                has sent you a{" "}
                <span className={"text-primary fw-bold"}>
                  {formatCurrency(
                    Decimal.div(notSeenGift.consideration.amount, 100).toNumber(),
                    notSeenGift.consideration.currency,
                    this._getLocale(),
                    0,
                    0
                  )}{" "}
                  gift!
                </span>
              </p>
            </div>
            <div className={"d-flex align-items-start mt-4 pt-1"}>
              <div className={"d-flex align-items-center"}>
                <div className="wh-avatar-img-sm me-2 text-white">{getUserInitials(gifter)}</div>
                <img alt="" src={"/images/gifts/gift-message-blob-corner.svg"} />
              </div>
              <p
                className={"p-3 mb-0 t-875 text-muted"}
                style={{
                  background: "#F1F3FD",
                  borderRadius: "12px",
                  marginLeft: "-2px",
                  maxWidth: "86%"
                }}
              >
                {notSeenGift.message}
              </p>
            </div>
          </>
        ) : (
          <>
            <div className={"d-flex align-items-center justify-content-center"} style={{ gap: "2rem" }}>
              <img
                className="me-2"
                style={{ width: "104px", height: "104px" }}
                alt=""
                src={"/images/avatars/astronaut-avatar.svg"}
              />
              <i className="fas fa-arrow-right fa-lg text-primary" />
              <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift.png"} />
            </div>
            <div className={"d-flex flex-column text-center mt-4 pt-2"}>
              <p className={"mb-0 fw-bolder"}>{gifter.email}</p>
              <p>
                has sent you a{" "}
                <span className={"text-primary fw-bold"}>
                  {formatCurrency(
                    notSeenGift.consideration.amount,
                    notSeenGift.consideration.currency,
                    this._getLocale(),
                    0,
                    0
                  )}{" "}
                  gift!
                </span>
              </p>
            </div>
            <div className={"d-flex align-items-start mt-4 pt-1"}>
              <div className={"d-flex align-items-center"}>
                <img
                  className="me-2"
                  style={{ width: "30px", height: "30px" }}
                  alt=""
                  src={"/images/avatars/astronaut-avatar.svg"}
                />
                <img alt="" src={"/images/gifts/gift-message-blob-corner.svg"} />
              </div>
              <p
                className={"p-3 mb-0 t-875 text-muted"}
                style={{
                  background: "#F1F3FD",
                  borderRadius: "12px",
                  marginLeft: "-2px",
                  maxWidth: "86%"
                }}
              >
                {notSeenGift.message}
              </p>
            </div>
          </>
        )}

        <div className={"d-flex flex-column mt-4 pt-1"}>
          <p className={"fw-bold pb-1"}>
            Unlock your{" "}
            {formatCurrency(
              Decimal.div(notSeenGift.consideration.amount, 100).toNumber(),
              notSeenGift.consideration.currency,
              this._getLocale(),
              0,
              0
            )}{" "}
            gift!
          </p>
          <div className={"position-relative"}>
            <ul className="step mx-0 mb-0 text-muted">
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>1</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Open your account.</span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>2</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Create your first portfolio.</span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-last-step-icon"}>3</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">
                      Invest your{" "}
                      {formatCurrency(
                        Decimal.div(notSeenGift.consideration.amount, 100).toNumber(),
                        notSeenGift.consideration.currency,
                        this._getLocale(),
                        0,
                        0
                      )}{" "}
                      gift and kick off your investing journey!
                    </span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </>
    );
  }

  private _getContentForReferralView(): JSX.Element {
    const { user } = this.props;

    const locale = this._getLocale();

    return (
      <>
        <h3 className="fw-bold mb-md-5 mb-4 text-center">Welcome on board!</h3>
        <div className={"d-flex align-items-center justify-content-center"} style={{ gap: "2rem" }}>
          <img
            className="me-2"
            style={{ width: "104px", height: "104px" }}
            alt=""
            src={"/images/avatars/astronaut-avatar.svg"}
          />
          <i className="fas fa-arrow-right fa-lg text-primary" />
          <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift-box.png"} />
        </div>
        <div className={"d-flex flex-column text-center mt-4 pt-2"}>
          <p className="text-muted">
            You signed up using a friend’s referral link. You’re now eligible for a{" "}
            <span className={"text-primary fw-bold"}>
              free share up to{" "}
              {formatCurrency(
                rewardsConfig.MAX_REWARD_AMOUNT_COPY,
                getUserCurrencyWithLocaleFallback(user, locale),
                locale,
                0,
                0
              )}
              !
            </span>
          </p>
        </div>

        <div className={"d-flex flex-column mt-4 pt-1"}>
          <p className={"fw-bold pb-1"}>Unlock your your free share!</p>
          <div className={"position-relative"}>
            <ul className="step mx-0 mb-0 text-muted">
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>1</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Open your account.</span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>2</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">Create your first portfolio!</span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-step-icon"}>3</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">
                      Invest at least{" "}
                      {formatCurrency(
                        rewardsConfig.MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY,
                        getUserCurrencyWithLocaleFallback(user, locale),
                        locale,
                        0,
                        0
                      )}{" "}
                      in your portfolio within 7 days!
                    </span>
                  </div>
                </div>
              </li>
              <li className={"step-item mb-3 learn-step-item ps-0 align-items-center"}>
                <div className={"step-content-wrapper align-items-center"}>
                  <span className={"step-icon text-small gift-last-step-icon"}>4</span>
                  <div className="step-content">
                    <span className="step-title text-regular text-light-grey">
                      Congratulations! Your free share is on the way!
                    </span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </>
    );
  }

  private _getContent(viewMode: ViewModeType): JSX.Element {
    if (viewMode === "welcome") {
      return this._getContentForOpenAccountView();
    } else if (viewMode === "gift") {
      return this._getContentForGiftView();
    } else if (viewMode === "referred") {
      return this._getContentForReferralView();
    }
  }

  private _getIcon(viewMode: ViewModeType): string {
    if (viewMode === "welcome") {
      return "/images/icons/astronaut-verified.png";
    } else {
      return "";
    }
  }

  private _getActionElement(): JSX.Element {
    return (
      <a
        href="/investor/residency-country"
        className="btn btn-primary w-100"
        style={{ maxWidth: "100% !important" }}
      >
        Open Account
      </a>
    );
  }

  render(): JSX.Element {
    const viewMode = this._getViewMode();

    return (
      <SuccessLayout
        user={this.props.user}
        imageUrl={this._getIcon(viewMode)}
        actionElement={this._getActionElement()}
      >
        {this._getContent(viewMode)}
      </SuccessLayout>
    );
  }
}

export default OpenAccountPage;
