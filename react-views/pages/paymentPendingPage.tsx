import React from "react";
import { PagePropsType } from "../types/page";
import axios from "axios";
import { captureException } from "@sentry/react";
import { DepositCashTransactionDocument } from "../../models/Transaction";
import MainLayout from "../layouts/mainLayout";
import { banksConfig } from "@wealthyhood/shared-configs";

export type PaymentPendingPropsType = {
  saltedgeCustomPaymentId: string;
  bankId: banksConfig.BankType;
} & PagePropsType;

const POLL_INTERVAL = 3000;

class PaymentPendingPage extends React.Component<PaymentPendingPropsType> {
  private _pollingTimer: NodeJS.Timeout;

  componentDidMount(): void {
    this._pollingTimer = setTimeout(() => this._triggerPaymentPolling(), POLL_INTERVAL);
  }

  componentWillUnmount() {
    this._stopPolling();
  }

  private _stopPolling = () => {
    clearInterval(this._pollingTimer);
    this._pollingTimer = null;
  };

  private _triggerPaymentPolling = async () => {
    const { saltedgeCustomPaymentId } = this.props;

    try {
      const res = await axios.get<DepositCashTransactionDocument[]>(
        `/transactions/deposits?saltedgeCustomPaymentId=${saltedgeCustomPaymentId}`
      );

      const syncedDepositTransaction = res?.data?.[0];
      const paymentStatus = syncedDepositTransaction.providers.saltedge.status;

      if (["rejected", "failed", "unknown", "deleted"].includes(paymentStatus)) {
        this._stopPolling();
        window.location.href = "/";
        return;
      } else if (paymentStatus === "accepted") {
        this._stopPolling();

        const { bankReference, consideration } = syncedDepositTransaction;
        window.location.href = `/investor/payment-success?bankReference=${bankReference}&paymentAmount=${
          consideration.amount / 100
        }`;

        return;
      }
    } catch (err) {
      captureException(err);
    }

    this._pollingTimer = setTimeout(() => this._triggerPaymentPolling(), POLL_INTERVAL);
  };

  render(): JSX.Element {
    const { bankId } = this.props;

    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className={"d-flex w-100 justify-content-center mb-5 mt-4"}>
            <img
              className="align-self-center rounded px-auto"
              style={{ height: "80px", width: "80px" }}
              src={banksConfig.BANKS_CONFIG[bankId].logo}
            />
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center text-center">
              <h5 className="fw-bolder text-center mb-4">Your deposit is being processed...</h5>
            </div>
          </div>
          <div className="row justify-content-center mb-3">
            <div className="col-md-3 d-flex justify-content-center">
              <div className={`row m-0 p-0 py-4 text-center justify-content-center`}>
                <div id="cover-spin-relative" />
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default PaymentPendingPage;
