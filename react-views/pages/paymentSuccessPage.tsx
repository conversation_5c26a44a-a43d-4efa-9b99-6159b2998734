import React from "react";
import { PagePropsType } from "../types/page";
import { formatCurrency } from "../utils/currencyUtil";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";
import ConfigUtil from "../../utils/configUtil";
import { getReportingFirm } from "../utils/userUtil";

export type PaymentSuccessPropsType = {
  bankReference: string;
  paymentAmount: number;
} & PagePropsType;

class PaymentSuccessPage extends React.Component<PaymentSuccessPropsType> {
  render(): JSX.Element {
    const { bankReference, paymentAmount, user } = this.props;

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center text-center">
              <h5 className="fw-bolder text-center mb-4">Yay! Your deposit has been created successfully!</h5>
              {bankReference && paymentAmount > 0 && (
                <>
                  <p className="text-center">
                    <span className="fw-bolder">{formatCurrency(paymentAmount, user.currency, locale)}</span> will
                    be sent to {getReportingFirm(user)}.
                  </p>
                  <p className="text-center">We{"'"}ll notify you by email when the deposit has been received.</p>
                  <p className="text-center m-0 text-muted">Ref. #{bankReference}</p>
                </>
              )}
              <p className="text-center">
                <a href="/investor/cash" className="btn btn-primary w-100 mt-5">
                  Got it!
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default PaymentSuccessPage;
