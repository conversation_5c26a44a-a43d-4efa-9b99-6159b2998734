import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import {
  AssetTransactionDocument,
  DepositCashTransactionDocument,
  RebalanceTransactionDocument,
  TransactionDocument,
  WithdrawalCashTransactionDocument
} from "../../models/Transaction";
import CashReceiptModal from "../components/modals/cashReceiptModal";
import InvestmentReceiptModal from "../components/modals/investmentReceiptModal";
import RebalanceReceiptModal from "../components/modals/rebalanceReceiptModal";
import InvestorTransactionRow from "../components/investorTransactionRow";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { ProviderType } from "../../services/truelayerService";
import RepeatingInvestmentReceiptModal from "../components/modals/repeatingInvestmentReceiptModal";
import { TopUpAutomationDocument } from "../../models/Automation";

export type PendingTransactionsPropsType = {
  pendingTransactions: TransactionDocument[];
  truelayerProviders: ProviderType[];
  investmentProducts: InvestmentProductDocument[];
  topUpAutomation: TopUpAutomationDocument;
} & PagePropsType;

type StateType = {
  selectedTransaction?: TransactionDocument;
};

class PendingTransactionsPage extends React.Component<PendingTransactionsPropsType, StateType> {
  constructor(props: PendingTransactionsPropsType) {
    super(props);
    this.state = {
      selectedTransaction: null
    };
  }

  private _setSelectedTransaction(selectedTransaction: TransactionDocument) {
    this.setState({ selectedTransaction });
  }

  private _clearTransactionSelection = () => {
    this._setSelectedTransaction(null);
  };

  render(): JSX.Element {
    const { selectedTransaction } = this.state;
    const { pendingTransactions, truelayerProviders, investmentProducts, user, topUpAutomation } = this.props;

    const showInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRepeatingInvestmentReceiptModal =
      selectedTransaction?.category == "AssetTransaction" &&
      !!(selectedTransaction as AssetTransactionDocument).linkedAutomation;
    const showRebalanceReceiptModal = selectedTransaction?.category == "RebalanceTransaction";
    const showCashReceiptModal = ["DepositCashTransaction", "WithdrawalCashTransaction"].includes(
      selectedTransaction?.category
    );

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        <div className="px-md-0 px-3">
          {/* Back Button */}
          <div className="row p-0 m-0 mb-3">
            <div className="col p-0">
              <span
                className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                onClick={() => (window.location.href = "/")}
                style={{
                  fontSize: "24px"
                }}
              >
                arrow_back
              </span>
            </div>
          </div>
          {/* End Back Button*/}
          {/* Transactions */}
          <div className="row m-0 p-0">
            <div className="col p-0 m-0">
              <MainCard className={"px-md-0 px-0"}>
                <h5 className="fw-bolder mb-4 px-md-5 px-0">Pending transactions</h5>
                <div className="row m-0 px-md-5 px-2">
                  <div className="col p-0">
                    {pendingTransactions.length > 0 ? (
                      pendingTransactions.map((transaction, index) => (
                        <InvestorTransactionRow
                          onClick={() => this._setSelectedTransaction(transaction)}
                          transaction={transaction}
                          truelayerProviders={truelayerProviders}
                          investmentProducts={investmentProducts}
                          key={`transaction_${index}`}
                        />
                      ))
                    ) : (
                      <p>No transactions made yet.</p>
                    )}
                  </div>
                </div>
                {showRebalanceReceiptModal && (
                  <RebalanceReceiptModal
                    investmentProducts={investmentProducts}
                    transaction={this.state.selectedTransaction as RebalanceTransactionDocument}
                    show={showRebalanceReceiptModal}
                    handleClose={this._clearTransactionSelection}
                  />
                )}
                {showInvestmentReceiptModal && (
                  <InvestmentReceiptModal
                    user={user}
                    transaction={this.state.selectedTransaction as AssetTransactionDocument}
                    investmentProducts={investmentProducts}
                    show={showInvestmentReceiptModal}
                    handleClose={this._clearTransactionSelection}
                  />
                )}
                {showCashReceiptModal && (
                  <CashReceiptModal
                    transaction={
                      this.state.selectedTransaction as
                        | WithdrawalCashTransactionDocument
                        | DepositCashTransactionDocument
                    }
                    truelayerProviders={truelayerProviders}
                    show={showCashReceiptModal}
                    handleClose={this._clearTransactionSelection}
                  />
                )}
                {showRepeatingInvestmentReceiptModal && (
                  <RepeatingInvestmentReceiptModal
                    user={user}
                    transaction={selectedTransaction as AssetTransactionDocument}
                    automation={topUpAutomation}
                    investmentProducts={investmentProducts}
                    show={showRepeatingInvestmentReceiptModal}
                    handleClose={this._clearTransactionSelection}
                  />
                )}
              </MainCard>
            </div>
          </div>
          {/* End Transactions */}
        </div>
      </MainLayout>
    );
  }
}

export default PendingTransactionsPage;
