import React from "react";
import { PagePropsType } from "../types/page";
import PortfolioCreationOption from "../components/portfolioCreationOption";
import CenteredOnboardingLayout from "../layouts/centeredOnboardingLayout";
import InfoModal from "../components/modals/infoModal";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

export type PortfolioCreationPropsType = {
  shouldSetupTargetAllocation: boolean;
  isRoboAdvisorEnabled: boolean;
} & PagePropsType;

enum ViewModeEnum {
  PORTFOLIO_OPTIONS = "PORTFOLIO_OPTIONS",
  SETUP_PORTFOLIO_TARGET_ALLOCATION = "SETUP_PORTFOLIO_TARGET_ALLOCATION"
}

const PORTFOLIO_TARGET_SETUP_INFO =
  "<h5 class='mb-4 pt-4'>What is your target portfolio?</h5><p class='text-muted'>Your target portfolio is a customised mix of investments where you decide the mix of assets (like stocks and ETFs) and the percentage you want to allocate to each one.</p><p class='text-muted'>Your target portfolio is like a recipe for your investments, saying how much of each type you want.</p><p class='text-muted'>Your actual portfolio (holdings) is what you really have and you can see this in your Dashboard. Your actual holdings may match or differ from your target allocation.</p><br><h5 class='mb-4'>Why set up a target portfolio?</h5><p class='text-muted'>Creating a target portfolio with Wealthyhood gives you a structured and efficient way to manage your investments, helping you stay focused on your financial goals and saving you time and effort.</p><p class='text-muted'>Your actual portfolio (holdings) is what you really have and you can see this in your Dashboard. Your actual holdings may match or differ from your target allocation.</p><p class='text-muted'>Your target portfolio simplifies the process of buying, topping up, and rebalancing your investments.</p><p class='text-muted ms-3'>• You can invest an amount and it will be distributed according to your target allocation automatically.</p><p class='text-muted ms-3'>• You can also use regular rebalancing to ensure your portfolio maintains the desired allocation, even if market conditions change.</p><p class='text-muted ms-3'>• You can experiment with different allocations and check past performance, future expectations and useful metrics before finalising your target allocation and putting it into practice.</p><br><h5 class='mb-4'>Customising your portfolio</h5><p class='text-muted'>Changes to your target portfolio will not affect your actual holdings, unless you decide to Rebalance.</p><p class='text-muted'>In every portfolio investment modal, you have the option to invest in your target portfolio or not.</p><p class='text-muted'>When the ‘Buy target portfolio’ option is active, your investment will be made in your target portfolio allocation. This is the portfolio allocation (weighting) you have defined in this section.</p><p class='text-muted'>If the ‘Buy target portfolio’ option is off, your investment will be made in your current holdings. This is the weighting of your actual current holdings that you can also see in your Dashboard.</p><p class='text-muted'>You can also set automated rebalancing from the Autopilot section, or rebalance one-off from your Target Portfolio section.</p>";

type StateType = {
  showInfoModal: boolean;
  infoConfig?: {
    title: string;
    content: string;
  };
  viewMode: ViewModeEnum;
};

class PortfolioCreationPage extends React.Component<PortfolioCreationPropsType, StateType> {
  constructor(props: PortfolioCreationPropsType) {
    super(props);
    this.state = {
      showInfoModal: false,
      viewMode: props.shouldSetupTargetAllocation
        ? ViewModeEnum.SETUP_PORTFOLIO_TARGET_ALLOCATION
        : ViewModeEnum.PORTFOLIO_OPTIONS
    };
  }

  private _showPortfolioTargetSetupInfoModal() {
    this.setState({
      showInfoModal: true,
      infoConfig: { title: "Why create a target portfolio?", content: PORTFOLIO_TARGET_SETUP_INFO }
    });
  }

  private _disableInfoModal() {
    this.setState({ showInfoModal: false, infoConfig: undefined });
  }

  private _onPortfolioPersonalisationClick() {
    window.location.href = "/portfolios/creation-template-info";
  }

  private _onFromScratchClick() {
    window.location.href = "/portfolios/setup";
  }

  private _onRoboAdvisorClick() {
    window.location.href = "/portfolios/creation-robo-advisor";
  }

  private _setViewMode(viewMode: ViewModeEnum) {
    this.setState({ viewMode });
  }

  private async _onPortfolioSkip() {
    try {
      await axios.post("/investor/skip-portfolio-creation");
      window.location.href = "/";
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  }

  render(): JSX.Element {
    const { user, shouldSetupTargetAllocation, isRoboAdvisorEnabled } = this.props;
    const { viewMode, showInfoModal, infoConfig } = this.state;

    const isPortfolioSkipAvailable = !shouldSetupTargetAllocation;

    return (
      <CenteredOnboardingLayout user={user}>
        <div className="d-flex justify-content-center align-items-center mt-sm-3 mt-md-5">
          <div
            className="wh-card wh-card-body bg-white px-md-5 pb-md-5 pt-5 pb-4 px-4 mb-md-4"
            style={{
              maxWidth: "520px",
              minWidth: "300px"
            }}
          >
            {viewMode === ViewModeEnum.SETUP_PORTFOLIO_TARGET_ALLOCATION && (
              <>
                <div>
                  {/* Back Button */}
                  <div className="row p-0 m-0 mb-3">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                        onClick={() => (window.location.href = "/")}
                        style={{
                          fontSize: "24px",
                          color: "#536AE3"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                  {/* End Back Button*/}
                  <div className="py-2 text-center">
                    <div className="pb-5 d-flex flex-column gap-3">
                      <div className="col-4 text-start align-self-center text-center p-0 mb-4">
                        <img
                          alt="icon"
                          className="h-100 align-self-center"
                          src={"/images/icons/target-portfolio-pie-chart.png"}
                          style={{ width: "150px", height: "150px" }}
                        />
                      </div>
                      <h4 className="fw-bold text-center">Set up a target portfolio</h4>
                      <p className="text-muted text-center">
                        Time to set up your target allocation? Start from scratch or use our portfolio builder!
                      </p>
                    </div>

                    <div className="text-center">
                      <p
                        className="d-flex justify-content-center cursor-pointer align-items-center mb-4"
                        style={{
                          color: "#536AE3"
                        }}
                        onClick={() => this._showPortfolioTargetSetupInfoModal()}
                      >
                        <i
                          className="material-symbols-outlined align-self-center text-primary"
                          style={{ fontSize: "16px" }}
                        >
                          info
                        </i>
                        <span style={{ paddingLeft: "4px", fontWeight: "500" }}>
                          Why create a target portfolio?
                        </span>
                      </p>
                      <button
                        className="btn btn-primary mw-100 w-100"
                        onClick={() => this._setViewMode(ViewModeEnum.PORTFOLIO_OPTIONS)}
                      >
                        Let&apos;s go
                      </button>
                    </div>
                  </div>
                </div>
              </>
            )}

            {viewMode === ViewModeEnum.PORTFOLIO_OPTIONS && (
              <div>
                {/* Back Button */}
                {shouldSetupTargetAllocation && (
                  <div className="row p-0 m-0">
                    <div className="col p-0">
                      <span
                        className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                        onClick={() => this._setViewMode(ViewModeEnum.SETUP_PORTFOLIO_TARGET_ALLOCATION)}
                        style={{
                          fontSize: "24px",
                          color: "#536AE3"
                        }}
                      >
                        arrow_back
                      </span>
                    </div>
                  </div>
                )}
                {/* End Back Button*/}
                <div className="d-flex flex-column m-0 p-0 pt-3">
                  <h3 className="fw-bold mb-3">Create your portfolio</h3>
                  <p className="p-0 text-muted mb-5">
                    Start from scratch or use one of Wealthyhood&rsquo;s portfolio templates as a starting point.
                  </p>
                  <div className={"d-flex flex-column p-0 gap-4"}>
                    <PortfolioCreationOption
                      title="Start from scratch"
                      description="Select stocks & ETFs and set your allocation, piece by piece."
                      onSelectionCb={this._onFromScratchClick}
                      icon="/images/icons/pink-add.png"
                    />
                    <PortfolioCreationOption
                      title="Use our portfolio builder"
                      description="Set your preferences, get your template and customise it!"
                      onSelectionCb={this._onPortfolioPersonalisationClick}
                      icon="/images/icons/pie-chart-3.png"
                    />
                    {isRoboAdvisorEnabled && (
                      <PortfolioCreationOption
                        title="Ready-made portfolio"
                        description="Diversified portfolio based on your risk appetite, created and actively managed by Vanguard!"
                        onSelectionCb={this._onRoboAdvisorClick}
                        icon="/images/icons/robo-advisor.png"
                      />
                    )}
                  </div>
                  {isPortfolioSkipAvailable && (
                    <h6
                      className="text-primary mt-5 mb-0 text-center cursor-pointer"
                      onClick={this._onPortfolioSkip}
                    >
                      Maybe later
                    </h6>
                  )}
                </div>
              </div>
            )}

            <InfoModal
              handleClose={() => this._disableInfoModal()}
              title={infoConfig?.title}
              show={showInfoModal}
              dialogClassName={"max-w-600px"}
            >
              <div dangerouslySetInnerHTML={{ __html: infoConfig?.content }} />
            </InfoModal>
          </div>
        </div>
      </CenteredOnboardingLayout>
    );
  }
}

export default PortfolioCreationPage;
