import React from "react";
import { PagePropsType } from "../types/page";
import CenteredOnboardingLayout from "../layouts/centeredOnboardingLayout";
import InfoModal from "../components/modals/infoModal";
import { ROBO_ADVISOR_CONFIG, RoboAdvisorRiskLevelEnum } from "../configs/roboAdvisorConfig";
import ConfigUtil from "../../utils/configUtil";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { formatPercentage } from "../utils/formatterUtil";
import { GlobalContext } from "../contexts/globalContext";
import { getTooltipsConfig, InfoKeyType } from "../components/modals/assetSideModal/configuration";
import AssetSideModal from "../components/modals/assetSideModal/assetSideModal";
import { getAssetClassBreakdownName } from "../utils/universeUtil";
import axios from "axios";
import Cookies from "universal-cookie";
import { PortfolioDocument } from "../../models/Portfolio";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";

export type PortfolioCreationRoboAdvisorPropsType = {
  realPortfolio: PortfolioDocument;
} & PagePropsType;

type StateType = {
  showReadyMadeInformationModal: boolean;
  showETFFactsModal: boolean;
  selectedETFFactInfo: string;
  selectedRoboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum;
  showEtfFacts: boolean;
  displayAssetModal: boolean;
};

class PortfolioCreationRoboAdvisorPage extends React.Component<PortfolioCreationRoboAdvisorPropsType, StateType> {
  private ASSET_CONFIG;
  constructor(props: PortfolioCreationRoboAdvisorPropsType) {
    super(props);
    this.ASSET_CONFIG = ConfigUtil.getActiveOnlyInvestmentUniverseAssets(props.user.companyEntity);
    this.state = {
      showReadyMadeInformationModal: false,
      selectedRoboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum.DEFENSIVE,
      showEtfFacts: false,
      showETFFactsModal: false,
      selectedETFFactInfo: "",
      displayAssetModal: false
    };
  }

  private _setShowInfoModal(showInfoModal: boolean): void {
    this.setState({
      showReadyMadeInformationModal: showInfoModal
    });
  }

  private _setShowInfoDialog(showInfoDialog: boolean) {
    this.setState({ showETFFactsModal: showInfoDialog });
  }

  private _setSelectedInfo(infoKey: InfoKeyType) {
    const { user } = this.props;
    const { selectedRoboAdvisorRiskLevel } = this.state;
    const selectedRiskLevel = ROBO_ADVISOR_CONFIG[selectedRoboAdvisorRiskLevel];

    this.setState({
      selectedETFFactInfo: getTooltipsConfig(user.currency, this.ASSET_CONFIG[selectedRiskLevel.assetId].category)[
        infoKey
      ],
      showETFFactsModal: true
    });
  }

  private _setDisplayAssetModalState(displayAssetModal: boolean): void {
    this.setState({
      displayAssetModal: displayAssetModal
    });
  }

  private _getAssetBreakdown() {
    const { selectedRoboAdvisorRiskLevel } = this.state;
    const selectedRiskLevel = ROBO_ADVISOR_CONFIG[selectedRoboAdvisorRiskLevel];
    const selectedAsset = this.ASSET_CONFIG[
      selectedRiskLevel.assetId
    ] as investmentUniverseConfig.ETFAssetConfigType;
    const { assetClassBreakdown } = selectedAsset;

    let breakdown = "";
    Object.keys(assetClassBreakdown).forEach((assetClassKey) => {
      breakdown += `${formatPercentage(
        assetClassBreakdown[assetClassKey as investmentUniverseConfig.AssetClassType],
        this.context.locale
      )} ${getAssetClassBreakdownName(assetClassKey as investmentUniverseConfig.AssetClassType)} - `;
    });

    breakdown = breakdown.slice(0, -3);

    return breakdown;
  }

  private async _onRoboAdvisorRiskLevelSelectClick() {
    const { selectedRoboAdvisorRiskLevel } = this.state;
    const { realPortfolio } = this.props;

    try {
      await axios({
        method: "post",
        url: `/portfolios/${this.props.realPortfolio.id}/personalisation-preferences`,
        data: {
          personalisationPreferences: {
            assetClasses: ["equities"],
            geography: "global",
            risk: 0.5,
            sectors: ["utilities"]
          }
        }
      });

      const response = await axios.post(`/portfolios/${realPortfolio.id}/allocation`, {
        allocation: { [ROBO_ADVISOR_CONFIG[selectedRoboAdvisorRiskLevel].assetId]: 100 },
        flow: "robo_advisor"
      });

      if (response.status === 200) {
        const cookies = new Cookies();
        const portfolioCreationSuccess =
          cookies.get("overridePortfolioCreationSuccess") ?? "/portfolios/creation-success";
        cookies.remove("overridePortfolioCreationSuccess");
        window.location.href = portfolioCreationSuccess;
      }
    } catch (err) {
      emitToast({
        content: "We couldn't update your portfolio.",
        toastType: ToastTypeEnum.error
      });
    }
  }

  private _setSelectedRoboAdvisorRiskLevel(selectedRoboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum) {
    this.setState({ selectedRoboAdvisorRiskLevel });
  }

  render(): JSX.Element {
    const { user } = this.props;
    const {
      showReadyMadeInformationModal,
      selectedRoboAdvisorRiskLevel,
      showEtfFacts,
      showETFFactsModal,
      selectedETFFactInfo,
      displayAssetModal
    } = this.state;

    const selectedRiskLevel = ROBO_ADVISOR_CONFIG[selectedRoboAdvisorRiskLevel];
    const selectedAsset = this.ASSET_CONFIG[
      selectedRiskLevel.assetId
    ] as investmentUniverseConfig.ETFAssetConfigType;

    return (
      <CenteredOnboardingLayout user={user}>
        <div className="d-flex justify-content-center align-items-center mt-sm-3 mt-md-5">
          <div
            className="wh-card wh-card-body bg-white px-md-5 pb-md-5 pt-5 pb-4 px-4 mb-md-4"
            style={{
              maxWidth: "520px",
              minWidth: "300px"
            }}
          >
            <div>
              {/* Back Button */}
              <div className="row p-0 m-0 mb-3">
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => window.history.back()}
                    style={{
                      fontSize: "24px",
                      color: "#536AE3"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}
              <div className="py-2 text-center">
                <div className="pb-5 d-flex flex-column gap-3">
                  <h4 className="fw-bold">Ready-made portfolios</h4>
                  <p className="text-muted">
                    Pick a diversified investment portfolio based on your risk appetite! Ready-made and actively
                    managed by Vanguard.
                  </p>
                  <div className="d-flex justify-content-center">
                    <img alt="icon" src={selectedRiskLevel.icon} width="132" height="132" />
                  </div>
                  <div className={"d-flex flex-row px-3 justify-content-center"}>
                    {Object.keys(ROBO_ADVISOR_CONFIG).map((riskLevel) => (
                      <div key={`robo-advisor-risk-level-${riskLevel}`} className="d-flex align-self-center p-2">
                        <div
                          className={`btn align-self-center ${
                            selectedRoboAdvisorRiskLevel === riskLevel
                              ? "btn-plan-recurrence-selected"
                              : "btn-plan-recurrence"
                          }`}
                          onClick={() =>
                            this._setSelectedRoboAdvisorRiskLevel(riskLevel as RoboAdvisorRiskLevelEnum)
                          }
                        >
                          {ROBO_ADVISOR_CONFIG[riskLevel as RoboAdvisorRiskLevelEnum].displayName}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="d-flex flex-row p-4 justify-content-centercard card-body wh-simple-card border-0">
                    <div className="w-100 p-0 justify-content-start text-start align-self-center">
                      <div className="d-flex justify-content-between align-items-center mb-4">
                        <h5 className="fw-bold mb-0">{selectedRiskLevel.title}</h5>
                        <button
                          className="btn p-0 border-0"
                          onClick={() => this.setState({ showEtfFacts: !showEtfFacts })}
                        >
                          <img src="/images/icons/arrow-down-button.png" alt="arrow down" width="28" height="28" />
                        </button>
                      </div>
                      <p className="text-secondary m-0">{selectedRiskLevel.description}</p>
                      <div
                        className="robo-advisor-asset-info mt-3 pt-4"
                        style={{ borderTop: "1px solid #DCE2FD" }}
                        hidden={!showEtfFacts}
                      >
                        {[
                          { label: "ETF", value: selectedAsset.simpleName },
                          { label: "ISIN", value: selectedAsset.isin },
                          {
                            label: "Ticker",
                            value: selectedAsset.formalTicker
                          },
                          { label: "Allocation", value: this._getAssetBreakdown() },
                          { label: "Income", value: selectedAsset.income, labelKey: "income" },
                          { label: "Base Currency", value: selectedAsset.tradedCurrency, labelKey: "baseCurrency" }
                        ].map((keyFact) => (
                          <div key={keyFact.label} className={`p-0 mb-3`}>
                            <div className="d-flex justify-content-between align-items-center">
                              <div className="d-flex flex-row">
                                <p className="text-muted m-0">{keyFact.label}</p>
                                {keyFact?.labelKey && (
                                  <span
                                    className="material-symbols-outlined cursor-pointer align-self-center ms-1"
                                    style={{
                                      fontSize: "16px"
                                    }}
                                    onClick={() => this._setSelectedInfo(keyFact.labelKey as InfoKeyType)}
                                  >
                                    info
                                  </span>
                                )}
                              </div>
                              <h6 className="fw-bolder">{keyFact.value}</h6>
                            </div>
                          </div>
                        ))}
                        <div className="d-flex justify-content-center">
                          <button
                            className="btn text-primary-blue border-0"
                            onClick={() => this._setDisplayAssetModalState(true)}
                          >
                            Read more
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <p
                    className="d-flex justify-content-center cursor-pointer align-items-center mb-4"
                    style={{
                      color: "#536AE3"
                    }}
                    onClick={() => this._setShowInfoModal(true)}
                  >
                    <i
                      className="material-symbols-outlined align-self-center text-primary"
                      style={{ fontSize: "16px" }}
                    >
                      info
                    </i>
                    <span style={{ paddingLeft: "4px", fontWeight: "500" }}>About ready-made portfolios</span>
                  </p>
                  <button
                    className="btn btn-primary mw-100 w-100"
                    onClick={() => this._onRoboAdvisorRiskLevelSelectClick()}
                  >
                    Select {ROBO_ADVISOR_CONFIG[selectedRoboAdvisorRiskLevel].displayName} portfolio
                  </button>
                </div>
              </div>
            </div>

            <InfoModal
              handleClose={() => this._setShowInfoModal(false)}
              title={null}
              show={showReadyMadeInformationModal}
              dialogClassName={"max-w-600px"}
            >
              <h4 className="pb-3">About ready-made portfolios</h4>
              <h5 className="py-3">What are ready-made portfolios?</h5>
              <p className="text-muted pb-3">
                Ready-made portfolios are professionally designed investment mixes that do the heavy lifting for
                you. Instead of picking individual stocks or funds, you get a complete, balanced portfolio in one
                simple investment.
              </p>
              <p className="text-muted pb-3">
                They are actively managed, allocating specific % to global equities and bonds, depending on their
                risk profile.
              </p>
              <h5 className="pb-3">Created by Vanguard</h5>
              <p className="text-muted pb-3">
                These portfolios are built and managed by Vanguard, one of the world's largest and most trusted
                asset managers with over $8 trillion in global assets. They've been helping investors build wealth
                for nearly 50 years.
              </p>
              <h5 className="pb-3">How they work</h5>
              <p className="text-muted pb-3">
                Each portfolio maintains a target mix of stocks and bonds that matches your risk level:
              </p>
              <p className="pb-3">
                <strong>Defensive (20% stocks, 80% bonds)</strong>
                <span className="text-muted"> - Lower risk, steadier returns</span>
              </p>
              <p className="pb-3">
                <strong>Cautious (40% stocks, 60% bonds)</strong>
                <span className="text-muted"> - Conservative growth with stability</span>
              </p>{" "}
              <p className="pb-3">
                <strong>Confident (60% stocks, 40% bonds)</strong>
                <span className="text-muted"> - Balanced growth potential</span>
              </p>{" "}
              <p className="pb-3">
                <strong>Growth (80% stocks, 20% bonds)</strong>
                <span className="text-muted"> - Higher growth potential, more volatility</span>
              </p>
              <h5 className="pb-3">Professional management</h5>
              <p className="text-muted pb-3">
                While these portfolios maintain their target allocation, they're actively managed by Vanguard's
                expert team. This means they automatically rebalance and make adjustments to keep your investment
                on track without you having to do anything.
              </p>
              <h5 className="pb-3">Built-in diversification</h5>
              <p className="text-muted pb-3">
                Each portfolio gives you instant diversification across thousands of companies worldwide. You'll
                own pieces of both developed and emerging markets, helping spread your risk across different
                countries and economies.
              </p>
              <h5 className="pb-3">Simple and cost-effective</h5>
              <p className="text-muted pb-3">
                One investment gets you a complete portfolio that would otherwise require buying multiple funds
                yourself. Vanguard's low-cost approach means more of your money stays invested and working for you.
              </p>
            </InfoModal>
            <InfoModal title={null} show={showETFFactsModal} handleClose={() => this._setShowInfoDialog(false)}>
              <div dangerouslySetInnerHTML={{ __html: selectedETFFactInfo }} />
            </InfoModal>
          </div>
        </div>
        {displayAssetModal && (
          <AssetSideModal
            userCurrency={user.currency}
            assetCommonId={selectedRiskLevel.assetId}
            handleClose={(): void => this._setDisplayAssetModalState(false)}
            includeSellButton={false}
            includeInvestmentDetails={false}
            includeRecentActivity={false}
          />
        )}
      </CenteredOnboardingLayout>
    );
  }
}

PortfolioCreationRoboAdvisorPage.contextType = GlobalContext;

export default PortfolioCreationRoboAdvisorPage;
