import React from "react";
import { PagePropsType } from "../types/page";
import SuccessLayout from "../layouts/successLayout";
import { isVerified } from "../utils/userUtil";
import { eventEmitter, EVENTS } from "../utils/eventService";
import { rewardsConfig } from "@wealthyhood/shared-configs";
import { GiftDocument } from "../../models/Gift";
import { formatCurrency } from "../utils/currencyUtil";
import ConfigUtil from "../../utils/configUtil";
import Decimal from "decimal.js";
import Cookies from "universal-cookie";

export type PortfolioCreationSuccessPagePropsType = {
  isGifted: boolean;
  gift?: GiftDocument;
} & PagePropsType;

type ViewModeType = "verified" | "unverified" | "gift" | "referred";

class PortfolioCreationSuccessPage extends React.Component<PortfolioCreationSuccessPagePropsType> {
  private _cookiesManager: Cookies;

  constructor(props: PortfolioCreationSuccessPagePropsType) {
    super(props);
    this._cookiesManager = new Cookies();
  }

  private _getViewMode(): ViewModeType {
    const { isGifted, user } = this.props;

    if (!isVerified(user)) {
      return "unverified";
    } else if (isGifted) {
      return "gift";
    } else if (user?.canUnlockFreeShare) {
      return "referred";
    } else {
      return "verified";
    }
  }

  private _getContentForReferredView(): JSX.Element {
    const { user } = this.props;

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">You’re all set!</h3>
        <p className="text-center text-muted">
          Invest at least{" "}
          <span className={"text-primary fw-bold"}>
            {formatCurrency(
              rewardsConfig.MIN_INVESTMENT_AMOUNT_FOR_REWARD_ELIGIBILITY,
              user.currency,
              locale,
              0,
              0
            )}
          </span>{" "}
          in your portfolio within 7 days to unlock a free share between{" "}
          {formatCurrency(rewardsConfig.MIN_REWARD_AMOUNT_COPY, user.currency, locale, 0, 0)} and{" "}
          {formatCurrency(rewardsConfig.MAX_REWARD_AMOUNT_COPY, user.currency, locale, 0, 0)}!
        </p>
      </>
    );
  }

  private _getContentForUnVerifiedView(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">You’re all set!</h3>
        <p className="p-0 text-muted text-center mb-4">
          Once your account has been verified, you’ll be ready to make your first investment and start building
          your wealth!
        </p>
      </>
    );
  }

  private _getContentForGiftView(): JSX.Element {
    const { gift, user } = this.props;

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">You’re all set!</h3>
        <p className="text-justify text-center text-muted">
          You have unlocked your{" "}
          <span className={"text-primary fw-bold"}>
            {formatCurrency(
              Decimal.div(gift.consideration.amount, 100).toNumber(),
              gift.consideration.currency,
              locale,
              0,
              0
            )}{" "}
            gift
          </span>
          , and you’re now ready to make your first investment!
        </p>
        <div
          className={"d-flex flex-column align-items-center justify-content-center pt-2 pt-md-3 pb-1 pb-md-2"}
          style={{ gap: "2rem" }}
        >
          <i className="fas fa-arrow-down fa-lg" style={{ color: "#536AE3" }} />
          <img style={{ width: "104px", height: "104px" }} alt="Gift!" src={"/images/icons/gift.png"} />
        </div>
      </>
    );
  }

  private _getContentForVerifiedView(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">You’re all set!</h3>
        <p className="p-0 text-muted text-center mb-4">You’re now ready to invest in your target portfolio!</p>
      </>
    );
  }

  private _getContent(viewMode: ViewModeType): JSX.Element {
    if (viewMode === "verified") {
      return this._getContentForVerifiedView();
    } else if (viewMode === "gift") {
      return this._getContentForGiftView();
    } else if (viewMode === "referred") {
      return this._getContentForReferredView();
    } else if (viewMode === "unverified") {
      return this._getContentForUnVerifiedView();
    }
  }

  private _getIcon(viewMode: ViewModeType): string {
    if (viewMode !== "gift") {
      return "/images/icons/etf.png";
    } else {
      return "";
    }
  }

  private _getActionElement(viewMode: ViewModeType): JSX.Element {
    const { isGifted, gift, user } = this.props;

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    if (viewMode === "unverified") {
      return (
        <a href="/" className="btn btn-primary w-100" style={{ maxWidth: "100% !important" }}>
          Let’s go!
        </a>
      );
    }

    const returnTo = this._cookiesManager.get("portfolioCreationSuccessReturnTo") ?? "/";
    return (
      <>
        <div className="row m-0 mb-4 w-100 text-center">
          <button
            style={{ maxWidth: "100% !important" }}
            type="button"
            className="btn btn-primary fw-100"
            onClick={() => eventEmitter.emit(EVENTS.portfolioBuyModal)}
          >
            {isGifted
              ? `Invest my ${formatCurrency(
                  Decimal.div(gift.consideration.amount, 100).toNumber(),
                  gift.consideration.currency,
                  locale,
                  0,
                  0
                )} gift`
              : "Invest in my target portfolio"}
          </button>
        </div>
        <div className="row m-0 w-100 text-center">
          <a
            onClick={() => (window.location.href = returnTo)}
            className="text-primary text-decoration-none cursor-pointer"
            style={{ maxWidth: "100% !important" }}
          >
            Maybe Later
          </a>
        </div>
      </>
    );
  }

  render(): JSX.Element {
    const viewMode = this._getViewMode();

    return (
      <SuccessLayout
        user={this.props.user}
        activePage={this.props.activePage}
        imageUrl={this._getIcon(viewMode)}
        actionElement={this._getActionElement(viewMode)}
        enableModals={true}
      >
        {this._getContent(viewMode)}
      </SuccessLayout>
    );
  }
}

export default PortfolioCreationSuccessPage;
