import React from "react";
import { PagePropsType } from "../types/page";
import CenteredOnboardingLayout from "../layouts/centeredOnboardingLayout";
import InfoModal from "../components/modals/infoModal";

export type PoPortfolioCreationTemplateInfoPropsType = {
  useSavedPersonalisationPreferences?: string;
} & PagePropsType;

const ROBO_ADVISOR_INFO = {
  title: "About our portfolio templates",
  content:
    "<h5 class='mb-4 pt-4'>Before you get started</h5><p class='text-muted'>Before diving in with your portfolio, please make sure you understand the main concepts of our portfolio templates.</p><p class='text-muted pb-4'>Our templates are just a starting point, however, It’s up to you to customise your template as you want until you’re happy to proceed with your selection.</p><h5 class='mb-4'>Filtering out your template</h5><p class='text-muted'>You can filter out your portfolio template in 4 simple steps.</p><p class='text-muted'><span class='fw-bolder-black'>Step 1:</span> Pick your asset classes (Stocks, Bonds, Commodities, Real Estate). You can use the info buttons to read more info about each asset class.</p><p class='text-muted'><span class='fw-bolder-black'>Step 2:</span> Select your geography (Global, US).</p><p class='text-muted'><span class='fw-bolder-black'>Step 3:</span> Include specific themes and sectors.</p><p class='text-muted'><span class='fw-bolder-black'>Step 4:</span> Define how your portfolio template is weighted. You can use custom weighting for your template or equal weights across assets.</p><p class='text-muted'>After you complete these steps, we fetch the portfolio template based on your filtering. This will include ETFs from our investment universe that match your selection with the corresponding weighting.</p><p class='text-muted pb-4'>At this point, your portfolio template is ready and you can use it as a starting point.</p><h5 class='mb-4'>Customising your portfolio</h5><p class='text-muted'>After you get your portfolio template, you can add or remove stocks and ETFs or customise the weights of the assets in your portfolio as you want.</p><p class='text-muted pb-4'>You can keep track of your portfolio insights with every change you make.</p><h5 class='mb-4'>Using ‘Custom’ weighting for your template</h5><p class='text-muted'>If you select <span class='fw-bolder-black'>‘Custom’ weighting</span>, we’ll take into account the assets that match your selection in the previous steps and use the combination that produces the maximum (expected) return with the minimum risk.</p><p class='text-muted'>We’ll then fetch the portfolio template that corresponds to the investment style you defined.</p><p class='text-muted pb-4'>However, keep in mind that during the process, we use data from the past 10 years. There is no guarantee that what happened in the past will also happen in the future.</p><h5 class='mb-4'>Things to keep in mind before you go</h5><p class='text-muted'>Please make sure you understand the process before proceeding. Always consider evaluating your portfolio allocation if your individual circumstances change.</p>"
};

type StateType = {
  showInfoModal: boolean;
};

export default class PortfolioCreationRoboAdvisorPage extends React.Component<
  PoPortfolioCreationTemplateInfoPropsType,
  StateType
> {
  constructor(props: PoPortfolioCreationTemplateInfoPropsType) {
    super(props);
    this.state = {
      showInfoModal: false
    };
  }

  private _setShowInfoModal(showInfoModal: boolean): void {
    this.setState({
      showInfoModal
    });
  }

  private _onPortfolioPersonalisationClick() {
    const { useSavedPersonalisationPreferences } = this.props;

    const queryParams = new URLSearchParams({ useSavedPersonalisationPreferences }).toString();

    window.location.href = `/portfolios/personalisation?${queryParams}`;
  }

  render(): JSX.Element {
    const { user } = this.props;
    const { showInfoModal } = this.state;

    return (
      <CenteredOnboardingLayout user={user}>
        <div className="d-flex justify-content-center align-items-center mt-sm-3 mt-md-5">
          <div
            className="wh-card wh-card-body bg-white px-md-5 pb-md-5 pt-5 pb-4 px-4 mb-md-4"
            style={{
              maxWidth: "520px",
              minWidth: "300px"
            }}
          >
            <div>
              {/* Back Button */}
              <div className="row p-0 m-0">
                <div className="col p-0">
                  <span
                    className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                    onClick={() => window.history.back()}
                    style={{
                      fontSize: "24px",
                      color: "#536AE3"
                    }}
                  >
                    arrow_back
                  </span>
                </div>
              </div>
              {/* End Back Button*/}
              <div className="py-2 text-center">
                <div className="pb-5 d-flex flex-column gap-3">
                  <div className="col-4 text-start align-self-center text-center p-0 mb-4">
                    <img
                      alt="icon"
                      className="h-100 align-self-center"
                      src={"/images/icons/pie-chart-3.png"}
                      style={{ width: "150px", height: "150px" }}
                    />
                  </div>
                  <h4 className="fw-bold text-center">Pick your portfolio template</h4>
                  <p className="text-muted text-center">
                    Select the asset classes, geography, sectors and investment style to get your portfolio
                    template. You’ll then be able to customise it!
                  </p>
                </div>

                <div className="text-center">
                  <p
                    className="d-flex justify-content-center cursor-pointer align-items-center mb-4"
                    style={{
                      color: "#536AE3"
                    }}
                    onClick={() => this._setShowInfoModal(true)}
                  >
                    <i
                      className="material-symbols-outlined align-self-center text-primary"
                      style={{ fontSize: "16px" }}
                    >
                      info
                    </i>
                    <span style={{ paddingLeft: "4px", fontWeight: "500" }}>About our portfolio templates</span>
                  </p>
                  <button
                    className="btn btn-primary mw-100 w-100"
                    onClick={() => this._onPortfolioPersonalisationClick()}
                  >
                    Let&apos;s go
                  </button>
                </div>
              </div>
            </div>

            <InfoModal
              handleClose={() => this._setShowInfoModal(false)}
              title={ROBO_ADVISOR_INFO.title}
              show={showInfoModal}
              dialogClassName={"max-w-600px"}
            >
              <div dangerouslySetInnerHTML={{ __html: ROBO_ADVISOR_INFO.content }} />
            </InfoModal>
          </div>
        </div>
      </CenteredOnboardingLayout>
    );
  }
}
