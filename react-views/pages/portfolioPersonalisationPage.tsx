import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PagePropsType } from "../types/page";
import PersonalisationWizard from "../components/personalisationWizard";
import OnboardingLayoutNew from "../layouts/onboardingLayoutNew";
import { stepsConfig } from "../configs/portfolioPersonalisationWizardConfig";
import WizardStepper from "../components/wizardStepper";

export type PortfolioPersonalisationPropsType = {
  portfolioId: string;
  savedPersonalisationPreferences: {
    assetClasses: investmentUniverseConfig.AssetClassType[];
    geography: investmentUniverseConfig.InvestmentGeographyType;
    risk: number;
    sectors: investmentUniverseConfig.InvestmentSectorType[];
  };
} & PagePropsType;

type StateType = {
  currentStep: number;
};

class PortfolioPersonalisationPage extends React.Component<PortfolioPersonalisationPropsType, StateType> {
  constructor(props: PortfolioPersonalisationPropsType) {
    super(props);
    this.state = {
      currentStep: 1
    };
  }

  private _setCurrentStep = (currentStep: number): void => {
    this.setState({ currentStep });
  };

  render(): JSX.Element {
    const { portfolioId, savedPersonalisationPreferences } = this.props;
    const { currentStep } = this.state;

    return (
      <OnboardingLayoutNew
        activePage={this.props.activePage}
        user={this.props.user}
        alignment={"center"}
        leftSideChild={<WizardStepper currentStep={currentStep} maxStepsDisplayed={5} stepsConfig={stepsConfig} />}
      >
        <div className="row justify-content-center">
          <PersonalisationWizard
            savedPersonalisationPreferences={savedPersonalisationPreferences}
            portfolioId={portfolioId}
            onStepChange={(step) => this._setCurrentStep(step)}
          />
        </div>
      </OnboardingLayoutNew>
    );
  }
}

export default PortfolioPersonalisationPage;
