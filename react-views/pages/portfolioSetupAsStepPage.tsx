import React from "react";
import axios from "axios";
import { addBreadcrumb, captureException } from "@sentry/react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import AllocationMethodSelection, { AllocationSelectionKeyType } from "../components/allocationMethodSelection";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import { PersonalisationType, PortfolioDocument } from "../../models/Portfolio";
import { PagePropsType } from "../types/page";
import { ToastTypeEnum } from "../configs/toastConfig";
import { emitToast } from "../utils/eventService";
import OnboardingLayoutNew from "../layouts/onboardingLayoutNew";
import DiscardChangesModal from "../components/modals/discardChangesModal";
import InvalidAllocationModal from "../components/modals/invalidAllocationModal";
import { stepsConfig } from "../configs/portfolioPersonalisationWizardConfig";
import AssetSideModal from "../components/modals/assetSideModal/assetSideModal";
import WizardStepper from "../components/wizardStepper";
import PortfolioAllocation from "../components/portfolioAllocation";
import DiscoverAssetsModal from "../components/modals/discoverAssetsModal";
import qs from "qs";
import { equallyWeightElements } from "../utils/portfolioUtil";
import { AllocationType } from "../types/allocation";
import Cookies from "universal-cookie";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContextType } from "../contexts/globalContext";
import { GlobalContext } from "../contexts/globalContext";

export type PortfolioSetupAsStepPagePropsType = {
  realPortfolio: PortfolioDocument;
} & PagePropsType;

type StateType = {
  activeAllocationSelectionKey: AllocationSelectionKeyType;
  currentAllocation: AllocationType;
  currentStep: number;
  hasSeenOptimalAllocation: boolean;
  displayModal: {
    discoverAssets: boolean;
    invalidAllocation: boolean;
    displayAssetModal: boolean;
    discardChangesModal: boolean;
  };
  displayedAsset?: investmentUniverseConfig.AssetType;
  selectedRisk: number;
  alignment: "center" | "start";
  fetchingOptimalAllocation: boolean;
  beforeEditAssetAllocation: AllocationType;
};

class PortfolioSetupAsStepPage extends React.Component<PortfolioSetupAsStepPagePropsType, StateType> {
  constructor(props: PortfolioSetupAsStepPagePropsType) {
    super(props);
    this.state = {
      activeAllocationSelectionKey: "custom",
      currentAllocation: {},
      hasSeenOptimalAllocation: false,
      currentStep: 1,
      // displayedAsset: Array.from(selectedUniverse)[0],
      displayModal: {
        discoverAssets: false,
        invalidAllocation: false,
        displayAssetModal: false,
        discardChangesModal: false
      },
      selectedRisk: 10,
      alignment: "center",
      fetchingOptimalAllocation: false,
      beforeEditAssetAllocation: {}
    };
    this._setAssetAllocation = this._setAssetAllocation.bind(this);
  }

  private _getInitiallySelectedUniverse = (): Set<investmentUniverseConfig.AssetType> => {
    const { realPortfolio, user } = this.props;
    const ASSET_CLASS_CONFIG = ConfigUtil.getAssetClasses(user.companyEntity);
    const SECTOR_CONFIG = ConfigUtil.getSectors(user.companyEntity);
    const { assetClasses, geography, sectors } = this._getBackwardsCompatiblePreferences(
      realPortfolio.personalisationPreferences
    );

    // Asset class corresponding investment universe
    const coreAssets: investmentUniverseConfig.AssetType[] = [];
    assetClasses.forEach((assetClass) => {
      coreAssets.push(...ASSET_CLASS_CONFIG[assetClass].coreAssets[geography]);
    });

    // Investment sectors corresponding investment universe
    const sectorAssets: investmentUniverseConfig.AssetType[] = [];
    sectors.forEach((sector) => {
      sectorAssets.push(...SECTOR_CONFIG[sector].coreAssets[geography]);
    });

    return new Set([...coreAssets, ...sectorAssets]);
  };

  private _fetchOptimalAllocation = async (universe: Set<investmentUniverseConfig.AssetType>) => {
    setTimeout(async () => {
      try {
        const { selectedRisk } = this.state;

        const response = await axios({
          method: "GET",
          url: "/portfolios/optimal-allocation",
          headers: { "Content-Type": "application/json" },
          params: {
            risk: parseFloat((selectedRisk / 20).toFixed(2)),
            asset: Array.from(universe).map((assetKey) => assetKey)
          },
          paramsSerializer: (params) => {
            return qs.stringify(params, { arrayFormat: "repeat" });
          }
        });

        const { allocation } = response.data;
        this.setState({
          currentAllocation: allocation.assets,
          fetchingOptimalAllocation: false,
          beforeEditAssetAllocation: allocation.assets
        });
      } catch (err) {
        captureException(err);
        // Fallback
        this.setState({ fetchingOptimalAllocation: false });
        this._equilizeCurrentAllocationFromUniverse(universe);
      }
    }, 2000);
  };

  private _equalizeAllocation = (allocation: AllocationType): AllocationType => {
    const newAllocation: investmentUniverseConfig.AllocationType = {
      assetClasses: {},
      assets: {}
    };

    // Group assets of universe in asset classes
    const assetGroupingDict: {
      [key in investmentUniverseConfig.AssetClassType]?: investmentUniverseConfig.AssetType[];
    } = {};
    Object.keys(allocation).forEach((assetKey: investmentUniverseConfig.AssetType) => {
      const assetClass = investmentUniverseConfig.ASSET_CONFIG[assetKey].assetClass;
      if (!assetGroupingDict[assetClass]) {
        assetGroupingDict[assetClass] = [];
      }
      assetGroupingDict[assetClass].push(assetKey);
    });

    // Equally weight asset classes
    const totalAssetClassSum = 100;
    const assetClasses = Object.keys(assetGroupingDict) as investmentUniverseConfig.AssetClassType[];
    newAllocation.assetClasses = equallyWeightElements(totalAssetClassSum, assetClasses);

    // Equally weight each asset class breakdown
    assetClasses.forEach((assetClassKey) => {
      const assets = assetGroupingDict[assetClassKey];
      const totalAssetSum = newAllocation.assetClasses[assetClassKey];
      const assetAllocation = equallyWeightElements(totalAssetSum, assets);
      newAllocation.assets = { ...newAllocation.assets, ...assetAllocation };
    });

    return newAllocation.assets;
  };

  private _equilizeCurrentAllocationFromUniverse(universe: Set<investmentUniverseConfig.AssetType>): void {
    const allocationFromUniverse: AllocationType = {};
    universe.forEach((asset: investmentUniverseConfig.AssetType) => {
      allocationFromUniverse[asset] = 1;
    });

    const currentAllocation = this._equalizeAllocation(allocationFromUniverse);
    this.setState({ currentAllocation });
  }

  private _equalizeCurrentAllocation() {
    const newEquilizedAllocation = this._equalizeAllocation(this.state.currentAllocation);
    this.setState({ currentAllocation: newEquilizedAllocation });
  }

  private _setActiveAllocationSelectionKey = (activeAllocationSelectionKey: AllocationSelectionKeyType): void => {
    this.setState({ activeAllocationSelectionKey });
  };

  private _setSelectedRisk = (selectedRisk: number): void => {
    this.setState({ selectedRisk });
  };

  private _submitTargetAllocation = async (): Promise<void> => {
    const { realPortfolio } = this.props;
    const { currentAllocation } = this.state;

    const url = `/portfolios/${realPortfolio.id}/allocation`;

    try {
      const response = await axios({
        method: "post",
        url,
        data: { allocation: currentAllocation, flow: "builder" }
      });
      if (response.status === 200) {
        const cookies = new Cookies();
        const portfolioCreationSuccess =
          cookies.get("overridePortfolioCreationSuccess") ?? "/portfolios/creation-success";
        cookies.remove("overridePortfolioCreationSuccess");
        window.location.href = portfolioCreationSuccess;
      }
    } catch (err) {
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: "POST",
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4),
          allocation: currentAllocation
        }
      });
      captureException(err);
      emitToast({
        content: "We couldn't create your portfolio. Please try again.",
        toastType: ToastTypeEnum.error
      });
      // Need to throw error again to stop spinner animation in submit button
      throw new Error();
    }
  };

  /**
   * @description Workaround for the users that stored their preferences with the
   * deprecated government/corporate bonds which have now been merged into bonds.
   */
  private _getBackwardsCompatiblePreferences = (
    personalisationPreferences: PersonalisationType
  ): PersonalisationType => {
    const { assetClasses } = personalisationPreferences;

    const newAssetClasses = [
      ...new Set(
        assetClasses.map((assetClass) => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          if (assetClass === "corporateBonds" || assetClass === "governmentBonds") {
            return "bonds";
          } else return assetClass;
        })
      )
    ] as investmentUniverseConfig.AssetClassType[];

    return {
      ...personalisationPreferences,
      assetClasses: newAssetClasses
    };
  };

  private _getCurrentTotalAllocation(): number {
    const { currentAllocation } = this.state;
    return Object.values(currentAllocation)
      .map((percentage) => percentage)
      .reduce((sum, pecentage) => sum + pecentage, 0);
  }

  private _handlePortfolioWeightingNextButton(): void {
    const { activeAllocationSelectionKey } = this.state;

    this.setState(
      {
        currentStep: 2,
        fetchingOptimalAllocation: activeAllocationSelectionKey === "custom"
      },
      () => {
        const universe = this._getInitiallySelectedUniverse();

        if (activeAllocationSelectionKey === "custom") {
          this._fetchOptimalAllocation(universe);
        } else {
          this._equilizeCurrentAllocationFromUniverse(universe);
        }
      }
    );
  }

  private _setDisplayModalState = (
    modalType: "discoverAssets" | "invalidAllocation" | "displayAssetModal" | "discardChangesModal",
    display: boolean
  ): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      newState.displayModal[modalType] = display;
      return newState;
    });
  };

  private _setDisplayAssetModalState = (
    displayAssetModal: boolean,
    assetCommonId?: investmentUniverseConfig.AssetType
  ): void => {
    this.setState((prevState) => {
      const { ...newState } = prevState;
      newState.displayModal.displayAssetModal = displayAssetModal;
      newState.displayedAsset = assetCommonId || prevState.displayedAsset;
      return newState;
    });
  };

  private _setAssetAllocation =
    (assetKey: investmentUniverseConfig.AssetType) =>
    (weight: number): void => {
      this.setState((prevState) => ({
        ...prevState,
        currentAllocation: {
          ...prevState.currentAllocation,
          [assetKey]: weight
        }
      }));
    };

  private _getPendingUniverse = (): Set<investmentUniverseConfig.AssetType> => {
    const { currentAllocation } = this.state;

    return new Set([...(Object.keys(currentAllocation) as investmentUniverseConfig.AssetType[])]);
  };

  private _toggleAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      if (prevState.currentAllocation[assetCommonId] === undefined) {
        newState.currentAllocation[assetCommonId] = 1;
      } else {
        delete newState.currentAllocation[assetCommonId];
      }
      return newState;
    });
  };

  private _deleteAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      delete newState.currentAllocation[assetCommonId];
      return newState;
    });
  };

  render(): JSX.Element {
    const { user, realPortfolio } = this.props;
    const {
      activeAllocationSelectionKey,
      currentStep,
      displayedAsset,
      displayModal,
      selectedRisk,
      currentAllocation,
      alignment,
      fetchingOptimalAllocation,
      hasSeenOptimalAllocation,
      beforeEditAssetAllocation
    } = this.state;

    const currentTotalAllocation = this._getCurrentTotalAllocation();

    return (
      <OnboardingLayoutNew
        activePage={this.props.activePage}
        user={this.props.user}
        alignment={alignment}
        leftSideChild={
          <WizardStepper currentStep={currentStep + 3} maxStepsDisplayed={5} stepsConfig={stepsConfig} />
        }
      >
        <div className="w-100">
          {/* Allocation Selection Method */}
          {currentStep == 1 && (
            <>
              {/* This style is needed to not realign to center after methods switch */}
              <div className="fade-in" style={{ height: "395px" }}>
                <AllocationMethodSelection
                  activeAllocationSelectionKey={activeAllocationSelectionKey}
                  selectedRisk={selectedRisk}
                  onRiskChangeCb={this._setSelectedRisk}
                  onSelectionCb={this._setActiveAllocationSelectionKey}
                />
              </div>
              {/* <!-- Nav buttons --> */}
              <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
                {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
                <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
                <div className="col-md-5 p-0">
                  <div className="row m-0 px-md-5 px-3 h-100 border-top bg-white overflow-hidden justify-content-end">
                    <div className="d-flex p-0 justify-content-end align-self-center">
                      <button
                        className="btn btn-secondary me-4"
                        onClick={() => window.location.replace("/portfolios/personalisation")}
                      >
                        Back
                      </button>
                      <button
                        className="btn btn-primary w-100"
                        onClick={() => {
                          this._handlePortfolioWeightingNextButton();
                        }}
                      >
                        {activeAllocationSelectionKey === "custom" ? "Custom weighting" : "Equal weighting"}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {/* <!-- /Nav buttons --> */}
              {/* <!-- Dummy spacer to solve overlapping issue with fixed bottom nav buttons --> */}
              <div className="row bg-white d-block d-sm-none" style={{ height: "73px" }} />
            </>
          )}
          {/* End Allocation Selection Method */}

          {/* Update Portfolio Allocation */}
          {currentStep == 2 && fetchingOptimalAllocation && (
            <div className="d-flex flex-column justify-content-center align-items-center gap-4">
              <div className="row m-0 p-0 py-4 text-center justify-content-center mb-4">
                <div id="cover-spin-relative" />
              </div>
              <h4>We are fetching your portfolio template!</h4>
              <p className="text-muted">Hang tight! This should only take a few seconds.</p>
            </div>
          )}
          {currentStep == 2 && !fetchingOptimalAllocation && !hasSeenOptimalAllocation && (
            <>
              <div className="d-flex flex-column justify-content-center align-items-center gap-4">
                <span
                  className="material-icons icon-primary align-self-center align-self-center mb-4"
                  style={{
                    fontSize: "64px"
                  }}
                >
                  check_circle_outline
                </span>
                <h4>Your portfolio template is ready!</h4>
                <p className="text-muted text-center">
                  Remember, this is only a starting point. You can now customise it as you want!
                </p>
              </div>
              <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
                {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
                <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
                <div className="col-md-5 p-0">
                  <div className="row m-0 px-md-5 px-3 h-100 border-top bg-white overflow-hidden justify-content-end">
                    <div className="d-flex p-0 justify-content-end align-self-center">
                      <button
                        className="btn btn-primary w-100"
                        onClick={() => {
                          this.setState({ hasSeenOptimalAllocation: true });
                        }}
                      >
                        Let’s have a look
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
          {currentStep == 2 && !fetchingOptimalAllocation && hasSeenOptimalAllocation && (
            <>
              <div>
                <PortfolioAllocation
                  user={user}
                  portfolio={realPortfolio}
                  assetAllocation={currentAllocation}
                  onAssetAllocationChanged={this._setAssetAllocation}
                  beforeEditAssetAllocation={beforeEditAssetAllocation}
                  onAssetClicked={(assetCommonId: investmentUniverseConfig.AssetType): void =>
                    this._setDisplayAssetModalState(true, assetCommonId)
                  }
                  onAddClicked={() => this._setDisplayModalState("discoverAssets", true)}
                  onEqualizeWeightsClicked={() => this._equalizeCurrentAllocation()}
                  onAssetDelete={this._deleteAsset}
                  isInEditMode={true} // user is always in edit mode and therefore we don't sort his assets
                />
              </div>

              {/* Nav Buttons */}
              <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
                {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
                <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
                <div className="col-md-5 p-0">
                  <div className="row m-0 px-md-5 px-3 h-100 border-top bg-white overflow-hidden justify-content-end">
                    <div className="d-flex p-0 justify-content-end align-self-center">
                      <button
                        className={"btn btn-secondary me-4 "}
                        onClick={() => {
                          this._setDisplayModalState("discardChangesModal", true);
                        }}
                      >
                        Back
                      </button>
                      {currentTotalAllocation == 100 ? (
                        <LoadingOnSubmitButton
                          type="button"
                          className={"btn btn-primary w-100 "}
                          customonclick={this._submitTargetAllocation}
                        >
                          {realPortfolio.isTargetAllocationSetup
                            ? "Update your Portfolio!"
                            : "Create your Portfolio!"}
                        </LoadingOnSubmitButton>
                      ) : (
                        <button
                          type="button"
                          className={"btn btn-danger w-100 "}
                          onClick={() => this._setDisplayModalState("invalidAllocation", true)}
                        >
                          <div className="d-flex justify-content-center">
                            <i
                              className="material-symbols-outlined align-self-center me-2"
                              style={{ fontSize: "16px" }}
                            >
                              running_with_errors
                            </i>
                            <div className="align-self-center">Total {currentTotalAllocation}%</div>
                          </div>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {/* End Nav Buttons */}

              {/* <!-- Dummy spacer to solve overlapping issue with fixed bottom nav buttons --> */}
              <div className="row bg-white" style={{ height: "73px" }} />
            </>
          )}
          {/* End Update Portfolio Allocation */}
        </div>

        <DiscoverAssetsModal
          show={displayModal.discoverAssets}
          handleClose={() => this._setDisplayModalState("discoverAssets", false)}
          selectedUniverse={this._getPendingUniverse()}
          onAssetClick={this._toggleAsset}
          onAssetSelection={this._toggleAsset}
        />

        {displayModal.displayAssetModal && (
          <AssetSideModal
            userCurrency={user.currency}
            assetCommonId={displayedAsset}
            handleClose={() => this._setDisplayModalState("displayAssetModal", false)}
            includeInvestmentDetails={false}
            includeSellButton={false}
            includeRecentActivity={false}
          />
        )}

        <DiscardChangesModal
          handleConfirmation={() => {
            this.setState({
              currentAllocation: {},
              currentStep: 1,
              hasSeenOptimalAllocation: false
            });
          }}
          handleClose={() => this._setDisplayModalState("discardChangesModal", false)}
          show={displayModal.discardChangesModal}
          isWithinBuilderFlow={true}
        />

        <InvalidAllocationModal
          allocation={currentTotalAllocation}
          handleClose={() => this._setDisplayModalState("invalidAllocation", false)}
          show={displayModal.invalidAllocation}
        />
      </OnboardingLayoutNew>
    );
  }
}

export default PortfolioSetupAsStepPage;
