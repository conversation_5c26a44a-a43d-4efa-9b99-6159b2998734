import React from "react";
import { PagePropsType } from "../types/page";
import CenteredOnboardingLayout from "../layouts/centeredOnboardingLayout";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PortfolioDocument } from "../../models/Portfolio";
import { ToastTypeEnum } from "../configs/toastConfig";
import axios from "axios";
import { emitToast } from "../utils/eventService";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import MainCard from "../layouts/mainCard";
import PortfolioAllocation from "../components/portfolioAllocation";
import InvalidAllocationModal from "../components/modals/invalidAllocationModal";
import AssetSideModal from "../components/modals/assetSideModal/assetSideModal";
import DiscardChangesModal from "../components/modals/discardChangesModal";
import DiscoverAssetsModal from "../components/modals/discoverAssetsModal";
import { AllocationType } from "../types/allocation";
import { ROBO_ADVISOR_CONFIG, RoboAdvisorRiskLevelEnum } from "../configs/roboAdvisorConfig";
import Cookies from "universal-cookie";

export type PortfolioSetupPropsType = {
  realPortfolio: PortfolioDocument;
  initialAllocation: AllocationType;
  roboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum;
} & PagePropsType;

type StateType = {
  displayModal: {
    discoverAssets: boolean;
    invalidAllocation: boolean;
    displayAssetModal: boolean;
    showDiscardChangesModal: boolean;
  };
  displayedAsset?: investmentUniverseConfig.AssetType;
  assetAllocation: AllocationType;
  beforeEditAssetAllocation: AllocationType;
  fetchingOptimalAllocation: boolean;
};

class PortfolioSetupPage extends React.Component<PortfolioSetupPropsType, StateType> {
  constructor(props: PortfolioSetupPropsType) {
    super(props);
    this.state = {
      displayModal: {
        discoverAssets: false,
        invalidAllocation: false,
        displayAssetModal: false,
        showDiscardChangesModal: false
      },
      fetchingOptimalAllocation: false,
      assetAllocation: this.props.initialAllocation ?? {},
      beforeEditAssetAllocation: this.props.initialAllocation ?? {}
    };

    this._setAssetAllocation = this._setAssetAllocation.bind(this);
  }

  private _isAllocationFromScratch = false;

  private _getPageTitle(roboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum): string {
    return roboAdvisorRiskLevel
      ? `${ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].displayName} portfolio`
      : "Create your portfolio";
  }

  private _getPageSubtitle(roboAdvisorRiskLevel: RoboAdvisorRiskLevelEnum): string {
    return roboAdvisorRiskLevel
      ? ROBO_ADVISOR_CONFIG[roboAdvisorRiskLevel].description
      : "Select the assets you'd like to include in your target portfolio and set the % allocation for each.";
  }

  private _hasAssetAllocationChanged() {
    const { assetAllocation, beforeEditAssetAllocation } = this.state;

    return !Object.keys(assetAllocation)
      .map((key: investmentUniverseConfig.AssetType) => beforeEditAssetAllocation[key] == assetAllocation[key])
      .every((value) => value);
  }

  private _equalizeAllocation = () => {
    const numberOfAssets = Object.keys(this.state.assetAllocation).length;

    const baseAllocation = Math.floor(100 / numberOfAssets);
    const remainder = 100 - baseAllocation * numberOfAssets;

    const newAllocation: typeof this.state.assetAllocation = {};

    let assetsProcessed = 0;
    for (const asset in this.state.assetAllocation) {
      if (assetsProcessed < remainder) {
        newAllocation[asset as investmentUniverseConfig.AssetType] = baseAllocation + 1;
        assetsProcessed++;
      } else {
        newAllocation[asset as investmentUniverseConfig.AssetType] = baseAllocation;
      }
    }

    this.setState({ assetAllocation: newAllocation });
  };

  /**
   * Returns the aggregated universe, including any pending changes.
   * The returned pending universe is being used to the border color for the corresponding assets.
   *
   * @returns
   */
  private _getPendingUniverse = (): Set<investmentUniverseConfig.AssetType> => {
    const { assetAllocation } = this.state;

    return new Set([...(Object.keys(assetAllocation) as investmentUniverseConfig.AssetType[])]);
  };

  private _setAssetAllocation =
    (assetKey: investmentUniverseConfig.AssetType) =>
    (weight: number): void => {
      this.setState((prevState) => ({
        ...prevState,
        assetAllocation: {
          ...prevState.assetAllocation,
          [assetKey]: weight
        }
      }));
    };

  private _getTotalAssetAllocation(): number {
    const { assetAllocation } = this.state;
    return Object.values(assetAllocation).reduce((sum, value) => (value ? value : 0) + sum, 0);
  }

  private _toggleAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      if (prevState.assetAllocation[assetCommonId] === undefined) {
        newState.assetAllocation[assetCommonId] = 1;
      } else {
        delete newState.assetAllocation[assetCommonId];
      }
      return newState;
    });
  };

  private _deleteAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      delete newState.assetAllocation[assetCommonId];
      return newState;
    });
  };

  private _setDisplayModalState = (
    modalType: "discoverAssets" | "invalidAllocation" | "displayAssetModal" | "showDiscardChangesModal",
    display: boolean
  ): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      newState.displayModal[modalType] = display;
      return newState;
    });
  };

  private _getNonZeroWeightedAssets(): investmentUniverseConfig.AssetType[] {
    const { assetAllocation } = this.state;

    return Object.keys(assetAllocation).filter(
      (assetCommonId: investmentUniverseConfig.AssetType) => assetAllocation[assetCommonId] > 0
    ) as investmentUniverseConfig.AssetType[];
  }

  private _submitTargetAllocation = async () => {
    const { realPortfolio, roboAdvisorRiskLevel } = this.props;
    const { assetAllocation } = this.state;

    const filteredAssetAllocation = Object.fromEntries(
      Object.entries(assetAllocation).filter(
        ([, value]: [investmentUniverseConfig.AssetType, number]) => value > 0
      )
    );

    try {
      await axios({
        method: "post",
        url: `/portfolios/${this.props.realPortfolio.id}/personalisation-preferences`,
        data: {
          personalisationPreferences: {
            assetClasses: ["equities"],
            geography: "global",
            risk: 0.5,
            sectors: ["utilities"]
          }
        }
      });

      const response = await axios.post(`/portfolios/${realPortfolio.id}/allocation`, {
        allocation: filteredAssetAllocation,
        flow: roboAdvisorRiskLevel ? "robo_advisor" : "from_scratch"
      });

      if (response.status === 200) {
        const cookies = new Cookies();
        const portfolioCreationSuccess =
          cookies.get("overridePortfolioCreationSuccess") ?? "/portfolios/creation-success";
        cookies.remove("overridePortfolioCreationSuccess");
        window.location.href = portfolioCreationSuccess;
      }
    } catch (err) {
      emitToast({
        content: "We couldn't update your portfolio.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setDisplayAssetModalState = (
    displayAssetModal: boolean,
    assetCommonId?: investmentUniverseConfig.AssetType
  ): void => {
    this.setState((prevState) => {
      const { ...newState } = prevState;
      newState.displayModal.displayAssetModal = displayAssetModal;
      newState.displayedAsset = assetCommonId || prevState.displayedAsset;
      return newState;
    });
  };

  private _onAddClicked = (startingAllocationFromScratch = false) => {
    this._setDisplayModalState("discoverAssets", true);
    if (startingAllocationFromScratch) {
      this._isAllocationFromScratch = true;
    }
  };

  private _hideDiscoverModal = () => {
    this._setDisplayModalState("discoverAssets", false);
    if (this._isAllocationFromScratch) this._equalizeAllocation();
    this._isAllocationFromScratch = false;
  };

  private _setShowDiscardChangesModal(showDiscardChangesModal: boolean) {
    this._setDisplayModalState("showDiscardChangesModal", showDiscardChangesModal);
  }

  private _getActionButton(): JSX.Element {
    const { realPortfolio } = this.props;
    const totalAssetAllocation = this._getTotalAssetAllocation();

    if (totalAssetAllocation == 100 && this._getNonZeroWeightedAssets().length == 0) {
      return (
        <button type="button" className="btn btn-block btn-primary font-weight-bolder disabled" disabled>
          No changes
        </button>
      );
    } else if (totalAssetAllocation != 100) {
      return (
        <button
          type="button"
          className="btn btn-danger"
          onClick={() => this._setDisplayModalState("invalidAllocation", true)}
        >
          <div className="d-flex justify-content-center">
            <i className="material-symbols-outlined align-self-center me-2" style={{ fontSize: "16px" }}>
              running_with_errors
            </i>
            <div className="align-self-center">Total {totalAssetAllocation}%</div>
          </div>
        </button>
      );
    } else {
      return (
        <>
          {totalAssetAllocation != 100 || this._getNonZeroWeightedAssets().length == 0 ? (
            <button type="button" className="btn btn-primary" disabled>
              No changes
            </button>
          ) : (
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-primary"
              customonclick={this._submitTargetAllocation}
            >
              {realPortfolio.isTargetAllocationSetup ? "Update your portfolio!" : "Create your portfolio!"}
            </LoadingOnSubmitButton>
          )}
        </>
      );
    }
  }

  render(): JSX.Element {
    const { user, realPortfolio, roboAdvisorRiskLevel } = this.props;
    const { displayModal, displayedAsset, assetAllocation } = this.state;

    const areAssetsSelected = Boolean(Object.keys(assetAllocation).length);

    return (
      <>
        <CenteredOnboardingLayout enableModals={false} user={user}>
          {/* Page Body */}
          <div className="container col-xxl-4 col-md-6 col-sm-10 px-0 pt-md-5 pt-0">
            {/* Back Button */}
            <div className="row p-0 m-0 mb-3">
              <div className="col p-0">
                <span
                  className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
                  onClick={() => {
                    if (areAssetsSelected) {
                      this._setShowDiscardChangesModal(true);
                    } else history.back();
                  }}
                  style={{
                    fontSize: "24px",
                    color: "#FFF"
                  }}
                >
                  arrow_back
                </span>
              </div>
            </div>
            {/* End Back Button*/}
            <div className="align-self-center p-0 mb-5" style={{ color: "#FFF" }}>
              {/* Description */}
              <h2 className="fw-bolder p-0 mb-4">{this._getPageTitle(roboAdvisorRiskLevel)}</h2>
              <p className="p-0">{this._getPageSubtitle(roboAdvisorRiskLevel)}</p>
              {/* End Description */}
            </div>
            {/* </div> */}
            <MainCard marginBottom={"160px"}>
              {/* Editable Holdings */}

              <div className="p-0">
                <PortfolioAllocation
                  user={user}
                  portfolio={realPortfolio}
                  assetAllocation={this.state.assetAllocation}
                  beforeEditAssetAllocation={this.state.beforeEditAssetAllocation}
                  onAssetAllocationChanged={this._setAssetAllocation}
                  onAssetClicked={(assetCommonId: investmentUniverseConfig.AssetType): void =>
                    this._setDisplayAssetModalState(true, assetCommonId)
                  }
                  onAddClicked={this._onAddClicked}
                  onEqualizeWeightsClicked={() => this._equalizeAllocation()}
                  onAssetDelete={this._deleteAsset}
                  isInEditMode={this._hasAssetAllocationChanged()}
                />
              </div>
              {/* End Editable Holdings */}

              {/* Bottom Buttons */}
              <div
                className="p-0 m-0 justify-content-center"
                style={{
                  position: "fixed",
                  right: "16px",
                  bottom: "0",
                  left: "0",
                  zIndex: "1030"
                }}
              >
                <div className="container col-xxl-4 col-md-6 col-sm-10 mb-4 bg-white wh-lab-bottom-buttons">
                  <div className="d-flex p-0 justify-content-center align-self-center mb-0">
                    {areAssetsSelected && <div className="my-4  d-sm-block px-5">{this._getActionButton()}</div>}
                  </div>
                </div>
              </div>
              {/* End Bottom Buttons */}
            </MainCard>

            <DiscoverAssetsModal
              show={displayModal.discoverAssets}
              handleClose={this._hideDiscoverModal}
              selectedUniverse={this._getPendingUniverse()}
              onAssetClick={this._toggleAsset}
              onAssetSelection={this._toggleAsset}
            />
            {displayModal.displayAssetModal && (
              <AssetSideModal
                userCurrency={user.currency}
                assetCommonId={displayedAsset}
                handleClose={(): void => this._setDisplayAssetModalState(false)}
                includeSellButton={false}
                includeInvestmentDetails={false}
                includeRecentActivity={false}
              />
            )}
            <DiscardChangesModal
              handleConfirmation={() => history.back()}
              handleClose={() => this._setShowDiscardChangesModal(false)}
              show={displayModal.showDiscardChangesModal}
              isWithinBuilderFlow={false}
            />
            {/* Invalid Allocation Modal */}
            <InvalidAllocationModal
              allocation={this._getTotalAssetAllocation()}
              handleClose={() => this._setDisplayModalState("invalidAllocation", false)}
              show={displayModal.invalidAllocation}
            />
            {/* End Invalid Allocation Modal */}
          </div>
        </CenteredOnboardingLayout>
      </>
    );
  }
}

export default PortfolioSetupPage;
