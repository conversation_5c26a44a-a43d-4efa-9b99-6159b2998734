import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import PortfolioAllocation from "../components/portfolioAllocation";
import { PendingOrderType } from "../types/order";
import { PagePropsType } from "../types/page";
import { PartialRecord } from "../types/utils";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import axios from "axios";
import { PortfolioDocument } from "../../models/Portfolio";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import MainLayout from "../layouts/mainLayout";
import InvalidAllocationModal from "../components/modals/invalidAllocationModal";
import MainCard from "../layouts/mainCard";
import ModalsWrapper from "../components/modalsWrapper";
import PortfolioTargetAllocationAutomatedRebalanceModal from "../components/modals/portfolioTargetAllocationAutomatedRebalanceModal";
import { isVerified } from "../utils/userUtil";
import PortfolioTargetAllocationUpdateSuccessModal from "../components/modals/portfolioTargetAllocationUpdateSuccessModal";
import DiscoverAssetsModal from "../components/modals/discoverAssetsModal";
import PortfolioReviewModal from "../components/modals/portfolioReviewModal";
import InfoModal from "../components/modals/infoModal";
import HorizontalScroller from "../components/horizontalScroller";
import TargetPortfolioBanner from "../components/targetPortfolioBanner";
import PortfolioCreationModal from "../components/modals/portfolioCreationModal";
import { ConfigCatFeatureFlags } from "../../config/featuresConfig";
import { isFeatureEnabled } from "../utils/featureFlagUtil";

export type PortfolioTargetPagePropsType = {
  portfolio: PortfolioDocument;
  hasActiveRebalanceAutomation: boolean;
  hasPendingRebalance: boolean;
  triggerTargetAllocationSuccessModal: boolean;
} & PagePropsType;

type StateType = {
  displayModal: {
    preferences: boolean;
    allocation: boolean;
    projection: boolean;
    portfolioSummary: boolean;
    orderSummary: boolean;
    discoverAssets: boolean;
    invalidAllocation: boolean;
    rebalanceModal: boolean;
    targetAllocationUpdateSuccess: boolean;
    portfolioCreation: boolean;
  };
  pendingOrders: PartialRecord<investmentUniverseConfig.AssetType, PendingOrderType>;
  assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  portfolio: PortfolioDocument;
  showTargetPortfolioInfo: boolean;
  reviewModal: boolean;
  beforeEditAssetAllocation: { [key in investmentUniverseConfig.AssetType]?: number };
  hasActiveRebalanceAutomation: boolean;
};

class PortfolioTargetPage extends React.Component<PortfolioTargetPagePropsType, StateType> {
  private _isAllocationFromScratch = false;

  constructor(props: PortfolioTargetPagePropsType) {
    super(props);

    this.state = {
      displayModal: {
        preferences: false,
        allocation: false,
        projection: false,
        portfolioSummary: false,
        orderSummary: false,
        discoverAssets: false,
        invalidAllocation: false,
        rebalanceModal: false,
        targetAllocationUpdateSuccess: props.triggerTargetAllocationSuccessModal,
        portfolioCreation: false
      },
      pendingOrders: {},
      assetAllocation: this._transformPortfolioAllocation(props.portfolio),
      portfolio: props.portfolio,
      showTargetPortfolioInfo: false,
      reviewModal: false,
      beforeEditAssetAllocation: this._transformPortfolioAllocation(props.portfolio),
      hasActiveRebalanceAutomation: props.hasActiveRebalanceAutomation
    };

    this._hideReviewModal = this._hideReviewModal.bind(this);
  }

  /**
   * This function returns the different assets between the current and the target portfolio.
   */
  private _getPortfolioDifference(initialHoldingsAllocation: {
    [key in investmentUniverseConfig.AssetType]?: number;
  }): {
    assetsInCurrentPortfolioButNotInTargetPortfolio: investmentUniverseConfig.AssetType[];
    assetsInTargetPortfolioButNotInCurrentPortfolio: investmentUniverseConfig.AssetType[];
  } {
    const { portfolio } = this.state;
    const currentPortfolio = portfolio.holdings;
    const targetPortfolio = initialHoldingsAllocation; // This is already a map of assetCommonId to percentage

    // Find assets in the target portfolio but not in the current
    const assetsInTargetPortfolioButNotInCurrentPortfolio: investmentUniverseConfig.AssetType[] = Object.keys(
      targetPortfolio
    )
      .filter((assetId) => !currentPortfolio.some((holding) => holding.assetCommonId === assetId))
      .map((assetId) => assetId as investmentUniverseConfig.AssetType); // Type assertion here

    const assetsInCurrentPortfolioButNotInTargetPortfolio: investmentUniverseConfig.AssetType[] = currentPortfolio
      .filter(({ assetCommonId }) => !(assetCommonId in targetPortfolio))
      .map((holding) => holding.assetCommonId as investmentUniverseConfig.AssetType); // Type assertion here

    return {
      assetsInCurrentPortfolioButNotInTargetPortfolio,
      assetsInTargetPortfolioButNotInCurrentPortfolio
    };
  }

  /**
   * Get initial asset allocation based on virtual portfolio.
   *
   * @private
   */
  private _transformPortfolioAllocation(portfolio: PortfolioDocument): {
    [key in investmentUniverseConfig.AssetType]?: number;
  } {
    const allocation: { [key in investmentUniverseConfig.AssetType]?: number } = {};
    portfolio.initialHoldingsAllocation.forEach(
      ({ assetCommonId, percentage }) => (allocation[assetCommonId] = percentage)
    );

    return allocation;
  }

  /**
   * Check if current asset allocation matches initial holdings allocation of virtual portfolio.
   *
   * @private
   */
  private _hasAssetAllocationChanged() {
    const { portfolio } = this.state;
    const initialAllocation = this._transformPortfolioAllocation(portfolio);
    const { assetAllocation } = this.state;

    // Check if the number of assets is the same in both allocations
    if (Object.keys(initialAllocation).length !== Object.keys(assetAllocation).length) {
      return true;
    }

    // Check if the weight for every asset the same in both allocations
    return !Object.keys(assetAllocation)
      .map((key: investmentUniverseConfig.AssetType) => initialAllocation[key] == assetAllocation[key])
      .every((value) => value);
  }

  private _getReviewBanners(): JSX.Element {
    const { hasPendingRebalance, portfolio } = this.props;
    const { hasActiveRebalanceAutomation } = this.state;

    const portfolioDifferences = this._getPortfolioDifference(this.state.assetAllocation);
    const numberOfChangesToReview =
      portfolioDifferences.assetsInCurrentPortfolioButNotInTargetPortfolio.length +
      portfolioDifferences.assetsInTargetPortfolioButNotInCurrentPortfolio.length;

    const showReviewBanner = numberOfChangesToReview > 0 && !hasPendingRebalance;
    const showAutomatedRebalanceBanner = !hasActiveRebalanceAutomation && portfolio.isTargetAllocationSetup;

    if (!showReviewBanner && !showAutomatedRebalanceBanner) {
      return <></>;
    }

    const reviewBanner = (
      <TargetPortfolioBanner
        icon="sync_problem"
        title={`${numberOfChangesToReview} ${numberOfChangesToReview === 1 ? "change" : "changes"} to review`}
        buttonTitle="Review"
        onButtonSelect={() => this._showReviewModal()}
      />
    );
    const automatedRebalanceBanner = (
      <TargetPortfolioBanner
        icon="sync"
        title="Automated rebalancing"
        buttonTitle="Set"
        onButtonSelect={() => this._setDisplayModalState("rebalanceModal", true)}
        infoTitle="What's that?"
        onInfoSelect={() => this._setDisplayModalState("rebalanceModal", true)}
      />
    );

    if (showAutomatedRebalanceBanner && showReviewBanner) {
      return (
        <HorizontalScroller
          id="notifications-scroller"
          className="mb-5 px-lg-2 px-md-5 px-0"
          scrollingDistance={900}
          showScrollDots={false}
        >
          {reviewBanner}
          {automatedRebalanceBanner}
        </HorizontalScroller>
      );
    } else {
      return (
        <div style={{ marginBottom: "1.5rem" }}>
          {showReviewBanner && reviewBanner}
          {showAutomatedRebalanceBanner && automatedRebalanceBanner}
        </div>
      );
    }
  }

  private _resetAllocation() {
    const { portfolio } = this.state;
    this.setState({ assetAllocation: this._transformPortfolioAllocation(portfolio) });
  }

  private _onAddAssetsClicked = (startingAllocationFromScratch = false) => {
    this._setDisplayModalState("discoverAssets", true);
    if (startingAllocationFromScratch) {
      this._isAllocationFromScratch = true;
    }
  };

  private _equalizeAllocation = () => {
    const numberOfAssets = Object.keys(this.state.assetAllocation).length;

    const baseAllocation = Math.floor(100 / numberOfAssets);
    const remainder = 100 - baseAllocation * numberOfAssets;

    const newAllocation: typeof this.state.assetAllocation = {};

    let assetsProcessed = 0;
    for (const asset in this.state.assetAllocation) {
      if (assetsProcessed < remainder) {
        newAllocation[asset as keyof typeof this.state.assetAllocation] = baseAllocation + 1;
        assetsProcessed++;
      } else {
        newAllocation[asset as keyof typeof this.state.assetAllocation] = baseAllocation;
      }
    }

    this.setState({ assetAllocation: newAllocation });
  };

  private _hideDiscoverModal = () => {
    this._setDisplayModalState("discoverAssets", false);
    if (this._isAllocationFromScratch) this._equalizeAllocation();
    this._isAllocationFromScratch = false;
  };
  /**
   * Returns the aggregated universe, including any pending changes.
   * The returned pending universe is being used to the border color for the corresponding assets.
   *
   * @returns
   */
  private _getPendingUniverse = (): Set<investmentUniverseConfig.AssetType> => {
    const { assetAllocation } = this.state;

    return new Set([...(Object.keys(assetAllocation) as investmentUniverseConfig.AssetType[])]);
  };

  private _updateAssetAllocation = (pendingAllocation: {
    toAdd: investmentUniverseConfig.AssetType[];
    toRemove: investmentUniverseConfig.AssetType[];
  }): void => {
    const pendingAssetAllocation = pendingAllocation.toAdd.concat(pendingAllocation.toRemove);
    pendingAssetAllocation.map((asset) => this._toggleAsset(asset));
    this.setState({ reviewModal: false });
  };

  private _saveTargetAllocation = async () => {
    const { assetAllocation, portfolio } = this.state;

    const filteredAssetAllocation = Object.fromEntries(
      Object.entries(assetAllocation).filter(
        ([, value]: [investmentUniverseConfig.AssetType, number]) => value > 0
      )
    );

    try {
      const updatedRealPortfolio: PortfolioDocument = (
        await axios.post(`/portfolios/${portfolio._id}/allocation`, {
          allocation: filteredAssetAllocation,
          investmentAmount: 1000
        })
      ).data;
      // update outdated target allocation
      this.props.user.portfolios[0].initialHoldingsAllocation = updatedRealPortfolio.initialHoldingsAllocation;

      // clear pending orders to not trigger alert on page leave
      this.setState((prevState) => {
        const newState = { ...prevState };
        newState.pendingOrders = {};

        newState.assetAllocation = this._transformPortfolioAllocation(updatedRealPortfolio);
        newState.portfolio = updatedRealPortfolio;
        newState.beforeEditAssetAllocation = this._transformPortfolioAllocation(updatedRealPortfolio);
        newState.displayModal["targetAllocationUpdateSuccess"] = true;
        return newState;
      });
    } catch (err) {
      emitToast({
        content: "We couldn't update your portfolio.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setAssetAllocation =
    (assetKey: investmentUniverseConfig.AssetType) =>
    (weight: number): void => {
      this.setState((prevState) => {
        const newState = { ...prevState };
        newState.assetAllocation = JSON.parse(JSON.stringify(prevState.assetAllocation));
        newState.assetAllocation[assetKey] = weight;
        return newState;
      });
    };

  private _getTotalAssetAllocation(): number {
    const { assetAllocation } = this.state;
    return Object.values(assetAllocation).reduce((sum, value) => (value ? value : 0) + sum, 0);
  }

  private _toggleAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      if (prevState.assetAllocation[assetCommonId] === undefined) {
        newState.assetAllocation[assetCommonId] = 1;
      } else {
        delete newState.assetAllocation[assetCommonId];
      }
      return newState;
    });
  };

  private _deleteAsset = (assetCommonId: investmentUniverseConfig.AssetType): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      delete newState.assetAllocation[assetCommonId];
      return newState;
    });
  };

  private _showReviewModal() {
    this.setState({ reviewModal: true });
  }

  private _hideReviewModal() {
    this.setState({ reviewModal: false });
  }

  private _setDisplayModalState = (
    modalType:
      | "preferences"
      | "allocation"
      | "projection"
      | "portfolioSummary"
      | "orderSummary"
      | "discoverAssets"
      | "invalidAllocation"
      | "rebalanceModal"
      | "targetAllocationUpdateSuccess"
      | "portfolioCreation",
    display: boolean
  ): void => {
    this.setState((prevState) => {
      const newState = { ...prevState };
      newState.displayModal[modalType] = display;
      return newState;
    });
  };

  private _handleAutomatedRebalanceSetup() {
    this.setState({ hasActiveRebalanceAutomation: true });
  }

  private _getModifiedNotZeroWeightedAssets(): investmentUniverseConfig.AssetType[] {
    const { assetAllocation, portfolio } = this.state;
    const portfolioAssetAllocation = this._transformPortfolioAllocation(portfolio);

    return Object.keys(assetAllocation).filter(
      (assetCommonId: investmentUniverseConfig.AssetType) =>
        assetAllocation[assetCommonId] > 0 &&
        portfolioAssetAllocation[assetCommonId] != assetAllocation[assetCommonId]
    ) as investmentUniverseConfig.AssetType[];
  }

  private _getActionButton(): JSX.Element {
    const totalAssetAllocation = this._getTotalAssetAllocation();

    if (totalAssetAllocation == 100 && this._getModifiedNotZeroWeightedAssets().length == 0) {
      return (
        <button type="button" className="btn btn-block btn-primary font-weight-bolder disabled" disabled>
          No changes
        </button>
      );
    } else if (totalAssetAllocation != 100) {
      return (
        <button
          type="button"
          className="btn btn-danger"
          onClick={() => this._setDisplayModalState("invalidAllocation", true)}
        >
          <div className="d-flex justify-content-center">
            <i className="material-symbols-outlined align-self-center me-2" style={{ fontSize: "16px" }}>
              running_with_errors
            </i>
            <div className="align-self-center">Total {totalAssetAllocation}%</div>
          </div>
        </button>
      );
    } else {
      return (
        <>
          {totalAssetAllocation != 100 || this._getModifiedNotZeroWeightedAssets().length == 0 ? (
            <button type="button" className="btn btn-primary" disabled>
              No changes
            </button>
          ) : (
            <LoadingOnSubmitButton
              type="button"
              className="btn btn-primary"
              customonclick={this._saveTargetAllocation}
            >
              Save changes
            </LoadingOnSubmitButton>
          )}
        </>
      );
    }
  }

  private _setShowInfoDialog(showTargetPortfolioInfo: boolean) {
    this.setState({ showTargetPortfolioInfo });
  }

  render(): JSX.Element {
    const { user } = this.props;
    const { portfolio, displayModal, showTargetPortfolioInfo, reviewModal, hasActiveRebalanceAutomation } =
      this.state;

    const userHasHoldings = portfolio.holdings.length > 0;
    const userIsUninvested = user.portfolioConversionStatus === "notStarted";

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        <ModalsWrapper user={this.props.user} activePage={this.props.activePage} />
        {/* Back Button */}
        <div className="row p-0 m-0 mb-3">
          <div className="col p-0">
            <span
              className="material-icons icon-primary cursor-pointer align-self-center align-self-center"
              onClick={() => (window.location.href = "/investor/investments")}
              style={{
                fontSize: "24px"
              }}
            >
              arrow_back
            </span>
          </div>
        </div>
        {/* End Back Button*/}
        {/*/!* About *!/*/}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4 mb-1">
          <div className="p-0 d-flex justify-content-between">
            {/* Header & Info */}
            <div className="d-flex align-items-center">
              <h2 className="fw-bolder m-0 p-0">Target Portfolio</h2>
              <span
                className="material-symbols-outlined cursor-pointer align-self-center icon-primary ms-2"
                style={{
                  fontSize: "20px"
                }}
                onClick={(event) => {
                  event.stopPropagation();
                  this._setShowInfoDialog(true);
                }}
              >
                info
              </span>
            </div>
            <span
              className="material-symbols-outlined cursor-pointer align-self-center icon-primary rounded-circle mb-1 p-3"
              style={{
                fontSize: "24px",
                backgroundColor: "#F1F3FD"
              }}
              onClick={(event) => {
                event.stopPropagation();
                this._setDisplayModalState("portfolioCreation", true);
              }}
            >
              data_saver_on
            </span>
            {/* End Header & Info */}
          </div>
        </div>
        {/*/!* End About *!/*/}
        {/* Page Body */}
        <MainCard marginBottom={"160px"}>
          {/* Editable Holdings */}
          {/* Review banners */}
          {userHasHoldings && this._getReviewBanners()}
          {/* End review banners */}
          <div className="row m-0">
            <div className="col p-0">
              <PortfolioAllocation
                portfolio={portfolio}
                assetAllocation={this.state.assetAllocation}
                beforeEditAssetAllocation={this.state.beforeEditAssetAllocation}
                onAssetAllocationChanged={this._setAssetAllocation}
                onAssetClicked={(assetKey) => eventEmitter.emit(EVENTS.investmentProductModal, assetKey)}
                onAddClicked={this._onAddAssetsClicked}
                onEqualizeWeightsClicked={() => this._equalizeAllocation()}
                onAssetDelete={this._deleteAsset}
                user={user}
                isInEditMode={this._hasAssetAllocationChanged()}
              />
            </div>
            {/* End Editable Holdings */}

            {/* Bottom Buttons */}
            <div className="row fixed-bottom p-0 m-0 justify-content-end">
              <div className="col-md-3 col-xxl-4 col-sm-1" />
              <div className="col-xxl-4 col-md-6 col-sm-10 mb-4 bg-white wh-lab-bottom-buttons">
                <div className="d-flex p-0 justify-content-end align-self-center mb-0">
                  {this._hasAssetAllocationChanged() && (
                    <div className="my-4 d-none d-sm-block px-5">
                      <button
                        type="button"
                        className="btn btn-secondary me-4"
                        onClick={() => this._resetAllocation()}
                      >
                        Reset
                      </button>
                      {this._getActionButton()}
                    </div>
                  )}
                </div>
              </div>
              <div className="col-md-3 col-xxl-4 col-sm-1" />
            </div>

            <div className="px-md-5 px-3 fixed-bottom bg-white w-100 d-block d-sm-none">
              <div className="d-flex p-0 justify-content-end align-self-center mb-0">
                {this._hasAssetAllocationChanged() && (
                  <div className="mb-4 mt-3">
                    <button
                      type="button"
                      className="btn btn-secondary me-4"
                      onClick={() => this._resetAllocation()}
                    >
                      Reset
                    </button>
                    {this._getActionButton()}
                  </div>
                )}
              </div>
            </div>
            {/* End Bottom Buttons */}
          </div>
        </MainCard>
        {/* End Page Body */}
        {/* Portfolio creation */}
        <PortfolioCreationModal
          show={displayModal.portfolioCreation}
          handleClose={() => this._setDisplayModalState("portfolioCreation", false)}
          isRoboAdvisorEnabled={this.props.user.isRoboAdvisorEnabled}
        />
        {/* Review modal */}
        {portfolio && isVerified(user) && (
          <PortfolioReviewModal
            show={reviewModal}
            handleClose={this._hideReviewModal}
            portfolioDifferences={this._getPortfolioDifference(this.state.assetAllocation)}
            onButtonClick={(pendingAllocation) => this._updateAssetAllocation(pendingAllocation)}
          />
        )}
        {/* Discover Assets Modal */}
        <DiscoverAssetsModal
          show={displayModal.discoverAssets}
          handleClose={this._hideDiscoverModal}
          selectedUniverse={this._getPendingUniverse()}
          onAssetClick={this._toggleAsset}
          onAssetSelection={this._toggleAsset}
        />
        {/* Invalid Allocation Modal */}
        <InvalidAllocationModal
          allocation={this._getTotalAssetAllocation()}
          handleClose={() => this._setDisplayModalState("invalidAllocation", false)}
          show={displayModal.invalidAllocation}
        />
        {/* End Invalid Allocation Modal */}
        {/* Automated rebalance modal */}
        {portfolio && isVerified(user) && (
          <PortfolioTargetAllocationAutomatedRebalanceModal
            show={displayModal.rebalanceModal}
            handleAutomatedRebalanceSetup={() => this._handleAutomatedRebalanceSetup()}
            handleClose={() => this._setDisplayModalState("rebalanceModal", false)}
          />
        )}
        {/* End Automated rebalance modal */}
        {/* Updated allocation modal */}
        {portfolio && (
          <PortfolioTargetAllocationUpdateSuccessModal
            userIsUninvested={userIsUninvested}
            hasActiveRebalanceAutomation={hasActiveRebalanceAutomation}
            show={displayModal.targetAllocationUpdateSuccess}
            handleAutomatedRebalanceSetup={() => this._handleAutomatedRebalanceSetup()}
            handleClose={() => this._setDisplayModalState("targetAllocationUpdateSuccess", false)}
          />
        )}
        {/* End Updated allocation modal */}
        <InfoModal
          title={"About your Target Portfolio"}
          show={showTargetPortfolioInfo}
          handleClose={() => this._setShowInfoDialog(false)}
          dialogClassName={"max-w-600px"}
        >
          <h6 className={"mb-3 mt-4"}>Build and edit your target portfolio!</h6>

          <p className={"text-muted"}>In this section, you can build and edit your target portfolio. You can: </p>
          <p className={"text-muted"}>
            <ul>
              <li>Add or remove stocks and ETFs</li>
              <li>Update your target allocation and asset weights</li>
              <li>Check useful insights about your portfolio</li>
            </ul>
          </p>
          <h6 className={"mb-3 mt-4"}>What is your target portfolio?</h6>

          <p className={"text-muted"}>
            Your target portfolio is like a recipe for your investments, saying how much of each type you want.
          </p>
          <p className={"text-muted"}>
            {" "}
            Your actual portfolio (holdings) is what you really have and you can see this in your Dashboard.
          </p>
          <h6 className={"mb-3 mt-4"}>Keep in mind!</h6>
          <p className={"text-muted"}>
            Differences between your target portfolio and your actual holdings can occur either from market
            movements (when one asset moves more than others) or if you buy/sell individual assets without updating
            your target portfolio allocation accordingly.{" "}
          </p>
          <p className={"text-muted"}>
            Changes to your target portfolio will not affect your actual holdings, unless you decide to Rebalance.{" "}
          </p>
          <p className={"text-muted"}>
            In every portfolio investment modal, you have the option to invest in your target portfolio.{" "}
          </p>
          <p className={"text-muted"}>
            When the ‘Buy target portfolio’ option is active, your investment will be made in your target portfolio
            allocation. This is the portfolio allocation (weighting) you have defined in this section.{" "}
          </p>
          <p className={"text-muted"}>
            If the ‘Buy target portfolio’ option is off, your investment will be made in your current holdings.
            This is the weighting of your actual current holdings that you can also see in your Dashboard.{" "}
          </p>
        </InfoModal>
      </MainLayout>
    );
  }
}

export default PortfolioTargetPage;
