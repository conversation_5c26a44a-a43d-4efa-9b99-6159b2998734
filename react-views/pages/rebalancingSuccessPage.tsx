import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";

export type RebalancingSuccessPropsType = PagePropsType;

class RebalancingSuccessPage extends React.Component<RebalancingSuccessPropsType> {
  render(): JSX.Element {
    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">Your rebalancing order has been placed successfully!</h5>
              <p className="text-muted">
                Rebalancing takes place over two consecutive trading sessions. First, overweight assets are sold,
                and then, the proceedings are used to buy underweight assets.{" "}
              </p>
              <p className="text-muted">
                When the rebalancing is completed, your holdings will closely match your target allocation.
              </p>
              <p className="text-muted">
                Keep in mind that you cannot sell any of your holdings until the rebalancing is completed.{" "}
              </p>
              <p className="text-center">
                <a href="/" className="btn btn-primary w-100 mt-5">
                  Got it!
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default RebalancingSuccessPage;
