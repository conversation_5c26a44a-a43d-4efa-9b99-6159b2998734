import React from "react";
import OnboardingLayoutNew from "../layouts/onboardingLayoutNew";
import CountriesSearch from "../components/countriesSearch";
import { PagePropsType } from "../types/page";

export type ResidencyCountryPropsType = PagePropsType;

class ResidencyCountryPage extends React.Component<ResidencyCountryPropsType> {
  render(): JSX.Element {
    const leftSideChild = (
      <div>
        <h2 className="mb-4" style={{ fontWeight: "bold" }}>
          Choose your country
        </h2>
        <h4>Where do you live?</h4>
      </div>
    );

    return (
      <>
        <OnboardingLayoutNew alignment="start" leftSideChild={leftSideChild} user={this.props.user}>
          <CountriesSearch />
        </OnboardingLayoutNew>
      </>
    );
  }
}

export default ResidencyCountryPage;
