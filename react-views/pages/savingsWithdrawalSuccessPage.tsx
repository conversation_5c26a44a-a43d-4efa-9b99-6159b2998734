import React, { Component } from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import SuccessAnimatedIcon from "../components/icons/successAnimatedIcon";

export type SavingsWithdrawalSuccessPagePropsType = {
  redirectUri: string;
} & PagePropsType;

const REDIRECT_TIMEOUT_MS = 2000;

export default class SavingsWithdrawalSuccessPage extends Component<SavingsWithdrawalSuccessPagePropsType> {
  componentDidMount(): void {
    const { redirectUri } = this.props;
    setTimeout(() => window.location.replace(redirectUri), REDIRECT_TIMEOUT_MS);
  }

  render(): JSX.Element {
    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col-md-3 d-flex justify-content-center">
              <SuccessAnimatedIcon />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center text-center">
              <h5 className="fw-bolder text-center mb-4">Your withdrawal is successful!</h5>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}
