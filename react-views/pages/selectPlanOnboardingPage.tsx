import React from "react";
import { plansConfig } from "@wealthyhood/shared-configs";
import { PagePropsType } from "../types/page";
import axios from "axios";
import OnboardingLayoutNew from "../layouts/onboardingLayoutNew";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import PlanSelection from "../components/planSelection";
import PlanSelectionButton from "../components/planSelectionButton";
import ConfigUtil from "../../utils/configUtil";
import CardSubscriptionPaymentMethodSelectModal from "../components/cardSubscriptionPaymentMethodSelectModal";
import { PaymentMethodDocument } from "../../models/PaymentMethod";
import { SavingsProductFeeDetailsWithIdType } from "../types/savings";
import { PlansContext } from "../contexts/plansContext";

export type SelectPlanOnboardingPropsType = {
  initialSelectedPrice?: plansConfig.PriceType;
  initialShowCardPaymentModal?: boolean;
  paymentMethods: PaymentMethodDocument[];
  promotionalSavingsProductData: SavingsProductFeeDetailsWithIdType;
} & PagePropsType;

type StateType = {
  selectedPrice: plansConfig.PriceType;
  selectedRecurrence: plansConfig.PriceRecurrenceType;
  showCardPaymentModal: boolean;
};

class SelectPlanOnboardingPage extends React.Component<SelectPlanOnboardingPropsType, StateType> {
  private _PRICE_CONFIG;
  constructor(props: SelectPlanOnboardingPropsType) {
    super(props);
    this._PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    this.state = {
      selectedPrice: this.props.initialSelectedPrice || "free_monthly",
      selectedRecurrence: "yearly",
      showCardPaymentModal: this.props.initialShowCardPaymentModal ?? false
    };
  }

  componentDidMount() {
    if (this.props.initialSelectedPrice) {
      this._setActivePrice(this.props.initialSelectedPrice);
      this._setActiveRecurrence(this._PRICE_CONFIG[this.props.initialSelectedPrice].recurrence ?? "yearly");
    }
  }

  private _setActivePrice = (price: plansConfig.PriceType): void => this.setState({ selectedPrice: price });

  private _setActiveRecurrence = (recurrence: plansConfig.PriceRecurrenceType): void =>
    this.setState({ selectedRecurrence: recurrence });

  private _setShowCardPaymentSubscriptionPaymentMethodSelectModal(showCardPaymentModal: boolean) {
    this.setState({ showCardPaymentModal });
  }

  private async _createSubscription() {
    const { selectedPrice } = this.state;
    const selectedPriceConfig = this._PRICE_CONFIG[selectedPrice];
    const price = selectedPriceConfig.keyName;

    let category;
    if (["monthly", "yearly"].includes(selectedPriceConfig.recurrence) && selectedPrice !== "free_monthly") {
      category = "CardPaymentSubscription";
    } else if (selectedPriceConfig.recurrence === "lifetime") {
      category = "SinglePaymentSubscription";
    } else {
      category = "FeeBasedSubscription";
    }

    try {
      await axios.post("subscriptions", {
        category,
        price
      });

      window.location.href = "/";
    } catch (err) {
      emitToast({
        toastType: ToastTypeEnum.error,
        content: "Oops! Something went wrong. Please try again."
      });
    }
  }

  private async _onNextClicked(): Promise<void> {
    const { selectedPrice } = this.state;
    const isFreePlanSelected = selectedPrice == "free_monthly";

    if (isFreePlanSelected) {
      await this._createSubscription();
    } else {
      this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(true);
    }
  }

  render(): JSX.Element {
    const { selectedPrice, selectedRecurrence, showCardPaymentModal } = this.state;
    const { user, paymentMethods, promotionalSavingsProductData } = this.props;

    const title = "Choose your plan";
    const body = "Get even more for your money with premium features and benefits.";
    return (
      <>
        <OnboardingLayoutNew
          featureFlags={this.props.featureFlags}
          user={this.props.user}
          alignment={"center"}
          title={"Choose your account"}
          leftSideChild={
            <>
              <div className="row mt-3 d-none d-sm-block">
                <h1 className="fw-bold mb-3 p-0">{title}</h1>
                <h4 className="p-0" style={{ lineHeight: "inherit" }}>
                  {body}
                </h4>
              </div>
              <div className="row m-0 mt-3 text-center d-block d-sm-none">
                <h4 className="fw-bold p-0">{title}</h4>
                <p className="p-0 m-0" style={{ lineHeight: "inherit" }}>
                  {body}
                </p>
              </div>
            </>
          }
        >
          <div className="container p-0">
            <PlansContext.Provider
              value={{
                user,
                locale: ConfigUtil.getDefaultUserLocale(user.residencyCountry),
                promotionalSavingsProductData
              }}
            >
              <PlanSelection
                selectedPrice={selectedPrice}
                handlePriceSelection={this._setActivePrice}
                selectedRecurrence={selectedRecurrence}
                handleRecurrenceSelection={this._setActiveRecurrence}
                userCanGetFreeTrial={true}
              />
            </PlansContext.Provider>
          </div>
          {/* EndBank Select Modal Content */}

          {/* <!-- Nav buttons --> */}
          <div className="row p-0 m-0 bg-light h-10 fixed-bottom">
            {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
            <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
            <div className="col-md-5 p-0">
              <div className="row m-0 px-md-5 px-3 h-100 border-top bg-light overflow-hidden justify-content-end">
                <div className="d-flex p-0 justify-content-end align-self-center">
                  <PlanSelectionButton
                    selectedPrice={selectedPrice}
                    onButtonClick={this._onNextClicked.bind(this)}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* <!-- Dummy spacer to solve overlapping issue with fixed bottom nav buttons --> */}
          <div className="row bg-white" style={{ height: "73px" }} />
          {/* <!-- /Nav buttons --> */}
        </OnboardingLayoutNew>

        {/* Setup Card Payments Modal */}
        <CardSubscriptionPaymentMethodSelectModal
          user={user}
          show={showCardPaymentModal}
          handleClose={() => this._setShowCardPaymentSubscriptionPaymentMethodSelectModal(false)}
          paymentMethods={paymentMethods}
          newPrice={selectedPrice}
          source={"select-plan"}
        />
        {/* End Setup Card Payments Modal */}
      </>
    );
  }
}

export default SelectPlanOnboardingPage;
