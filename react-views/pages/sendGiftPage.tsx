import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import axios from "axios";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import { dateDiffInDays } from "../utils/dateUtil";
import { formatCurrency } from "../utils/currencyUtil";
import { currenciesConfig, localeConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";
import LegalDocumentUtil from "../utils/legalDocumentUtil";

export type SendGiftPropsType = PagePropsType;

type StateType = {
  email: string;
  message: string;
};

const MESSAGE_MAX_CHARACTER_COUNT = 200;
const getDefaultGiftEmail = (userCurrency: currenciesConfig.MainCurrencyType, locale: localeConfig.LocaleType) =>
  `Hi there! Welcome to Wealthyhood! Here’s a ${formatCurrency(
    20,
    userCurrency,
    locale,
    0,
    0
  )} gift to kickstart your investing journey! 🤑`;

class SendGiftPage extends React.Component<SendGiftPropsType, StateType> {
  constructor(props: SendGiftPropsType) {
    super(props);
    this.state = {
      email: undefined,
      message: undefined
    };
  }

  private _sendGift = async () => {
    const { user } = this.props;
    const { email, message } = this.state;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    try {
      await axios.post("/gifts", {
        targetUserEmail: email,
        message: message ?? getDefaultGiftEmail(user.currency, locale)
      });
      window.location.replace("/investor/gift-success");
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _isFormFilled = () => {
    const { email } = this.state;

    return email?.length > 0;
  };

  private _getDaysLeftString = (): string => {
    const { user } = this.props;

    const daysDiff = Math.abs(dateDiffInDays(new Date(user.canSendGiftUntil), new Date()));

    if (daysDiff === 0) {
      return "Last day";
    } else return `${daysDiff + 1} days left`;
  };

  render(): JSX.Element {
    const { email, message } = this.state;
    const { user } = this.props;
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    const characterCount = message?.length ?? 0;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
      >
        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Send {formatCurrency(20, user.currency, locale, 0, 0)} to a friend</h5>
            <p className="text-muted">
              Here’s a {formatCurrency(20, user.currency, locale, 0, 0)} voucher from us you can send to a friend
              to help them get started investing. Enter your friend’s email below, and we’ll send them{" "}
              {formatCurrency(20, user.currency, locale, 0, 0)} to make their first investment.
            </p>
          </div>
        </div>
        {/* End About Page */}

        <div className="wh-card-body bg-white mb-5 p-md-5 py-5 px-3">
          <div className="col-md-12 d-flex flex-column align-self-center justify-content-center">
            <div className={"position-relative"}>
              <img
                className="mb-2 w-100"
                style={{ borderRadius: "18px" }}
                alt=""
                src={"/images/gifts/gift-astronaut.png"}
              />
              <span
                className={"position-absolute pt-4 text-white fw-bold"}
                style={{ top: "0px", right: "0px", fontSize: "2.5rem", paddingRight: "2.5rem" }}
              >
                {this._getDaysLeftString()}
              </span>
            </div>

            <form onSubmit={(event) => event.preventDefault()}>
              <div className="row mb-2 pt-4">
                <div className="col">
                  <div className="form-group">
                    <label className="form-label fw-bold">Email</label>
                    <input
                      type="text"
                      className="verification-input"
                      name="email"
                      placeholder="Your friend’s email"
                      value={email}
                      onChange={(event: any) => {
                        this.setState({ email: event?.target.value });
                      }}
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="row mb-2 pt-3">
                <div className="col-12">
                  <div className="form-group">
                    <label className="form-label fw-bold">Write a custom message</label>
                    <div className={"position-relative"}>
                      <textarea
                        style={{ resize: "none" }}
                        className="verification-input h-auto"
                        rows={7}
                        name="message"
                        placeholder={getDefaultGiftEmail(user.currency, locale)}
                        value={message}
                        onChange={(event: any) => {
                          this.setState({ message: event?.target.value });
                        }}
                        required={false}
                        maxLength={MESSAGE_MAX_CHARACTER_COUNT}
                      />
                      <p
                        className={"position-absolute text-muted mb-0 p-2"}
                        style={{ right: "0px", bottom: "0px", opacity: "0.8" }}
                      >{`${characterCount}/${MESSAGE_MAX_CHARACTER_COUNT}`}</p>
                    </div>
                  </div>
                </div>
              </div>
            </form>
            <p className={"text-muted t-875 pt-4"}>
              By sending a gift, you confirm that you agree to our{" "}
              <a
                className="fw-bolder text-decoration-none"
                href={LegalDocumentUtil.getLegalPageUrls(user.companyEntity).PlatformInvestorTerms}
                target="_blank"
                rel="noreferrer"
              >
                Terms
              </a>
              . You can only send gifts to friends who haven’t yet invested with Wealthyhood and has no pending
              gifts.
            </p>
            <div className={"d-flex justify-content-center pt-3"}>
              {this._isFormFilled() ? (
                <LoadingOnSubmitButton
                  type="button"
                  className={"btn btn-primary w-100"}
                  customonclick={this._sendGift}
                >
                  Send a {formatCurrency(20, user.currency, locale, 0, 0)} gift
                </LoadingOnSubmitButton>
              ) : (
                <button type="button" className="btn btn-primary w-100" disabled={true}>
                  Send a {formatCurrency(20, user.currency, locale, 0, 0)} gift
                </button>
              )}
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default SendGiftPage;
