import React from "react";
import { PagePropsType } from "../types/page";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import SuccessLayout from "../layouts/successLayout";
import axios from "axios";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import ValidationInput from "../components/validationInput";

export type SetReferralCodePropsType = PagePropsType;

type StateType = {
  referralInput: string;
  showErrorMessage: boolean;
};

class SetReferralCodePage extends React.Component<SetReferralCodePropsType, StateType> {
  constructor(props: SetReferralCodePropsType) {
    super(props);

    this.state = {
      referralInput: undefined,
      showErrorMessage: false
    };
  }

  private _getReferralCodeFromInput = () => {
    const { referralInput } = this.state;

    return referralInput?.includes("wlthd=") ? referralInput?.split("wlthd=")?.[1] : referralInput;
  };

  private _isReferralCodeValid = () => {
    const referralCode = this._getReferralCodeFromInput();
    const referralCodeRegex = new RegExp("^[a-zA-Z0-9]{8}$");

    return referralCodeRegex.test(referralCode);
  };

  private _submitReferralCodeIfValid = async () => {
    if (!this._isReferralCodeValid()) {
      this.setState({ showErrorMessage: true });
      return;
    }

    const referralCode = this._getReferralCodeFromInput();

    try {
      await axios.post("/investor/set-referrer", {
        referralCode: referralCode
      });

      window.location.href = "/investor/open-account";
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  render(): JSX.Element {
    const { referralInput, showErrorMessage } = this.state;

    return (
      <SuccessLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        enableModals={true}
        imageUrl={"/images/icons/referral.png"}
        actionElement={
          <div className="p-0 fade-in">
            <div className="row m-0 mb-4 w-100 text-center">
              {referralInput && referralInput?.length > 0 ? (
                <LoadingOnSubmitButton
                  style={{ maxWidth: "100% !important" }}
                  type="button"
                  className="btn btn-primary fw-100"
                  customonclick={async () => this._submitReferralCodeIfValid()}
                  enableOnCompletion={true}
                >
                  Next
                </LoadingOnSubmitButton>
              ) : (
                <button
                  style={{ maxWidth: "100% !important" }}
                  type="button"
                  className="btn btn-primary fw-100"
                  disabled
                >
                  Next
                </button>
              )}
            </div>
            <div className="row m-0 w-100 text-center">
              <a
                href="/investor/open-account"
                className="text-primary text-decoration-none"
                style={{ maxWidth: "100% !important" }}
              >
                Skip
              </a>
            </div>
          </div>
        }
      >
        <div className="row m-0 p-0 mb-4 mt-4 text-center">
          <h3 className="fade-in fw-bolder">Promo code?</h3>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="p-0 fade-in">
            <p className="text-center text-muted px-3">
              If you have a promo code, please enter it below! If not, simply skip this step.
            </p>
          </div>
          <div className="form-group mt-4 px-0 mx-0 position-relative">
            <ValidationInput
              isValid={() => !referralInput || referralInput?.length === 0 || this._isReferralCodeValid()}
              onChange={(referralInput: any) => {
                this.setState({ referralInput: referralInput, showErrorMessage: false });
              }}
              value={referralInput}
              placeholder="Paste your promo code here"
              errorMessage="This promo code is invalid."
              required={true}
              showError={showErrorMessage}
            />
          </div>
        </div>
      </SuccessLayout>
    );
  }
}

export default SetReferralCodePage;
