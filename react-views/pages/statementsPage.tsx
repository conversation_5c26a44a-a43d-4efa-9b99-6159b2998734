import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import MainCard from "../layouts/mainCard";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import axios from "axios";
import { captureException } from "@sentry/react";
import StatementCustomDatePickerModal from "../components/modals/statementCustomDatePickerModal";
import { statementsConfig } from "@wealthyhood/shared-configs";

export type StatementsPropsType = {} & PagePropsType;

type StateType = {
  selectedTimePeriod: statementsConfig.StatementPeriodEnum;
  showCustomDatePickerModal: boolean;
};

class StatementsPage extends React.Component<StatementsPropsType, StateType> {
  constructor(props: StatementsPropsType) {
    super(props);
    this.state = {
      selectedTimePeriod: null,
      showCustomDatePickerModal: false
    };
  }

  private _downloadSelectedPeriod = async (): Promise<void> => {
    try {
      const { user } = this.props;
      const { selectedTimePeriod } = this.state;

      const periodCategoryConfig = statementsConfig.getStatementPeriodConfig(new Date(), user.residencyCountry);

      const selectedPeriodConfig: statementsConfig.StatementPeriodConfigType = periodCategoryConfig
        .flatMap((periodConfig) => periodConfig.entries)
        .find(({ key }) => key === selectedTimePeriod);

      let uri = "/investor/account-statements/generate";
      if (selectedPeriodConfig.startDate && selectedPeriodConfig.endDate) {
        uri += `?startDate=${selectedPeriodConfig.startDate}&endDate=${selectedPeriodConfig.endDate}`;
      } else if (selectedPeriodConfig.startDate) {
        uri += `?startDate=${selectedPeriodConfig.startDate}`;
      }

      const response = await axios.post(uri);

      window.open(response.data.fileUri, "_blank");
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _downloadCustomRange = async (startDate?: Date, endDate?: Date): Promise<void> => {
    try {
      const response = await axios.post(
        `/investor/account-statements/generate?startDate=${startDate}&endDate=${endDate}`
      );

      window.open(response.data.fileUri, "_blank");
    } catch (err) {
      captureException(err);
      emitToast({
        content: "Oops! Something went wrong. Please try again.",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _setSelectedTimePeriod = (selectedTimePeriod: statementsConfig.StatementPeriodEnum) => {
    this.setState({ selectedTimePeriod });
  };

  private _setShowCustomDatePickerModal = (showCustomDatePickerModal: boolean) => {
    this.setState({ showCustomDatePickerModal });
  };

  render(): JSX.Element {
    const { user } = this.props;
    const { selectedTimePeriod, showCustomDatePickerModal } = this.state;

    return (
      <MainLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        featureFlags={this.props.featureFlags}
      >
        {/* About Page */}
        <div className="row p-0 px-md-0 px-3 m-0 mb-4">
          <div className="col p-0">
            <h5 className="fw-bolder mb-4">Account statement</h5>
            <p className="text-muted">
              Get a summary of your activity. Your account statement will include, buys, sells, dividends,
              deposits, withdrawals and other transactions.
            </p>
          </div>
        </div>
        {/* End About Page */}

        <MainCard marginBottom={"160px"}>
          <div className="p-0 px-md-0 px-3 m-0 mb-4">
            <div
              className="row m-0 wh-account-card-option outline-hover mb-4"
              onClick={() => this._setShowCustomDatePickerModal(true)}
            >
              <div className="col-2 p-0 align-self-center text-center">
                <i className="material-symbols-outlined align-self-center wh-card" style={{ fontSize: "32px" }}>
                  event
                </i>
              </div>
              <div className="col-9 align-self-center">
                {/* Account Details */}
                <div className="d-flex flex-column">
                  <>
                    <span className="fw-bold text-truncate">Choose a custom date range</span>
                  </>
                </div>
                {/* End Account Details */}
              </div>
              <div className={"col-1 p-0 align-self-center text-center"}>
                <i className="material-symbols-outlined align-self-center" style={{ fontSize: "20px" }}>
                  chevron_right
                </i>
              </div>
            </div>
            <h5 className="fw-bolder mt-5 mb-4">Get a monthly or annual statement</h5>
            <div className="form-check d-flex flex-column ps-0">
              {statementsConfig
                .getStatementPeriodConfig(new Date(), user.residencyCountry)
                .map(({ category, entries }) => (
                  <div key={category}>
                    {entries.map((entry) => (
                      <div className={"d-flex align-items-center py-2 w-100"} key={entry.key}>
                        <label className="form-check-label w-100" htmlFor="option1">
                          <div className={"d-flex flex-column"}>
                            <div>{entry.title}</div>
                            <div className={"text-muted t-875"}>{entry.subtitle}</div>
                          </div>
                        </label>
                        <input
                          className="form-check-input"
                          style={{ zIndex: 1 }}
                          type="radio"
                          name="options"
                          id={entry.key}
                          checked={selectedTimePeriod === entry.key}
                          onChange={() =>
                            this._setSelectedTimePeriod(entry.key as statementsConfig.StatementPeriodEnum)
                          }
                        />
                      </div>
                    ))}
                    <hr style={{ color: "#F1F3FD", opacity: 1 }} />
                  </div>
                ))}
              <LoadingOnSubmitButton
                enableOnCompletion={true}
                className="btn btn-primary w-100 mw-100 mt-4"
                customonclick={this._downloadSelectedPeriod}
                disabled={!selectedTimePeriod}
              >
                Download
              </LoadingOnSubmitButton>
            </div>
          </div>
        </MainCard>

        <StatementCustomDatePickerModal
          show={showCustomDatePickerModal}
          handleClose={(): void => this._setShowCustomDatePickerModal(false)}
          handleDownload={(startDate, endDate) => this._downloadCustomRange(startDate, endDate)}
        />
      </MainLayout>
    );
  }
}

export default StatementsPage;
