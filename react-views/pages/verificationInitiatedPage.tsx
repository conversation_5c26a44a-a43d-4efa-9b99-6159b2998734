import React from "react";
import { PagePropsType } from "../types/page";
import axios from "axios";
import { captureException } from "@sentry/react";
import BaseLayout from "../layouts/baseLayout";

export type VerificationInitiatedPropsType = PagePropsType;

const POLL_INTERVAL = 4000;
const TIMEOUT = 90000;

class VerificationInitiatedPage extends React.Component<VerificationInitiatedPropsType> {
  private _pollingTimer: NodeJS.Timeout;
  private _timeout: NodeJS.Timeout;

  componentDidMount(): void {
    this._pollingTimer = setTimeout(() => this._triggerVerification(), POLL_INTERVAL);
    this._timeout = setTimeout(() => this._showTimeoutExceeded(), TIMEOUT);
  }

  componentWillUnmount() {
    this._stopPolling();
  }

  private _stopPolling = () => {
    clearInterval(this._pollingTimer);
    clearTimeout(this._timeout);
    this._pollingTimer = null;
    this._timeout = null;
  };

  private _showTimeoutExceeded = () => {
    this._stopPolling();

    window.location.replace("/investor/verification-pending");
  };

  private _triggerVerification = async () => {
    try {
      const res = await axios.post("/investor/verify");
      if (res?.data?.status === "verified") {
        this._stopPolling();
        window.location.href = "/investor/verification-success";
        return;
      }
    } catch (err) {
      captureException(err);
    }

    this._pollingTimer = setTimeout(() => this._triggerVerification(), POLL_INTERVAL);
  };

  render(): JSX.Element {
    return (
      <BaseLayout user={this.props.user}>
        <div className={"container-fluid p-0 vh-100 bg-primary"}>
          <div className="row mt-md-5 ms-md-5 mb-md-0 mt-4 mb-4">
            <span className="d-none d-sm-block">
              <img alt="" src="/images/icons/wealthyhood-logo.svg" />
            </span>
            <span className="d-flex justify-content-center align-self-center d-block d-sm-none">
              <img alt="" src="/images/icons/wealthyhood-logo.svg" />
            </span>
          </div>
          <div className="row justify-content-center m-0 h-100 w-100">
            <div className="d-flex justify-content-center align-self-center p-md-0 p-3 mb-5">
              <div className="text-light onboarding-container mb-5">
                <h1 className="fw-bold mb-3 p-0 w-100">{"We’re now verifying your details!"}</h1>
                <h4 className="p-0 mb-5">{"This may take a minute..."}</h4>
                <span className="bar-loader mt-3" />
              </div>
            </div>
          </div>
        </div>
      </BaseLayout>
    );
  }
}

export default VerificationInitiatedPage;
