import React from "react";
import { PagePropsType } from "../types/page";
import axios from "axios";
import { captureException } from "@sentry/react";
import SuccessLayout from "../layouts/successLayout";
import IntercomUtil from "../utils/intercomUtil";
import { UserDocument } from "../../models/User";

export type VerificationPendingPropsType = PagePropsType;

const POLL_INTERVAL = 4000;

class VerificationPendingPage extends React.Component<VerificationPendingPropsType> {
  private _pollingTimer: NodeJS.Timeout;

  componentDidMount(): void {
    this._pollingTimer = setTimeout(() => this._triggerVerification(), POLL_INTERVAL);
  }

  componentWillUnmount() {
    this._stopPolling();
  }

  private _stopPolling = () => {
    clearInterval(this._pollingTimer);
    this._pollingTimer = null;
  };

  private _triggerVerification = async () => {
    try {
      const res = await axios.get<UserDocument>("/investor/me?populate=portfolios");
      if (res?.data?.isVerified) {
        this._stopPolling();
        window.location.href = "/investor/verification-success";
        return;
      }
    } catch (err) {
      captureException(err);
    }

    this._pollingTimer = setTimeout(() => this._triggerVerification(), POLL_INTERVAL);
  };

  private _getActionElement(): JSX.Element {
    return (
      <button
        onClick={() => IntercomUtil.show()}
        className="btn btn-primary w-100"
        style={{ maxWidth: "100% !important" }}
      >
        Chat with us
      </button>
    );
  }

  private _getContent(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">Verification is taking longer than expected...</h3>
        <p className="p-0 text-center text-muted mb-4">
          We’ll notify you by email once your account has been verified.
        </p>
      </>
    );
  }

  render(): JSX.Element {
    return (
      <SuccessLayout user={this.props.user} actionElement={this._getActionElement()} enableIntercom>
        <div className="d-flex justify-content-center align-self-center mb-5">
          <img src={"/images/icons/schedule.svg"} />
        </div>
        {this._getContent()}
      </SuccessLayout>
    );
  }
}

export default VerificationPendingPage;
