import React from "react";
import { PagePropsType } from "../types/page";
import SuccessLayout from "../layouts/successLayout";

export type VerificationSuccessPropsType = PagePropsType;

class VerificationSuccessPage extends React.Component<VerificationSuccessPropsType> {
  private _getActionElement(): JSX.Element {
    return (
      <button
        onClick={() => (window.location.href = "/")}
        className="btn btn-primary w-100"
        style={{ maxWidth: "100% !important" }}
      >
        Let’s go!
      </button>
    );
  }

  private _getContent(): JSX.Element {
    return (
      <>
        <h3 className="text-center fw-bold mb-md-5 mb-4">We have successfully verified your details!</h3>
      </>
    );
  }

  render(): JSX.Element {
    return (
      <SuccessLayout user={this.props.user} actionElement={this._getActionElement()}>
        <div className="d-flex justify-content-center align-self-center mb-5">
          <img src={"/images/icons/check_circle.svg"} />
        </div>
        {this._getContent()}
      </SuccessLayout>
    );
  }
}

export default VerificationSuccessPage;
