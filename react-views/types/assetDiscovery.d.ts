import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

export type TopMoverCellType = {
  asset: investmentUniverseConfig.AssetType;
  returnPercentage: number;
};

export type TopMoversType = {
  best: TopMoverCellType[];
  worst: TopMoverCellType[];
};
type AssetCollectionsType = Record<
  investmentUniverseConfig.InvestmentCollectionType,
  investmentUniverseConfig.AssetCollectionConfigType
>;
type EtfSectionType = {
  top: investmentUniverseConfig.AssetType[];
  popularIndex: investmentUniverseConfig.AssetType[];
};

type AssetDiscoveryDataType = {
  topMovers: TopMoversType;
  collections: AssetCollectionsType;
  popularAssetsSection: investmentUniverseConfig.AssetType[];
  etfSection: EtfSectionType;
};
