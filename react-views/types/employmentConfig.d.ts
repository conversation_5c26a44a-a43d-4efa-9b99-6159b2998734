export type EmploymentInfoConfiguration = {
  employmentStatuses: {
    id: string;
    label: string;
  }[];
  industries: {
    id: string;
    label: string;
  }[];
  sourcesOfWealth: {
    id: string;
    label: string;
  }[];
  incomeRanges: {
    id: string;
    label: string;
  }[];
  employmentStatusThatRequireIndustry: string[];
};

type EmploymentInfoType = {
  incomeRangeId: string;
  sourcesOfWealth: string[];
  industry?: string;
  annualIncome: number;
  employmentStatus: string;
};
