import { TenorEnum } from "./../configs/durationConfig";

export type InvestmentProductPriceDataPointType = {
  timestamp: number;
  close: number;
  data?: { type: "buy" | "sell" | "net"; quantity: number };
};

export type InvestmentProductPricesType = {
  data: InvestmentProductPriceDataPointType[];
  returns: number;
  displayIntraday: boolean;
};

export type InvestmentProductPricesByTenor = Record<TenorEnum, InvestmentProductPricesType>;
