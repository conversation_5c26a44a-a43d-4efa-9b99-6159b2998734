import { GiftDocument } from "../../models/Gift";
import { RewardDocument } from "../../models/Reward";
import { WealthyhoodDividendTransactionDocument } from "../../models/Transaction";

/**
 * Modal Prompt Data Types
 */
export type GiftModalPromptDataType = {
  gifts?: GiftDocument[];
};

export type RewardModalPromptDataType = {
  rewards?: RewardDocument[];
};

export type WealthyhoodDividendModalPromptDataType = {
  wealthyhoodDividends?: WealthyhoodDividendTransactionDocument[];
};

export type AppRatingPromptModalPromptDataType = {
  appRatingId?: string;
};

/**
 * Modal Types
 */
export const ModalTypeArray = [
  "Gift",
  "Reward",
  "WealthyhoodDividend",
  "AppRatingPrompt",
  "RewardSettled"
] as const;

export type ModalTypeType = typeof ModalTypeArray[number];

/**
 * Main Modal Prompt Type
 */
export type ModalPromptType = {
  order: number;
  modalType: ModalTypeType;
  data:
    | GiftModalPromptDataType
    | RewardModalPromptDataType
    | WealthyhoodDividendModalPromptDataType
    | AppRatingPromptModalPromptDataType;
};
