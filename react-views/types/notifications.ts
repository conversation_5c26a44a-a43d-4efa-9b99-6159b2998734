export enum AppNotificationSettingEnum {
  TRANSACTIONAL = "app_transactional",
  LEARNING_GUIDE = "app_learning_guide",
  ANALYST_INSIGHT = "app_analyst_insight",
  QUICK_TAKE = "app_quick_take",
  WEEKLY_REVIEW = "app_weekly_review",
  DAILY_RECAP = "app_daily_recap",
  PROMOTIONAL = "app_promotional"
}

export enum EmailNotificationSettingEnum {
  TRANSACTIONAL = "email_transactional",
  PROMOTIONAL = "email_promotional",
  WEALTHYBITES = "email_wealthybites"
}

export type NotificationSettingEnum = AppNotificationSettingEnum | EmailNotificationSettingEnum;

export type Notification<T> = {
  id: T;
  name: string;
  description: string;
  active: boolean;
};

export type NotificationSettingsCategory<T> = {
  category: string;
  notifications: Notification<T>[];
};
