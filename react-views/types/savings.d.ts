import { currenciesConfig, plansConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import {
  SavingsDividendTransactionDocument,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument
} from "../../models/Transaction";

export type UserSavingsItemType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  netInterestRate: string;
  currency: currenciesConfig.MainCurrencyType;
  displayUnrealisedInterest?: string;
  savingsAmount: number;
  displaySavingsAmount: string;
};

type SavingsProductHighlightItemType = {
  label: string;
  value: string;
};

export type SavingsProductHighlightsSectionType = {
  oneDayYieldGross: SavingsProductHighlightItemType;
  oneDayYieldNet: SavingsProductHighlightItemType;
  earnedLastMonth: SavingsProductHighlightItemType;
  lifetimeEarnings: SavingsProductHighlightItemType;
};

export type SavingsProductInformationSectionType = {
  fundName: string;
  fundManager: string;
  isin: string;
  benchmark: string;
  baseCurrency: currenciesConfig.MainCurrencyType;
  income: savingsUniverseConfig.SavingsProductIncomeType;
  distribution: savingsUniverseConfig.SavingsProductDistributionType;
};

export type SavingsProductFundQualitySectionType = {
  rating: string;
  creditRatings: { label: string; rating: string }[];
  ratingSubtitle: string;
  risk: savingsUniverseConfig.SavingsProductRiskType;
};

export type SavingsProductDataType = {
  highlightsSection: SavingsProductHighlightsSectionType;
  informationSection: SavingsProductInformationSectionType;
  fundQualitySection: SavingsProductFundQualitySectionType;
};

type SavingsToCashTranferType = {
  type: "savingsToCash";
  item: SavingsWithdrawalTransactionDocument;
};

type CashToSavingsTranferType = {
  type: "cashToSavings";
  item: SavingsTopupTransactionDocument;
};

type BankDepositToSavingsTranferType = {
  type: "bankDepositToSavings";
  item: SavingsTopupTransactionDocument;
};

type SavingsInterestType = {
  type: "savingsInterest";
  item: SavingsDividendTransactionDocument;
};

export type SavingsProductActivityItemType =
  | BankDepositToSavingsTranferType
  | CashToSavingsTranferType
  | SavingsToCashTranferType
  | SavingsInterestType;

export type FeeDetailsPerPlanType = {
  fundManagerAnnualFeePercentage: string;
  wealthyhoodAnnualFeePercentage: string;
  netInterestRate: string;
  netInterestRateValue: number;
  plan: plansConfig.PlanType;
};
export type SavingsProductFeeDetailsType = {
  grossInterestRate: string;
  netInterestRateOfCurrentPlan?: string;
  planColumnLabel: string;
  fundManagerFeeColumnLabel: string;
  wealthyhoodFeeColumnLabel: string;
  netInterestRateColumnLabel: string;
  feeDetails: FeeDetailsPerPlanType[];
};

export type SavingsProductFeeDetailsWithIdType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
} & SavingsProductFeeDetailsType;
