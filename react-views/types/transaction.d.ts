import { TransactionDocument } from "../../models/Transaction";
import { RewardActivityFilterEnum, RewardDocument } from "../../models/Reward";
import { CashActivityFilterEnum, InvestmentActivityFilterEnum } from "../configs/activityConfig";

export type TransactionActivityItemType =
  | TransactionActivityRewardItemType
  | TransactionActivityTransactionItemType;

type TransactionActivityFilterType = InvestmentActivityFilterEnum | CashActivityFilterEnum;

type TransactionActivityRewardItemType = {
  type: "reward";
  item: RewardDocument;
  activityFilter: RewardActivityFilterEnum;
};

export type TransactionActivityTransactionItemType = {
  type: "transaction";
  item: TransactionDocument;
  activityFilter: TransactionActivityFilterType;
  cashFlowSign?: number;
};
