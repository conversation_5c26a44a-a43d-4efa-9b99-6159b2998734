import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { PartialRecord } from "./utils";
import { ExecutionWindowsType } from "../../models/Transaction";

export type OrderPreviewType = {
  side: "buy" | "sell";
  quantity?: number;
  money?: number;
};

export type OrdersPreviewType = {
  [key in investmentUniverseConfig.AssetType]?: OrderPreviewType;
};

export interface DualExecutionValue<T> {
  express?: T;
  smart?: T;
}

export interface TransactionPreview {
  executionWindow?: DualExecutionValue<ExecutionWindowsType>;
  fees?: DualExecutionValue<FeesType>;
  orders: DualExecutionValue<OrdersPreviewType>;
  foreignCurrencyRates?: ForeignCurrencyRatesType;
  willSkipOrders?: boolean;
  cashback?: number;
  willResultInLowQuantityHolding?: boolean;
  hasETFOrders?: boolean;
}

/**
 * TYPES
 */
export type FeesType = {
  fx: FeeType;
  commission?: FeeType;
  realtimeExecution?: FeeType;
};

export type FeeType = {
  currency: string;
  amount: number; // Stored in whole units (not cents)
};

export type ForeignCurrencyRatesType = PartialRecord<currenciesConfig.MainCurrencyType, number>;
