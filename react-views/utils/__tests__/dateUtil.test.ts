import {
  getEveryMonthOnTheXthDateString,
  getEveryXthOfTheMonthDateString,
  getMonthlyOnTheXthDateString
} from "../dateUtil";

describe("DateUtil", () => {
  describe("getEveryXthOfTheMonthDateString", () => {
    it("should return 'Every 27th of the month' for 2022-07-27", () => {
      expect(getEveryXthOfTheMonthDateString({ capitalFirst: true }, 27)).toEqual("Every 27th of the month");
    });

    it("should return 'every 27th of the month' for 2022-07-27 and capitalFirst: false", () => {
      expect(getEveryXthOfTheMonthDateString({ capitalFirst: false }, 27)).toEqual("every 27th of the month");
    });

    it("should return 'Every month on the last day' for 2022-07-29 and capitalFirst: true", () => {
      expect(getEveryXthOfTheMonthDateString({ capitalFirst: true }, -1)).toEqual("Every month on the last day");
    });

    it("should return 'every month on the last day' for 2022-07-29 and capitalFirst: false", () => {
      expect(getEveryXthOfTheMonthDateString({ capitalFirst: false }, -1)).toEqual("every month on the last day");
    });
  });

  describe("getMonthlyOnTheXthDateString", () => {
    it("should return 'Monthly on the 27th' for 2022-07-27", () => {
      expect(getMonthlyOnTheXthDateString({ capitalFirst: true }, 27)).toEqual("Monthly on the 27th");
    });

    it("should return 'monthly on the 27th' for 2022-07-27 and capitalFirst: false", () => {
      expect(getMonthlyOnTheXthDateString({ capitalFirst: false }, 27)).toEqual("monthly on the 27th");
    });

    it("should return 'Monthly on the last day' for 2022-07-29 and capitalFirst: true", () => {
      expect(getMonthlyOnTheXthDateString({ capitalFirst: true }, -1)).toEqual("Monthly on the last day");
    });

    it("should return 'monthly on the last day' for 2022-07-29 and capitalFirst: false", () => {
      expect(getMonthlyOnTheXthDateString({ capitalFirst: false }, -1)).toEqual("monthly on the last day");
    });
  });

  describe("getEveryMonthOnTheXthDateString", () => {
    it("should return 'Every month on the 27th' for 2022-07-27", () => {
      expect(getEveryMonthOnTheXthDateString({ capitalFirst: true }, 27)).toEqual("Every month on the 27th");
    });

    it("should return 'every month on the 27th' for 2022-07-27 and capitalFirst: false", () => {
      expect(getEveryMonthOnTheXthDateString({ capitalFirst: false }, 27)).toEqual("every month on the 27th");
    });

    it("should return 'Every month on the last day' for 2022-07-29 and capitalFirst: true", () => {
      expect(getEveryMonthOnTheXthDateString({ capitalFirst: true }, -1)).toEqual("Every month on the last day");
    });

    it("should return 'every month on the last day' for 2022-07-29 and capitalFirst: false", () => {
      expect(getEveryMonthOnTheXthDateString({ capitalFirst: false }, -1)).toEqual("every month on the last day");
    });
  });
});
