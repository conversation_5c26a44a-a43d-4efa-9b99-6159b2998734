import { banksConfig } from "@wealthyhood/shared-configs";

export default class BanksUtil {
  public static getEuropeanBankFullAddress(): string {
    return `${banksConfig.EU_BANK_DETAILS.bank.address.number} ${banksConfig.EU_BANK_DETAILS.bank.address.street}, ${banksConfig.EU_BANK_DETAILS.bank.address.city}, ${banksConfig.EU_BANK_DETAILS.bank.address.postCode}, ${banksConfig.EU_BANK_DETAILS.bank.address.country}`;
  }
}
