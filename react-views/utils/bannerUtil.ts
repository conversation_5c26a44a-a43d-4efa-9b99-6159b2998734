import {
  AlwaysVisibleIfAvailableBannersArray,
  BannerEnum,
  LearningGuideBannersArray,
  PlanPromotionBannersArray
} from "../configs/bannerConfig";

export function getBannerCookieName(bannerId: BannerEnum): string {
  return `SEEN_${BannerEnum[bannerId]}_COOKIE_KEY`;
}

export function getHideBannerDurationInDays(id: BannerEnum): number {
  if (PlanPromotionBannersArray.includes(id)) return 7;
  else if (LearningGuideBannersArray.includes(id)) return 3;
  else if (AlwaysVisibleIfAvailableBannersArray.includes(id)) return 0;
  else if (id === BannerEnum.EarnFreeShares) return 15;
  else return 0;
}
