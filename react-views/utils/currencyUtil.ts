import { currenciesConfig, localeConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "../../models/User";

const MAXIMUM_CURRENCY_DIGITS = 2;

/**
 * Formats currency to British pounds.
 *
 * @param money
 * @param currency
 * @param locale
 * @param minDigits
 * @param maxDigits
 * @param alwaysDisplaySign
 * @returns
 */
export function formatCurrency(
  money: number,
  currency: currenciesConfig.MainCurrencyType,
  locale: localeConfig.LocaleType,
  minDigits?: number,
  maxDigits?: number,
  alwaysDisplaySign?: boolean
): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: minDigits ?? MAXIMUM_CURRENCY_DIGITS,
    maximumFractionDigits: maxDigits ?? MAXIMUM_CURRENCY_DIGITS,
    signDisplay: alwaysDisplaySign ? "always" : "auto"
  }).format(money);
}

export function getUserCurrencyWithLocaleFallback(
  user: UserDocument,
  locale: string
): currenciesConfig.MainCurrencyType {
  if (user && user.currency) {
    return user.currency;
  }

  const currencyMap: Record<string, currenciesConfig.MainCurrencyType> = {
    "en-GB": "GBP"
  };

  // We default user locale to EUR if not set to above.
  return currencyMap[locale] || "EUR";
}
