/**
 * @description Takes a string of format dd/mm/yyyy and converts it to date
 * NOTE: we don't want to make the conversion to date in the method, because this runs on the browser
 * and we cannot control whether the date will be created properly. For earlier versions of node it will
 * convert the date to ISO format taking into account the time difference with the users location and this results
 * in a wrong date.
 * @param friendlyDate
 */
export function dateFriendlyFormatToISO(friendlyDate: string): string {
  const [days, months, years] = (friendlyDate || "").split("/");
  return `${years}/${months}/${days}`;
}

/**
 * @description
 * Returns difference between two dates in days.
 * Input dates are bing normalized to utc.
 *
 * @param firstDate
 * @param secondDate
 */
export function dateDiffInDays(firstDate: Date, secondDate: Date): number {
  const dayInMilliSeconds = 1000 * 60 * 60 * 24;
  // Discard the time and time-zone information.
  const utc1 = Date.UTC(firstDate.getFullYear(), firstDate.getMonth(), firstDate.getDate());
  const utc2 = Date.UTC(secondDate.getFullYear(), secondDate.getMonth(), secondDate.getDate());

  return Math.floor((utc2 - utc1) / dayInMilliSeconds);
}

export function datesAreEqual(date: Date, anotherDate: Date) {
  return (
    date.getDate() == anotherDate.getDate() &&
    date.getMonth() == anotherDate.getMonth() &&
    date.getFullYear() == anotherDate.getFullYear()
  );
}

export function getDateAfterNdays(date: Date, days: number): Date {
  const dateAfterNdays = new Date(date);
  dateAfterNdays.setDate(date.getDate() + days);
  dateAfterNdays.setHours(0, 0, 0, 0);

  return dateAfterNdays;
}

/**
 * Returns string in format of 'Every 27th of the month' except for the 29th, 30th and 31st of the month, where it
 * returns 'Every month on the last day'
 * @param options
 * @param recurrenceDate takes a value of 1-28 or -1 if 29, 30, 31
 */
export function getEveryXthOfTheMonthDateString(
  options: { capitalFirst: boolean } = { capitalFirst: false },
  recurrenceDate: number = convertIntoRecurrenceDate(new Date(Date.now()))
): string {
  const formattedString =
    recurrenceDate === -1 ? "every month on the last day" : `every ${getDateOrdinal(recurrenceDate)} of the month`;

  if (options.capitalFirst) {
    return formattedString.charAt(0).toUpperCase() + formattedString.slice(1);
  } else return formattedString;
}

/**
 * Returns string in format of 'Monthly on the 27th' except for the 29th, 30th and 31st of the month, where it
 * returns 'Monthly on the last day'
 * @param options
 * @param recurrenceDate
 */
export function getMonthlyOnTheXthDateString(
  options: { capitalFirst: boolean } = { capitalFirst: false },
  recurrenceDate: number = convertIntoRecurrenceDate(new Date(Date.now()))
): string {
  const formattedString =
    recurrenceDate === -1 ? "monthly on the last day" : `monthly on the ${getDateOrdinal(recurrenceDate)}`;

  if (options.capitalFirst) {
    return formattedString.charAt(0).toUpperCase() + formattedString.slice(1);
  } else return formattedString;
}

/**
 * Returns string in format of 'On the 27th' except for the 29th, 30th and 31st of the month, where it
 * returns 'On the last day'
 * @param options
 * @param recurrenceDate
 */
export function getOnTheXthDateString(
  options: { capitalFirst: boolean } = { capitalFirst: false },
  recurrenceDate: number = convertIntoRecurrenceDate(new Date(Date.now()))
): string {
  const formattedString = recurrenceDate === -1 ? "on the last day" : `on the ${getDateOrdinal(recurrenceDate)}`;

  if (options.capitalFirst) {
    return formattedString.charAt(0).toUpperCase() + formattedString.slice(1);
  } else return formattedString;
}

/**
 * Returns string in format of 'Every month on the 27th' except for the 29th, 30th and 31st of the month, where it
 * returns 'Every month on the last day'
 * @param options
 * @param recurrenceDate
 */
export function getEveryMonthOnTheXthDateString(
  options: { capitalFirst: boolean } = { capitalFirst: false },
  recurrenceDate: number = convertIntoRecurrenceDate(new Date(Date.now()))
): string {
  const formattedString =
    recurrenceDate === -1 ? "every month on the last day" : `every month on the ${getDateOrdinal(recurrenceDate)}`;

  if (options.capitalFirst) {
    return formattedString.charAt(0).toUpperCase() + formattedString.slice(1);
  } else return formattedString;
}

/**
 * Converts into a day of month, 1-28 or -1 if date is 29, 30 or 31.
 * @param date
 */
export function convertIntoRecurrenceDate(date: Date): number {
  return date.getDate() <= 28 ? date.getDate() : -1;
}

/**
 * Returns the date ordinal as a string i.e. 1st, 2nd, 3rd etc.
 * @param dayOfMonth
 */
export function getDateOrdinal(dayOfMonth: number): string {
  return (
    dayOfMonth +
    (dayOfMonth > 0
      ? ["th", "st", "nd", "rd"][(dayOfMonth > 3 && dayOfMonth < 21) || dayOfMonth % 10 > 3 ? 0 : dayOfMonth % 10]
      : "")
  );
}

/**
 * @description Checks if a date is valid
 * @param date
 * @returns
 */
export function dateIsValid(date: Date): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}

export function isOverEighteen(birthDate: Date) {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const month = today.getMonth() - birthDate.getMonth();
  // check if 12 months have passed
  if (month < 0 || (month === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age >= 18;
}

/**
 * @description Takes a valid date string and converts it to dd/mm/yyyy format
 * @param date
 */
export function isoDateToFriendlyFormat(date: string): string {
  const days = new Date(date).toLocaleDateString("en-GB", { day: "2-digit" });
  const months = new Date(date).toLocaleDateString("en-GB", { month: "2-digit" });
  const years = new Date(date).toLocaleDateString("en-GB", { year: "numeric" });
  return `${days}/${months}/${years}`;
}

export function formatDateToDDMONYYYY(date: Date, options?: { separatorCharacter: string }): string {
  const DEFAULT_SEPARATOR_CHARACTER = " ";

  const formattedDate = new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric"
  });

  if (options?.separatorCharacter && options?.separatorCharacter !== DEFAULT_SEPARATOR_CHARACTER) {
    return formattedDate.replace(/ /g, options?.separatorCharacter);
  }

  return formattedDate;
}

export function formatDateToDDMON(date: Date): string {
  return formatDateToDDMONYYYY(date).substr(0, 6).trim();
}

export function formatDateToDDMONYYHHMM(date: Date): string {
  const newDate = new Date(date);
  return `${newDate.getDate()} ${newDate.toLocaleString("default", { month: "short" })} ${newDate
    .getFullYear()
    .toString()}, ${newDate.getHours() < 10 ? "0" + newDate.getHours() : newDate.getHours()}:${
    newDate.getMinutes() < 10 ? "0" + newDate.getMinutes() : newDate.getMinutes()
  }`;
}

export function formatDateToDDMONYY(date: Date): string {
  return new Date(date).toLocaleDateString("en-GB", { day: "2-digit", month: "short", year: "2-digit" });
}

export function formatDateToMONYY(date: Date): string {
  const shortMonth = date.toLocaleString("default", { month: "short" });
  const twoDigitYear = date.toLocaleString("default", { year: "2-digit" });

  return `${shortMonth} ${twoDigitYear}`;
}
export function formatDateToMONYYYY(date: Date): string {
  const shortMonth = date.toLocaleString("default", { month: "short" });
  const fourDigitYear = date.toLocaleString("default", { year: "numeric" });

  return `${shortMonth} ${fourDigitYear}`;
}

/**
 * Formats a string such as '2023-11' to 'Nov 23'
 * @param yearAndMonth
 */
export function formatYYYYMMToMONYY(yearAndMonth: string): string {
  const date = new Date(`${yearAndMonth}-01T00:00:00`);

  const shortMonth = date.toLocaleString("default", { month: "short" });
  const twoDigitYear = date.toLocaleString("default", { year: "numeric" }).substring(2);

  return `${shortMonth} ${twoDigitYear}`;
}

export function formatDateToHHMMSS(date: Date): string {
  return `${date.getHours().toLocaleString("en-US", {
    minimumIntegerDigits: 2,
    useGrouping: false
  })}:${date.getMinutes().toLocaleString("en-US", {
    minimumIntegerDigits: 2,
    useGrouping: false
  })}:${date.getSeconds().toLocaleString("en-US", {
    minimumIntegerDigits: 2,
    useGrouping: false
  })}`;
}

export function formatDateToDayDashHHMM(date: Date): string {
  if (isToday(date)) {
    return "Today, " + formatDateToHHMMSS(date).substr(0, 5);
  } else if (isTomorrow(date)) {
    return "Tomorrow, " + formatDateToHHMMSS(date).substr(0, 5);
  } else {
    return formatDateToDDMONYYHHMM(date);
  }
}

export function formatDateToYYYYMMDD(date: Date) {
  return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
}

export function getDateXdaysAgo(daysAgo: number) {
  return new Date(new Date().setDate(new Date().getDate() - daysAgo));
}

export function isToday(date: Date): boolean {
  const today = new Date();

  return today.toDateString() == date.toDateString();
}

export function isTomorrow(date: Date): boolean {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1); // Set to tomorrow's date

  return (
    tomorrow.toDateString() === date.toDateString() // Compare to the input date
  );
}

export function getDaysDiff(date1: Date, date2: Date): number {
  if (!date1 || !date2) {
    return 0;
  }

  const _MS_PER_DAY = 1000 * 60 * 60 * 24;
  // Discard the time and time-zone information.
  const utc1 = Date.UTC(date1.getFullYear(), date1.getMonth(), date1.getDate());
  const utc2 = Date.UTC(date2.getFullYear(), date2.getMonth(), date2.getDate());

  return Math.floor((utc2 - utc1) / _MS_PER_DAY);
}

export function isWeekend(date = new Date(Date.now())): boolean {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 6 || dayOfWeek === 0; // 6 = Saturday, 0 = Sunday
}

export function getRelativeTime(createdAt: Date): string {
  const diffInDays = dateDiffInDays(createdAt, new Date());

  const diffInWeeks = diffInDays / 7;
  const diffInMonths = diffInDays / 30.44; // Approximate average days per month
  const diffInYears = diffInDays / 365.25; // Accounting for leap years
  if (diffInDays < 1) {
    return "Today";
  } else if (diffInDays < 2) {
    return "Yesterday";
  } else if (diffInDays < 7) {
    return `${Math.floor(diffInDays)} days ago`;
  } else if (diffInWeeks < 4) {
    if (diffInWeeks < 2) return "1 week ago";
    return `${Math.floor(diffInWeeks)} weeks ago`;
  } else if (diffInMonths < 12) {
    if (diffInMonths < 2) return "1 month ago";
    return `${Math.floor(diffInMonths)} months ago`;
  } else {
    return `${Math.floor(diffInYears)} years ago`;
  }
}

const DAY_IN_MILLISECONDS = 24 * 60 * 60 * 1000;
export function getDateOfDaysAgo(date: Date, days: number): Date {
  if (!date) return;

  if (days === Number.MAX_VALUE) {
    return new Date(0);
  }

  return new Date(date.getTime() - days * DAY_IN_MILLISECONDS);
}
