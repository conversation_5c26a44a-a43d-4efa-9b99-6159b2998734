import EventEmitter from "events";
import { EventToastBodyType } from "../types/event";
import { TOAST_DELAY } from "../configs/toastConfig";

export const EVENTS = {
  toast: "toastEvent",
  depositModal: "depositEvent",
  depositMethodsModal: "depositMethodsEvent",
  withdrawalModal: "withdrawalEvent",
  portfolioBuyModal: "portfolioBuyModalEvent",
  investmentProductModal: "investmentProductModal",
  cancelOrderModal: "cancelOrderModal",
  orderSuccessModal: "orderSuccessModal",
  assetBuyModal: "assetBuyModal",
  assetSellModal: "assetSellModal",
  linkBankAccountModal: "linkBankAccount",
  loadingSplashMask: "loadingSplash",
  pendingRewardsModal: "pendingRewards",
  setupRepeatingInvestmentModal: "setupRepeatingInvestmentModal",
  setupRepeatingSavingsModal: "setupRepeatingSavingsModal",
  promptToRepeatingInvestmentModal: "promptToRepeatingInvestmentModal",
  pendingGiftModal: "pendingGift",
  pendingWhDividendModal: "pendingWealthyhoodDividendModal",
  assetListModal: "assetListModal",
  savingsProductModal: "savingsProductModal",
  savingsTopupModal: "savingsTopupModal",
  savingsWithdrawalModal: "savingsWithdrawalModal",
  savingsProductFeesModal: "savingsProductFeesModal",
  bankAccountList: "bankAccountList"
};
export const eventEmitter = new EventEmitter.EventEmitter();

export function emitToast(toast: EventToastBodyType): void {
  eventEmitter.emit(EVENTS.toast, { delay: TOAST_DELAY, ...toast });
}
