import { ConfigCatFeatureFlags, ConfigCatFeatureStatusType } from "./../../config/featuresConfig";

export const isFeatureDisabled = (
  feat: ConfigCatFeatureFlags,
  featureFlags?: ConfigCatFeatureStatusType[]
): boolean => {
  return featureFlags ? featureFlags?.findIndex((flag) => flag.feat === feat && !flag.active) > -1 : true;
};

export const isFeatureEnabled = (
  feat: ConfigCatFeatureFlags,
  featureFlags?: ConfigCatFeatureStatusType[]
): boolean => {
  return featureFlags ? featureFlags?.findIndex((flag) => flag.feat === feat && flag.active) > -1 : false;
};
