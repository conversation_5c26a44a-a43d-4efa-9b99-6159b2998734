import Decimal from "decimal.js";
import { localeConfig } from "@wealthyhood/shared-configs";

const MAXIMUM_SHARES_DIGITS = 4;

const MINIMUM_PERCENTAGE_DIGITS = 0;
const MAXIMUM_PERCENTAGE_DIGITS = 2;

/**
 * Formats shares to 4 decimal places,
 * e.g. 4.03 becomes "4.0300" and 4.03009 becomes "4.0300".
 * The number returned is the floor of the parameter.
 * No rounding occurs.
 *
 * @param shares
 * @param locale
 * @returns
 */
export function formatShares(shares: number, locale: localeConfig.LocaleType): string {
  return new Decimal(shares)
    .toNumber()
    .toLocaleString(locale, { maximumFractionDigits: MAXIMUM_SHARES_DIGITS })
    .toString();
}

export function formatPercentage(
  num: number,
  locale: localeConfig.LocaleType,
  minDigits = MINIMUM_PERCENTAGE_DIGITS,
  maxDigits = MAXIMUM_PERCENTAGE_DIGITS
): string {
  // When the number has three decimals (e.g. 0.015 representing 1.5%, we want to force two fraction digits e.g. 1.5% becomes 1.50%
  const hasThreeDecimals = !Decimal.mod(num, 0.01).eq(0) && Decimal.mod(num, 0.001).eq(0);

  return num.toLocaleString(locale, {
    style: "percent",
    minimumFractionDigits: hasThreeDecimals ? 2 : minDigits,
    maximumFractionDigits: maxDigits
  });
}
