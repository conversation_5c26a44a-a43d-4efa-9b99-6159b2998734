/**
 *
 * This util class helps to deal with intercom api as
 * react-intercom library is a little mess regarding event handling.
 * If you follow the suggested approach of https://www.npmjs.com/package/react-intercom
 * for dealing with intercom api you will end up getting compilation errors.
 *
 * Intercom web doc : https://developers.intercom.com/installing-intercom/docs/intercom-javascript
 */
class IntercomUtil {
  public static show() {
    (window as any).Intercom("show");
  }

  public static hide() {
    (window as any).Intercom("hide");
  }

  public static showSpace(space: SpaceType) {
    (window as any).Intercom("showSpace", space);
  }

  public static showMessages() {
    (window as any).Intercom("showMessages");
  }

  public static showNewMessage() {
    (window as any).Intercom("showNewMessage");
  }
}

export type SpaceType = "home" | "messages" | "help" | "news" | "tasks";

export default IntercomUtil;
