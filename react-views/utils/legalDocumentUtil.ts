import { entitiesConfig, legalDocumentsConfig } from "@wealthyhood/shared-configs";

const { LegalDocumentUrlsByCompanyEntity, LegalPagesUrlsByCompanyEntity } = legalDocumentsConfig;

export default class LegalDocumentUtil {
  public static getLegalDocumentUrls(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Record<legalDocumentsConfig.LegalDocumentEnum, string> {
    return LegalDocumentUrlsByCompanyEntity[companyEntity];
  }

  public static getLegalPageUrls(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Record<legalDocumentsConfig.LegalDocumentEnum, string> {
    return LegalPagesUrlsByCompanyEntity[companyEntity];
  }
}
