import { DateTime } from "luxon";
import { MarketInfoType } from "./../components/modals/assetSideModal/assetSideModal.types";
import { datesAreEqual, getDateAfterNdays } from "./dateUtil";

export const getMarketInfoSubtitle = (marketInfo: MarketInfoType): string => {
  if (!marketInfo) {
    return "";
  }

  if (marketInfo.isOpen) {
    return "";
  }

  const nextMarketOpen = DateTime.fromMillis(marketInfo.nextMarketOpen).toLocal();
  const today = new Date();
  if (datesAreEqual(new Date(), nextMarketOpen.toJSDate())) {
    return `Market closed, order placed from ${nextMarketOpen.toFormat("HH:mm")} today.`;
  }

  if (datesAreEqual(getDateAfterNdays(today, 1), nextMarketOpen.toJSDate())) {
    return `Market closed, order placed from ${nextMarketOpen.toFormat("HH:mm")} tomorrow.`;
  }

  return `Market closed, order placed from ${nextMarketOpen.toFormat("HH:mm")} on ${nextMarketOpen.toFormat(
    "dd MMM yyyy"
  )}.`;
};
