import Decimal from "decimal.js";
import { cashbacksConfig, plansConfig } from "@wealthyhood/shared-configs";

const { CASHBACK_RATES, MINIMUM_AMOUNT_FOR_CASHBACK } = cashbacksConfig;

/**
 * @returns the cashback amount for the user (in cents) given an order amount (in cents).
 * @param orderAmount
 * @param plan
 */
export const getCashbackAmount = (orderAmount: number, plan: plansConfig.PlanType): number => {
  if (new Decimal(orderAmount).lessThan(MINIMUM_AMOUNT_FOR_CASHBACK)) {
    return 0;
  }

  return Decimal.mul(orderAmount, CASHBACK_RATES[plan]).toNumber();
};
