import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { InvestmentProductDocument } from "../../models/InvestmentProduct";
import { HoldingsType } from "../../models/Portfolio";

const { ASSET_CONFIG } = investmentUniverseConfig;
export type AllocationCategoryType = {
  assetCategory: {
    [key in investmentUniverseConfig.AssetCategoryType]?: number;
  };
  assets: {
    [key in investmentUniverseConfig.AssetType]?: number;
  };
};

/**
 * @description Equally weights the elements of an array for a given total sum weight.
 *
 * Weigths have to be integers, so the weighting logic is:
 * - Equally weight the components, with integer allocation rounded down
 * - The remaining has to add up to the required total sum, so split it to units of 1 on the first elements of
 * array, until there is no remaining allocation left.
 *
 * @param allocationSum The total sum that the elements' weights have to add up to
 * @param universeArray The array that describes the elements that have to be weighted
 * @returns A dictionary with keys they weighted elements and values the corresponding weights
 */
export const equallyWeightElements = (
  allocationSum: number,
  universeArray: investmentUniverseConfig.AssetType[] | investmentUniverseConfig.AssetClassType[]
): { [key in investmentUniverseConfig.AssetType | investmentUniverseConfig.AssetClassType]?: number } => {
  const allocationDict: {
    [key in investmentUniverseConfig.AssetType | investmentUniverseConfig.AssetClassType]?: number;
  } = {};

  // Set initial allocation to each element
  const componentAllocation = Math.floor(allocationSum / universeArray.length);
  universeArray.forEach(
    (componentKey: investmentUniverseConfig.AssetType | investmentUniverseConfig.AssetClassType) => {
      allocationDict[componentKey] = componentAllocation;
      allocationSum -= componentAllocation;
    }
  );

  // Split remaining allocation as units of 1 in first elements until there is no allocation left
  universeArray.forEach(
    (componentKey: investmentUniverseConfig.AssetType | investmentUniverseConfig.AssetClassType) => {
      if (allocationSum > 0) {
        allocationDict[componentKey] += 1;
        allocationSum -= 1;
      }
    }
  );

  return allocationDict;
};

/**
 *
 * @param holdings
 * @param sumTo100
 */
export const mapHoldingsToAllocationClassFormat = (
  holdings: HoldingsType[],
  userCurrency: currenciesConfig.MainCurrencyType,
  sumTo100?: boolean
): investmentUniverseConfig.AllocationType => {
  const assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number } = {};
  const investedAmount = holdings.reduce(
    (sum, { quantity, asset }) => quantity * asset.currentTicker.pricePerCurrency[userCurrency] + sum,
    0
  );

  const multiplier = sumTo100 ? 100 : 1;

  // 1. Calculate asset allocation
  holdings.forEach(({ quantity, asset, assetCommonId }) => {
    const assetValue = quantity * asset.currentTicker.pricePerCurrency[userCurrency];
    assetAllocation[assetCommonId] = (multiplier * assetValue) / investedAmount;
  });

  // 2. Use asset allocation to calculate asset class allocation
  const assetClassAllocation: { [key in investmentUniverseConfig.AssetClassType]?: number } = {};
  Object.keys(assetAllocation).forEach((assetKey: investmentUniverseConfig.AssetType) => {
    const assetClassKey = ASSET_CONFIG[assetKey].assetClass;
    if (assetClassAllocation[assetClassKey] > 0) {
      assetClassAllocation[assetClassKey] += assetAllocation[assetKey];
    } else {
      assetClassAllocation[assetClassKey] = assetAllocation[assetKey];
    }
  });

  return { assetClasses: assetClassAllocation, assets: assetAllocation };
};

export const mapHoldingsToAllocationCategoryFormat = (
  holdings: HoldingsType[],
  userCurrency: currenciesConfig.MainCurrencyType,
  sumTo100?: boolean
): AllocationCategoryType => {
  const assetAllocation: { [key in investmentUniverseConfig.AssetType]?: number } = {};
  const investedAmount = holdings.reduce(
    (sum, { quantity, asset }) => quantity * asset.currentTicker.pricePerCurrency[userCurrency] + sum,
    0
  );

  const multiplier = sumTo100 ? 100 : 1;

  // 1. Calculate asset allocation
  holdings.forEach(({ quantity, asset, assetCommonId }) => {
    const assetValue = quantity * asset.currentTicker.pricePerCurrency[userCurrency];
    assetAllocation[assetCommonId] = (multiplier * assetValue) / investedAmount;
  });

  // 2. Use asset allocation to calculate asset category allocation
  const assetCategoryAllocation: { [key in investmentUniverseConfig.AssetCategoryType]?: number } = {};
  Object.keys(assetAllocation).forEach((assetKey: investmentUniverseConfig.AssetType) => {
    const assetCategoryKey = ASSET_CONFIG[assetKey].category;
    if (assetCategoryAllocation[assetCategoryKey] > 0) {
      assetCategoryAllocation[assetCategoryKey] += assetAllocation[assetKey];
    } else {
      assetCategoryAllocation[assetCategoryKey] = assetAllocation[assetKey];
    }
  });

  return { assetCategory: assetCategoryAllocation, assets: assetAllocation };
};

export const holdingsArrayToDict = (
  holdingsArray: HoldingsType[]
): Record<investmentUniverseConfig.AssetType, HoldingsType> => {
  return Object.fromEntries(holdingsArray.map((holding) => [holding.assetCommonId, holding])) as Record<
    investmentUniverseConfig.AssetType,
    HoldingsType
  >;
};

export const assetsArrayToDict = (
  assetsArray: InvestmentProductDocument[]
): Record<investmentUniverseConfig.AssetType, InvestmentProductDocument> => {
  return Object.fromEntries(assetsArray.map((asset) => [asset.commonId, asset])) as Record<
    investmentUniverseConfig.AssetType,
    InvestmentProductDocument
  >;
};

export const assetsDictToArray = (
  assetDict: Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>
): InvestmentProductDocument[] => {
  return Object.values(assetDict);
};

export const allocationsMatch = (
  allocationA: { [key in investmentUniverseConfig.AssetType]?: number },
  allocationB: { [key in investmentUniverseConfig.AssetType]?: number }
): boolean => {
  // Check if both objects have the same number of keys
  const keys1 = Object.keys(allocationA);
  const keys2 = Object.keys(allocationB);
  if (keys1.length !== keys2.length) {
    return false;
  }

  // Check if all keys and values in allocationA are the same in allocationB
  for (const key of keys1) {
    if (
      allocationA[key as investmentUniverseConfig.AssetType] !==
      allocationB[key as investmentUniverseConfig.AssetType]
    ) {
      return false;
    }
  }

  return true;
};
