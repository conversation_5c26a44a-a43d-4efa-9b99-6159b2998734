export function capitalizeFirstLetter(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * @description Convert an object to a query string without the leading `?`.
 */
export function toQueryParams(params: Record<string, any>): string {
  const queryParams: string[] = [];

  for (const key in params) {
    if (params[key] !== null && params[key] !== undefined) {
      queryParams.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
    }
  }

  return queryParams.join("&");
}
