import {
  AssetTransactionDocument,
  ChargeTransactionDocument,
  TransactionDocument
} from "./../../models/Transaction";

export function isCustodyCharge(transaction: TransactionDocument): boolean {
  return (transaction as ChargeTransactionDocument)?.chargeType === "custody";
}

export function isSubscriptionCharge(transaction: TransactionDocument): boolean {
  return (transaction as ChargeTransactionDocument)?.chargeType === "subscription";
}

export function isSingleAssetTransaction(transaction: TransactionDocument): boolean {
  const assetTransaction = transaction as AssetTransactionDocument;

  return assetTransaction?.portfolioTransactionCategory === "update" && assetTransaction?.orders?.length === 1;
}
