import { investmentUniverseConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";
import { StockAssetConfigType } from "@wealthyhood/shared-configs/dist/investmentUniverse";
import { InvestmentUniverseAssets } from "../../utils/configUtil";

const { ASSET_CONFIG, ASSET_PROVIDER_CONFIG } = investmentUniverseConfig;

const doesAssetMatchSearchTerm = (
  asset: investmentUniverseConfig.AssetType,
  searchTerm: string,
  assetsToSearch: InvestmentUniverseAssets
) => {
  const searchTermInLowerCase = searchTerm.toLowerCase();
  const assetConfig = assetsToSearch[asset];
  if (
    assetConfig.simpleName.toLowerCase().includes(searchTermInLowerCase) ||
    assetConfig.shortDescription.toLowerCase().includes(searchTermInLowerCase) ||
    assetConfig.formalTicker.toLowerCase().includes(searchTermInLowerCase) ||
    assetConfig.searchTerms.some((term) => term.toLowerCase().includes(searchTermInLowerCase)) ||
    (assetConfig.assetClass === "equities" &&
      assetConfig.similarCompanies.some((term) => term.toLowerCase().includes(searchTermInLowerCase)))
  ) {
    return true;
  }

  if (assetConfig.category === "stock") {
    return (assetConfig as StockAssetConfigType).typos.some((term) =>
      term.toLowerCase().includes(searchTermInLowerCase)
    );
  }

  return false;
};
/**
 * Compare assets by following these 3 rules:
 * 1. ETFs have higher priority than Stocks
 * 2. ETFs use `sorting` from investment universe
 * 3. Stocks are sorted alphabetically
 *
 */
export const compareAssets = (
  a: investmentUniverseConfig.AssetType,
  b: investmentUniverseConfig.AssetType
): number => {
  const categoryA = ASSET_CONFIG[a]?.category;
  const categoryB = ASSET_CONFIG[b]?.category;

  if (categoryA !== categoryB) {
    return categoryA === "etf" ? -1 : 1;
  } else if (categoryA === "etf" && categoryB === "etf") {
    const etfA = ASSET_CONFIG[a] as investmentUniverseConfig.ETFAssetConfigType;
    const etfB = ASSET_CONFIG[b] as investmentUniverseConfig.ETFAssetConfigType;
    return etfA.sorting > etfB.sorting ? 1 : -1;
  } else if (categoryA === "stock" && categoryB === "stock") {
    const stockA = ASSET_CONFIG[a] as investmentUniverseConfig.StockAssetConfigType;
    const stockB = ASSET_CONFIG[b] as investmentUniverseConfig.StockAssetConfigType;
    return stockA.formalTicker.localeCompare(stockB.formalTicker);
  }
};

export const getAssetsFilteredOnSearchTerm = (
  searchTerm: string,
  assetsToSearch: InvestmentUniverseAssets
): investmentUniverseConfig.AssetType[] => {
  let assets = Object.keys(assetsToSearch) as investmentUniverseConfig.AssetType[];

  if (searchTerm) {
    assets = assets.filter((asset) => doesAssetMatchSearchTerm(asset, searchTerm, assetsToSearch));
  }
  return assets;
};

/**
 * Sort assets in search component by following these 3 rules:
 * 1. Assets that have complete match have higher priority
 * 2. Assets that have partial match have higher priority than the rest
 * 3. The rest of the assets are sorted alphabetically
 */
export const sortSearchAssets = (
  a: investmentUniverseConfig.AssetType,
  b: investmentUniverseConfig.AssetType,
  searchTerm: string
): number => {
  const nameA = ASSET_CONFIG[a]?.simpleName.toLowerCase();
  const nameB = ASSET_CONFIG[b]?.simpleName.toLowerCase();
  const formalTickerA = ASSET_CONFIG[a]?.formalTicker.toLowerCase();
  const formalTickerB = ASSET_CONFIG[b]?.formalTicker.toLowerCase();
  const assetAMatchesNameOrFormalTicker = nameA.startsWith(searchTerm) || formalTickerA.startsWith(searchTerm);
  const assetBMatchesNameOrFormalTicker = nameB.startsWith(searchTerm) || formalTickerB.startsWith(searchTerm);

  // Check if there is an exact match
  if (nameA === searchTerm.toLowerCase() && nameB !== searchTerm.toLowerCase()) {
    return -1;
  } else if (nameB === searchTerm.toLowerCase() && nameA !== searchTerm.toLowerCase()) {
    return 1;
  }

  // Check for partial match in simpleName or formalTicker
  if (assetAMatchesNameOrFormalTicker && !assetBMatchesNameOrFormalTicker) {
    return -1;
  } else if (assetBMatchesNameOrFormalTicker && !assetAMatchesNameOrFormalTicker) {
    return 1;
  } else {
    // return in alphabetical order
    return nameA.localeCompare(nameB);
  }
};

export const compareBondCategories = (
  a: investmentUniverseConfig.BondCategoryConfigType,
  b: investmentUniverseConfig.BondCategoryConfigType
): number => {
  return a.sorting - b.sorting;
};

export const getAssetIconUrl = (assetCommonId: investmentUniverseConfig.AssetType): string => {
  const assetConfig = ASSET_CONFIG[assetCommonId];
  const category = assetConfig?.category;
  if (category === "etf") {
    const providerLogo = ASSET_PROVIDER_CONFIG[assetConfig.provider]?.icon;
    return `https://landing-page-assets.wealthyhood.cloud/provider-logos/${providerLogo}`;
  } else if (category === "stock") {
    let activeCommonId = assetCommonId;
    if (assetConfig.deprecated) {
      activeCommonId = assetConfig.deprecatedBy;
    }
    return `https://landing-page-assets.wealthyhood.cloud/logos/${activeCommonId}.svg`;
  }
};

export const getPublicAssetIconUrl = (assetCommonId: publicInvestmentUniverseConfig.PublicAssetType): string => {
  return `https://landing-page-assets.wealthyhood.cloud/logos/${assetCommonId}.svg`;
};

export const getAssetClassBreakdownName = (assetClass: investmentUniverseConfig.AssetClassType) => {
  const nameMap: Record<investmentUniverseConfig.AssetClassType, string> = {
    equities: "Equities",
    bonds: "Bonds",
    commodities: "Commodities",
    realEstate: "Real Estate",
    readyMade: "Ready Made"
  };

  return nameMap[assetClass];
};
