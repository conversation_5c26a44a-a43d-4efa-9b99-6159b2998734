import { entitiesConfig } from "@wealthyhood/shared-configs/dist";
import { UserDocument } from "../../models/User";
import { captureException } from "@sentry/react";

export enum UserTypeEnum {
  ADMIN = "ADMIN",
  INVESTOR = "INVESTOR",
  TEST_ACCOUNT = "TEST_ACCOUNT"
}

export const isAdmin = (user: UserDocument): boolean => user.role.includes(UserTypeEnum.ADMIN);
export const isInvestor = (user: UserDocument): boolean => user.role.includes(UserTypeEnum.INVESTOR);

const _userHasWealthkernelPortfolio = (user: UserDocument): boolean =>
  user.portfolios[0]?.providers?.wealthkernel?.id != null;

// user has submitted all required info but is not verified yet
// or user has been verified but has no portfolio id
export const isVerifying = (user: UserDocument): boolean =>
  (user.submittedRequiredInfo && user.kycStatus == "pending") ||
  (user.hasPassedKyc && !_userHasWealthkernelPortfolio(user));

export const isVerified = (user: UserDocument): boolean =>
  user.hasPassedKyc && _userHasWealthkernelPortfolio(user);

export const isVerificationFailed = (user: UserDocument): boolean => user.hasFailedKyc;

/**
 * Retrieves the initials of the user, if the user has submitted their name. If not, returns empty string.
 * @param user
 */
export const getUserInitials = (user: UserDocument): string => {
  try {
    const nameLetter = user.firstName.substr(0, 1);
    const surnameLetter = user.lastName.substr(0, 1);

    return nameLetter + surnameLetter;
  } catch (err) {
    captureException(err);
    return "";
  }
};

export const canSendGift = (user: UserDocument): boolean => {
  return user.canSendGiftUntil && new Date(user.canSendGiftUntil) > new Date();
};

export const isAllowedOneStepInvest = (user: UserDocument): boolean =>
  user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK;

export const getReportingFirm = (user: UserDocument): string => {
  if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
    return "Wealthkernel Ltd";
  } else if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE) {
    return "Wealthyhood Europe Investment Services SA";
  }
};
