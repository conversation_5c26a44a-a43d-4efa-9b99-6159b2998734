import { countriesConfig } from "@wealthyhood/shared-configs";
import ConfigUtil from "../../utils/configUtil";

export function isTaxIdentifierValid(country: countriesConfig.CountryCodesType, identifier = ""): boolean {
  const { inputRegex, inputLength } = ConfigUtil.getTaxResidencyConfig(country);
  const regex = new RegExp(inputRegex, "i");
  const cleansedIdentifier = identifier.replace(/ /g, "");

  return cleansedIdentifier.length >= inputLength && regex.test(cleansedIdentifier);
}
