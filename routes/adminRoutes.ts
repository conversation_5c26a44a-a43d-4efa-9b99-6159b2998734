import express from "express";
import * as adminController from "../controllers/adminController";
import { catchErrors } from "../controllers/errorController";
import { isAdminHandler } from "../middlewares/userMiddleware";

const router = express.Router();

/**
 * GET
 */
router.get("/order-management", isAdmin<PERSON>and<PERSON>, catchErrors(adminController.getOrderManagementView));
router.get("/order-management/analytics", isAdmin<PERSON><PERSON><PERSON>, catchErrors(adminController.getOrderAnalyticsView));
router.get(
  "/order-management/analytics/breakdown",
  isAdminHandler,
  catchErrors(adminController.getOrderAnalyticsBreakdownView)
);
+router.get(
  "/order-management/analytics/breakdown/orders",
  isAdmin<PERSON>andler,
  catchErrors(adminController.getOrderAnalyticsBreakdownViewForAsset)
);
router.get("/rewards", isAd<PERSON><PERSON><PERSON><PERSON>, catchErrors(adminController.getRewardsView));
router.get("/rewards/:id", isAdmin<PERSON><PERSON><PERSON>, catchErrors(adminController.getRewardView));
router.get("/bank-accounts", isAdminHandler, catchErrors(adminController.getBankAccountsView));
router.get("/bank-accounts/suspended", isAdminHandler, catchErrors(adminController.getSuspendedBankAccounts));
router.get("/bank-accounts/pending", isAdminHandler, catchErrors(adminController.getPendingBankAccounts));
router.get("/reward-submission", isAdminHandler, catchErrors(adminController.getRewardSubmissionView));
router.get("/gifts", isAdminHandler, catchErrors(adminController.getGiftsView));
router.get("/gifts/:id", isAdminHandler, catchErrors(adminController.getGiftView));
router.get("/users", isAdminHandler, catchErrors(adminController.getUsersView));
router.get("/users/failed", isAdminHandler, catchErrors(adminController.getFailedUsersView));
router.get("/users/:userId", isAdminHandler, catchErrors(adminController.getUserView));
router.get("/asset-transactions", isAdminHandler, catchErrors(adminController.getAssetTransactionsView));
router.get("/deposit-transactions", isAdminHandler, catchErrors(adminController.getDepositTransactionsView));
router.get("/withdrawal-transactions", isAdminHandler, catchErrors(adminController.getWithdrawalTransactionsView));
router.get("/transactions/:id/", isAdminHandler, catchErrors(adminController.getTransactionView));

/**
 * POST
 */
router.post(
  "/users/:userId/account-statements/generate",
  isAdminHandler,
  catchErrors(adminController.getTaxStatement)
);
router.post("/portfolios/add-bonus-deposit", isAdminHandler, catchErrors(adminController.createBonusDeposit));
router.post(
  "/bank-accounts/:bankAccountId/activate",
  isAdminHandler,
  catchErrors(adminController.activateBankAccount)
);
router.post("/rewards", isAdminHandler, catchErrors(adminController.createRewards));
router.post("/rewards/create-single", isAdminHandler, catchErrors(adminController.createSingleReward));
router.post("/rewards/:id", isAdminHandler, catchErrors(adminController.updateReward));
router.post("/transactions/:transactionId/sync", isAdminHandler, catchErrors(adminController.syncTransaction));
router.post("/users/:userId", isAdminHandler, catchErrors(adminController.updateUser));
router.post("/users/:userId/delete", isAdminHandler, catchErrors(adminController.deleteUser));
router.post("/investment-products/:id/pause", isAdminHandler, catchErrors(adminController.pauseLine));
router.post("/investment-products/:id/resume", isAdminHandler, catchErrors(adminController.resumeLine));
router.post("/users/:id/remove-duplicate-flag", isAdminHandler, catchErrors(adminController.removeDuplicateFlag));
router.post(
  "/users/:id/remove-kyc-passport-flag",
  isAdminHandler,
  catchErrors(adminController.removeKycPassportFlag)
);
router.post("/users/:id/override-kyc-decision", isAdminHandler, catchErrors(adminController.overrideKycDecision));
router.post("/users/:id/update-aml-checks", isAdminHandler, catchErrors(adminController.updateAmlChecks));

export default router;
