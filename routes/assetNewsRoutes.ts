import express from "express";
import { catchErrors } from "../controllers/errorController";
import * as assetNewsController from "../controllers/assetNewsController";
import { isInvestorHandler } from "../middlewares/userMiddleware";
import { isLoggedInHandler } from "../middlewares/authMiddleware";

const router = express.Router();

router.get("/", isInvestor<PERSON>andler, isLoggedInHandler, catchErrors(assetNewsController.getAllAssetNews));

export default router;
