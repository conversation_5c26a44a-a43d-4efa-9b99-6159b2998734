import express from "express";
import { isInvestor<PERSON>and<PERSON> } from "../middlewares/userMiddleware";
import * as automationController from "../controllers/automationController";

const router = express.Router();

/**
 * GET ROUTES
 */

router.get("/", isIn<PERSON><PERSON><PERSON><PERSON><PERSON>, automationController.getAutomations);

/**
 * POST ROUTES
 */

router.post("/:id/cancel", isInvestor<PERSON>andler, automationController.cancelAutomation);

export default router;
