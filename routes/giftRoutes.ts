import express from "express";
import { isInvestor<PERSON><PERSON><PERSON> } from "../middlewares/userMiddleware";
import * as giftController from "../controllers/giftController";

const router = express.Router();

/**
 * GET ROUTES
 */

router.get("/", isInvestor<PERSON><PERSON><PERSON>, giftController.getGifts);

/**
 * POST ROUTES
 */

router.post("/", isInvestor<PERSON><PERSON><PERSON>, giftController.createGift);
router.post("/:id", isInvestor<PERSON>andler, giftController.updateGift);

export default router;
