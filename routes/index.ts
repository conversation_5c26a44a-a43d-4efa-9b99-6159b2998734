import { NextFunction, Response, Router } from "express";
import { CustomRequest } from "custom";
import * as authController from "../controllers/authController";
import { catchErrors } from "../controllers/errorController";
import * as userController from "../controllers/userController";
import { isLoggedInHandler, isNotLoggedInHandler } from "../middlewares/authMiddleware";
import { isAdmin } from "../utils/userRoleUtil";
import { getHomeView } from "../controllers/investorController";

const router = Router();

router.get(
  "/",
  isLoggedInHandler,
  (req: CustomRequest, res: Response, next: NextFunction) => {
    if (isAdmin(req.user)) {
      return res.redirect("/admin/users");
    } else {
      if (req.user.hasDisassociationRequest) {
        return res.redirect("/investor/closing-account");
      } else if (req.user.hasFailedKyc) {
        return res.redirect("/investor/verification-pending");
      } else if (!req.user.viewedWealthybitesScreen) {
        return res.redirect("/investor/join-wealthybites");
      } else if (!req.user.viewedWelcomePage && !req.user.referredByEmail) {
        return res.redirect("/investor/set-referral");
      } else if (!req.user.viewedWelcomePage) {
        return res.redirect("/investor/open-account");
      } else if (!req.user.residencyCountry || (req.user.hasJoinedWaitingList && !req.user.isEuWhitelisted)) {
        return res.redirect("/investor/residency-country");
      } else if (!req.user.submittedRequiredInfo && !req.user.hasCompletedKycJourney) {
        return res.redirect("/investor/id-verification/resume");
      } else if (!req.user.submittedRequiredInfo && !req.user?.kycOperation?.isProcessed) {
        return res.redirect("/investor/id-verification/pending");
      } else if (!req.user.submittedRequiredInfo) {
        return res.redirect("/investor/collect-personal-details");
      } else if (!req.user.hasAcceptedTerms) {
        return res.redirect("/investor/email-verified");
      } else if (!req.user.isVerified) {
        return res.redirect("/investor/verification-pending");
      } else if (req.user.shouldShowKYCSuccessPage) {
        return res.redirect("/investor/verification-success");
      } else if (!req.user.hasSubscription) {
        return res.redirect("/investor/select-plan");
      } else if (!req.user?.portfolios?.[0]?.isTargetAllocationSetup && !req.user.skippedPortfolioCreation) {
        return res.redirect("/investor/create-my-portfolio");
      } else {
        return next();
      }
    }
  },
  catchErrors(getHomeView)
);

/**
 * Authentication route
 */
router.get("/auth", isNotLoggedInHandler, authController.auth);

/**
 * Required Email
 */
router.get("/email-disposable", userController.getEmailDisposableView);

export default router;
