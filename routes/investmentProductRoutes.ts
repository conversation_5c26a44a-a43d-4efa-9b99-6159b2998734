import express from "express";
import { catchErrors } from "../controllers/errorController";
import * as investmentProductController from "../controllers/investmentProductController";
import { inUserRolesHandler, isAdmin<PERSON>andler, isInvestorHandler } from "../middlewares/userMiddleware";
import { UserTypeEnum } from "../models/User";
import { isLoggedInHandler } from "../middlewares/authMiddleware";

const router = express.Router();

router.get(
  "/",
  isInvestor<PERSON>andler,
  isLoggedInHandler,
  catchErrors(investmentProductController.getInvestmentProducts)
);

//Views
router.get("/create", isAdminHandler, investmentProductController.getCreateProductView);
router.get(
  "/:assetCommonId/history",
  inUserRolesHandler([UserTypeEnum.INVESTOR, UserTypeEnum.ADMIN]),
  investmentProductController.getInvestmentProductHistory
);
router.get(
  "/:assetCommonId/prices-by-tenor",
  isInvestor<PERSON><PERSON><PERSON>,
  investmentProductController.getInvestmentProductPricesByTenor
);
router.get(
  "/:assetCommonId",
  inUserRolesHandler([UserTypeEnum.INVESTOR, UserTypeEnum.ADMIN]),
  investmentProductController.getInvestmentProduct
);
router.get(
  "/:assetCommonId/investment-details",
  inUserRolesHandler([UserTypeEnum.INVESTOR, UserTypeEnum.ADMIN]),
  investmentProductController.getInvestmentProductInvestmentDetails
);
router.get(
  "/:assetCommonId/recent-activity",
  isInvestorHandler,
  investmentProductController.getInvestmentProductRecentActivity
);

export default router;
