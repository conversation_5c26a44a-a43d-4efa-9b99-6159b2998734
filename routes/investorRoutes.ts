import { addFeatureFlagsToRequest } from "../middlewares/featuresMiddleware";
import express from "express";
import * as investorController from "../controllers/investorController";
import { catchErrors } from "../controllers/errorController";
import { isLoggedInHandler } from "../middlewares/authMiddleware";
import {
  hasNotBeenWelcomedHandler,
  hasNotCompletedIdVerificationHandler,
  hasNotResidencyCountry,
  hasNotSubscribedHandler,
  isNotVerifiedHandler,
  isVerifiedHandler,
  userHasNotDeposit
} from "../middlewares/investorMiddleware";
import { isInvestorHandler } from "../middlewares/userMiddleware";
import { addLocaleToRequest } from "../middlewares/localeMiddleware";
import { ConfigCatFeatureFlags } from "../config/featuresConfig";

const router = express.Router();

/**
 * Investor routes
 */

/**
 * GET Handlers
 */
router.get("/cash", isLoggedIn<PERSON><PERSON><PERSON>, isInvestor<PERSON>and<PERSON>, catchErrors(investorController.getCashView));
router.get(
  "/learning-hub",
  isLoggedInHandler,
  isInvestorHandler,
  addFeatureFlagsToRequest([ConfigCatFeatureFlags.ANALYST_INSIGHT_MIGRATION]),
  catchErrors(investorController.getLearningHubView)
);
router.get(
  "/learning-hub/guides/:slug",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getGuideView)
);
router.get(
  "/learning-hub/articles/analyst-insights/:id",
  isLoggedInHandler,
  isInvestorHandler,
  addFeatureFlagsToRequest([ConfigCatFeatureFlags.ANALYST_INSIGHT_MIGRATION]),
  catchErrors(investorController.getAnalystInsightsArticleView)
);
router.get(
  "/learning-hub/analyst-insights",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getAnalystInsights)
);
router.get(
  "/learning-hub/articles/news/:id",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getNewsArticleView)
);
router.get(
  "/pending-transactions",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getPendingTransactionsView)
);
router.get("/billing", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.getBillingView));
router.get("/statements", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.getStatementsView));
router.get(
  "/notification-settings",
  isLoggedInHandler,
  catchErrors(investorController.getNotificationSettingsView)
);
router.post(
  "/notification-settings/update",
  isLoggedInHandler,
  catchErrors(investorController.updateNotificationSettings)
);
router.get(
  "/account/details",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getAccountDetailsView)
);

router.get(
  "/linked-bank-accounts",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getLinkedBankAccounts
);
router.get("/truelayer-providers", isLoggedInHandler, isInvestorHandler, investorController.getTruelayerProviders);
router.get(
  "/collect-personal-details",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  catchErrors(investorController.getCollectPersonalDetailsView)
);
/**
 * TODO: Remove this route, as this should be implemented as an order placed modal.
 */
router.get(
  "/investment-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getInvestmentSuccessView
);
router.get(
  "/plan-update-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getPlanUpdateSuccessView
);
router.get("/new-mandate-success", isLoggedInHandler, isInvestorHandler, investorController.getMandateSuccessView);
router.get(
  "/new-recurring-top-up-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getNewRecurringTopUpSuccessView
);
router.get(
  "/rebalancing-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getRebalancingSuccessView
);
router.get(
  "/cancellation-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getTransactionCancellationSuccessView
);
router.get(
  "/truelayer-auth-url",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getTruelayerDataAuthUrl)
);

router.get("/payment-pending", isLoggedInHandler, isInvestorHandler, investorController.getPaymentPendingView);
router.get("/payment-success", isLoggedInHandler, isInvestorHandler, investorController.getPaymentSuccessView);
router.get(
  "/gift-success",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getGiftSuccessView)
);
router.get("/send-gift", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.getSendGiftView));
router.get(
  "/earn-free-shares",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getEarnFreeSharesView)
);
router.get(
  "/invite-friend",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getInviteFriendView)
);
router.get(
  "/withdrawal-success",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.getWithdrawalSuccessView
);
router.get("/profile", isLoggedInHandler, isInvestorHandler, investorController.getInvestorProfileView);
router.get(
  "/request-profile-update",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getRequestProfileUpdateView)
);
router.get(
  "/truelayer-pay-callback/:action/",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.truelayerPayCallback)
);
router.get(
  "/saltedge-pay-callback/:action/",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.saltedgePayCallback)
);
router.get(
  "/stripe-add-payment-method-callback",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.stripeAddPaymentMethodCallback)
);
router.get(
  "/verification-initiated",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  investorController.getVerificationInitiatedView
);
router.get(
  "/verification-pending",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  investorController.getVerificationPendingView
);
router.get("/closing-account", isLoggedInHandler, investorController.getClosingAccountView);
router.get(
  "/residency-country",
  isLoggedInHandler,
  hasNotResidencyCountry,
  investorController.getResidencyCountryView
);
router.get("/billing-activity", isLoggedInHandler, isInvestorHandler, investorController.getBilling);
router.get("/email-verified", isLoggedInHandler, investorController.getEmailVerifiedView);
router.get(
  "/verification-success",
  isLoggedInHandler,
  isInvestorHandler,
  isVerifiedHandler,
  userHasNotDeposit,
  investorController.getVerificationSuccessView
);
router.get(
  "/create-my-portfolio",
  isLoggedInHandler,
  isVerifiedHandler,
  isInvestorHandler,
  catchErrors(investorController.getCreateMyPortfolioView)
);
router.get(
  "/select-plan",
  isLoggedInHandler,
  isInvestorHandler,
  isVerifiedHandler,
  hasNotSubscribedHandler,
  catchErrors(investorController.getSelectPlanView)
);
router.get(
  "/change-plan",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getChangePlanView)
);

router.get("/set-referral", isLoggedInHandler, catchErrors(investorController.getSetReferralCodeView));
router.get("/join-wealthybites", isLoggedInHandler, catchErrors(investorController.getJoinWealthybitesView));
router.get("/investments", isInvestorHandler, catchErrors(investorController.getInvestmentsView));
router.get("/investment-activity", isInvestorHandler, catchErrors(investorController.getInvestmentActivityView));
router.get("/autopilot", isInvestorHandler, catchErrors(investorController.getAutopilotView));
router.get(
  "/open-account",
  isLoggedInHandler,
  hasNotBeenWelcomedHandler,
  addLocaleToRequest,
  catchErrors(investorController.getOpenAccountView)
);
router.get("/me", isLoggedInHandler, catchErrors(investorController.getInvestorInfo));
router.get(
  "/id-verification",
  isLoggedInHandler,
  isInvestorHandler,
  hasNotCompletedIdVerificationHandler,
  catchErrors(investorController.getIdVerificationView)
);
router.get(
  "/id-verification/pending",
  isInvestorHandler,
  catchErrors(investorController.getIdVerificationPollingView)
);
router.get(
  "/id-verification/resume",
  isInvestorHandler,
  catchErrors(investorController.getIdVerificationResumeView)
);
router.get("/id-verification/token", isInvestorHandler, catchErrors(investorController.getIdVerificationToken));
router.get(
  "/savings-topup-success",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getSavingsTopupSuccessView)
);
router.get(
  "/savings-withdrawal-success",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getSavingsWithdrawalSuccessView)
);
router.get(
  "/bank-providers",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getBankProviders)
);
router.get(
  "/gocardless-auth-url",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getGoCardlessAuthUrl)
);
router.get(
  "/modal-prompts",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getModalPrompts)
);

/**
 * POST Handlers
 */

router.post("/verify", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.verifyUser));
router.post(
  "/address",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  catchErrors(investorController.setAddress)
);
router.post(
  "/payments",
  isLoggedInHandler,
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(investorController.createPayment)
);
router.post("/residency-country", isLoggedInHandler, catchErrors(investorController.setResidencyCountry));
router.post(
  "/charges/lifetime/payments",
  isLoggedInHandler,
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(investorController.createLifetimeChargePayment)
);
router.post(
  "/deposit-and-invest",
  isLoggedInHandler,
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(investorController.depositAndInvest)
);
router.post(
  "/deposit-and-buy-asset",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.depositAndBuyAsset)
);
router.post(
  "/personal-details",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  catchErrors(investorController.setPersonalDetails)
);
router.post(
  "/tax-residency",
  isLoggedInHandler,
  isInvestorHandler,
  isNotVerifiedHandler,
  catchErrors(investorController.setTaxResidency)
);
router.get(
  "/transactions/:id/",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.getTransactionView)
);
router.post(
  "/bank-accounts/:id/deactivate",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.deactivateBankAccount)
);
router.post(
  "/payment-methods/initiate-stripe",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.initiateStripePaymentMethod)
);
router.post(
  "/subscriptions",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.createSubscription)
);
router.post(
  "/subscriptions/initiate-stripe",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.initiateStripeSubscription)
);
router.post(
  "/subscriptions/complete-stripe",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.completeStripeSubscription)
);
router.post(
  "/subscriptions/payment-method",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.updateSubscriptionPaymentMethod)
);
router.post(
  "/subscriptions/:id",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.updateSubscription)
);
router.post(
  "/subscriptions/:id/renew",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.renewSubscription)
);
router.post("/accept-terms", isLoggedInHandler, catchErrors(investorController.acceptTermsAndConditions));
router.post("/setup-recurring-topup", isLoggedInHandler, catchErrors(investorController.setupRecurringTopUp));
router.post(
  "/setup-recurring-savings-topup",
  isLoggedInHandler,
  catchErrors(investorController.setupRecurringSavingsTopUp)
);
router.post(
  "/setup-automated-rebalancing",
  isLoggedInHandler,
  catchErrors(investorController.setupAutomatedRebalancing)
);
router.post("/close-account", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.closeAccount));
router.post("/set-referrer", isLoggedInHandler, catchErrors(investorController.setReferrer));
router.post("/subscribe-wealthybites", isLoggedInHandler, catchErrors(investorController.subscribeWealthybites));
router.post("/invite-friend", isLoggedInHandler, isInvestorHandler, catchErrors(investorController.inviteFriend));
router.post(
  "/reset-referral-code",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.resetReferralCode)
);
router.post("/deletion-feedback", isLoggedInHandler, catchErrors(investorController.sendDeletionFeedback));
router.post(
  "/employment-info",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.setEmploymentInfo)
);
router.post(
  "/sync-id-verification",
  isLoggedInHandler,
  isInvestorHandler,
  catchErrors(investorController.syncIdVerification)
);
router.post("/skip-portfolio-creation", isLoggedInHandler, catchErrors(investorController.skipPortfolioCreation));
router.post(
  "/account-statements/generate",
  isLoggedInHandler,
  isInvestorHandler,
  investorController.generateAccountStatement
);

export default router;
