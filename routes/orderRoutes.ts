import express from "express";
import { isInvestor<PERSON><PERSON><PERSON> } from "../middlewares/userMiddleware";
import * as orderController from "../controllers/orderController";
import { catchErrors } from "../controllers/errorController";

const router = express.Router();

/**
 * POST ROUTES
 */
router.post("/:id/cancel", isInvestor<PERSON><PERSON><PERSON>, catchErrors(orderController.cancelOrder));
router.post(
  "/:id/trade-confirmations/generate",
  isInvestor<PERSON>and<PERSON>,
  catchErrors(orderController.generateTradeConfirmation)
);

export default router;
