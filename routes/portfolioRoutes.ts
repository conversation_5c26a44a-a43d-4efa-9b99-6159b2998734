import express from "express";
import { catchErrors } from "../controllers/errorController";
import * as portfolioController from "../controllers/portfolioController";
import { realPortfolioAllocationIsNotCreatedHandler } from "../middlewares/portfolioMiddleware";
import { inUserRolesHandler, isInvestorHandler } from "../middlewares/userMiddleware";
import { UserTypeEnum } from "../models/User";
import { isVerifiedHandler } from "../middlewares/investorMiddleware";

const router = express.Router();

/**
 * GET Requests
 */
router.get("/asset-discovery", isInvestor<PERSON>andler, catchErrors(portfolioController.getAssetDiscoveryView));
router.get("/back-testing", isInvestorHandler, portfolioController.getPortfolioBackTesting);
router.get("/creation-success", isInvestor<PERSON>and<PERSON>, isVerified<PERSON>and<PERSON>, portfolioController.getCreationSuccessView);
router.get("/target", isInvestor<PERSON><PERSON><PERSON>, catchErrors(portfolioController.getTargetPortfolioView));
router.get("/optimal-allocation", isInvestorHandler, catchErrors(portfolioController.getOptimalAllocationAPI));
router.get("/past-performance", isInvestorHandler, catchErrors(portfolioController.getPastPerformance));
router.get("/future-performance", isInvestorHandler, catchErrors(portfolioController.getFuturePerformance));
router.get("/:id/restricted-holdings", isInvestorHandler, catchErrors(portfolioController.getRestrictedHoldings));
router.get("/:id/asset-restriction", isInvestorHandler, catchErrors(portfolioController.getRestrictedAssets));
router.get(
  "/future-performance-monte-carlo",
  isInvestorHandler,
  catchErrors(portfolioController.getFuturePerformanceMonteCarlo)
);
router.get(
  "/personalisation",
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(portfolioController.getPersonalisationView)
);
router.get(
  "/creation",
  isInvestorHandler,
  isVerifiedHandler,
  realPortfolioAllocationIsNotCreatedHandler,
  catchErrors(portfolioController.getPortfolioCreationView)
);
router.get(
  "/creation-robo-advisor",
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(portfolioController.getPortfolioCreationRoboAdvisorView)
);
router.get(
  "/creation-template-info",
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(portfolioController.getPortfolioCreationTemplateInfoView)
);
router.get("/setup", isInvestorHandler, isVerifiedHandler, catchErrors(portfolioController.getPortfolioSetupView));
router.get(
  "/projection",
  inUserRolesHandler([UserTypeEnum.ADMIN, UserTypeEnum.INVESTOR]),
  catchErrors(portfolioController.getAllocationProjectionAPI)
);
router.get(
  "/setup-as-step",
  isInvestorHandler,
  isVerifiedHandler,
  catchErrors(portfolioController.getPortfolioSetupAsStepView)
);
router.get(
  "/:id/with-returns-by-tenor",
  isInvestorHandler,
  catchErrors(portfolioController.getPortfolioWithReturnsByTenor)
);
router.get("/:id/available-holdings", isInvestorHandler, catchErrors(portfolioController.getAvailableHoldings));
router.get("/:id/prices-by-tenor", isInvestorHandler, catchErrors(portfolioController.getPricesByTenor));

/**
 * POST Requests
 */
router.post(
  "/:id/allocation",
  isInvestorHandler,
  catchErrors(portfolioController.createOrUpdatePortfolioAllocation)
);
router.post("/:id/buy", isInvestorHandler, catchErrors(portfolioController.buyPortfolio));
router.post(
  "/:id/personalisation-preferences",
  isInvestorHandler,
  realPortfolioAllocationIsNotCreatedHandler,
  catchErrors(portfolioController.submitPersonalisationPreferences)
);
router.post("/:id/withdraw", isInvestorHandler, catchErrors(portfolioController.withdraw));
router.post("/:id", isInvestorHandler, catchErrors(portfolioController.updatePortfolio));
router.post("/:id/rebalance", isInvestorHandler, catchErrors(portfolioController.rebalancePortfolio));
router.post("/:id/topup-savings", isInvestorHandler, catchErrors(portfolioController.topupSavings));
router.post(
  "/:id/topup-savings-pending-deposit",
  isInvestorHandler,
  catchErrors(portfolioController.topupSavingsPendingDeposit)
);
router.post("/:id/withdraw-savings", isInvestorHandler, catchErrors(portfolioController.withdrawSavings));

export default router;
