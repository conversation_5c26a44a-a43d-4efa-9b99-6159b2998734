import express from "express";
import { isInvestor<PERSON>and<PERSON> } from "../middlewares/userMiddleware";
import * as rewardController from "../controllers/rewardController";
import { isLoggedInHandler } from "../middlewares/authMiddleware";
import { catchErrors } from "../controllers/errorController";

const router = express.Router();

/**
 * GET ROUTES
 */

router.get("/", isInvestor<PERSON><PERSON><PERSON>, rewardController.getRewards);

/**
 * POST ROUTES
 */

router.post("/:id", isLogged<PERSON>n<PERSON><PERSON><PERSON>, isInvestor<PERSON><PERSON><PERSON>, catchErrors(rewardController.updateReward));
router.post(
  "/:id/trade-confirmations/generate",
  isInvestor<PERSON><PERSON><PERSON>,
  catchErrors(rewardController.generateTradeConfirmation)
);

export default router;
