import * as savingsProductController from "./../controllers/savingsProductController";
import express from "express";
import { catchErrors } from "../controllers/errorController";
import { isInvestorHandler } from "../middlewares/userMiddleware";
import { isLoggedInHandler } from "../middlewares/authMiddleware";

const router = express.Router();

router.get(
  "/activity",
  isInvestor<PERSON><PERSON><PERSON>,
  isLoggedInHandler,
  catchErrors(savingsProductController.getSavingsProductActivity)
);

router.get(
  "/fee-details",
  isInvestor<PERSON>andler,
  isLoggedInHandler,
  catchErrors(savingsProductController.getSavingsProductFeeDetails)
);

router.get(
  "/data",
  isInvestor<PERSON><PERSON><PERSON>,
  isLoggedInHandler,
  catchErrors(savingsProductController.getSavingsProductData)
);

export default router;
