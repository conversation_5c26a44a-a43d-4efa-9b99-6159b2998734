import express from "express";
import { isInvestorHandler } from "../middlewares/userMiddleware";
import * as transactionController from "../controllers/transactionController";
import { catchErrors } from "../controllers/errorController";

const router = express.Router();

/**
 * GET ROUTES
 */
router.get("/deposits", isInvestor<PERSON>andler, transactionController.getDeposits);
router.get("/rebalances/preview", isInvestor<PERSON><PERSON>ler, transactionController.getRebalanceTransactionPreview);
router.get("/portfolio/preview", isInvestorHandler, transactionController.getPortfolioTransactionPreview);
router.get("/wealthyhood-dividends", isInvestorHandler, transactionController.getWhDividends);

/**
 * POST ROUTES
 */
router.post("/asset/preview", isInvestorHandler, transactionController.getSingleAssetTransactionPreview);
router.post("/:id/cancel", isInvestor<PERSON><PERSON><PERSON>, catchErrors(transactionController.cancelTransaction));
router.post("/wealthyhood-dividends/:id", isInvestor<PERSON>andler, transactionController.updateWhDividend);

export default router;
