#!/usr/bin/env node

// Load env variables
import "./loaders/environment";
import { envIsDev } from "./utils/environmentUtil";

import debug from "debug";
const debugServer = debug("wealthyhood-web:server");

// Initiate mongo db loader to connect to db
import "./loaders/eventEmitter";

import app from "./app";
import https from "https";
import http from "http";
import fs from "fs";

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val: string): string | number | boolean {
  const port: number = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

/**
 * Get port from environment and store in Express.
 */

const port = normalizePort(process.env.PORT || "3000");
app.set("port", port);

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error: NodeJS.ErrnoException): void {
  if (error.syscall !== "listen") {
    throw error;
  }

  const bind = typeof port === "string" ? "Pipe " + port : "Port " + port;

  // handle specific listen errors with friendly messages
  switch (error.code) {
    case "EACCES":
      debugServer(bind + " requires elevated privileges");
      process.exit(1);
      break;
    case "EADDRINUSE":
      debugServer(bind + " is already in use");
      process.exit(1);
      break;
    default:
      throw error;
  }
}

/**
 * Create HTTP server.
 */
let server: https.Server | http.Server;

if (envIsDev()) {
  /**
   * Read self-signed certificates for localhost
   */
  const key = fs.readFileSync("./key.pem");
  const cert = fs.readFileSync("./cert.pem");

  server = https.createServer({ key: key, cert: cert }, app);
} else {
  server = http.createServer(app);
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening(): void {
  const addr = server.address();
  const bind = typeof addr === "string" ? "pipe " + addr : "port " + addr.port;
  debugServer("Listening on " + bind);
}

/**
 * Listen on provided port, on all network interfaces.
 */

server.listen(port);
server.on("error", onError);
server.on("listening", onListening);
