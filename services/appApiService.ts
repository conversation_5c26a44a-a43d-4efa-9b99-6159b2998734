import axios, { AxiosRequestConfig } from "axios";
import { captureException } from "@sentry/node";
import logger from "./loggerService";
import qs from "qs";
import { randomUUID } from "crypto";

interface Auth0AccessToken {
  access_token: string;
  scope: string; // string space separated
  expires_in: number;
  token_type: "Bearer";
}

enum HttpMethodEnum {
  GET = "get",
  POST = "post"
}

export const API_ROUTES = {
  investmentProducts: {
    all: () => "investment-products",
    id: (id: string) => `${API_ROUTES.investmentProducts.all()}/${id}`,
    getEtfData: () => `${API_ROUTES.investmentProducts.all()}/etf-data`,
    getHistoricalPrices: () => `${API_ROUTES.investmentProducts.all()}/prices`,
    pause: (id: string) => `${API_ROUTES.investmentProducts.id(id)}/pause`,
    resume: (id: string) => `${API_ROUTES.investmentProducts.id(id)}/resume`,
    getInvestmentDetails: () => `${API_ROUTES.investmentProducts.all()}/investment-details`,
    recentActivity: () => `${API_ROUTES.investmentProducts.all()}/recent-activity`,
    pricesByTenor: () => `${API_ROUTES.investmentProducts.all()}/prices-by-tenor`
  },
  addresses: {
    all: () => "addresses"
  },
  portfolios: {
    all: () => "portfolios",
    id: (id: string) => `${API_ROUTES.portfolios.all()}/${id}`,
    idWithReturnsByTenor: (id: string) => `${API_ROUTES.portfolios.id(id)}/with-returns-by-tenor`,
    availableHoldings: (id: string) => `${API_ROUTES.portfolios.id(id)}/available-holdings`,
    allocation: (id: string) => `${API_ROUTES.portfolios.id(id)}/allocation`,
    preferences: (id: string) => `${API_ROUTES.portfolios.id(id)}/personalisation-preferences`,
    buy: (id: string) => `${API_ROUTES.portfolios.id(id)}/buy`,
    submitOrders: (id: string) => `${API_ROUTES.portfolios.id(id)}/submit-orders`,
    investPendingDeposit: (id: string) => `${API_ROUTES.portfolios.id(id)}/invest-pending-deposit`,
    buyAssetPendingDeposit: (id: string) => `${API_ROUTES.portfolios.id(id)}/buy-asset-pending-deposit`,
    withdraw: (id: string) => `${API_ROUTES.portfolios.id(id)}/withdraw`,
    rebalance: (id: string) => `${API_ROUTES.portfolios.id(id)}/rebalance`,
    isImbalanced: (id: string) => `${API_ROUTES.portfolios.id(id)}/is-imbalanced`,
    addBonusDeposit: () => `${API_ROUTES.portfolios.all()}/add-bonus-deposit`,
    pendingCashFlows: (id: string) => `${API_ROUTES.portfolios.id(id)}/pending-cash-flows`,
    restrictedHoldings: (id: string) => `${API_ROUTES.portfolios.id(id)}/restricted-holdings`,
    restrictedAssets: (id: string) => `${API_ROUTES.portfolios.id(id)}/asset-restriction`,
    withdrawSavings: (id: string) => `${API_ROUTES.portfolios.id(id)}/withdraw-savings`,
    topupSavings: (id: string) => `${API_ROUTES.portfolios.id(id)}/topup-savings`,
    pricesByTenor: (id: string) => `${API_ROUTES.portfolios.id(id)}/prices-by-tenor`
  },
  subscriptions: {
    all: () => "subscriptions",
    id: (id: string) => `${API_ROUTES.subscriptions.all()}/${id}`,
    renew: (id: string) => `${API_ROUTES.subscriptions.all()}/${id}/renew`,
    initiateStripe: () => `${API_ROUTES.subscriptions.all()}/initiate-stripe`,
    completeStripe: () => `${API_ROUTES.subscriptions.all()}/complete-stripe`,
    updatePaymentMethod: () => `${API_ROUTES.subscriptions.all()}/payment-method`
  },
  paymentMethods: {
    all: () => "payment-methods",
    initiateStripe: () => `${API_ROUTES.paymentMethods.all()}/initiate-stripe`,
    completeStripe: () => `${API_ROUTES.paymentMethods.all()}/complete-stripe`
  },
  automations: {
    all: () => "automations",
    id: (id: string) => `${API_ROUTES.automations.all()}/${id}`,
    cancel: (id: string) => `${API_ROUTES.automations.all()}/${id}/cancel`
  },
  mandates: {
    all: () => "mandates"
  },
  statistics: {
    optimalAllocation: () => "statistics/optimal-allocation",
    pastPerformance: () => "statistics/past-performance",
    futurePerformance: () => "statistics/future-performance",
    futurePerformanceMonteCarlo: () => "statistics/future-performance-monte-carlo"
  },
  transactions: {
    all: () => "transactions",
    me: () => `${API_ROUTES.transactions.all()}/me`,
    id: (id: string) => `${API_ROUTES.transactions.all()}/${id}`,
    cashActivity: () => `${API_ROUTES.transactions.me()}/cash-activity`,
    sync: (id: string) => `${API_ROUTES.transactions.all()}/${id}/sync`,
    cancel: (id: string) => `${API_ROUTES.transactions.all()}/${id}/cancel`,
    preview: () => `${API_ROUTES.transactions.all()}/preview`,
    pendingRebalances: () => `${API_ROUTES.transactions.all()}/pending/rebalances`,
    billingActivity: () => `${API_ROUTES.transactions.all()}/billing`,
    getAssetTransactionLinkedToDeposit: (id: string) =>
      `${API_ROUTES.transactions.all()}/assets/pending-deposit/${id}`,
    rebalances: {
      all: () => `${API_ROUTES.transactions.all()}/rebalances`
    },
    pending: {
      all: () => `${API_ROUTES.transactions.all()}/pending`
    },
    assets: {
      all: () => `${API_ROUTES.transactions.all()}/assets`,
      id: (id: string) => `${API_ROUTES.transactions.assets.all()}/${id}`
    },
    deposits: {
      all: () => `${API_ROUTES.transactions.all()}/deposits`,
      id: (id: string) => `${API_ROUTES.transactions.deposits.all()}/${id}`,
      syncTruelayer: () => `${API_ROUTES.transactions.deposits.all()}/sync-truelayer`
    },
    dividends: {
      all: () => `${API_ROUTES.transactions.all()}/dividends`
    },
    withdrawals: {
      all: () => `${API_ROUTES.transactions.all()}/withdrawals`
    },
    wealthyhoodDividends: {
      id: (id: string) => `${API_ROUTES.transactions.all()}/wealthyhood-dividends/${id}`
    },
    charges: {
      all: () => `${API_ROUTES.transactions.all()}/charges`,
      lifetime: {
        all: () => `${API_ROUTES.transactions.charges.all()}/lifetime`,
        syncTruelayer: () => `${API_ROUTES.transactions.charges.lifetime.all()}/sync-truelayer`
      }
    },
    savings: {
      all: () => `${API_ROUTES.transactions.all()}/savings`,
      deposit: () => `${API_ROUTES.transactions.savings.all()}/deposit`
    }
  },
  accounts: {
    all: () => "accounts"
  },
  bankAccounts: {
    all: () => "bank-accounts",
    nameFlagged: () => `${API_ROUTES.bankAccounts.all()}/name-flagged`,
    suspended: () => `${API_ROUTES.bankAccounts.all()}/suspended`,
    pending: () => `${API_ROUTES.bankAccounts.all()}/pending`,
    deactivate: (id: string) => `${API_ROUTES.bankAccounts.all()}/${id}/deactivate`,
    setWealthyhoodStatus: (id: string) => `${API_ROUTES.bankAccounts.all()}/${id}/wealthyhood-status`,
    bankProviders: () => `${API_ROUTES.bankAccounts.all()}/banks`,
    initiateBankLinking: () => `${API_ROUTES.bankAccounts.all()}/initiate-bank-linking`
  },
  gifts: {
    all: () => "gifts",
    id: (id: string) => `${API_ROUTES.gifts.all()}/${id}`
  },
  users: {
    all: () => "users",
    id: (id: string) => `${API_ROUTES.users.all()}/${id}`,
    me: () => `${API_ROUTES.users.all()}/me`,
    investmentActivity: () => `${API_ROUTES.users.all()}/me/investment-activity`,
    summaries: () => `${API_ROUTES.users.all()}/me/daily-summaries`,
    linkedBankAccounts: () => `${API_ROUTES.users.all()}/me/linked-bank-accounts`,
    statement: () => `${API_ROUTES.users.all()}/me/account-statements/generate`,
    verify: () => `${API_ROUTES.users.all()}/verify`,
    email: (email: string) => `${API_ROUTES.users.all()}/email/${email}`,
    setReferrer: () => `${API_ROUTES.users.all()}/me/set-referrer`,
    subscribeWealthybites: () => `${API_ROUTES.users.all()}/me/subscribe-wealthybites`,
    deletionFeedback: () => `${API_ROUTES.users.all()}/me/deletion-feedback`,
    prompts: () => `${API_ROUTES.users.all()}/prompts`,
    residencyCountry: () => `${API_ROUTES.users.all()}/me/residency-country`,
    employmentConfig: () => `${API_ROUTES.users.all()}/employment-config`,
    employmentInfo: () => `${API_ROUTES.users.all()}/me/employment-info`,
    // admin only
    removeDuplicateFlag: (id: string) => `${API_ROUTES.users.all()}/${id}/remove-duplicate-flag`,
    removeKycPassportFlag: (id: string) => `${API_ROUTES.users.all()}/${id}/remove-kyc-passport-flag`,
    overrideKycDecision: (id: string) => `${API_ROUTES.users.all()}/${id}/override-kyc-decision`,
    updateAmlChecks: (id: string) => `${API_ROUTES.users.all()}/${id}/update-aml-checks`,
    specifiedUserStatement: (id: string) => `${API_ROUTES.users.all()}/${id}/account-statements/generate`
  },
  referralCodes: {
    all: () => "referral-codes"
  },
  rewards: {
    all: () => "rewards",
    id: (id: string) => `${API_ROUTES.rewards.all()}/${id}`,
    tradeConfirmations: (id: string) => `${API_ROUTES.rewards.all()}/${id}/trade-confirmations/generate`
  },
  rewardInvitations: {
    all: () => "reward-invitations"
  },
  orders: {
    all: () => "orders",
    id: (id: string) => `${API_ROUTES.orders.all()}/${id}`,
    cancel: (id: string) => `${API_ROUTES.orders.all()}/${id}/cancel`,
    analytics: () => `${API_ROUTES.orders.all()}/analytics`,
    unsubmittedOrderAnalytics: () => `${API_ROUTES.orders.all()}/analytics/unsubmitted`,
    tradeConfirmations: (id: string) => `${API_ROUTES.orders.all()}/${id}/trade-confirmations/generate`
  },
  userDataRequests: {
    all: () => "user-data-requests"
  },
  events: {
    all: () => "events"
  },
  learningHub: {
    all: () => "wealthyhub",
    news: () => `${API_ROUTES.learningHub.all()}/news`,
    analystInsights: () => `${API_ROUTES.learningHub.all()}/analyst-insights`,
    analystInsightById: (id: string) => `${API_ROUTES.learningHub.all()}/analyst-insights/${id}`,
    glossary: () => `${API_ROUTES.learningHub.all()}/glossary`,
    learningGuides: () => `${API_ROUTES.learningHub.all()}/learning-guides`,
    learningGuideBySlug: (slug: string) => `${API_ROUTES.learningHub.all()}/learning-guides/slug/${slug}`
  },
  kycOperations: {
    all: () => "kyc-operations",
    initiate: () => `${API_ROUTES.kycOperations.all()}/initiate`,
    me: () => `${API_ROUTES.kycOperations.all()}/me`
  },
  assetNews: {
    all: () => "asset-news"
  },
  savingsProducts: {
    all: () => "savings-products",
    me: () => `${API_ROUTES.savingsProducts.all()}/me`,
    activity: () => `${API_ROUTES.savingsProducts.all()}/activity`,
    feeDetails: () => `${API_ROUTES.savingsProducts.all()}/fee-details`,
    data: () => `${API_ROUTES.savingsProducts.all()}/data`
  },
  notificationSettings: {
    all: () => "notification-settings",
    me: () => `${API_ROUTES.notificationSettings.all()}/me`
  }
};
class AppApiService {
  private _accessToken: string;
  private _authPromise: Promise<any>;

  constructor() {
    this._pollingFetchAccessToken();
  }

  private async _pollingFetchAccessToken(): Promise<void> {
    try {
      this._authPromise = axios.post(`https://${process.env.AUTH0_DOMAIN}/oauth/token`, {
        grant_type: "client_credentials",
        client_id: `${process.env.AUTH0_M2M_CLIENT_ID}`,
        client_secret: `${process.env.AUTH0_M2M_CLIENT_SECRET}`,
        audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`
      });

      const responseData: Auth0AccessToken = (await this._authPromise).data;
      this._accessToken = responseData.access_token;

      setTimeout(() => this._pollingFetchAccessToken(), (responseData.expires_in - 60) * 1000);
    } catch (err) {
      logger.error("Retrieval of M2M access token failed", {
        module: "AppApiService",
        method: "_pollingFetchAccessToken",
        data: {
          error: err
        }
      });
      captureException(err);
      setTimeout(() => this._pollingFetchAccessToken(), 1000); // If we couldn't reach the Auth0 API, re-request in 1 second
    }
  }

  public async postM2M(
    url: string,
    userId: string,
    body: { [key in string]: any } = {},
    queryParams: { [key in string]: any } = {},
    headers: { [key in string]: any } = {},
    options: { useApiPrefix: boolean } = { useApiPrefix: true }
  ): Promise<any> {
    const config: AxiosRequestConfig = {
      params: queryParams,
      headers: {
        "external-user-id": userId,
        ...headers
      }
    };
    return await this._fetch(HttpMethodEnum.POST, `${options.useApiPrefix ? "api/m2m/" : ""}${url}`, body, config);
  }

  public async postM2Madmin(
    url: string,
    body: { [key in string]: any } = {},
    queryParams: { [key in string]: any } = {},
    externalUserId = ""
  ): Promise<any> {
    const config: AxiosRequestConfig = {
      params: queryParams,
      headers: externalUserId
        ? {
            "external-user-id": externalUserId
          }
        : {}
    };
    return await this._fetch(HttpMethodEnum.POST, `api/admin/m2m/${url}`, body, config);
  }

  public async getM2M(
    url: string,
    userId: string,
    queryParams: { [key in string]: any } = {},
    options = { rawResponse: false }
  ): Promise<any> {
    const config: AxiosRequestConfig = {
      params: queryParams,
      headers: { "external-user-id": userId }
    };
    return await this._fetch(HttpMethodEnum.GET, `api/m2m/${url}`, {}, config, options);
  }

  public async getM2Madmin(url: string, queryParams: { [key in string]: any } = {}) {
    const config: AxiosRequestConfig = {
      params: queryParams
    };
    return await this._fetch(HttpMethodEnum.GET, `api/admin/m2m/${url}`, {}, config);
  }

  private async _fetch(
    method: HttpMethodEnum,
    url: string,
    data?: any,
    config: AxiosRequestConfig = {},
    options?: { rawResponse: boolean }
  ): Promise<any> {
    await this._authPromise;

    const requestId = randomUUID();

    const response = await axios({
      method,
      url: `${process.env.APP_API_URL}/${url}`,
      data,
      headers: { ...config.headers, Authorization: `Bearer ${this._accessToken}`, "Request-Id": requestId },
      params: config.params,
      paramsSerializer: (params) => {
        return qs.stringify(params, { arrayFormat: "repeat" });
      }
    });
    const actualRes = response.data;
    // FIXME: as discussed all api responses will have a data property (array) for the actual payload from now on
    // so return res' data field and as a fallback option retrieve whole res
    return options?.rawResponse ? actualRes : actualRes?.data ?? actualRes;
  }
}

const appApiService = new AppApiService();
export default appApiService;
