import * as configcat from "configcat-js-ssr";
import { ConfigCatFeatureFlags, ConfigCatFeatureStatusType } from "../config/featuresConfig";

export class ConfigCatService {
  private _client: configcat.IConfigCatClient;

  constructor() {
    this._client = configcat.getClient(process.env.CONFIG_CAT_API_KEY as string, configcat.PollingMode.AutoPoll);
  }

  public async getFeatureFlagStatuses(
    featuresToInclude: ConfigCatFeatureFlags[],
    email: string
  ): Promise<ConfigCatFeatureStatusType[]> {
    const allFeatures = await this._client.getAllValuesAsync({
      identifier: email
    });

    return allFeatures
      .filter((f) => featuresToInclude.includes(f.settingKey as ConfigCatFeatureFlags))
      .map((f) => ({ feat: f.setting<PERSON>ey as ConfigCatFeatureFlags, active: <PERSON><PERSON><PERSON>(f.settingValue) }));
  }
}

const configCatService = new ConfigCatService();

export default configCatService;
