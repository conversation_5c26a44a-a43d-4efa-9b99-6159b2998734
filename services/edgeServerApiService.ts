import axios, { AxiosRequestConfig } from "axios";
import { addBreadcrumb, captureException } from "@sentry/node";
import logger from "./loggerService";
import qs from "qs";
import { randomUUID } from "crypto";

enum HttpMethodEnum {
  GET = "get",
  POST = "post"
}

export enum EdgeServerApiRoutes {
  appConfig = "app-config"
}

export default class EdgeServerApiService {
  public static async get<T>(endpoint: EdgeServerApiRoutes, queryParams: { [key in string]: any } = {}) {
    const config: AxiosRequestConfig = {
      params: queryParams
    };
    const url = EdgeServerApiService._constructUrlFromEndpoint(endpoint);
    const data = await this._fetch(HttpMethodEnum.GET, url, {}, config);
    return data as T;
  }

  /**
   * PRIVATE
   */
  private static _constructUrlFromEndpoint(endpoint: EdgeServerApiRoutes): string {
    const baseUrl = process.env.EDGE_SERVER_BASE_URL;
    if (!baseUrl) {
      throw new Error("Could not found edge server base url!");
    }

    return `${baseUrl}/${endpoint}`;
  }

  private static async _fetch(
    method: HttpMethodEnum,
    url: string,
    data?: any,
    config: AxiosRequestConfig = {}
  ): Promise<any> {
    try {
      const response = await axios({
        method,
        url,
        data,
        params: config.params,
        paramsSerializer: (params) => {
          return qs.stringify(params, { arrayFormat: "repeat" });
        }
      });

      return response.data;
    } catch (err) {
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      captureException(err);
    }
  }
}
