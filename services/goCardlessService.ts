import { countriesConfig } from "@wealthyhood/shared-configs";

/**
 * DATA TYPES
 */
export type CustomerBankAccountDataType = {
  account_number: string;
  branch_code: string;
  account_holder_name: string;
  country_code: countriesConfig.CountryCodesType;
  metadata: {
    wealthyhoodId: string;
  };
  links: {
    customer: string;
  };
};

export type CustomerDataType = {
  email: string;
  given_name: string;
  family_name: string;
  address_line1: string;
  address_line2: string;
  city: string;
  postal_code: string;
  country_code: countriesConfig.CountryCodesType;
  metadata: {
    wealthyhoodId: string;
  };
};

/**
 * ENTITY TYPES
 */
export const GoCardlessMandateStatusArray = [
  "pending_customer_approval",
  "pending_submission",
  "submitted",
  "active",
  "suspended_by_payer",
  "failed",
  "cancelled",
  "expired",
  "consumed",
  "blocked"
] as const;
export type GoCardlessMandateStatusType = typeof GoCardlessMandateStatusArray[number];
export type MandateType = {
  status: GoCardlessMandateStatusType;
  next_possible_charge_date: Date;
  id: string;
};
export type MandatesType = {
  mandates: MandateType;
};

export const PaymentStatusArray = [
  "pending_customer_approval",
  "pending_submission",
  "submitted",
  "confirmed",
  "paid_out",
  "cancelled",
  "customer_approval_denied",
  "failed",
  "charged_back"
] as const;
export type PaymentStatusType = typeof PaymentStatusArray[number];

export type PaymentType = {
  amount: number; // in cents
  id: string;
  status: PaymentStatusType;
  reference: string;
  metadata: any;
};
export type PaymentsType = {
  payments: PaymentType;
};

export const EventLinkKeyArray = ["mandate", "payment"] as const;
type EventLinkKeyType = typeof EventLinkKeyArray[number];

export const ResourceTypeArray = [
  "billing_requests",
  "creditors",
  "instalment_schedules",
  "mandates",
  "payer_authorisations",
  "payments",
  "payouts",
  "refunds",
  "scheme_identifiers",
  "subscriptions"
] as const;
export type ResourceTypeType = typeof ResourceTypeArray[number];
export type EventsType = {
  events: EventType[];
};
export type EventType = {
  id: string;
  resource_type: ResourceTypeType;
  action: string;
  links: {
    [key in EventLinkKeyType]?: string;
  };
  details?: {
    cause: string;
    description: string;
  };
};

export type CustomerBankAccountType = {
  id: string;
  metadata?: {
    wealthyhoodId: string;
  };
};

export type CustomerType = {
  id: string;
};
