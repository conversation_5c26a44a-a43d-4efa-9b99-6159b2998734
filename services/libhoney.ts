import { NextFunction, Response } from "express";
import libHoney from "libhoney";
import { CustomRequest } from "custom";

export enum HoneyMessageTypeEnum {
  AUTH_MESSAGE = "AUTH_MESSAGE",
  INVEST_BTN_CLICKED = "INVEST_BTN_CLICKED",
  INVEST_BTN_CLICKED_PORTFOLIO = "INVEST_BTN_CLICKED_PORTFOLIO"
}

export const hcCaptureXhr = (options: any): ((req: CustomRequest, res: Response, next: NextFunction) => void) => {
  const honey: any = new libHoney(options);
  return function (req: CustomRequest, res: Response, next: NextFunction): void {
    honey.sendNow({
      app: req.app,
      baseUrl: req.baseUrl,
      body: req.body,
      environment: process.env.NODE_ENV,
      fresh: req.fresh,
      hostname: req.hostname,
      ip: req.ip,
      method: req.method,
      originalUrl: req.originalUrl,
      params: req.params,
      path: req.path,
      protocol: req.protocol,
      query: req.query,
      route: req.route,
      secure: req.secure,
      xhr: req.xhr,
      userId: req.user && req.user._id,
      userEmail: req.user && req.user.email
    });
    next();
  };
};

export const hcCaptureMessage = (
  options: any
): ((req: CustomRequest, message: string, messageType: HoneyMessageTypeEnum) => void) => {
  const honey: any = new libHoney(options);
  return (req: CustomRequest, message: string, messageType: HoneyMessageTypeEnum): void => {
    honey.sendNow({
      message,
      messageType,
      environment: process.env.NODE_ENV,
      ip: req.ip,
      userId: req.user && req.user._id,
      userEmail: req.user && req.user.email,
      secure: req.secure,
      protocol: req.protocol,
      method: req.method,
      hostname: req.hostname,
      originalUrl: req.originalUrl
    });
  };
};
