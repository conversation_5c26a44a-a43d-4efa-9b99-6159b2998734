import "../loaders/environment";
import winston, { createLogger, format, transports } from "winston";
import { captureException } from "@sentry/node";
import { envIsDev } from "../utils/environmentUtil";

const HTTP_TRANSPORT_OPTIONS = {
  host: "http-intake.logs.datadoghq.eu",
  path: `/v1/input/${process.env.DATADOG_API_KEY}?ddsource=nodejs&service=wealthyhood-web-server-${process.env.NODE_ENV}`,
  ssl: true
};

type LoggerOptionsType = {
  userEmail?: string;
  module?: string;
  method?: string;
  successStep?: boolean;
  data?: any;
};

class Logger {
  private _logger: winston.Logger;
  constructor() {
    this._logger = createLogger({
      level: "info",
      format: format.json(),
      exitOnError: false,
      transports: [new transports.Http(HTTP_TRANSPORT_OPTIONS)]
    });

    if (envIsDev()) {
      this._logger.add(
        new winston.transports.Console({
          format: winston.format.simple()
        })
      );
    }

    this._logger.on("error", (err) => {
      captureException(err);
    });
  }

  public info(message: string, options?: LoggerOptionsType): void {
    this._logger.info(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(options)
    });
  }

  public warn(message: string, options?: LoggerOptionsType): void {
    this._logger.warn(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(options)
    });
  }

  public error(message: string, options?: LoggerOptionsType): void {
    this._logger.error(message, {
      env: process.env.NODE_ENV,
      ...filterSensitiveValues(options)
    });
  }
}

/**
 * This function filters out any keys we want to avoid logging by replacing their value
 * with '***'. For example, if the array contains "authorization", any authorization
 * token values will be replaced by '***' before being sent to our logging destination.
 */
function filterSensitiveValues(obj: any): any {
  const keysToFilter = ["authorization", "Authorization"];

  let stringifiedObject = JSON.stringify(obj);

  keysToFilter.forEach((key) => {
    stringifiedObject = stringifiedObject.replace(new RegExp(`"${key}":"(.*?)"`), `"${key}":"***"`);
  });

  return JSON.parse(stringifiedObject);
}

const logger = new Logger();

export default logger;
