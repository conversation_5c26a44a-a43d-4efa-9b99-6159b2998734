import { ServerClient, TemplatedMessage } from "postmark";
import { captureException } from "@sentry/node";
import { UserDocument } from "../models/User";

type EmailType =
  | "depositCreation"
  | "depositFailure"
  | "depositSuccess"
  | "assetTransactionCreation"
  | "assetTransactionSuccess"
  | "userVerification"
  | "userWelcome";
const EMAIL_TEMPLATE_IDS: Record<"development" | "staging" | "production", { [key in EmailType]: number }> = {
  development: {
    depositCreation: 26217260,
    depositFailure: 24756359,
    depositSuccess: 24756301,
    assetTransactionCreation: 24756298,
    assetTransactionSuccess: 24756297,
    userVerification: 24756299,
    userWelcome: 26294051
  },
  staging: {
    depositCreation: 26217260,
    depositFailure: 24756359,
    depositSuccess: 24756301,
    assetTransactionCreation: 24756298,
    assetTransactionSuccess: 24756297,
    userVerification: 24756299,
    userWelcome: 26294051
  },
  production: {
    depositCreation: 26033860,
    depositFailure: 24618729,
    depositSuccess: 24618725,
    assetTransactionCreation: 24618731,
    assetTransactionSuccess: 24619180,
    userVerification: 24618719,
    userWelcome: 26294042
  }
};

const nodeEnv = process.env.NODE_ENV as "development" | "staging" | "production";
const postmarkServerClient = new ServerClient(process.env.POSTMARK_SERVER_API_KEY, { useHttps: true });

class MailerService {
  /**
   * PUBLIC METHODS
   */
  public static sendDepositCreation(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].depositCreation,
      TemplateModel: {
        // eslint-disable-next-line camelcase
        user_first_name: user.firstName
      }
    };

    MailerService._sendEmail(msg);
  }

  public static sendDepositFailure(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].depositFailure,
      TemplateModel: {
        // eslint-disable-next-line camelcase
        payment_url: "/"
      }
    };

    MailerService._sendEmail(msg);
  }

  public static sendDepositSuccess(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].depositSuccess,
      TemplateModel: {
        // eslint-disable-next-line camelcase
        user_first_name: user.firstName,
        // eslint-disable-next-line camelcase
        investment_url: "https://app.wealthyhood.com/?displayPortfolioBuyModal=true"
      }
    };

    MailerService._sendEmail(msg);
  }

  public static sendCreatedPortfolioTransaction(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].assetTransactionCreation,
      TemplateModel: {
        // eslint-disable-next-line camelcase
        user_first_name: user.firstName
      }
    };

    MailerService._sendEmail(msg);
  }

  public static sendWelcomeEmail(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].userWelcome,
      TemplateModel: {}
    };

    MailerService._sendEmail(msg);
  }
  public static sendEmailVerification(user: UserDocument, verificationLink: string): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: 25892720,
      TemplateModel: {
        product_url: "https://wealthyhood.com",
        product_name: "Wealthyhood",
        action_url: verificationLink,
        company_name: "Wealthyhood Ltd",
        company_address: "Unit 1 Rowan Court, 56 High Street Wimbledon, SW19 5EE, London, UK"
      }
    };

    MailerService._sendEmail(msg);
  }

  public static sendPortfolioTransactionSuccess(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].assetTransactionSuccess,
      TemplateModel: {}
    };

    MailerService._sendEmail(msg);
  }

  public static sendKYCSuccess(user: UserDocument): void {
    const msg = {
      From: "<NAME_EMAIL>",
      To: user.email,
      ReplyTo: "<NAME_EMAIL>",
      TemplateId: EMAIL_TEMPLATE_IDS[nodeEnv].userVerification,
      TemplateModel: {
        // eslint-disable-next-line camelcase
        user_first_name: user.firstName,
        // eslint-disable-next-line camelcase
        payment_url: "/"
      }
    };

    MailerService._sendEmail(msg);
  }

  /**
   * PRIVATE METHODS
   */
  private static async _sendEmail(msg: TemplatedMessage): Promise<void> {
    (async (): Promise<void> => {
      try {
        await postmarkServerClient.sendEmailWithTemplate(msg);
      } catch (err) {
        captureException(err);
      }
    })();
  }
}

export default MailerService;
