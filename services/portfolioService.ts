import { PortfolioDocument } from "../models/Portfolio";

class PortfolioService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Method to check whether a portfolio has been personalised. That means that the
   * asset class & geography preferences have been set.
   *
   * @param portfolio The portfolio db document to check.
   * @returns A boolean that indicates whether a portfolio has personalisation preferences.
   */
  public static isPortfolioPersonalised(portfolio: PortfolioDocument): boolean {
    if (!portfolio) {
      return false;
    }
    const { personalisationPreferences } = portfolio;
    // Asset classes preferences cannot be empty.
    // If they are, that means that personalisation process has not been executed.
    return (
      personalisationPreferences?.assetClasses?.length > 0 && personalisationPreferences?.geography?.length > 0
    );
  }
}

export default PortfolioService;
