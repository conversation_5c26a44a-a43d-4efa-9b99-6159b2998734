export type CustomerDataType = {
  email: string;
  name: string;
  metadata: {
    wealthyhoodId: string;
  };
};

export type CustomerType = {
  id: string;
};

export const PaymentIntentStatusArray = [
  "canceled",
  "processing",
  "requires_action",
  "requires_capture",
  "requires_confirmation",
  "requires_payment_method",
  "succeeded"
] as const;
export type PaymentIntentStatusType = typeof PaymentIntentStatusArray[number];

export const SubscriptionStatusArray = [
  "active",
  "canceled",
  "incomplete",
  "incomplete_expired",
  "past_due",
  "paused",
  "trialing",
  "unpaid"
] as const;
export type SubscriptionStatusType = typeof SubscriptionStatusArray[number];
