import axios from "axios";
import { addBreadcrumb, captureException } from "@sentry/node";
import { IResult } from "truelayer-client";
import qs from "qs";

const WK_BENEFICIARY_NAME = process.env.WEALTHKERNEL_BENEFICIARY_NAME;
const WK_BENEFICIARY_ACCOUNT_NUMBER = process.env.WEALTHKERNEL_BENEFICIARY_ACCOUNT_NUMBER;
const WK_BENEFICIARY_SORT_CODE = process.env.WEALTHKERNEL_BENEFICIARY_SORT_CODE;
if (!WK_BENEFICIARY_NAME || !WK_BENEFICIARY_ACCOUNT_NUMBER || !WK_BENEFICIARY_SORT_CODE) {
  throw new Error("WK beneficiary bank details in env vars are undefined");
}
const TRUELAYER_DOMAIN_CONFIG: Record<"development" | "staging" | "production", string> = {
  development: "truelayer-sandbox.com",
  staging: "truelayer-sandbox.com",
  production: "truelayer.com"
};

/**
 * ENUMS
 */
enum HttpMethodEnum {
  GET = "GET",
  POST = "POST"
}
enum TruelayerEndpointEnum {
  PAYMENTS = "payments"
}

/**
 * TYPES
 */

const FailureStatusArray = [
  "authorization_failed",
  "blocked",
  "canceled",
  "expired",
  "internal_server_error",
  "rejected",
  "not_authorized",
  "provider_error",
  "provider_rejected",
  "scheme_unavailable"
];
export type FailureStatusType = typeof FailureStatusArray[number];

const TruelayerPayProvidersArray = [
  "ob-starling",
  "ob-barclays",
  "ob-boi",
  "ob-bos",
  "ob-danske",
  "ob-first-direct",
  "ob-halifax",
  "ob-hsbc",
  "ob-lloyds",
  "ob-monzo",
  "ob-nationwide",
  "ob-natwest",
  "ob-rbs",
  "ob-revolut",
  "ob-santander",
  "ob-tesco",
  "ob-tsb",
  "ob-ulster"
] as const;
export type TruelayerPayProvidersType = typeof TruelayerPayProvidersArray[number];

type AccountIdentifierType = {
  type: "sort_code_account_number";
  sort_code: string; // 560029
  account_number: string; // ********
};

/* eslint-disable camelcase */
type AuthorizationFlowType = {
  configuration: {
    provider_selection: ProviderSelectionUserSelectedType | ProviderSelectionPreselectedType;
    redirect: {
      return_uri: string;
    };
    form: {
      input_types: ("text" | "select" | "text_with_image")[];
    };
  };
};

type ProviderSelectionUserSelectedType = {
  type: "user_selected";
  filter?: {
    countries?: (
      | "AT"
      | "BE"
      | "DE"
      | "DK"
      | "ES"
      | "FI"
      | "FR"
      | "GB"
      | "IE"
      | "IT"
      | "LT"
      | "NL"
      | "NO"
      | "PL"
      | "PT"
      | "RO"
    )[];
    release_channel?: "general_availability" | "public_beta" | "private_beta";
    customer_segments?: ("retail" | "business" | "corporate")[];
    provider_ids?: TruelayerPayProvidersType[];
    excludes?: {
      provider_ids?: TruelayerPayProvidersType[];
    };
  };
  // Payments in the UK use Faster Payments (faster_payments_service), which is free and instant.
  // If you only plan on making payments within the UK, you do not need to set scheme_selection for instant payments
  scheme_selection?:
    | {
        type: "instant_only";
        allow_remitter_fee?: boolean;
      }
    | {
        type: "instant_preferred";
        allow_remitter_fee?: boolean;
      };
};
type ProviderSelectionPreselectedType = {
  type: "preselected";
  provider_id: TruelayerPayProvidersType;
  // use Faster payments in the UK
  // https://docs.truelayer.com/docs/select-a-provider-for-a-payment#scheme_ids-for-payments
  scheme_id:
    | "provider_determined"
    | "sepa_credit_transfer"
    | "sepa_credit_transfer_instant"
    | "faster_payments_service";
  remitter?: {
    account_holder_name: string;
    account_identifier: AccountIdentifierType;
  };
  data_access_token?: string;
};
type BeneficiaryExternalAccountType = {
  type: "external_account";
  account_holder_name: string;
  account_identifier: AccountIdentifierType;
  reference: string; // pattern: ^[a-zA-Z0-9-:()\.,'\+ \?\/]+$
};

type PaymentMethodType = {
  type: "bank_transfer";
  provider_selection: ProviderSelectionUserSelectedType | ProviderSelectionPreselectedType;
  beneficiary: BeneficiaryExternalAccountType;
};
type UserType = {
  id?: string; // f9b48c9d-176b-46dd-b2da-fe1a2b77350c
  name?: string; // Remi Terr
  email?: string; // <EMAIL>
  phone?: string; // +************
  date_of_birth?: Date; // in YYYY-MM-DD format - 1990-01-31
  address?: {
    address_line1: string;
    address_line2?: string;
    city: string;
    state: string;
    zip: string;
    country_code: string; // https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
  };
};
export type PaymentOptionsType = {
  amount_in_minor: number; // in minor units (cents)
  currency: "GBP" | "EUR" | "PLN" | "NOK";
  payment_method: PaymentMethodType;
  user: UserType;
  metadata?: object;
};
type PaymentSourceType = {
  account_identifies: AccountIdentifierType[];
  account_holder_name: string;
};

export type PaymentCreationResponseType = {
  id: string;
  user: {
    id: string;
  };
  resource_token: string;
  status: TruelayerPaymentStatusType;
};
/* eslint-enable camelcase */

export const PaymentStatusArray = [
  "authorization_required",
  "authorizing",
  "authorized",
  "executed", // terminal state for external accounts
  "failed", // terminal for all payments
  "settled" // terminal for payments into merchant accounts
] as const;
export type TruelayerPaymentStatusType = typeof PaymentStatusArray[number];
// Corresponds to the type of the payment response object returned by the API
export type PaymentType = {
  id: string;
  created_at: Date;
  executed_at?: Date;
  status: TruelayerPaymentStatusType;
  authorization_flow: AuthorizationFlowType;
  payment_source?: PaymentSourceType;
  failure_reason?: FailureStatusType;
} & PaymentOptionsType;
export type ProviderType = {
  provider_id: string;
  logo_url: string;
  icon_url: string;
  display_name: string;
  country: string;
  divisions: ("retail" | "business")[];
  single_immediate_payment_schemes: { scheme_id: "faster_payments_service"; requirements: object[] }[];
};

const TRUELAYER_API_URL = process.env.TRUELAYER_API_URL;
const TRUELAYER_AUTH_URL = process.env.TRUELAYER_AUTH_URL;
const TRUELAYER_PAY_URL = process.env.TRUELAYER_PAY_URL;
export const TRUELAYER_ENABLED_PROVIDERS =
  process.env.NODE_ENV === "production" ? TruelayerPayProvidersArray : ["mock-payments-gb-redirect", "mock"];

export const TRUELAYER_DATA_PROVIDERS =
  process.env.NODE_ENV === "production" ? "uk-ob-all+uk-oauth-all" : "uk-cs-mock";

/**
 * OTHER TYPES
 */
type RequestHeadersType = {
  Authorization?: string;
  "Idempotency-Key"?: string;
  "Tl-signature"?: string;
};

export class TruelayerPaymentsClient {
  private static _clientId = process.env.TRUELAYER_CLIENT_ID;
  private static _clientSecret = process.env.TRUELAYER_CLIENT_SECRET;
  private _accessToken: string;
  private _authPromise: Promise<any>;

  constructor() {
    TruelayerPaymentsClient._verifyCredentialsExist();
    this._requestAccessToken();
  }

  // ==========
  // PUBLIC METHODS
  // ==========

  public async getPayment(paymentId: string): Promise<PaymentType> {
    const url = `${TruelayerEndpointEnum.PAYMENTS}/${paymentId}`;
    return await this._fetch({ method: HttpMethodEnum.GET, url });
  }

  public async getProviders(): Promise<IResult<ProviderType>> {
    const response = await axios({
      method: HttpMethodEnum.GET,
      url: `${TRUELAYER_PAY_URL}/v2/single-immediate-payments-providers`,
      params: {
        currency: "GBP",
        auth_flow_type: "redirect",
        account_type: "sort_code_account_number",
        client_id: process.env.TRUELAYER_CLIENT_ID
      }
    });
    return response.data;
  }

  // ==========
  // PRIVATE METHODS
  // ==========

  private _buildHostedPageUrl({
    payment_id,
    resource_token,
    return_uri
  }: {
    payment_id: string;
    resource_token: string;
    return_uri: string;
  }): string {
    return `https://payment.${
      TRUELAYER_DOMAIN_CONFIG[process.env.NODE_ENV as "development" | "staging" | "production"]
    }/payments#payment_id=${payment_id}&resource_token=${resource_token}&return_uri=${return_uri}`;
  }

  /**
   * @description Returns a promise to indicate whether the API authentication is complete
   */
  private async _isReady(): Promise<boolean> {
    await this._authPromise;
    return Boolean(this._accessToken);
  }

  /**
   * @description Throws an error if any of env variables required to use the Truelayer API, are not set.
   */
  private static _verifyCredentialsExist(): void {
    if (!TruelayerPaymentsClient._clientId) {
      throw new Error("TRUELAYER_CLIENT_ID env variable is not set");
    } else if (!TruelayerPaymentsClient._clientSecret) {
      throw new Error("TRUELAYER_CLIENT_SECRET env variable is not set");
    }
  }

  /**
   * @description Method to fetch the access token that's used to authenticate TL API requests. Once the access
   * token & the expiration date of that token are fetched, a timeout is set (a minute faster than the expiration)
   * to renew the access token
   *
   */
  private async _requestAccessToken(): Promise<void> {
    try {
      // 1. Use client id & secret to obtain access token
      this._authPromise = axios.post(
        `${TRUELAYER_AUTH_URL}/connect/token`,
        qs.stringify({
          // eslint-disable-next-line camelcase
          grant_type: "client_credentials",
          // eslint-disable-next-line camelcase
          client_id: TruelayerPaymentsClient._clientId,
          // eslint-disable-next-line camelcase
          client_secret: TruelayerPaymentsClient._clientSecret,
          scope: "payments"
        })
      );
      const response = await this._authPromise;
      this._accessToken = response.data.access_token;

      // 2. Check when it expires to renew it
      setTimeout(() => this._requestAccessToken(), (response.data.expires_in - 60) * 1000);
    } catch (err) {
      captureException(err);
      setTimeout(() => this._requestAccessToken(), 1000);
    }
  }

  /**
   * @description This is the method for making any requests to access the TL PAY API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param url the Truelayer endpoint that we want to access as defined in the endpoint enum
   * @param headers
   * @param data any data that may be posted with the request
   */
  private async _fetch({
    method,
    url,
    headers,
    data
  }: {
    method: HttpMethodEnum;
    url: string;
    headers?: RequestHeadersType;
    data?: any;
  }): Promise<any> {
    try {
      await this._isReady();

      const response = await axios({
        method,
        url: `${TRUELAYER_API_URL}/${url}`,
        headers: {
          ...headers,
          Authorization: `Bearer ${this._accessToken}`
        },
        data
      });
      return response.data;
    } catch (err) {
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4)
        }
      });
      captureException(err);
    }
  }
}
