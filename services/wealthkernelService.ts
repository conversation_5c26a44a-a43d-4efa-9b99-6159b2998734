import { countriesConfig } from "@wealthyhood/shared-configs";

export enum CurrencyEnum {
  EUR = "EUR",
  GBP = "GBP",
  USD = "USD"
}

/**
 * DATA TYPES
 */
export type AccountDataType = {
  type: PortfolioWrapperTypeEnum;
  clientReference?: string;
  name: string;
  productId: string;
  owner: string;
};
type AddressDataType = {
  partyId: string;
  clientReference?: string;
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  region?: string;
  countryCode: countriesConfig.CountryCodesType; // Must be ISO3166 two-letter country code.
  postalCode: string;
  startDate?: Date;
  endDate?: Date;
};

type BankAccountDataType = {
  partyId: string;
  clientReference?: string;
  name: string;
  accountNumber: string;
  sortCode: string;
  currency: CurrencyEnum;
  countryCode: countriesConfig.CountryCodesType; // Currently only 'GB' is supported
};

export const DocumentCodesArray = ["NINO"] as const;
export type DocumentCodesType = typeof DocumentCodesArray[number];

type IdentifierType = {
  issuer: countriesConfig.CountryCodesType;
  type: DocumentCodesType;
  value: string;
};

export enum PortfolioWrapperTypeEnum {
  GIA = "GIA",
  ISA = "ISA",
  JISA = "JISA",
  SIPP = "SIPP"
}
export const PortfolioWrapperArray = ["GIA", "ISA", "JISA", "SIPP"] as const;
const PortfolioMandateArray = ["ExecutionOnlyMandate"];
type PortfolioMandateType = typeof PortfolioMandateArray[number];
export type PortfolioDataType = {
  accountId: string;
  clientReference?: string;
  name: string;
  currency: CurrencyEnum;
  mandate: {
    type: PortfolioMandateType;
  };
};
export type CashValuationType = {
  currency: string;
  value: MoneyType;
  amount: MoneyType;
  fxRate: number;
};
export type DepositDataType = {
  portfolioId: string; // wealthkernel portfolio id
  useDefaultAccount: boolean;
  bankAccountId?: string;
  consideration: {
    currency: string;
    amount: number;
  };
  reference: string; // bank reference for the deposit
};
export type HoldingValuationType = {
  isin: string;
  quantity: number;
  price: MoneyType;
  value: MoneyType;
  fxRate: number;
};
type MoneyType = {
  currency: string;
  amount: number;
};
export type OrderDataType = {
  portfolioId: string;
  isin: string;
  settlementCurrency: CurrencyEnum;
  side: OrderSideType;
  consideration?: {
    currency: CurrencyEnum;
    amount: number;
  };
  clientReference?: string;
  quantity?: number;
};

export type PartyDataType = {
  clientReference?: string;
  type: "Person";
  title?: string;
  forename: string;
  middlename?: string;
  surname: string;
  previousSurname?: string;
  emailAddress: string;
  telephoneNumber?: string;
  dateOfBirth: string;
  taxResidencies: countriesConfig.CountryCodesType[];
  nationalities: countriesConfig.CountryCodesType[];
  identifiers: IdentifierType[];
};

export const WithdrawalRequestArray = ["SpecifiedAmount", "Full"] as const;
export type WithdrawalRequestType = typeof WithdrawalRequestArray[number];

export type WithdrawalRetrievalResponseType = {
  id: string;
  type: "SpecifiedAmount" | "Full";
  portfolioId: string;
  bankAccountId: string;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  reference: string;
  status: WithdrawalStatusType;
  requestedAt: Date;
};

/**
 * ENTITY TYPES
 */

export const BonusStatusArray = ["Created", "Rejected", "Settled"] as const;
export type BonusStatusType = typeof BonusStatusArray[number];

export const AccountStatusArray = ["Pending", "Active", "Suspended", "Closing", "Closed"] as const;
export type AccountStatusType = typeof AccountStatusArray[number];
export type AccountType = AccountDataType & {
  status: AccountStatusType;
  addedAt: Date;
  id: string;
};
export type AddressType = AddressDataType & {
  id: string;
};
export const BankAccountStatusArray = ["Pending", "Active", "Inactive", "Suspended"] as const;
export type BankAccountStatusType = typeof BankAccountStatusArray[number];
export type BankAccountType = BankAccountDataType & {
  id: string;
  status: BankAccountStatusType;
  activatedAt: Date;
  deactivatedAt: Date;
};
export const DepositStatusArray = ["Created", "Settled", "Cancelled", "Rejected", "Cancelling"] as const;
export type DepositStatusType = typeof DepositStatusArray[number];
export type DepositType = {
  id: string;
  portfolioId: string;
  accountId: string;
  consideration: {
    currency: string;
    amount: number;
  };
  reference: string;
  status: DepositStatusType;
  createdAt: string;
};
export type FillType = {
  transactionId: string;
  price: MoneyType;
  consideration: {
    currency: CurrencyEnum;
    amount: number;
  };
  quantity: number;
  status: "Matched" | "Cancelled";
};

export const DirectDebitPaymentStatusArray = [
  "Pending",
  "Collecting",
  "Collected",
  "Completed",
  "Cancelled",
  "Failed"
] as const;
export type DirectDebitPaymentStatusType = typeof DirectDebitPaymentStatusArray[number];
export const OrderSideArray = ["Buy", "Sell"] as const;
export type OrderSideType = typeof OrderSideArray[number];

export const OrderStatusArray = ["Pending", "Open", "Matched", "Rejected", "Cancelling", "Cancelled"];
export type OrderStatusType = typeof OrderStatusArray[number];
export type OrderType = OrderDataType & {
  fills: FillType[];
  reason: string;
  receivedAt: Date;
  id: string;
  status: OrderStatusType;
};

export type PartyType = PartyDataType & {
  addedAt: Date;
  id: string;
};

export const PortfolioStatusArray = ["Created", "Active", "Closing", "Closed"] as const;
export type PortfolioStatusType = typeof PortfolioStatusArray[number];
export const TransactionStatusArray = ["Matched", "Settled", "Cancelled"] as const;
export type TransactionStatusType = typeof TransactionStatusArray[number];
export const WithdrawalStatusArray = ["Pending", "Active", "Settled", "Cancelling", "Cancelled", "Rejected"];
export type WithdrawalStatusType = typeof WithdrawalStatusArray[number];

export type ValuationType = {
  portfolioId: string;
  date: Date;
  value: MoneyType;
  cash: CashValuationType[];
  holdings: HoldingValuationType[];
  changedAt: Date;
};
