import "express-openid-connect"; // Need it in order to have the oidc type in req & res
import { Response } from "express";
import faker from "faker";
import { CustomRequest } from "../../types/custom";

function buildUserOIDC(overrides: Record<string, any> = {}) {
  return {
    [`${process.env.DOMAIN_URL}/roles`]: ["INVESTOR"],
    [`${process.env.DOMAIN_URL}/createdAt`]: faker.date.recent(),
    nickname: faker.internet.userName(),
    name: faker.internet.email(),
    updated_at: faker.date.recent(),
    email: faker.internet.email(),
    email_verified: true,
    sub: `auth0|${faker.datatype.uuid()}`,
    ...overrides
  };
}

function buildReq(overrides: any = {}): CustomRequest {
  const req: any = {
    body: {},
    params: {},
    query: {},
    flash: jest.fn().mockName("flash"),
    oidc: { user: buildUserOIDC(), isAuthenticated: () => true },
    ...overrides
  };
  return req;
}

function buildRes(overrides: any = {}): Response {
  const res: any = {
    json: jest.fn(() => res).mockName("json"),
    redirect: jest.fn().mockName("redirect"),
    render: jest.fn().mockName("render"),
    status: jest.fn(() => res).mockName("status"),
    sendStatus: jest.fn(() => res).mockName("sendStatus"),
    ...overrides
  };
  return res;
}

function buildNext(impl?: any) {
  return jest.fn(impl).mockName("next");
}

function buildAppApiServiceError(overrides: any = {}) {
  return {
    message: "Something went wrong with axios request",
    response: {
      data: {
        status: 500,
        error: { message: faker.lorem.sentence(), description: faker.lorem.sentence() },
        responseId: faker.datatype.uuid(),
        ...overrides
      }
    }
  };
}

export { buildReq, buildRes, buildNext, buildAppApiServiceError };
