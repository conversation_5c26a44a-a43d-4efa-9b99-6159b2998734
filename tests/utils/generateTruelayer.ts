import faker from "faker";
import { IResult } from "truelayer-client";
import { PaymentType, ProviderType } from "../../services/truelayerService";

function buildPaymentType(overrides: any = {}): PaymentType {
  return {
    simp_id: faker.datatype.uuid(),
    created_at: faker.datatype.datetime().toString(),
    amount: faker.datatype.number(),
    currency: faker.datatype.string(),
    beneficiary_reference: faker.datatype.string(),
    beneficiary_name: faker.datatype.string(),
    beneficiary_sort_code: faker.datatype.string(),
    beneficiary_account_number: faker.datatype.string(),
    remitter_reference: faker.datatype.string(),
    redirect_uri: faker.datatype.string(),
    remitter_provider_id: faker.datatype.string(),
    status: "authorized",
    webhook_uri: faker.datatype.string(),
    ...overrides
  };
}

function buildProviderType(overrides: any = {}): ProviderType {
  return {
    provider_id: faker.datatype.uuid(),
    logo_url: faker.commerce.productName(),
    icon_url: faker.datatype.string(),
    display_name: faker.name.firstName(),
    country: faker.address.countryCode(),
    divisions: [],
    ...overrides
  };
}

function buildPaymentTypeResults(overrides: any = {}): IResult<PaymentType> {
  return { results: [buildPaymentType(), buildPaymentType()], ...overrides };
}

export { buildPaymentType, buildPaymentTypeResults, buildProviderType };
