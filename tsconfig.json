{"compilerOptions": {"module": "commonjs", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react", "target": "es2019", "noImplicitAny": true, "moduleResolution": "node", "sourceMap": true, "outDir": "dist", "baseUrl": ".", "paths": {"*": ["node_modules/*", "types/*"]}}, "include": ["app.ts", "server.ts", "jobs/asset-transaction-cron.ts", "jobs/ongoing-cron.ts", "jobs/post-valuation-cron.ts", "jobs/order-submission-cron.ts", "jobs/nightly-cron.ts", "jobs/ticker-cron.ts"], "exclude": ["*.js, **/*.js"]}