import { TransactionDocument } from "../models/Transaction";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

export interface PaginatedTransactionsResponse<T extends TransactionDocument> {
  pagination: PaginationType;
  transactions: T[];
}

export interface PaginatedApiResponse<T> {
  pagination: PaginationType;
  data: T[];
}

export type PaginationType = {
  page: number;
  pageSize: number;
  pages: number;
  total: number;
};

export interface UnsubmittedOrderAnalyticsResponse {
  lines: {
    [etf in investmentUniverseConfig.AssetType]: {
      buyOrders: number;
      buyConsideration: number;
      sellOrders: number;
      sellConsideration: number;
    };
  };
}

export interface OrderAnalyticsResponse {
  analytics: {
    date: string;
    lines: {
      [etf in investmentUniverseConfig.AssetType]: {
        buyOrders: number;
        buyConsideration: number;
        sellOrders: number;
        sellConsideration: number;
        allMatched: boolean;
        allInTerminalState: boolean;
      };
    };
  }[];
}
