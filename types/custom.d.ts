import { ConfigCatFeatureStatusType } from "../config/featuresConfig";
import { UserDocument } from "../models/User";
import { Request } from "express";
import { localeConfig } from "@wealthyhood/shared-configs";

declare namespace custom {
  interface CustomRequest extends Request {
    featureFlags?: ConfigCatFeatureStatusType[];
    user: UserDocument;
    file: any;
    locale: localeConfig.LocaleType;
  }

  interface CustomError extends Error {
    status?: number;
  }
}

export = custom;
