import glossary, { GlossaryKeyType } from "../glossary";

export const getTermDescription = (term: GlossaryKeyType): string =>
  Object.keys(glossary).includes(term) ? glossary[term].description : "";

export const getTermUrl = (term: GlossaryKeyType): string =>
  Object.keys(glossary).includes(term)
    ? `${process.env.LANDING_PAGE_URL}/glossary#${glossary[term].urlPath}`
    : `${process.env.LANDING_PAGE_URL}/glossary`;
