import UAParser from "ua-parser-js";

/**
 * Returns true if we have a mobile app for the given user agent string
 * @param userAgent
 */
export const isSupportedMobile = (userAgent: string): boolean => {
  const parser = new UAParser(userAgent);

  const isIPhone = parser.getDevice().model?.includes("iPhone");
  const isMobileAndroid = parser.getOS().name?.includes("Android") && parser.getDevice().type === "mobile";

  return isIPhone || isMobileAndroid;
};
