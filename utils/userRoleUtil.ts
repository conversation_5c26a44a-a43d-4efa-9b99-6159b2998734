import util from "util";
import config from "../config";
import { UserDocument, UserTypeEnum } from "../models/User";

/**
 * @description Gives the file path for the user profile image inside the S3 bucket
 */
export const getUserImagePath = (userId: string, imageName: string): string =>
  util.format(config.userImagePath, userId, imageName);

/**
 * @description Checks if a user is in the allowed array of roles
 * @param allowedRoles
 * @param user
 */
export const inUserRoles = (user: UserDocument, allowedRoles: UserTypeEnum[]): boolean =>
  user.role.filter((role) => allowedRoles.includes(role)).length > 0;
export const isAdmin = (user: UserDocument): boolean => user.role.includes(UserTypeEnum.ADMIN);
export const isInvestor = (user: UserDocument): boolean => user.role.includes(UserTypeEnum.INVESTOR);
export const isTestAccount = (user: UserDocument): boolean => user.role.includes(UserTypeEnum.TEST_ACCOUNT);
