extends layout

block content
  .row.justify-content-center
    //- Fund submission form
    .col-md-10
      .kt-portlet
        .kt-portlet__head
          .kt-portlet__head-label
            - const firm = user.firm || {}
            if firm.name || firm.link || firm.description
              h3.kt-portlet__head-title Update Company
            else
              h3.kt-portlet__head-title New Company
        .kt-portlet__body
          form(action='/professional-adviser/firm/add' method='POST' enctype='multipart/form-data')
            .form-group.row
              label.col-1.col-form-label(for='logo') Logo
              .col-11
                #kt_profile_avatar_1.kt-avatar
                  if user.firm && user.firm.logo
                    .kt-avatar__holder(style=`background-image: url(${h.firmUtil.getFirmLogoURL(user._id, user.firm.logo)})`)
                  else
                    .kt-avatar__holder(style='background-image: url(/images/avatars/company_default.png)')
                  label.kt-avatar__upload(data-toggle='kt-tooltip', title='Change logo')
                    i.flaticon2-edit
                    input(type='file', name='logo', accept='.png, .jpg, .jpeg')
                  span.kt-avatar__cancel(data-toggle='kt-tooltip', title='Cancel logo')
                    i.flaticon-cancel
            .form-row
              .col-md-6
                .position-relative.form-group
                  label(for='name') Name
                    | !{' '}
                    span.text-danger *
                  input.form-control(name='name', placeholder='Company Name', type='text', value=`${firm.name || ''}`)
              .col-md-6
                .position-relative.form-group
                  label(for='url') Link
                  input.form-control(name='url', placeholder='Company url e.g. https://yourcompany.com', type='text', value=`${firm.url || ''}`)
            .position-relative.form-group
              label(for='description') Description
                | !{' '}
                span.text-danger *
              textarea.form-control(rows='8', name='description', placeholder='A 4-5 sentences intro for your company', type='text')
                | #{firm.description || ''}
            input.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit" value="Submit")

block scripts
  script(src='/vendors/js/components/avatar.js', type='text/javascript')
