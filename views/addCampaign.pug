extends layout

block content
  .row.justify-content-center
    //- Fund submission form
    .col-md-10
      .kt-portlet
        .kt-portlet__head
          .kt-portlet__head-label
            if fund.isCampaignCreated
              h3.kt-portlet__head-title Update Campaign
            else
              h3.kt-portlet__head-title New Campaign
        .kt-portlet__body
          form(action=`/fund/${fund._id}/campaign/add` method='POST')
            .row
              .col-md-4
                .position-relative.form-group
                  h5 Total Tokens
                  input.form-control(name='totalTokens' placeholder='Total tokens to issue' type='text' value=`${fund.totalTokens || ''}`)
              .col-md-4
                .position-relative.form-group
                  h5 Initial Token Price
                  input.form-control(name='initialTokenPrice' placeholder='Token price' type='text' value=`${fund.initialTokenPrice || ''}`)
              //- .col-md-4
                .position-relative.form-group
                  h5 Minimum Investment
                  input.form-control(name='minimumInvestment' placeholder='Minimum amount a user can invest' type='text' value=`${fund.minimumInvestment || ''}`)

            .row
              .col-md-6
                .position-relative.form-group
                  - date = fund.fundraiseStartDate && new Date(fund.fundraiseStartDate)
                  - formatedDate = date && `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`

                  h5 Fundraise Starting Date
                  .input-group
                    input#kt_datepicker_1.form-control(readonly="" type="text" name="fundraiseStartDate" data-toggle="datepicker" placeholder="Select a starting date to fundraise" value=`${formatedDate || ''}`)
                    .input-group-append
                      span.input-group-text
                        i.flaticon-calendar-3

              .col-md-6
                .position-relative.form-group
                  - date = fund.fundraiseStartDate && new Date(fund.fundraiseEndDate)
                  - formatedDate = date && `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`

                  h5 Fundraise Completion Date
                  .input-group
                    input#kt_datepicker_1b.form-control(readonly="" type="text" name="fundraiseEndDate" data-toggle="datepicker" placeholder="Select a date to complete fundraising" value=`${formatedDate || ''}`)
                    .input-group-append
                      span.input-group-text
                        i.flaticon-calendar-3

            input.btn.btn-outline-brand.btn-square(type="submit" value="Submit")
