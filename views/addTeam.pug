extends layout

block content
  .row.justify-content-center
    //- Fund submission form
    .col-md-10
      .kt-portlet
        .kt-portlet__head
          .kt-portlet__head-label
            if person
              h3.kt-portlet__head-title Update
            else
              h3.kt-portlet__head-title New Member
        .kt-portlet__body
          form(action=`/fund-manager/team/${member.id || ""}` method='POST' enctype='multipart/form-data')
            .form-group.row
              label.col-1.col-form-label(for='img') Image
              .col-11
                #kt_profile_avatar_1.kt-avatar
                  if member.img
                    .kt-avatar__holder(style=`background-image: url(${h.firmUtil.getTeamLogoURL(member.img)})`)
                  else
                    .kt-avatar__holder(style='background-image: url(/images/avatars/user_default.jpg)')
                  label.kt-avatar__upload(data-toggle='kt-tooltip', title='Change image')
                    i.flaticon2-edit
                    input(type='file', name='img', accept='.png, .jpg, .jpeg')
                  span.kt-avatar__cancel(data-toggle='kt-tooltip', title='Cancel image')
                    i.flaticon-cancel
            .form-row
              .col-md-6
                .position-relative.form-group
                  label(for='name') Name
                    | !{' '}
                    span.text-danger *
                  input.form-control(name='name', placeholder='First name & last name', type='text', value=`${member.name || ''}`)
              .col-md-6
                .position-relative.form-group
                  label(for='role') Role
                    | !{' '}
                    span.text-danger *
                  input.form-control(name='role', placeholder='Role in the firm e.g. Investment Committee Member', type='text', value=`${member.role || ''}`)
            .form-row
              .col-md-6
                .position-relative.form-group
                  label(for='personal_url') Personal Url
                  input.form-control(name='personal_url', placeholder='Link to your personal website or company profile (optional)', type='text', value=`${member.personalUrl || ''}`)
              .col-md-6
                .position-relative.form-group
                  label(for='linkedin_url') Linkedin Url
                  input.form-control(name='linkedin_url', placeholder='Link to your LinkedIn profile (optional)', type='text', value=`${member.linkedinUrl || ''}`)
            .position-relative.form-group
              label(for='bio') Bio
                | !{' '}
                span.text-danger *
              textarea.form-control(rows='8', name='bio', placeholder='A 4-5 sentences description of your experience and/or interests', type='text')
                | #{member.bio || ''}
            input.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit" value="Submit")

block scripts
  script(src='/vendors/js/components/avatar.js', type='text/javascript')
