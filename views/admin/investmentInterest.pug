extends ../layout

block content
  -
    const INVESTMENT_MAPPING = {
      "1_to_10": "1,000-10,000",
      "10_to_20": "10,000-20,000",
      "20_to_50": "20,000-50,000",
      "gt_50": "50,000+"
    }

  .row.justify-content-center
    .col-lg-6
      for interest in investmentInterests
        .kt-portlet
          .kt-portlet__head
            .kt-portlet__head-label
              if interest.fund && interest.fund[0] && interest.fund[0].fundName
                h3.kt-portlet__head-title= interest.fund[0].fundName
              else
                h3.kt-portlet__head-title Wealthyhood Portfolios
          .kt-portlet__body
            .kt-widget-4
              - const investments = interest.investments
              for investment in investments
                .kt-widget-4__item
                  .kt-widget-4__item-content
                    .kt-widget-4__item-section
                      .kt-widget-4__item-info
                        a.kt-widget-4__item-username(href='javascript:void(0)')= [investment.investor[0].firstName, investment.investor[0].lastName].join(" ")
                        .kt-widget-4__item-desc= investment.investor[0].email
                  .kt-widget-4__item-content
                    .kt-widget-4__item-price
                      span.kt-widget-4__item-number= INVESTMENT_MAPPING[investment.amountRange]
