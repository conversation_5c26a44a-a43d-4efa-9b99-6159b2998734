extends ../layout

block stylesheets
  style.
    .card-title:after {
      content: "" !important;
    }

block content
  .row.justify-content-center
    .col-lg-6
      h4.mb-3.text-center Watchlist Stats

      .accordion.accordion-outline
        for item, index in watchlistData
          .card
            .card-header(id=`heading_${index}`)
              .card-title.collapsed(data-toggle='collapse', data-target=`#collapse_${index}`, aria-expanded='false', aria-controls='collapseOne3')
                | #{item.fund.fundName} : #{item.watchers.length}
            .card-body-wrapper.collapse(aria-labelledby=`heading_${index}`, id=`collapse_${index}`)
              .card-body.px-0.mx-0
                ul(style="list-style-type:none;")
                  for user in item.watchers
                    li
                      span.kt-font-bolder= [user.firstName, user.lastName].join(" ")
                      span.kt-badge.kt-badge--primary.kt-badge--dot.mx-2
                      span= user.email
