extends layout

block stylesheets
  link(href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.css" rel="stylesheet" type="text/css")
  link(href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" type="text/css")

block content
  .row
    .col-lg-3.col-md-3.col-sm-12
      .sticky(style="position: -webkit-sticky; position:sticky; top:80px;")
        //- Section selection
        .main-card.mb-3.card.no-shadow.pr-3
          .card-body
            h5.card-title Sections
            ul.list-group
              a.list-group-item(href='#editFund__admin-section') Admin Fields
              a.list-group-item(href='#editFund__performance-section') Performance Section

        //- New fund button
        a.mb-3.mr-5.btn-wide.btn.btn-lg.btn-outline-brand.btn-block.btn-square(href="/admin/dashboard" data-style="expand-right")
          | Dashboard

    .col-lg-7.col-md-7.col-sm-12
      form(method="POST" action=`/admin/fund/${fund._id}` enctype="multipart/form-data")
        // Fund Header Section
        div#editFund__admin-section
          .kt-portlet.mb-5
            .kt-portlet__head
              .kt-portlet__head-label
                h3.kt-portlet__head-title Fund Admin Fields
                  | !{" "}
                  span.text-muted #{fund.fundName ? `(${fund.fundName})` : ""}
            .kt-portlet__body
              - const overview = fund.overview || {};

              .form-group.row
                label.col-4.col-form-label(for='pseudo_name') Has pseudo name
                .col-8
                  label.kt-checkbox
                    if fund.hasPseudoName
                      input(type='checkbox', name='pseudo_name', checked='checked', value='1')
                    else
                      input(type='checkbox', name='pseudo_name')
                    span
              .form-group.row
                label.col-4.col-form-label(for='investment_allowed') Is investment allowed
                .col-8
                  label.kt-checkbox
                    if fund.investmentAllowed
                      input(type='checkbox', name='investment_allowed', checked='checked', value='1')
                    else
                      input(type='checkbox', name='investment_allowed')
                    span
              .form-group.row
                label.col-4.col-form-label(for='symbol') Symbol
                .col-8
                  input#symbol_mask.form-control(name='symbol', placeholder='Short, max 5 letter symbol', type='text', value=`${fund.symbol || ''}`, maxlength='5')
              .form-group.row
                label.col-4.col-form-label(for='overview_minimum_investment') Minimum investment
                .col-8
                  input#min_investment_mask.form-control(type='text', value=`${overview.minimumInvestment || ''}`, name='overview_minimum_investment', maxlength='7' style="text-align: left;", placeholder="0")
              .form-group.row
                label.col-4.col-form-label(for='overview_minimum_investment_currency') Minimum investment currency
                .col-8
                  .kt-radio-inline
                    label.kt-radio
                      if overview.minimumInvestmentCurrency === "$"
                        input(type="radio" name="overview_minimum_investment_currency" checked="checked" value="$")
                      else
                        input(type="radio" name="overview_minimum_investment_currency" value="$")
                      | $
                      span
                    label.kt-radio
                      if overview.minimumInvestmentCurrency === "£"
                        input(type="radio" name="overview_minimum_investment_currency" checked="checked" value="£")
                      else
                        input(type="radio" name="overview_minimum_investment_currency" value="£")
                      | £
                      span
                    label.kt-radio
                      if overview.minimumInvestmentCurrency === "€"
                        input(type="radio" name="overview_minimum_investment_currency" checked="checked" value="€")
                      else
                        input(type="radio" name="overview_minimum_investment_currency" value="€")
                      | €
                      span
              .form-group.row
                label.col-4.col-form-label Transfer of securities allowed
                .col-8
                  .kt-radio-inline
                    label.kt-radio
                      if overview.securitiesTransfer === "Yes"
                        input(type="radio" name="overview_securities_transfer" checked="checked" value="Yes")
                      else
                        input(type="radio" name="overview_securities_transfer" value="Yes")
                      | Yes
                      span
                    label.kt-radio
                      if overview.securitiesTransfer === "No"
                        input(type="radio" name="overview_securities_transfer" checked="checked" value="No")
                      else
                        input(type="radio" name="overview_securities_transfer" value="No")
                      | No
                      span
              .form-group.row
                label.col-4.col-form-label(for='priority_weight') Priority Weight
                .col-8
                  input.int_mask.form-control(type='text', value=`${fund.priorityWeight || ''}`, name='priority_weight', placeholder="0")
              .form-group.row
                label.col-4.col-form-label(for='symbol') Focus Tags
                .col-8
                  select#select2_tags_focus.form-control.kt-select2(name='tags_focus', multiple='multiple')
                    - const focusTags = fund.tags && fund.tags.focus || [];
                    for tag in FocusTagEnum
                      if focusTags.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
              .form-group.row
                label.col-4.col-form-label(for='tags_exposure') Exposure Tags
                .col-8
                  select#select2_tags_exposure.form-control.kt-select2(name='tags_exposure', multiple='multiple')
                    - const exposureTags = fund.tags && fund.tags.exposure || [];
                    for tag in ExposureTagEnum
                      if exposureTags.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
              .form-group.row
                label.col-4.col-form-label(for='tags_method') Method Tags
                .col-8
                  select#select2_tags_method.form-control.kt-select2(name='tags_method', multiple='multiple')
                    - const methodTags = fund.tags && fund.tags.method || [];
                    for tag in MethodTagEnum
                      if methodTags.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
              .col-lg-2.col-md-12.pl-0
                button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Submit

        div#editFund__performance-section
          //- Documents Section
          .kt-portlet.mb-5
            .kt-portlet__head
              .kt-portlet__head-label
                h3.kt-portlet__head-title Performance Section
            .kt-portlet__body
              .row
                .col-12
                  .kt-widget-7
                    .kt-widget-7__items
                      if fund.performanceCSV
                        .kt-widget-7__item
                          .kt-widget-7__item-pic
                            img(src='/images/files/csv.svg', alt='CSV')
                          .kt-widget-7__item-info
                            a.kt-widget-7__item-title(href=`${h.fundUtil.getOssUrl(fund.performanceCSV)}` target='_blank') Current CSV
                          .kt-widget-7__item-toolbar
                            .dropdown.dropdown-inline
                              button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                                i.flaticon-more-1
                              .dropdown-menu.dropdown-menu-right
                                ul.kt-nav
                                  li.kt-nav__section.kt-nav__section--first
                                    span.kt-nav__section-text ACTIONS
                                  li.kt-nav__item
                                    a.kt-nav__link(href=`${h.fundUtil.getOssUrl(fund.performanceCSV)}` target="_blank")
                                      i.kt-nav__link-icon.flaticon-eye
                                      span.kt-nav__link-text View
                      else
                        p No file uploaded yet.
              .form-group.row
                label.col-3.col-form-label(for='performance_csv') Upload new document
                .col-9
                  #kt_profile_avatar_1.kt-avatar
                    .kt-avatar__holder(style='background-image: url(/images/files/csv.svg)')
                    label.kt-avatar__upload(data-toggle='kt-tooltip', title='Upload CSV')
                      i.flaticon2-edit
                      input(type='file', name='performance_csv', accept='.csv')
                    span.kt-avatar__cancel(data-toggle='kt-tooltip', title='Cancel upload')
                      i.flaticon-cancel
              .col-lg-2.col-md-12.pl-0
                button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Submit

      //- Trigger Performance Section
      if fund.performanceCSV
        .kt-portlet.mb-5
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Trigger Performance Section
          .kt-portlet__body
            form(method="POST" action=`/fund/${fund._id}/trigger-performance-calc`)
              .form-group.row
                .col-lg-6.col-md-12.pl-0
                  button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Trigger Performance Calculation
      //- End Trigger Performance Section

block scripts
  script(src='/vendors/js/components/avatar.js', type='text/javascript')
  script(src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/3.3.4/jquery.inputmask.bundle.min.js", type="text/javascript")
  script(src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/3.3.4/inputmask/inputmask.numeric.extensions.min.js", text="text/javascript")
  script.
    jQuery(document).ready(function() {   
      $('#symbol_mask').inputmask({
        "rightAlign": false,
        "mask": "A",
        "repeat": 5,
        "greedy": false
      });
 
      $('#min_investment_mask').inputmask({
        "alias": "currency",
        'groupSeparator': ',',
        'digits': 0,
        'digitsOptional': false,
        'prefix': '',
        'placeholder': '0',
        "allowMinus": false,
        "min": 0,
        "max": 200000,
        "rightAlign": false
      });
      $(".int_mask").inputmask({
        "alias": "integer",
        "allowMinus": true,
        "rightAlign": false,
        "digits": 0,
        "digitsOptional": false
      });
    });

block scripts
  script(src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js" type="text/javascript")
  script.
    $('#select2_tags_focus').select2({placeholder: "Select fund 'Focus' themes"});
    $('#select2_tags_exposure').select2({placeholder: "Select fund 'Exposure' themes"});
    $('#select2_tags_method').select2({placeholder: "Select fund 'Method' themes"});
