extends layout

block content
  .row.justify-content-center
    .col-lg-10
      //- Investor Certification Wizard
      .kt-portlet
        .kt-portlet__body.kt-portlet__body--fit
          #kt_wizard_v1.kt-grid.kt-grid--desktop-xl.kt-grid--ver-desktop-xl.kt-wizard-v1(data-ktwizard-state='step-first')
            .kt-grid__item.kt-wizard-v1__aside
              // begin: Form Wizard Nav
              .kt-wizard-v1__nav
                .kt-wizard-v1__nav-items
                  a.kt-wizard-v1__nav-item(href='#', data-ktwizard-type='step', data-ktwizard-state='current')
                    span 1
                  a.kt-wizard-v1__nav-item(href='#', data-ktwizard-type='step')
                    span 2
                .kt-wizard-v1__nav-details
                  .kt-wizard-v1__nav-item-wrapper(data-ktwizard-type='step-info', data-ktwizard-state='current')
                    .kt-wizard-v1__nav-item-title
                      | Sophisticated Investor Self-Certification
                    .kt-wizard-v1__nav-item-desc
                      | To start off, please choose the one that fits your investor profile.
                  .kt-wizard-v1__nav-item-wrapper(data-ktwizard-type='step-info')
                    .kt-wizard-v1__nav-item-title
                      | Sophisticated Investor Statement
                    .kt-wizard-v1__nav-item-desc
                      | Please confirm that you have read and understood the Sophisticated Investor Statement
                      | and check to agree before you can complete your registration.
              // end: Form Wizard Nav
            .kt-grid__item.kt-grid__item--fluid.kt-wizard-v1__wrapper
              // begin: Form Wizard Form
              form#certify-investor__form.kt-form(method='POST' action='/investor/certify')
                // begin: Form Wizard Step 1
                .kt-wizard-v1__content(data-ktwizard-type='step-content', data-ktwizard-state='current')
                  .kt-heading.kt-heading--md Which of the following applies?
                  .kt-separator.kt-separator--height-xs
                  .kt-form__section.kt-form__section--first
                    .row
                      .col-xl-6
                        .kt-radio-list
                          label.kt-radio
                            input(type="radio" name="selfCertificationRadio" value="self-certified")
                            |  I am a member of a network or syndicate of business angels and have been so for at least the last six months prior to the date below.
                            span
                          label.kt-radio
                            input(type="radio" name="selfCertificationRadio" value="self-certified")
                            | I have made more than one investment in an unlisted company in the two years prior to the date below.
                            span

                          label.kt-radio
                            input(type="radio" name="selfCertificationRadio" value="self-certified")
                            | I am working, or have worked in the two years prior to the date below, in a professional capacity in the private equity sector, or in the provision of finance for small and medium enterprises.
                            span

                          label.kt-radio
                            input(type="radio" name="selfCertificationRadio" value="self-certified")
                            | I am currently, or have been in the two years prior to the date below, a director of a company with an annual turnover of at least £1 million.
                            span

                          label.kt-radio
                            input(type="radio" name="selfCertificationRadio" value="no-self-certified")
                            | None of the above.
                            span

                // end: Form Wizard Step 1
                // begin: Form Wizard Step 2
                .kt-wizard-v1__content(data-ktwizard-type='step-content')
                  .kt-heading.kt-heading--md Sophisticated Investor Statement
                  .kt-separator.kt-separator--height-sm
                  .kt-form__section.kt-form__section--first
                    .row
                      .col-xl-6
                        h3.pb-3 Sophisticated Investor Statement

                        p I make this statement so that I can receive promotional communications which are exempt from the restriction on promotion of non-mainstream pooled investments. The exemption relates to certified sophisticated investors and I declare that I qualify as such.

                        .form-group.row
                          label.col-1.col-form-label(for='investor-cert__input-signature') Signature
                          .col-4
                            input#investor-cert__input-signature.form-control(type='text' name='signature')

                        .form-group.row
                          label.col-1.col-form-label(for='kt_datepicker_1') Date
                          .col-4
                            .input-group
                              input#kt_datepicker_1.form-control(readonly="" type="text" name="date" data-toggle="datepicker")
                              .input-group-append
                                span.input-group-text
                                  i.flaticon-calendar-3

                        .form-group
                          label.kt-checkbox
                            input(type='checkbox', name='accept', value='1')
                            |  I accept that the investments to which the promotions will relate may expose me to a significant risk of losing all of the money or other property invested. I am aware that it is open to me to seek advice from an authorised person who specialises in advising on non-mainstream pooled investments.
                            span
                // end: Form Wizard Step 2
                // begin: Form Actions
                .kt-form__actions
                  button.btn.btn-outline-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper.btn-square(data-ktwizard-type='action-prev')
                    | Previous
                  button.btn.btn-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper.btn-square(data-ktwizard-type='action-submit')
                    | Submit
                  button.btn.btn-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper.btn-square(data-ktwizard-type='action-next')
                    | Next Step
                // end: Form Actions
                // end: Form Wizard Form
