mixin allocationSlider(labelName, colorClass, componentName, isAsset, isLocked)
  .kt-form__label.pb-1
    if isAsset
      label.kt-font-bolder.h5= labelName
    else
      label= labelName
    unless isLocked
      span.float-right.slider-value(id=`nouislider_${componentName}_span`)
      input(id=`nouislider_${componentName}_input` type="text" style="display: none;" name=`${componentName}`)
    else
      span.float-right
        i.fas.fa-lock
  .kt-form__control
    .row
      .col-12
        div(id=`nouislider_${componentName}_bar` class=`ml-1 nouislider ${colorClass}`)
