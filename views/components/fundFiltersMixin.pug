mixin fundFilters(focusFilters, methodFilters, exposureFilters, FocusTagEnum, ExposureTagEnum, MethodTagEnum)
  .kt-portlet
    .kt-portlet__body.kt-portlet__body--fit.px-3.py-4(style="font-size: 0.8rem;")
      //- begin: Fund Filters
      h5.mb-3 Performance
      form.kt-form.kt-fork--label-right(action="/fund/all" method="GET")
        .row.align-items-center
          .col-md-12
            .kt-form__group
              .kt-form__label
                label Annualized returns >
                  | !{' '}
                  span#kt_nouislider_1_span
                  input#kt_nouislider_1_input(type="text" style="display: none;" name="annualizedReturns")
              .kt-form__control
                .row
                  .col-12
                    div.ml-1.nouislider#kt_nouislider_1
            .kt-form__group.kt-margin-t-15
              .kt-form__label
                label Total AUM >
                  | !{' '}
                  span#kt_nouislider_2_span
                  input#kt_nouislider_2_input(type="text" style="display: none;" name="aum")
              .kt-form__control
                .row
                  .col-12
                    div.ml-1.nouislider#kt_nouislider_2
            .kt-form__group.kt-margin-t-15
              .kt-form__label
                label Track record >
                  | !{' '}
                  span#kt_nouislider_3_span
                  input#kt_nouislider_3_input(type="text" style="display: none;" name="trackRecordMonths")
              .kt-form__control
                .row
                  .col-12
                    div.ml-1.nouislider#kt_nouislider_3
            .kt-form__group.kt-margin-t-15
              .kt-form__label
                label Sharpe ratio >
                  | !{' '}
                  span#kt_nouislider_4_span
                  input#kt_nouislider_4_input(type="text" style="display: none;" name="sharpeRatio")
              .kt-form__control
                .row
                  .col-12
                    div.ml-1.nouislider#kt_nouislider_4

            h5.mb-3.mt-5 Themes

            .kt-form__group.kt-margin-t-15
              .row
                .col-12
                  select#select2_tags_focus.form-control.kt-select2(name='tags_focus', multiple='multiple')
                    for tag in FocusTagEnum
                      if focusFilters.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
            .kt-form__group.kt-margin-t-15
              .row
                .col-12
                  select#select2_tags_exposure.form-control.kt-select2(name='tags_exposure', multiple='multiple')
                    for tag in ExposureTagEnum
                      if exposureFilters.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
            .kt-form__group.kt-margin-t-15
              .row
                .col-12
                  select#select2_tags_method.form-control.kt-select2(name='tags_method', multiple='multiple')
                    for tag in MethodTagEnum
                      if methodFilters.includes(tag)
                        option(value=`${tag}` selected='')= tag
                      else
                        option(value=`${tag}`)= tag
            .kt-form__group.kt-margin-t-15
              .kt-form__control.float-right
                button.btn.btn-secondary.btn-square.btn-sm(type="Submit") Apply Filters
      //- end: Fund Filters

