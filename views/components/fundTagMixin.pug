mixin fundTag(fund, displayShort)
  - 
    const TAG_MAPPING = {
      "Commodities": "CMD",
      "Credit": "CRE",
      "Equities": "EQ",
      "ESG": "ESG",
      "Fixed Income": "FI",
      "Luxury Items": "LUX",
      "Macro": "MACRO",
      "Options": "OPT",
      "Real Estate": "RE",
      "Crypto": "CRY",

      "Directional": "DRCT",
      "Long-Short": "L/S",

      "Discretionary": "DISCR",
      "Systematic": "SYST"
    };

  - const focusTags = fund.tags && fund.tags.focus || [];
  for tag in focusTags
    if displayShort
      span.btn.btn-xs.btn-label-info.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1(tabindex="0" data-placement="bottom" data-toggle="kt-tooltip" data-skin="light" title="" data-original-title=`${tag}`)= TAG_MAPPING[tag]
    else
      span.btn.btn-xs.btn-label-info.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1= tag
  
  - const methodTags = fund.tags && fund.tags.method || [];
  for tag in methodTags
    if displayShort
      span.btn.btn-xs.btn-label-dark.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1(tabindex="0" data-placement="bottom" data-toggle="kt-tooltip" data-skin="light" title="" data-original-title=`${tag}`)= displayShort ? TAG_MAPPING[tag] : tag
    else
      span.btn.btn-xs.btn-label-dark.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1= tag
  
  - const exposureTags = fund.tags && fund.tags.exposure || [];
  for tag in exposureTags
    if displayShort
      span.btn.btn-xs.btn-label-success.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1(tabindex="0" data-placement="bottom" data-toggle="kt-tooltip" data-skin="light" title="" data-original-title=`${tag}`)= displayShort ? TAG_MAPPING[tag] : tag
    else
      span.btn.btn-xs.btn-label-success.btn-sm.btn-bold.py-0.px-1.mr-1.mb-1= tag
