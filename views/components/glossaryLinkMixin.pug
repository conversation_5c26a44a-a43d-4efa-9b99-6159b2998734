mixin glossaryLink(term)
  - const description = h.glossaryUtil.getTermDescription(term);
  - const urlPath = h.glossaryUtil.getTermUrl(term);
  sup.pl-1(tabindex="0" data-container="body" data-toggle="popover" data-trigger="focus" data-placement="right" data-content=`${description}<br><a href="${urlPath}" target="_blank">Read More</a>` data-original-title="" title="" data-html="true")
    i.flaticon-exclamation-1
