mixin toaster(content, type)
  -  
    const TOASTER_CONFIG = {
      "success": {
        "bg": "bg-light-success",
        "text": "text-success",
        "icon": "fas fa-check"
      },
      "error": {
        "bg": "bg-light-danger",
        "text": "text-danger",
        "icon": "fas fa-exclamation"
      },
      "warning": {
        "bg": "bg-light-warning",
        "text": "text-warning",
        "icon": "fas fa-exclamation"
      }
    }

  .toast.bg-white.border-radius-lg.w-100(role='alert', aria-live='assertive', aria-atomic='true', data-dismiss="toast" aria-label="Close")
    .toast-header.border-radius-lg.border-0.py-3(class=`${TOASTER_CONFIG[type].bg}`)
      i.toast-icon.mr-5(class=`${TOASTER_CONFIG[type].icon} ${TOASTER_CONFIG[type].text}`)
      h6.mr-auto.font-weight-bold.py-0.my-0(class=`${TOASTER_CONFIG[type].text}`)= content
      button.ml-2.mb-1.close.pl-2(type='button', data-dismiss='toast', aria-label='Close')
        span(aria-hidden='true') ×
