extends layout

block content
  .row.py-3
    .col-lg-5.col-xl-4.order-lg-3.order-xl-1

      // begin::Portlet
      .kt-portlet.kt-portlet--height-fluid
        .kt-portlet__head.kt-portlet__head--noborder
          .kt-portlet__head-label
            h3.kt-portlet__head-title Portfolio

        .kt-portlet__body
          if portfolio.length
            input#portfolio-composition__data(style='display:none' data-portfoliocomposition=`${portfolioComposition}`)
            p.text-center Portfolio Value = #{portfolioValue.toLocaleString()} USD
            canvas#dashboard__portfolio-composition(style='height: 300px;')
            .kt-widget-4
              for investment in portfolio
                - fund = investment.fund

                hr
                .kt-widget-4__item
                  .kt-widget-4__item-content
                    .kt-widget-4__item-section
                      .kt-widget-4__item-pic
                        a(href=`/fund/${fund._id}`)
                          //- img(src='stylesheets/assets/media/users/100_3.jpg', alt='')
                          if fund.logo
                            img.rounded(src=`${fund.logo}`, alt='')
                          else
                            span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= fund.symbol.toUpperCase()

                      .kt-widget-4__item-info
                        a.kt-widget-4__item-username(href=`/fund/${fund._id}`)= fund.symbol.toUpperCase()
                        .kt-widget-4__item-desc= fund.fundName
                  .kt-widget-4__item-content
                    .kt-widget-4__item-price
                      span.kt-widget-4__item-badge $
                      span.kt-widget-4__item-number #{(fund.currentValue * investment.tokenAmount).toLocaleString()}

          else
            p Your portfolio is empty.
              | !{' '}
              a(href="/fund/all") Start here 
              | to find your first investment
      // end::Portlet

    //- .col-lg-1.order-lg-2
    .col-lg-7.col-xl-4.order-lg-1.order-xl-1.pr-5
      .kt-portlet.kt-portlet--tabs.kt-portlet--height-fluid
        .kt-portlet__head
          .kt-portlet__head-label
            h3.kt-portlet__head-title
              | Market Commentary
          .kt-portlet__head-toolbar
            ul.nav.nav-tabs.nav-tabs-line.nav-tabs-line-brand.nav-tabs-bold(role='tablist')
              li.nav-item
                a.nav-link.active(data-toggle='tab', href='#kt_portlet_tabs_1_1_1_content', role='tab')
                  | Today
              //- TODO: insert 'ALL' tab
              //- li.nav-item
                a.nav-link(data-toggle='tab', href='#kt_portlet_tabs_1_1_2_content', role='tab')
                  | All
        .kt-portlet__body
          .tab-content
            #kt_portlet_tabs_1_1_1_content.tab-pane.fade.active.show(role='tabpanel')
              //- .kt-scroll(data-scroll='true')
              .kt-scroll(data-scroll='true', style='height: 500px;', data-mobile-height='350')
                // Begin::Timeline
                .kt-timeline
                // End::Timeline 1

  if newFunds.length
    .row.my-5
      .col-md-12
        // begin::Portlet
        .kt-portlet
          .kt-portlet__head.kt-portlet__head--noborder
            .kt-portlet__head-label
              h3.kt-portlet__head-title New Funds
          .kt-portlet__body
            // begin::Section
            .kt-section
              .kt-section__content
                table.table.m-table
                  thead
                    tr
                      th Fund
                      th Type
                      th Minimum Investment
                      th Token Price
                      th Days left
                      th Target
                      th 
                  tbody
                    for fund in newFunds
                      tr
                        td
                          .kt-widget-4
                            .kt-widget-4__item
                              .kt-widget-4__item-content
                                .kt-widget-4__item-section
                                  a(href=`/fund/${fund._id}`)
                                    .kt-widget-4__item-pic
                                      if fund.logo
                                        img.rounded(src=`${fund.logo}`, alt='')
                                      else
                                        span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= fund.symbol.toUpperCase()

                                  .kt-widget-4__item-info
                                    if fund.logo
                                      a.kt-widget-4__item-username(href=`/fund/${fund._id}`)= fund.symbol.toUpperCase()
                        td.flex2.align-middle
                          if fund.fundType
                            span.kt-badge.kt-badge--primary.kt-badge--dot
                            | &nbsp;
                            span.kt-font-bold.kt-font-primary= h.fundUtil.toTitleCase(fund.fundType)
                        td.flex2.align-middle #{fund.overview && fund.overview.minimumInvestment ? fund.overview.minimumInvestment : "" }
                        td.flex2.align-middle $#{fund.initialTokenPrice}
                        td.flex2.align-middle 6
                        td.flex2.align-middle
                          .progress
                            - campaignGoalMoney = fund.totalTokens * fund.initialTokenPrice / 1000000;
                            - tokensSold = fund.campaignInvestors ? fund.campaignInvestors.map(investmentObj => investmentObj.investmentSize).reduce((a, b) => a + b, 0) : 0;
                            - campaignRaisedMoney = tokensSold * fund.initialTokenPrice / 1000000;

                            .progress-bar.bg-dark(role='progressbar', aria-valuenow=`${campaignRaisedMoney / campaignGoalMoney * 100}`, aria-valuemin='0', aria-valuemax='100', style=`width: ${campaignRaisedMoney / campaignGoalMoney * 100}%;`)
                              | #{campaignRaisedMoney / campaignGoalMoney * 100}%
                        td.flex2.align-middle.kt-align-right
                          a.btn.btn-sm.btn-transition.btn-outline-secondary.btn-square(href=`/fund/${fund._id}`) Details

            // end::Section
        // end::Portlet

block modals
  //- Certified Investor Modal
  #certifiedInvestorModal.modal.fade(tabindex='-1', role='dialog', aria-labelledby='certifiedInvestorModalLabel', aria-hidden='true')
    .modal-dialog(role='document')
      .modal-content
        .modal-body
          p.pb-3.kt-font-bolder In order to invest to any alternative investment fund, it is required by the FCA to be a certified sophisticated investor.
        .modal-footer
          button.btn.btn-outline-secondary.btn-square(type='button', data-dismiss='modal') Skip for now
          a(href='/investor/certify')
            button.btn.btn-brand.btn-square(type='button') Certification

  include components/investModal
