extends layout

block stylesheets
  link(href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.5.1/min/dropzone.min.css" rel="stylesheet" type="text/css")
  link(href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.11/css/bootstrap-select.css" rel="stylesheet" type="text/css")

block content
  .row
    .col-lg-3.col-md-3.col-sm-12
      .sticky(style="position: -webkit-sticky; position:sticky; top:80px;")
        //- Section selection
        .main-card.mb-3.card.no-shadow.pr-3
          .card-body
            h5.card-title Sections
            ul.list-group
              a.list-group-item(href='#editFund__header-section') Header
              a.list-group-item(href='#editFund__overview-section') Overview
              a.list-group-item(href='#editFund__team-section') Team
              if fund._id
                a.list-group-item(href='#editFund__documents-section') Documents
              //- a.list-group-item(href='#admin__commentary-form') Market Commentary Form

        //- New fund button
        a.mb-3.mr-5.btn-wide.btn.btn-lg.btn-outline-brand.btn-block.btn-square(href="/fund-manager/dashboard" data-style="expand-right")
          | Dashboard

    .col-lg-7.col-md-7.col-sm-12
      form(method="POST" action=`/fund/add/${fund._id || ''}` enctype=`${fund._id ? "multipart/form-data" : ""}`)

        //- Fund Type Section
        .kt-portlet.mb-5
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Fund Type Section
          .kt-portlet__body
            .position-relative.form-group
              label(for='fund_type') Fund Type
                | !{' '}
                span.text-danger *
              select.form-control.selectpicker#selectFundType(name='fund_type')
                option(value='HEDGE_FUND' selected=(fundType === 'HEDGE_FUND')) Hedge Fund
                option(value='STRUCTURED_PRODUCT' selected=(fundType === 'STRUCTURED_PRODUCT')) Structured Product
                option(value='PRIVATE_EQUITY' selected=(fundType === 'PRIVATE_EQUITY')) Private Equity
                option(value='ALTERNATIVES' selected=(fundType === 'ALTERNATIVES')) Alternatives

        // Fund Header Section
        div#editFund__header-section
          .kt-portlet.mb-5
            .kt-portlet__head
              .kt-portlet__head-label
                h3.kt-portlet__head-title Fund Header Section
            .kt-portlet__body
                .position-relative.form-group
                  label(for='name') Name
                    | !{' '}
                    span.text-danger *
                  input.form-control(name='name', placeholder='Fund name', type='text', value=`${fund.fundName || ''}`)
                .position-relative.form-group
                  label(for='description') Summary
                    | !{' '}
                    span.text-danger *
                  textarea.form-control.autosize-input(rows='8' name="description" placeholder="A brief description about the fund" style="min-height: 80px;")
                    | #{fund.description || ''}

        // Overview Section
        div#editFund__overview-section
          .kt-portlet.mb-5
            .kt-portlet__head
              .kt-portlet__head-label
                h3.kt-portlet__head-title Overview Section
            .kt-portlet__body
                - const overview = fund.overview || {};
                .form-group.row
                  .col-12
                    label(for='strategy') Objective/Strategy
                      | !{' '}
                      span.text-danger *
                    textarea.form-control.autosize-input(rows='8' name="strategy" placeholder="" style="min-height: 80px;")
                      | #{overview.strategy || ''}

                h4.mb-4 Overview

                .form-group.row
                  label.col-2.col-form-label(for='overview_inception_date') Inception date
                    | !{' '}
                    span.text-danger *
                  .col-10
                    input.form-control(type='text', value=`${overview.inceptionDate || ''}`, name='overview_inception_date')
                .form-group.row
                  label.col-2.col-form-label(for='overview_fund_horizon') Investment horizon
                  .col-10
                    input.form-control(type='text', value=`${overview.fundHorizon || ''}`, name='overview_fund_horizon')
                .form-group.row
                  label.col-2.col-form-label(for='overview_total_aum') Total AUM
                    | !{' '}
                    span.text-danger *
                  .col-10
                    input.form-control(type='text', value=`${overview.totalAum || ''}`, name='overview_total_aum')
                .form-group.row
                  label.col-2.col-form-label(for='overview_location') Location
                    | !{' '}
                    span.text-danger *
                  .col-10
                    input.form-control(type='text', value=`${overview.location || ''}`, name='overview_location')
                .form-group.row
                  label.col-2.col-form-label(for='overview_currency') Currency
                    | !{' '}
                    span.text-danger *
                  .col-10
                    input.form-control(type='text', value=`${overview.currency || ''}`, name='overview_currency')
                .form-group.row
                  label.col-2.col-form-label(for='overview_industry') Industry
                    | !{' '}
                    span.text-danger *
                  .col-10
                    input.form-control(type='text', value=`${overview.industry || ''}`, name='overview_industry')
                .form-group.row
                  label.col-2.col-form-label(for='overview_url') URL
                  .col-10
                    input.form-control(type='text', value=`${overview.url || ''}`, name='overview_url')
                .position-relative.form-group
                  label(for='overview_notes') Notes on overview
                  textarea.form-control.autosize-input(rows='8' name="overview_notes" style="min-height: 80px;")
                    | #{overview.notes || ''}

                h4.mb-4 Fees

                .form-group.row
                  label.col-2.col-form-label(for='overview_entry_charge') Entry charge
                  .col-10
                    input.form-control(type='text', value=`${overview.entryCharge || ''}`, name='overview_entry_charge')
                .form-group.row
                  label.col-2.col-form-label(for='overview_exit_charge') Exit charge
                  .col-10
                    input.form-control(type='text', value=`${overview.exitCharge || ''}`, name='overview_exit_charge')
                .form-group.row
                  label.col-2.col-form-label(for='overview_commission') Commission
                  .col-10
                    input.form-control(type='text', value=`${overview.commission || ''}`, name='overview_commission')
                .form-group.row
                  label(for='overview_commission_notes') Notes on commissions
                  textarea.form-control(rows='8', name='overview_commission_notes')
                    | #{overview.commissionNotes || ''}
                .col-lg-2.col-md-12.pl-0
                  button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Submit

        //- Team Section
        div#editFund__team-section
          .kt-portlet.mb-5
            .kt-portlet__head
              .kt-portlet__head-label
                h3.kt-portlet__head-title Team Section
            .kt-portlet__body
              .row
                .col-12
                  h6 Select the team working on this fund
                  .kt-widget-7
                    .kt-widget-7__items
                      - const allChecked = fund.team ? fund.team.length === 0 : true
                      for member in team
                        .kt-widget-7__item
                          .kt-widget-7__item-pic(style="width: 3rem;")
                            if member.img
                              img(src=`${h.firmUtil.getTeamLogoURL(member.img)}` alt='image' style="height: 3rem; border-radius:5px;")
                            else
                              img(src='/images/avatars/user_default.jpg' alt='User' style="height: 3rem;")
                          .kt-widget-7__item-info
                            a.kt-widget-7__item-title(href='javascript: void(0)')= member.name
                            .kt-widget-7__item-desc= member.role || ""
                          .kt-widget-7__item-toolbar
                            label.kt-checkbox
                              if allChecked || (fund.team && fund.team.includes(member._id))
                                input(type='checkbox', name='team', value=`${member._id}`, checked)
                              else
                                input(type='checkbox', name='team', value=`${member._id}`)
                              span

        if fund._id
          div#editFund__documents-section
            //- Documents Section
            .kt-portlet.mb-5
              .kt-portlet__head
                .kt-portlet__head-label
                  h3.kt-portlet__head-title Documents Section
              .kt-portlet__body
                .row
                  .col-12
                    h5 Uploaded Documents
                    .kt-widget-7
                      .kt-widget-7__items
                        for document in fund.documents
                          .kt-widget-7__item
                            .kt-widget-7__item-pic
                              img(src='/images/files/pdf.svg', alt='Doc')
                            .kt-widget-7__item-info
                              a.kt-widget-7__item-title(href=`${h.fundUtil.getFundDocsURL(fund._id, document)}` target='_blank')= document
                              //- .kt-widget-7__item-desc
                                | 3 MB
                            .kt-widget-7__item-toolbar
                              .dropdown.dropdown-inline
                                button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                                  i.flaticon-more-1
                                .dropdown-menu.dropdown-menu-right
                                  ul.kt-nav
                                    li.kt-nav__section.kt-nav__section--first
                                      span.kt-nav__section-text ACTIONS
                                    li.kt-nav__item
                                      a.kt-nav__link(href=`${h.fundUtil.getFundDocsURL(fund._id, document)}` target="_blank")
                                        i.kt-nav__link-icon.flaticon-eye
                                        span.kt-nav__link-text View
                                    li.kt-nav__item
                                      a.kt-nav__link(href=`/fund/${fund._id}/docs/delete/${document}`)
                                        i.kt-nav__link-icon.flaticon-delete
                                        span.kt-nav__link-text Delete
                        else
                          p No documents uploaded yet.
                .form-group.row
                  label.col-3.col-form-label(for='document') Upload new document
                  .col-9
                    #kt_profile_avatar_1.kt-avatar
                      .kt-avatar__holder(style='background-image: url(/images/files/pdf.svg)')
                      label.kt-avatar__upload(data-toggle='kt-tooltip', title='Upload PDF')
                        i.flaticon2-edit
                        input(type='file', name='document', accept='.pdf')
                      span.kt-avatar__cancel(data-toggle='kt-tooltip', title='Cancel upload')
                        i.flaticon-cancel
                .col-lg-2.col-md-12.pl-0
                  button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Submit

block scripts
  script(src='/vendors/js/components/avatar.js', type='text/javascript')
  script(src='https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.11/js/bootstrap-select.js', type='text/javascript')
