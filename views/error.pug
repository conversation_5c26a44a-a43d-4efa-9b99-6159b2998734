doctype html
//
  Theme: Keen - The Ultimate Bootstrap Admin Theme
  Author: KeenThemes
  Website: http://www.keenthemes.com/
  Contact: <EMAIL>
  Follow: www.twitter.com/keenthemes
  Dribbble: www.dribbble.com/keenthemes
  Like: www.facebook.com/keenthemes
  License: You must have a valid license purchased only from https://themes.getbootstrap.com/product/keen-the-ultimate-bootstrap-admin-theme/ in order to legally use the theme for your project.
html(lang='en')
  // begin::Head
  head
    meta(charset="utf-8")
    meta(name="viewport" content="width=device-width, initial-scale=1")
    meta(http-equiv="X-UA-Compatible" content="IE=edge")

    title= `${title} | ${h.siteName}`
    // begin::Fonts
    script(src='https://ajax.googleapis.com/ajax/libs/webfont/1.6.16/webfont.js')
    script.
      WebFont.load({
        google: {
          "families": ["Poppins:300,400,500,600,700"]
        },
        active: function() {
          sessionStorage.fonts = true;
        }
      });
    // end::Fonts

    // begin::Page Custom Styles(used by this page)
    link(href="/stylesheets/keen/pages/error/404-v1.css" rel="stylesheet" type="text/css")
    // end::Page Custom Styles

    // begin::Global Theme Styles(used by all pages)
    link(href='/stylesheets/keen/pages/style.bundle.min.css', rel='stylesheet', type='text/css')
    // end::Global Theme Styles

    link(rel="shortcut icon" href="/images/icons/logo.png" type="image/png" sizes="16x16")


    // begin::Intercom
    script.
      var intercomAppId = "!{INTERCOM_APP_ID}";
      var intercomVerificationSecret = "!{INTERCOM_WEB_VERIFICATION_SECRET}";
      var user= "!{user}";

        (function(){
          function initializeIntercom() {

            if (user) {
              window.Intercom('boot', {
                app_id: intercomAppId,
                name: user.firstName,
                email: user.email,
                user_id: user._id,
                alignment: 'right',
                api_key: intercomVerificationSecret
              });
            } else {
              // If user is not logged in
              window.Intercom('boot', {
                app_id: intercomAppId,
                alignment: 'right',
                api_key: intercomVerificationSecret
              });
            }
          }

          // load intercom script
          var documentObject = document;
          var intercomScript = documentObject.createElement('script');
          intercomScript.type = 'text/javascript';
          intercomScript.async = true;
          intercomScript.src = 'https://widget.intercom.io/widget/' + intercomAppId;
          intercomScript.onload = initializeIntercom;  // Initialize after script is loaded
          var firstScriptTag = documentObject.getElementsByTagName('script')[0];
          firstScriptTag.parentNode.insertBefore(intercomScript, firstScriptTag);
        })();

    // end::Intercom

  // end::Head

  // begin::Body
  body()
    // begin:: Page
    .kt-grid.kt-grid--ver.kt-grid--root
      .kt-error404-v2
        .kt-error404-v1__content.text-center.justify-content-center
          // Div for logo at the top of the page
          div(style='width: 70vw; text-align: start; padding-top: 5vh;')
            img(src='/images/icons/wealthyhood-logo-dark.svg' alt='Wealthyhood Logo' style='height: auto;')
          img(src='/images/illustrations/kidnapping.png' alt='Error Image' style='max-height: 43vh; margin-top:2vh; height: auto; margin-bottom: 5vh;')
          div.error-page-subtitle-v1 Oops! Something went wrong. 
          p.error-page-desc-v1 If you've been involuntarily probed by extraterrestrial technology, hit start to resume your task. If you're still lost in space, contact us, and we'll organize a search party!
          a.error-page-button-v1(href='/') Start over
    // end:: Page

  // end::Body
