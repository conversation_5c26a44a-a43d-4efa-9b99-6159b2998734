extends layout

block stylesheets
  link(href="/stylesheets/keen/components/profile-card-4.css" rel="stylesheet" type="text/css")
  link(href="/stylesheets/keen/components/media.css" rel="stylesheet" type="text/css")
  link(href="https://d1azc1qln24ryf.cloudfront.net/114779/Socicon/style-cf.css?u8vidh" rel="stylesheet")

block content
  .row
    .col-md-3
      .sticky(style="position: -webkit-sticky; position:sticky; top:80px;")
        //- Section selection
        .main-card.mb-3.card.no-shadow.pr-3
          .card-body
            h5.card-title Sections
            ul.list-group
              a.list-group-item(href='#admin__firm-section') Firm
              a.list-group-item(href='#admin__team-section') Team
              if publishedFunds.length
                a.list-group-item(href='#admin__published-funds') Your Published Funds
              if unpublishedFunds.length
                a.list-group-item(href='#admin__unpublished-funds') Your Unpublished Funds

        //- New fund button
        a.mb-3.mr-5.btn-wide.btn.btn-lg.btn-outline-brand.btn-block.btn-square(href="/fund/add" data-style="expand-right")
          | New Fund

    .col-md-7
      .row

        //- Firm Section
        .col-md-12
          div#admin__firm-section
            .kt-portlet.mb-5.no-shadow
              .kt-portlet__head
                .kt-portlet__head-label
                  h3.kt-portlet__head-title Fund Manager Firm
              .kt-portlet__body
                .row
                  .col-md-12.col-lg-12
                    - const firm = user.firm
                    if firm && firm.name
                      .float-right
                        a.btn.btn-sm.btn-square.btn-outline-secondary(href='/fund-manager/firm/add') Edit

                      .kt-profile
                        .kt-profile__content(style='border-bottom: 0px;')
                          .kt-profile__main.mb-4(style='border-right: 0px;')
                            .kt-profile__main-pic
                              if firm.logo
                                img.rounded(src=`${h.firmUtil.getFirmLogoURL(user._id, firm.logo)}`, alt='Firm Logo')
                              else
                                img.rounded(src='/images/avatars/company_default.png', alt='Firm Logo')
                            .kt-profile__main-info
                              .kt-profile__main-info-name= firm.name
                              .kt-profile__main-info-position
                                if firm.url
                                  a(href=`${firm.url}` target="_blank")
                                    i.flaticon2-link
                                    | !{' '}
                                    span Website
                          hr.mb-4
                          div !{h.markdownIt.render(firm.description)}

                    else
                      p Your company profile is empty.
                      a.mt-2.btn.btn.btn-outline-secondary.btn-square(href="/fund-manager/firm/add") Create Firm

        //- Team Section
        .col-md-12
          div#admin__team-section
            .kt-portlet.mb-5.no-shadow
              .kt-portlet__head
                .kt-portlet__head-label
                  h3.kt-portlet__head-title The Team
              .kt-portlet__body
                .row
                  .col-md-12.col-lg-12
                    if team && team.length
                      //- p TODO: add team widget
                    else
                      p Fund management team is empty.
                    a.mt-2.btn.btn.btn-outline-secondary.btn-square(href="/fund-manager/team/add") Add team member
            if team
              .row
                for member, index in team
                  .col-12
                    // begin::Portlet
                    .kt-portlet.no-shadow
                      .kt-portlet__body
                        .kt-widget.kt-widget--general-4
                          .kt-widget__head
                            .kt-media.kt-media--lg
                              if member.img
                                img(src=`${h.firmUtil.getTeamLogoURL(member.img)}` alt='image')
                              else
                                img(src='/images/avatars/user_default.jpg' alt='User')
                            .kt-widget__toolbar
                              .btn-group
                                .dropdown.dropdown-inline
                                  button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false')
                                    i.flaticon-more-1
                                  .dropdown-menu.dropdown-menu-fit.dropdown-menu-md.dropdown-menu-right
                                    // begin::Nav
                                    ul.kt-nav
                                      li.kt-nav__item
                                        a.kt-nav__link(href=`/fund-manager/team/${member._id}/update`)
                                          i.kt-nav__link-icon.flaticon2-edit
                                          span.kt-nav__link-text Edit
                                      li.kt-nav__item
                                        a.kt-nav__link(href="javascript:void(0)" onclick=`(() => document.getElementsByName('form_${index}')[0].submit())()`)
                                          i.kt-nav__link-icon.flaticon-delete-1
                                          span.kt-nav__link-text Delete
                                          form(action=`/fund-manager/team/${member._id}/delete` method="POST" style="display: none;" name=`form_${index}`)
                                            input(type="submit" value="Sumit")
                                    // end::Nav

                          a.kt-widget__title(href='#')= member.name
                            p.kt-widget__subtitle= member.role
                          .kt-widget__desc
                            div !{h.markdownIt.render(member.bio)}
                          .kt-widget__links
                            if member.linkedinUrl
                              .kt-widget__link
                                i.socicon-linkedin.kt-font-primary
                                a(href=`${member.linkedinUrl}` target='_blank') LinkedIn
                            if member.personalUrl
                              .kt-widget__link
                                i.flaticon2-link.kt-font-success
                                a(href=`${member.personalUrl}` target='_blank') Bio
                    // end::Portlet

        //- Published Funds Section
        .col-md-12
          div#admin__published-funds
            if publishedFunds.length
              .table-desktop-list
                .kt-portlet.mb-5.no-shadow
                  .kt-portlet__head
                    .kt-portlet__head-label
                      h3.kt-portlet__head-title Your Published Funds
                  .kt-portlet__body.kt-portlet__body--fit.mt-3
                    .table-responsive
                      table.align-middle.mb-0.table.table-borderless.table-striped.table-hover
                        thead
                          tr
                            th Symbol
                            th Fund Name
                            th Type
                            //- th Fundraising
                            th Actions
                        tbody
                          for fund, index in publishedFunds
                            - const symbol = fund.symbol || ""

                            tr
                              td
                                .kt-widget-4
                                  .kt-widget-4__item
                                    .kt-widget-4__item-content
                                      .kt-widget-4__item-section
                                        a(href=`/fund/${fund._id}`)
                                          .kt-widget-4__item-pic
                                            if fund.logo
                                              img.rounded(src=`${fund.logo}`, alt='')
                                            else
                                              span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= symbol.toUpperCase()
                                        .kt-widget-4__item-info
                                          if fund.logo
                                            a.kt-widget-4__item-username(href=`/fund/${fund._id}` style='text-decoration:none;')= symbol.toUpperCase()
                              td.flex2.align-middle
                                a.text-dark(href=`/fund/${fund._id}`)= fund.fundName
                              td.flex2.align-middle
                                if fund.fundType
                                  span.kt-badge.kt-badge--primary.kt-badge--dot
                                  | &nbsp;
                                  span.kt-font-bold.kt-font-primary= h.fundUtil.toTitleCase(fund.fundType)
                              //- td.flex2.align-middle
                                if fund.isCampaignCreated
                                  span.btn.btn-label-info.btn-sm.btn-bold.btn-upper(tabindex="0" data-placement="top" data-toggle="kt-tooltip" data-skin="dark" title="" data-original-title="Open refers to fundraising status on the Wealthyhood platform and does not reflect fund status outside of the platform." style="width: 100%;") OPEN
                              td.flex2.align-middle.kt-align-right
                                //- TODO: following buttons will be added back
                                //- //- Start/Update campaign button
                                //- .d-inline-block.mr-3
                                //-   a.mr-5.btn.btn-outline-dark.btn-sm.btn-block.btn-square(href=`/fund/${fund._id}/campaign/add`) #{fund.isCampaignCreated ? "Update Campaign" : "Fundraise"}

                                //- //- Launch campaign button
                                //- if fund.isCampaignCreated
                                //-   form.d-inline-block(action=`/fund/${fund._id}/campaign/launch` method="POST")
                                //-     button.btn.btn-secondary.btn-sm.btn-block.btn-square(type="submit") Launch

                                //- a.btn.btn-sm.btn-clean.btn-icon.btn-icon-md.ml-2(title='Edit details' href=`/fund/${fund._id}/edit`)
                                  i.flaticon-edit

                                .kt-widget-7__item-toolbar
                                  .dropdown.dropdown-inline
                                    button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                                      i.flaticon-more-1
                                    .dropdown-menu.dropdown-menu-right
                                      ul.kt-nav
                                        li.kt-nav__section.kt-nav__section--first
                                          span.kt-nav__section-text ACTIONS
                                        li.kt-nav__item
                                          a.kt-nav__link(href=`/fund/${fund._id}`)
                                            i.kt-nav__link-icon.flaticon-eye
                                            span.kt-nav__link-text View
                                        li.kt-nav__item
                                          a.kt-nav__link(title='Edit details' href=`/fund/${fund._id}/edit`)
                                            i.kt-nav__link-icon.flaticon-edit
                                            span.kt-nav__link-text Edit

              .mobile-fund-list.mb-5
                h3.mb-4 Your Published Funds
                for fund, index in publishedFunds
                  - const symbol = fund.symbol || ""

                  .kt-portlet
                    .kt-portlet__body
                      .kt-widget-4
                        .kt-widget-4__item
                          .kt-widget-4__item-content
                            .kt-widget-4__item-section
                              a(href=`/fund/${fund._id}`)
                                .kt-widget-4__item-pic
                                  span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= symbol.toUpperCase()
                              .kt-widget-4__item-info
                                a.kt-widget-4__item-username(href=`/fund/${fund._id}`)= fund.fundName
                                .kt-widget-4__item-desc= h.fundUtil.applyPrefixToSymbol(symbol)
                        span.btn.btn-label-info.btn-sm.btn-bold.py-0.px-1= h.fundUtil.toTitleCase(fund.fundType)
                        .kt-widget-7__item-toolbar.float-right
                          .dropdown.dropdown-inline
                            button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                              i.flaticon-more-1
                            .dropdown-menu.dropdown-menu-right
                              ul.kt-nav
                                li.kt-nav__section.kt-nav__section--first
                                  span.kt-nav__section-text ACTIONS
                                li.kt-nav__item
                                  a.kt-nav__link(href=`/fund/${fund._id}`)
                                    i.kt-nav__link-icon.flaticon-eye
                                    span.kt-nav__link-text View
                                li.kt-nav__item
                                  a.kt-nav__link(title='Edit details' href=`/fund/${fund._id}/edit`)
                                    i.kt-nav__link-icon.flaticon-edit
                                    span.kt-nav__link-text Edit

        //- Unpublished Funds Section
        .col-md-12
          div#admin__unpublished-funds
            if unpublishedFunds.length
              .table-desktop-list
                .kt-portlet.mb-5.no-shadow
                  .kt-portlet__head
                    .kt-portlet__head-label
                      h3.kt-portlet__head-title Your Unpublished Funds
                  .kt-portlet__body.kt-portlet__body--fit.mt-3
                    .table-responsive
                      table.align-middle.mb-0.table.table-borderless.table-striped.table-hover
                        thead
                          tr
                            th Symbol
                            th Fund Name
                            th Type
                            //- th Fundraising
                            th Actions
                        tbody
                          for fund, index in unpublishedFunds
                            - const symbol = fund.symbol || ""

                            tr
                              td
                                .kt-widget-4
                                  .kt-widget-4__item
                                    .kt-widget-4__item-content
                                      .kt-widget-4__item-section
                                        a(href=`/fund/${fund._id}`)
                                          .kt-widget-4__item-pic
                                            if fund.logo
                                              img.rounded(src=`${fund.logo}`, alt='')
                                            else
                                              span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= symbol.toUpperCase()
                                        .kt-widget-4__item-info
                                          if fund.logo
                                            a.kt-widget-4__item-username(href=`/fund/${fund._id}` style='text-decoration:none;')= symbol.toUpperCase()
                              td.flex2.align-middle
                                a.text-dark(href=`/fund/${fund._id}`)= fund.fundName
                              td.flex2.align-middle
                                if fund.fundType
                                  span.kt-badge.kt-badge--primary.kt-badge--dot
                                  | &nbsp;
                                  span.kt-font-bold.kt-font-primary= h.fundUtil.toTitleCase(fund.fundType)
                              //- td.flex2.align-middle
                                if fund.isCampaignCreated
                                  span.btn.btn-label-info.btn-sm.btn-bold.btn-upper(tabindex="0" data-placement="top" data-toggle="kt-tooltip" data-skin="dark" title="" data-original-title="Open refers to fundraising status on the Wealthyhood platform and does not reflect fund status outside of the platform." style="width: 100%;") OPEN
                              td.flex2.align-middle.kt-align-right
                                //- TODO: following buttons will be added back
                                //- //- Start/Update campaign button
                                //- .d-inline-block.mr-3
                                //-   a.mr-5.btn.btn-outline-dark.btn-sm.btn-block.btn-square(href=`/fund/${fund._id}/campaign/add`) #{fund.isCampaignCreated ? "Update Campaign" : "Fundraise"}

                                //- //- Launch campaign button
                                //- if fund.isCampaignCreated
                                //-   form.d-inline-block(action=`/fund/${fund._id}/campaign/launch` method="POST")
                                //-     button.btn.btn-secondary.btn-sm.btn-block.btn-square(type="submit") Launch

                                //- a.btn.btn-sm.btn-clean.btn-icon.btn-icon-md.ml-2(title='Edit details' href=`/fund/${fund._id}/edit`)
                                  i.flaticon-edit

                                .kt-widget-7__item-toolbar
                                  .dropdown.dropdown-inline
                                    button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                                      i.flaticon-more-1
                                    .dropdown-menu.dropdown-menu-right
                                      ul.kt-nav
                                        li.kt-nav__section.kt-nav__section--first
                                          span.kt-nav__section-text ACTIONS
                                        li.kt-nav__item
                                          a.kt-nav__link(href=`/fund/${fund._id}`)
                                            i.kt-nav__link-icon.flaticon-eye
                                            span.kt-nav__link-text View
                                        li.kt-nav__item
                                          a.kt-nav__link(href="javascript:void(0)" onclick=`(() => document.getElementsByName('form_${index}')[0].submit())()`)
                                            i.kt-nav__link-icon.flaticon-paper-plane
                                            span.kt-nav__link-text Publish
                                            form(action=`/fund/${fund._id}/publish` method="POST" style="display: none;" name=`form_${index}`)
                                              input(type="submit" value="Sumit")
                                        li.kt-nav__item
                                          a.kt-nav__link(title='Edit details' href=`/fund/${fund._id}/edit`)
                                            i.kt-nav__link-icon.flaticon-edit
                                            span.kt-nav__link-text Edit

              .mobile-fund-list
                h3.mb-4 Your Unpublished Funds
                for fund, index in unpublishedFunds
                  - const symbol = fund.symbol || ""

                  .kt-portlet
                    .kt-portlet__body
                      .kt-widget-4
                        .kt-widget-4__item
                          .kt-widget-4__item-content
                            .kt-widget-4__item-section
                              a(href=`/fund/${fund._id}`)
                                .kt-widget-4__item-pic
                                  span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= symbol.toUpperCase()
                              .kt-widget-4__item-info
                                a.kt-widget-4__item-username(href=`/fund/${fund._id}`)= fund.fundName
                                .kt-widget-4__item-desc= h.fundUtil.applyPrefixToSymbol(symbol)
                        span.btn.btn-label-info.btn-sm.btn-bold.py-0.px-1= h.fundUtil.toTitleCase(fund.fundType)
                        .kt-widget-7__item-toolbar.float-right
                          .dropdown.dropdown-inline
                            button.btn.btn-clean.btn-sm.btn-icon.btn-icon-md(type='button', data-toggle='dropdown', aria-haspopup='true', aria-expanded='false')
                              i.flaticon-more-1
                            .dropdown-menu.dropdown-menu-right
                              ul.kt-nav
                                li.kt-nav__section.kt-nav__section--first
                                  span.kt-nav__section-text ACTIONS
                                li.kt-nav__item
                                  a.kt-nav__link(href=`/fund/${fund._id}`)
                                    i.kt-nav__link-icon.flaticon-eye
                                    span.kt-nav__link-text View
                                li.kt-nav__item
                                  a.kt-nav__link(href="javascript:void(0)" onclick=`(() => document.getElementsByName('form_${index}')[0].submit())()`)
                                    i.kt-nav__link-icon.flaticon-paper-plane
                                    span.kt-nav__link-text Publish
                                    form(action=`/fund/${fund._id}/publish` method="POST" style="display: none;" name=`form_${index}`)
                                      input(type="submit" value="Sumit")
                                li.kt-nav__item
                                  a.kt-nav__link(title='Edit details' href=`/fund/${fund._id}/edit`)
                                    i.kt-nav__link-icon.flaticon-edit
                                    span.kt-nav__link-text Edit