extends ../layout


block content
  .row.justify-content-center
    .col-lg-6.col-md-5.col-sm-9
      //- Product Info
      div#editproduct__core-section
        .kt-portlet.mb-5
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Investment Product
          .kt-portlet__body
            form(method="POST" action=`/investment-products/${investmentProduct._id || ''}`)
              .form-group.row
                label.col-4.col-form-label(for='name') Name
                .col-8
                  input.form-control(name='name', type='text', value=`${investmentProduct.name || ''} ` required)
              .form-group.row
                label.col-4.col-form-label(for='name') Ticker Name
                .col-8
                  input.form-control(name='tickerName', type='text', value=`${investmentProduct.tickerName || ''} `)
              .form-group.row
                label.col-4.col-form-label(for='commonId') Common ID
                .col-8
                  input.form-control(name='commonId', type='text', value=`${investmentProduct.commonId || ''}` required)
              .form-group.row
                label.col-4.col-form-label(for='wealthkernelPortfolioId') WK Portfolio ID
                .col-8
                  input.form-control(name='wealthkernelPortfolioId', type='text', value=`${investmentProduct.wealthkernelPortfolioId || ''}` required)
              .form-group.row
                label.col-4.col-form-label(for='isin') Isin
                .col-8
                  input.form-control(name='isin', type='text', value=`${investmentProduct.isin || ''}` required)
              .form-group.row
                label.col-4.col-form-label(for='assetClass') Asset Class
                .col-8
                  select.form-control.kt-select2(name='assetClass')
                    option(disabled selected value) -- select an option --
                    - 
                      const ASSET_CLASSES = {
                        EQUITIES: "Equities",
                        GOVERNMENT_BONDS: "Government Bonds",
                        CORPORATE_BONDS: "Corporate Bonds",
                        COMMODITIES: "Commodities",
                        REAL_ESTATE: "Real Estate"
                      }
                    for assetClassKey in Object.keys(ASSET_CLASSES)
                      if investmentProduct.assetClass===assetClassKey
                        option(value=`${assetClassKey}` selected='')= ASSET_CLASSES[assetClassKey]
                      else
                        option(value=`${assetClassKey}`)= ASSET_CLASSES[assetClassKey]
              .form-group.row
                label.col-4.col-form-label(for='listed') Listed
                .col-8
                  label.kt-checkbox.kt-checkbox--brand
                    if investmentProduct.listed
                      input(type='checkbox', name='listed', checked='checked', value='1' )
                    else
                      input(type='checkbox', name='listed')
                    span
              .col-lg-2.col-md-12.pl-0
                button.mt-2.btn.btn.btn-outline-secondary.btn-square(type="submit") Confirm
      //- End of Product Info