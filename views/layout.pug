doctype html
html(lang='en')
  //- begin::Head
  head
    base(href='')
    meta(charset='utf-8')
    meta(name='viewport' content='width=device-width, initial-scale=1, shrink-to-fit=no')
    meta(http-equiv="X-UA-Compatible" content="IE=edge")

    title= `${title} | ${h.siteName}`

    //- begin::Fonts
    link(rel='stylesheet' href='https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700')
    link(rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap")
    //- end::Fonts

    //- begin::Global Theme Styles(used by all pages)
    link(href='/keen2/assets/plugins/global/plugins.bundle.css' rel='stylesheet' type='text/css')
    link(href='/keen2/assets/css/style.bundle.css' rel='stylesheet' type='text/css')
    //- end::Global Theme Styles

    //-begin:: Global Vendors - CDN
    link(href='https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css', rel='stylesheet', type='text/css')
    link(href='https://releases.transloadit.com/uppy/v1.22.0/uppy.min.css', rel='stylesheet', type='text/css')
    link(rel="stylesheet" type='text/css' href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,700,0,0")
    //-end:: Global Vendors - CDN

    //- begin::Custom
    link(href='/keen2/assets/css/wizard-3.css' rel='stylesheet' type='text/css')
    link(href='/stylesheets/admin/style.css', rel='stylesheet', type='text/css')
    //- end::Custom

    block stylesheets

    //- begin::Favicon
    link(rel="shortcut icon" href="/images/icons/logo.png" type="image/png" sizes="16x16")
    //- end::Favicon
  //- end::Head

  //- begin::Body
  body#kt_body.header-mobile-fixed.aside-enabled.aside-fixed.aside-minimize-hoverable.page-loading
    //- begin::Main
    block content

    //- begin::Toasters
    include components/toasterMixin
    .col-lg-3.col-md-6.col-sm-12.float-right(style="position: fixed; top: 90px; right: 0px;")
      - const messageTypes = Object.keys(flashes)
      for messageType in messageTypes
        for message in flashes[messageType]
          +toaster(message, messageType)
    //- end::Toasters
    //- end::Main

    //- begin::Global Config(global config for global JS scripts)
    script.
      var KTAppSettings = { "breakpoints": { "sm": 576, "md": 768, "lg": 992, "xl": 1200, "xxl": 1200 }, "colors": { "theme": { "base": { "white": "#ffffff", "primary": "#3699FF", "secondary": "#E5EAEE", "success": "#1BC5BD", "info": "#6993FF", "warning": "#FFA800", "danger": "#F64E60", "light": "#F3F6F9", "dark": "#212121" }, "light": { "white": "#ffffff", "primary": "#E1F0FF", "secondary": "#ECF0F3", "success": "#C9F7F5", "info": "#E1E9FF", "warning": "#FFF4DE", "danger": "#FFE2E5", "light": "#F3F6F9", "dark": "#D6D6E0" }, "inverse": { "white": "#ffffff", "primary": "#FFFFFF", "secondary": "#212121", "success": "#ffffff", "info": "#ffffff", "warning": "#ffffff", "danger": "#ffffff", "light": "#464E5F", "dark": "#ffffff" } }, "gray": { "gray-100": "#F3F6F9", "gray-200": "#ECF0F3", "gray-300": "#E5EAEE", "gray-400": "#D6D6E0", "gray-500": "#B5B5C3", "gray-600": "#80808F", "gray-700": "#464E5F", "gray-800": "#1B283F", "gray-900": "#212121" } }, "font-family": "Poppins" };
    //- end::Global Config

    //- begin::Global Theme Bundle(used by all pages)
    script(src='/keen2/assets/plugins/global/plugins.bundle.js')
    script(src='/keen2/assets/js/scripts.bundle.js')
    //- end::Global Theme Bundle

    //- begin::Global Optional Vendors
    script(src='https://cdn.jsdelivr.net/npm/toastr@2.1.4/toastr.min.js', type='text/javascript')
    //- end::Global Optional Vendors

    block scripts

    block modals
      include components/linkBankModal
      +linkBankModal()

    //- Toasters
    script.
      $('.toast').toast({ delay: 5000 });
      $('.toast').toast('show');
    //- End Toasters

  //- end::Body

