doctype html
html(lang='en')
    //- begin::Head
    head
        base(href='')
        meta(charset='utf-8')
        meta(name='viewport' content='width=device-width, initial-scale=1, shrink-to-fit=no')
        meta(http-equiv="X-UA-Compatible" content="IE=edge")

        title= `${title} | ${h.siteName}`

        //-begin:: Global Vendors - CDN
        link(href='https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css', rel='stylesheet', type='text/css')
        link(href='https://releases.transloadit.com/uppy/v1.22.0/uppy.min.css', rel='stylesheet', type='text/css')
        //-end:: Global Vendors - CDN

        //- begin::Fonts
        link(rel='stylesheet' href='https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700')
        //- end::Fonts

        //- begin::Custom
        link(href='/stylesheets/bootstrap.css' rel='stylesheet' type='text/css')
        link(href='/stylesheets/style.css' rel='stylesheet' type='text/css')
        link(rel="stylesheet" type='text/css' href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css")
        link(rel="stylesheet" type='text/css' href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,700,0,0")
        link(rel="stylesheet" type='text/css' href="https://fonts.googleapis.com/icon?family=Material+Icons")
        //- end::Custom

        block stylesheets

        //- begin::Favicon
        link(rel="shortcut icon" href="/images/icons/logo-dark.svg" type="image/png" sizes="16x16")
        //- end::Favicon
    //- end::Head

    //- begin::Body
    body(class='bg-main')
        //- begin::Main
        block content

        block scripts

    //- end::Body

