doctype html
html(lang='en')
  //- begin::Head
  head
    base(href='')
    meta(charset='utf-8')
    meta(name='viewport' content='width=device-width, initial-scale=1, shrink-to-fit=no')
    meta(http-equiv="X-UA-Compatible" content="IE=edge")

    title= `${title} | ${h.siteName}`

    //- begin::Fonts
    link(rel='stylesheet' href='https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700')
    link(rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap")
    //- end::Fonts

    //- begin::Global Theme Styles(used by all pages)
    link(href='/keen2/assets/plugins/global/plugins.bundle.css' rel='stylesheet' type='text/css')
    link(href='/keen2/assets/css/style.bundle.css' rel='stylesheet' type='text/css')
    //- end::Global Theme Styles

    //- begin::Custom
    link(href='/keen2/assets/css/wizard-3.css' rel='stylesheet' type='text/css')
    link(href='/stylesheets/style.css', rel='stylesheet', type='text/css')
    //- end::Custom

    block stylesheets

    //- begin::Favicon
    link(rel="shortcut icon" href="/images/icons/logo.png" type="image/png" sizes="16x16")
    //- end::Favicon

    //- begin::Custom Style
    style.
      @media (max-width: 991.98px) {
        .header {
          display: none;
        }
      }
    //- end::Custom Style
 
  //- end::Head

  //- begin::Body
  body#kt_body.header-static.page-loading
    //- begin::Main
    block content
    //- end::Main

    //- begin::Global Config(global config for global JS scripts)
    script.
      var KTAppSettings = { "breakpoints": { "sm": 576, "md": 768, "lg": 992, "xl": 1200, "xxl": 1200 }, "colors": { "theme": { "base": { "white": "#ffffff", "primary": "#3699FF", "secondary": "#E5EAEE", "success": "#1BC5BD", "info": "#6993FF", "warning": "#FFA800", "danger": "#F64E60", "light": "#F3F6F9", "dark": "#212121" }, "light": { "white": "#ffffff", "primary": "#E1F0FF", "secondary": "#ECF0F3", "success": "#C9F7F5", "info": "#E1E9FF", "warning": "#FFF4DE", "danger": "#FFE2E5", "light": "#F3F6F9", "dark": "#D6D6E0" }, "inverse": { "white": "#ffffff", "primary": "#FFFFFF", "secondary": "#212121", "success": "#ffffff", "info": "#ffffff", "warning": "#ffffff", "danger": "#ffffff", "light": "#464E5F", "dark": "#ffffff" } }, "gray": { "gray-100": "#F3F6F9", "gray-200": "#ECF0F3", "gray-300": "#E5EAEE", "gray-400": "#D6D6E0", "gray-500": "#B5B5C3", "gray-600": "#80808F", "gray-700": "#464E5F", "gray-800": "#1B283F", "gray-900": "#212121" } }, "font-family": "Poppins" };
    //- end::Global Config

    //- begin::Global Theme Bundle(used by all pages)
    script(src='/keen2/assets/plugins/global/plugins.bundle.js')
    script(src='/keen2/assets/js/scripts.bundle.js')
    //- end::Global Theme Bundle

    block scripts

    block modals
  //- end::Body

