extends layout

block scriptsHeader
  script(src='https://www.amcharts.com/lib/4/core.js')
  script(src='https://www.amcharts.com/lib/4/charts.js')
  script(src='https://www.amcharts.com/lib/4/themes/animated.js')
  script(src="https://www.amcharts.com/lib/4/themes/dataviz.js")
  script(src="https://www.amcharts.com/lib/4/themes/frozen.js")

  // Chart code
  script.
    am4core.ready(function() {

    // Themes begin
    am4core.useTheme(am4themes_dataviz);
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Create chart
    var chart = am4core.create("chartdiv", am4charts.XYChart);
    chart.padding(0, 15, 0, 15);
    chart.colors.step = 3;

    // the following line makes value axes to be arranged vertically.
    chart.leftAxesContainer.layout = "vertical";

    // uncomment this line if you want to change order of axes
    //chart.bottomAxesContainer.reverseOrder = true;

    var dateAxis = chart.xAxes.push(new am4charts.DateAxis());
    dateAxis.renderer.grid.template.location = 0;
    dateAxis.renderer.ticks.template.length = 8;
    dateAxis.renderer.ticks.template.strokeOpacity = 0.1;
    dateAxis.renderer.grid.template.disabled = true;
    dateAxis.renderer.ticks.template.disabled = false;
    dateAxis.renderer.ticks.template.strokeOpacity = 0.2;
    dateAxis.renderer.minLabelPosition = 0.01;
    dateAxis.renderer.maxLabelPosition = 0.99;
    dateAxis.minHeight = 30;

    dateAxis.groupData = true;
    dateAxis.minZoomCount = 5;

    // these two lines makes the axis to be initially zoomed-in
    // dateAxis.start = 0.7;
    // dateAxis.keepSelection = true;

    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.tooltip.disabled = true;
    valueAxis.zIndex = 1;
    valueAxis.renderer.baseGrid.disabled = true;
    // height of axis
    valueAxis.height = am4core.percent(65);

    valueAxis.renderer.gridContainer.background.fill = am4core.color("#000000");
    valueAxis.renderer.gridContainer.background.fillOpacity = 0.05;
    valueAxis.renderer.inside = true;
    valueAxis.renderer.labels.template.verticalCenter = "bottom";
    valueAxis.renderer.labels.template.padding(2, 2, 2, 2);

    //valueAxis.renderer.maxLabelPosition = 0.95;
    valueAxis.renderer.fontSize = "0.8em"

    var series1 = chart.series.push(new am4charts.LineSeries());
    series1.defaultState.transitionDuration = 0;
    series1.dataFields.dateX = "Date";
    series1.dataFields.valueY = "Adj Close";
    series1.dataFields.valueYShow = "changePercent";
    series1.tooltipText = "{name}: {valueY.changePercent.formatNumber('[#0c0]+#.00|[#c00]#.##|0')}%";
    series1.name = "Investment Fund 1";
    series1.tooltip.getFillFromObject = false;
    series1.tooltip.getStrokeFromObject = true;
    series1.tooltip.background.fill = am4core.color("#fff");
    series1.tooltip.background.strokeWidth = 2;
    series1.tooltip.label.fill = series1.stroke;

    series1.dataSource.url = "https://www.amcharts.com/wp-content/uploads/assets/stock/MSFT.csv";
    series1.dataSource.parser = new am4core.CSVParser();
    series1.dataSource.parser.options.useColumnNames = true;
    series1.dataSource.parser.options.reverse = true;

    var series2 = chart.series.push(new am4charts.LineSeries());
    series2.dataFields.dateX = "Date";
    series2.dataFields.valueY = "Adj Close";
    series2.dataFields.valueYShow = "changePercent";
    series2.tooltipText = "{name}: {valueY.changePercent.formatNumber('[#0c0]+#.00|[#c00]#.##|0')}%";
    series2.name = "TXN";
    series2.tooltip.getFillFromObject = false;
    series2.tooltip.getStrokeFromObject = true;
    series2.tooltip.background.fill = am4core.color("#fff");
    series2.tooltip.background.strokeWidth = 2;
    series2.tooltip.label.fill = series2.stroke;

    series2.dataSource.url = "https://www.amcharts.com/wp-content/uploads/assets/stock/TXN.csv";
    series2.dataSource.parser = new am4core.CSVParser();
    series2.dataSource.parser.options.useColumnNames = true;
    series2.dataSource.parser.options.reverse = true;

    var valueAxis2 = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis2.tooltip.disabled = true;
    // height of axis
    valueAxis2.height = am4core.percent(35);
    valueAxis2.zIndex = 3
    // this makes gap between panels
    valueAxis2.marginTop = 30;
    valueAxis2.renderer.baseGrid.disabled = true;
    valueAxis2.renderer.inside = true;
    valueAxis2.renderer.labels.template.verticalCenter = "bottom";
    valueAxis2.renderer.labels.template.padding(2, 2, 2, 2);
    //valueAxis.renderer.maxLabelPosition = 0.95;
    valueAxis2.renderer.fontSize = "0.8em";

    valueAxis2.renderer.gridContainer.background.fill = am4core.color("#000000");
    valueAxis2.renderer.gridContainer.background.fillOpacity = 0.05;

    var volumeSeries = chart.series.push(new am4charts.ColumnSeries());
    volumeSeries.defaultState.transitionDuration = 0
    volumeSeries.fill = series1.stroke;
    volumeSeries.stroke = series1.stroke;
    volumeSeries.dataFields.dateX = "Date";
    volumeSeries.dataFields.valueY = "Volume";
    volumeSeries.yAxis = valueAxis2;
    volumeSeries.tooltipText = "{name} Volume: {valueY.value}";
    volumeSeries.name = "Investment Fund 2";
    // volume should be summed
    volumeSeries.groupFields.valueY = "sum";
    volumeSeries.tooltip.label.fill = volumeSeries.stroke;

    volumeSeries.dataSource.url = "https://www.amcharts.com/wp-content/uploads/assets/stock/MSFT.csv";
    volumeSeries.dataSource.parser = new am4core.CSVParser();
    volumeSeries.dataSource.parser.options.useColumnNames = true;
    volumeSeries.dataSource.parser.options.reverse = true;

    // Cursor
    chart.cursor = new am4charts.XYCursor();

    // Scrollbar
    var scrollbarX = new am4charts.XYChartScrollbar();
    scrollbarX.series.push(series1);
    scrollbarX.marginBottom = 20;
    chart.scrollbarX = scrollbarX;
    scrollbarX.scrollbarChart.xAxes.getIndex(0).minHeight = undefined;


    /**
    * Set up external controls
    */

    // Date format to be used in input fields
    var inputFieldFormat = "yyyy-MM-dd";

    document.getElementById("b1m").addEventListener("click", function() {
      resetButtonClass();
      var max = dateAxis.groupMax["day1"];
      var date = new Date(max);
      date.setMonth(date.getMonth() - 1);

      dateAxis.zoomToDates(
        date,
        new Date(max)
      );
      //this.className = "amcharts-input amcharts-input-selected";
    });

    document.getElementById("b3m").addEventListener("click", function() {
      resetButtonClass();
      var max = dateAxis.groupMax["day1"];
      var date = new Date(max);
      date.setMonth(date.getMonth() - 3);

      dateAxis.zoomToDates(
        date,
        new Date(max)
      );
      //this.className = "amcharts-input amcharts-input-selected";
    });

    document.getElementById("b6m").addEventListener("click", function() {
      resetButtonClass();
      var max = dateAxis.groupMax["day1"];
      var date = new Date(max);
      date.setMonth(date.getMonth() - 6);

      dateAxis.zoomToDates(
        date,
        new Date(max)
      );
      //this.className = "amcharts-input amcharts-input-selected";
    });

    document.getElementById("b1y").addEventListener("click", function() {
      resetButtonClass();
      var max = dateAxis.groupMax["week1"];
      var date = new Date(max);
      date.setFullYear(date.getFullYear() - 1);

      dateAxis.zoomToDates(
        date,
        new Date(max)
      );
      //this.className = "amcharts-input amcharts-input-selected";
    });

    document.getElementById("bytd").addEventListener("click", function() {
      resetButtonClass();
      var date = new Date(dateAxis.max);
      date.setMonth(0, 1);
      date.setHours(0, 0, 0, 0);
      dateAxis.zoomToDates(date, new Date(dateAxis.max));
      //this.className = "amcharts-input amcharts-input-selected";
    });

    document.getElementById("bmax").addEventListener("click", function() {
      resetButtonClass();
      dateAxis.zoom({start:0, end:1});
      //this.className = "amcharts-input amcharts-input-selected";
    });

    function resetButtonClass() {
      var selected = document.getElementsByClassName("amcharts-input-selected");
      for(var i = 0; i < selected.length; i++) {
        selected[i].className = "amcharts-input";
      }
    }

    dateAxis.events.on("selectionextremeschanged", function() {
      updateFields();
    });

    dateAxis.events.on("extremeschanged", updateFields);

    function updateFields() {
      var minZoomed = dateAxis.minZoomed + am4core.time.getDuration(dateAxis.mainBaseInterval.timeUnit, dateAxis.mainBaseInterval.count) * 0.5;
      document.getElementById("fromfield").value = chart.dateFormatter.format(minZoomed, inputFieldFormat);
      document.getElementById("tofield").value = chart.dateFormatter.format(new Date(dateAxis.maxZoomed), inputFieldFormat);
    }

    document.getElementById("fromfield").addEventListener("keyup", updateZoom);
    document.getElementById("tofield").addEventListener("keyup", updateZoom);

    var zoomTimeout;
    function updateZoom() {
      if (zoomTimeout) {
        clearTimeout(zoomTimeout);
      }
      zoomTimeout = setTimeout(function() {
        resetButtonClass();
        var start = document.getElementById("fromfield").value;
        var end = document.getElementById("tofield").value;
        if ((start.length < inputFieldFormat.length) || (end.length < inputFieldFormat.length)) {
          return;
        }
        var startDate = chart.dateFormatter.parse(start, inputFieldFormat);
        var endDate = chart.dateFormatter.parse(end, inputFieldFormat);

        if (startDate && endDate) {
          dateAxis.zoomToDates(startDate, endDate);
        }
      }, 500);
    }

    }); // end am4core.ready()

  script.
    am4core.ready(function() {

    // Themes begin
    am4core.useTheme(am4themes_frozen);
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Create chart instance
    var chart = am4core.create("chartdiv-composition", am4charts.PieChart);

    // Set data
    var selected;
    var types = [{
      type: "Investment Fund 1",
      percent: 70,
      color: chart.colors.getIndex(0),
      subs: [{
        type: "Oil",
        percent: 15
      }, {
        type: "Coal",
        percent: 35
      }, {
        type: "Nuclear",
        percent: 20
      }]
    }, {
      type: "Investment Fund 2",
      percent: 30,
      color: chart.colors.getIndex(1),
      subs: [{
        type: "Hydro",
        percent: 15
      }, {
        type: "Wind",
        percent: 10
      }, {
        type: "Other",
        percent: 5
      }]
    }];

    // Add data
    chart.data = generateChartData();

    // Add and configure Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "percent";
    pieSeries.dataFields.category = "type";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.propertyFields.isActive = "pulled";
    pieSeries.slices.template.strokeWidth = 0;

    function generateChartData() {
      var chartData = [];
      for (var i = 0; i < types.length; i++) {
        if (i == selected) {
          for (var x = 0; x < types[i].subs.length; x++) {
            chartData.push({
              type: types[i].subs[x].type,
              percent: types[i].subs[x].percent,
              color: types[i].color,
              pulled: true
            });
          }
        } else {
          chartData.push({
            type: types[i].type,
            percent: types[i].percent,
            color: types[i].color,
            id: i
          });
        }
      }
      return chartData;
    }

    pieSeries.slices.template.events.on("hit", function(event) {
      if (event.target.dataItem.dataContext.id != undefined) {
        selected = event.target.dataItem.dataContext.id;
      } else {
        selected = undefined;
      }
      chart.data = generateChartData();
    });

    }); // end am4core.ready()

block content

  .row
    .col-lg-3
      .sticky(style="position: -webkit-sticky; position:sticky; top:80px;")
        //- Section selection
        .main-card.mb-3.card.no-shadow.pr-3
          .card-body
            h5.card-title Sections
            ul.list-group
              a.list-group-item(href='#investor__portfolio-value') Portfolio Value
              a.list-group-item(href='#investor__portfolio-composition') Portfolio Composition
              a.list-group-item(href='#investor__pending-investments') Pending Investments

    .col-lg-9
      div#investor__portfolio-value
        .kt-portlet
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Portfolio Value
          .kt-portlet__body(style="position:relative; max-width:100%; height: 500px;")
            .text-center.w-100.h-100(style="position: absolute; top: 0; left: 0; z-index: 5;")
              span.text-secondary.btn.btn-black.btn-bold.btn-upper.mx-4(style="margin-top: 180px; background-color: #404040;")
                | Your portfolio is currently empty
            .blur-effect.w-100.h-100(style="position: absolute; top: 0; left: 0;")
              .w-100#chartdiv(style="height: 500px;")

      // end::Portlet
      div#investor__portfolio-composition      
        .kt-portlet
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Portfolio Composition
          .kt-portlet__body(style="position:relative; max-width:100%; height: 300px;")
            .text-center.w-100.h-100(style="position: absolute; top: 0; left: 0; z-index: 5;")
              span.text-secondary.btn.btn-black.btn-bold.btn-upper.mx-4(style="margin-top: 130px; background-color: #404040;")
                | Your portfolio is currently empty
            .blur-effect.w-100.h-100(style="position: absolute; top: 0; left: 0;")
              .w-100#chartdiv-composition(style="height: 300px;")

      // begin:: Portlet Pending Investments
      div#investor__pending-investments
        .kt-portlet
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Pending Investments
          .kt-portlet__body
            // begin::Section
            .kt-section
              .kt-section__content
                p You currently don't have any open investments
        
      // end:: Portlet Pending Investments

  //- if funds.length

    //- Value Tracking Chart
    .row
      .col-sm-12
        // begin::Portlet
        .kt-portlet.mb-5
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Portfolio Value
          .kt-portlet__body
            input#portfolio-chart__data(style='display:none' data-portfoliochart=`${portfolioChart}`)
            canvas#portfolio-chart(style='height: 300px;')
        // end::Portlet

    //- Portfolio Composition
    .row
      .col-md-12
        // begin::Portlet
        .kt-portlet.mb-5
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Portfolio Composition
          .kt-portlet__body
            .row
              .col-md-5
                input#portfolio-composition__data(style='display:none' data-portfoliocomposition=`${portfolioComposition}`)
                p.text-center Portfolio Value = #{portfolioValue.toLocaleString()} USD
                canvas#dashboard__portfolio-composition(style='height: 300px;')
              .col-md-7
                table.table.m-table.table-responpsive.mt-3
                  thead
                    tr
                      th.text-center Fund
                      th.text-center(style='min-width: 180px;') Type
                      th.text-center #Tokens
                      th.text-center(style='min-width: 120px;') Token-Value
                      th.text-center(style='min-width: 120px;') Holding-Value
                  tbody
                    for investment in portfolio
                      - fund = investment.fund

                      tr
                        td
                          .kt-widget-4
                            .kt-widget-4__item
                              .kt-widget-4__item-content
                                .kt-widget-4__item-section
                                  a(href=`fund/${fund._id}`)
                                    .kt-widget-4__item-pic
                                      if fund.logo
                                        img.rounded(src=`${fund.logo}`, alt='')
                                      else
                                        span.bg-dark.text-white(style='margin-left: 20px;align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= fund.symbol.toUpperCase()
                                  .kt-widget-4__item-info
                                    if fund.logo
                                      a.kt-widget-4__item-username(href=`fund/${fund._id}`)= fund.symbol.toUpperCase()
                        td.text-center.flex2.align-middle
                          if fund.fundType
                            .mr-2
                              span.kt-badge.kt-badge--primary.kt-badge--dot
                              | &nbsp;
                              span.kt-font-bold.kt-font-primary= h.fundUtil.toTitleCase(fund.fundType)
                        td.text-center.flex2.align-middle= investment.tokenAmount
                        td.text-center.flex2.align-middle $#{fund.currentValue}
                        td.text-center.flex2.align-middle $#{fund.currentValue * investment.tokenAmount}
        // end::Portlet

  //- else
    p.bg-secondary.p-3.mb-3 You don't have any pending investments.
      | !{' '}
      a(href="/fund/all") Start here 
      | to discover your first investment.


  //- Active Campaign Investments
  //- .row
    .col-md-12
      if funds.length == 0
        p.bg-secondary.p-3 You don't have any pending investments.
      else
        // begin::Portlet
        .kt-portlet
          .kt-portlet__head
            .kt-portlet__head-label
              h3.kt-portlet__head-title Open Investments
          .kt-portlet__body
            // begin::Section
            .kt-section
              .kt-section__content
                table.table.m-table.table-responpsive.mt-3
                  thead
                    tr
                      th Fund
                      th Type
                      th Minimum Investment
                      th Token Price
                      th Days left
                      th Target
                      th 
                  tbody
                    for fund in funds
                      tr
                        th
                          .kt-widget-4
                            .kt-widget-4__item
                              .kt-widget-4__item-content
                                .kt-widget-4__item-section
                                  a(href=`fund/${fund._id}`)
                                    .kt-widget-4__item-pic
                                      if fund.logo
                                        img.rounded(src=`${fund.logo}`, alt='')
                                      else
                                        span.bg-dark.text-white(style='align-items: center; background-attachment: scroll;background-clip: border-box;background-image: none;background-origin: padding-box;background-position: 0 0;background-repeat: repeat;background-size: auto;border-radius: 5%;box-sizing: border-box;display: flex;font-size: 1rem;font-weight: 600;height: 55px; justify-content: center; width: 55px;')= fund.symbol.toUpperCase()
                                  .kt-widget-4__item-info
                                    if fund.logo
                                      a.kt-widget-4__item-username(href=`fund/${fund._id}`)= fund.symbol.toUpperCase()
                        td.flex2.align-middle
                          if fund.fundType
                            span.kt-badge.kt-badge--primary.kt-badge--dot
                            | &nbsp;
                            span.kt-font-bold.kt-font-primary= h.fundUtil.toTitleCase(fund.fundType)
                        td.flex2.align-middle #{fund.overview && fund.overview.minimumInvestment ? fund.overview.minimumInvestment : "" }
                        td.flex2.align-middle $#{fund.initialTokenPrice}
                        td.flex2.align-middle 6
                        td.flex2.align-middle
                          .progress
                            - campaignGoalMoney = fund.totalTokens * fund.initialTokenPrice / 1000000;
                            - tokensSold = fund.campaignInvestors ? fund.campaignInvestors.map(investmentObj => investmentObj.investmentSize).reduce((a, b) => a + b, 0) : 0;
                            - campaignRaisedMoney = tokensSold * fund.initialTokenPrice / 1000000;

                            .progress-bar.bg-dark(role='progressbar', aria-valuenow=`${campaignRaisedMoney / campaignGoalMoney * 100}`, aria-valuemin='0', aria-valuemax='100', style=`width: ${campaignRaisedMoney / campaignGoalMoney * 100}%;`)
                              | #{campaignRaisedMoney / campaignGoalMoney * 100}%
                        td.flex2.align-middle.kt-align-right
                          a.btn.btn-sm.btn-transition.btn-outline-secondary.btn-square(href=`fund/${fund._id}` target="_blank") Details
                          button.offering-card__invest-btn.btn.btn-sm.btn-dark.btn-square.ml-2(type="button" style="vertical-align: middle;" data-target="#investModal" data-toggle="modal" data-tokensymbol=`${fund.symbol}` data-tokenprice=`${fund.initialTokenPrice}` data-minimuminvestment=`${fund.minimumInvestment}` data-fundid=`${fund._id}`) Invest
            // end::Section
        // end::Portlet