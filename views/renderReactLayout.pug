extends layout

block content
  //- Renders the markup (react-generated html)
  div#root !{h.renderReact(reactComp, {title, subtitle, activePage, user, ...JSON.parse(props)})}
  div#props(style="display:none" data-props=props)

block scripts
  //- Used to pass props through window object to entrypoint of each page for hydration.
  //- Otherwise we don't have access to props during hydration and props object is empty - overwrites SSR.
  script.
    const propsTitle = "#{title}"
    const propsSubtitle = "#{subtitle}"
    const propsActivePage = "#{activePage}"
    const propsUser = !{JSON.stringify(user)}
    window.__REACT__SSR__DATA__ = {
      title: propsTitle,
      subtitle: propsSubtitle,
      activePage: propsActivePage,
      user: propsUser,
      ...JSON.parse(document.getElementById("props").dataset.props)
    }

  //- Hydrated react component - adds event listeners to make markup interactive
  script(src=`/dist/${scriptName}.bundle.js` type='text/javascript')
  