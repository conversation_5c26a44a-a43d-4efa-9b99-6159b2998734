extends onboardingLayout


block stylesheets
  link(href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" type="text/css")
  style.
    .select2.select2-container.select2-container--default {
      width: 100% !important;
    }

block content
  .row.justify-content-center
    .col-lg-8.col-md-10.col-sm-11.col-xs-11
      .kt-portlet
        .kt-portlet__body.kt-portlet__body--fit
          #kt_wizard_v1.kt-wizard-v1(data-ktwizard-state='step-first')
            .row
              .col-12.kt-wizard-v1__aside.border-0

                //- Form Wizard Nav
                .kt-wizard-v1__nav.pb-0
                  .row
                    .col-12
                      .text-center
                        h1.h2 Welcome to Wealthyhood Platform
                        p.mt-4(style="font-weight: 500;font-size: 1.2rem;line-height: 1.8rem;color: #918ea7;")
                          | Start building your wealth with lower risk! First, we would like to find out a bit more about you!
                  .kt-wizard-v1__nav-items.mt-5.mb-0
                    a.kt-wizard-v1__nav-item(href='#', data-ktwizard-type='step', data-ktwizard-state='current')
                      span 1
                    a.kt-wizard-v1__nav-item(href='#', data-ktwizard-type='step')
                      span 2
                    a.kt-wizard-v1__nav-item(href='#', data-ktwizard-type='step')
                      span 3
                //- End Form Wizard Nav

            //- Wizard Wrapper
            .row
              .col-12.kt-wizard-v1__wrapper

                //- Form Wizard Step 1 - Name
                .kt-wizard-v1__content.border-0(data-ktwizard-type='step-content', data-ktwizard-state='current')
                  form#kt_form_1.kt-form
                    .kt-heading.kt-heading--md Set up Your Account
                    .kt-separator.kt-separator--height-xs
                    .kt-form__section.kt-form__section--first
                      .row
                        .col-lg-6
                          .form-group
                            label First Name
                              | !{' '}
                              span.text-danger *

                            input.form-control(type='text', name='firstname', placeholder='Enter first name' value=`${user.firstName || ""}`)
                            span.form-text.text-muted Please enter your first name
                        .col-lg-6
                          .form-group
                            label Last Name
                              | !{' '}
                              span.text-danger *

                            input.form-control(type='text', name='lastname', placeholder='Enter last name' value=`${user.lastName || ''}`)
                            span.form-text.text-muted Please enter your last name
                //- End Form Wizard Step 1 - Name

                //- Form Wizard Step 2 - Investment Preferences
                .kt-wizard-v1__content.border-0(data-ktwizard-type='step-content')
                  form#kt_form_2.kt-form
                    .kt-heading.kt-heading--md What is your Investment Experience?
                    .kt-form__section.kt-form__section--first.pt-0

                      .form-group.row
                        label.col-lg-5.col-form-label(for='past_investments_size') How much did you invest last year?
                        .col-lg-7
                          select.selectpicker.form-control(name='past_investments_size')
                            option(disabled selected value) -- select an option --
                            option(value="0_to_1") 0 - £1,000
                            option(value="1_to_5") £1,000 - £5,000
                            option(value="5_to_20") £5,000 - £20,000
                            option(value="20_plus") £20,000+
                      .form-group.row
                        label.col-lg-5.col-form-label(for='past_investments_asset') What did you invest in last year?
                        .col-lg-7
                          select#select2_past_investments.form-control.kt-select2(name='past_investments_asset', multiple='multiple')
                            option(value="stocks") Stocks (e.g. AAPL, TSLA)
                            option(value="bonds") Bonds
                            option(value="etfs") ETFs
                            option(value="mutualfunds") Mutual Funds
                            option(value="hedgefunds") Hedge Funds
                            option(value="privateequity") Private Equity
                            option(value="realestate") Real Estate
                            option(value="startups") Startups (e.g. through crowdfunding)
                            option(value="commodities") Commodities (e.g. gold, silver, oil)
                            option(value="crypto") Crypto
                            option(value="other") Other

                    .kt-separator.kt-separator--height-sm

                    .kt-heading.kt-heading--md What is your Investment Style?

                    .form-group.row.mt-2
                      .col-lg-12
                        .row
                          .col-lg-4
                            label.kt-option
                              span.kt-option__control
                                span.kt-radio.kt-radio--bold.kt-radio--brand.kt-radio--check-bold(checked='')
                                  input(type='radio', name='investing_style', value='CAUTIOUS')
                                  span
                              span.kt-option__label
                                span.kt-option__head
                                  span.kt-option__title
                                    | Cautious
                                span.kt-option__body(style="width: 100%;")
                                  | My priority is to minimise losses. Only small up and down movements of my portfolio value are acceptable.
                          .col-lg-4
                            label.kt-option
                              span.kt-option__control
                                span.kt-radio.kt-radio--bold.kt-radio--brand
                                  input(type='radio', name='investing_style', value='BALANCED')
                                  span
                              span.kt-option__label
                                span.kt-option__head
                                  span.kt-option__title
                                    | Balanced
                                .mx-auto.d-block.justify-content-center
                                  span.kt-option__body(style="width: 100%;")
                                    | Minimising losses is as important as high returns. Finding the right balance is the key priority for me.
                          .col-lg-4
                            label.kt-option
                              span.kt-option__control
                                span.kt-radio.kt-radio--bold.kt-radio--brand
                                  input(type='radio', name='investing_style', value='AMBITIOUS')
                                  span
                              span.kt-option__label
                                span.kt-option__head
                                  span.kt-option__title
                                    | Ambitious
                                span.kt-option__body(style="width: 100%;")
                                  | My priority is to maximise returns. Up and down movements are acceptable, to achieve superior returns.
                //- End Form Wizard Step 2 - Investment Preferences

                //- Form Wizard Step 3 - Experience
                .kt-wizard-v1__content.border-0(data-ktwizard-type='step-content')
                  form#kt_form_3.kt-form(method="POST" action="/onboarding/professional-experience")
                    .kt-heading.kt-heading--md What is your Professional Experience?
                    .kt-separator.kt-separator--height-sm
                    .kt-form__section.kt-form__section--first
                      .row
                        .col-lg-6
                          .form-group
                            label Industry:
                            select.selectpicker.form-control(name='industry')
                              option(disabled selected value) -- select an option --
                              option(value="ACCOUNTING_LEGAL") Accounting & Legal
                              option(value="BUSINESS") Business Services
                              option(value="FINANCE") Finance
                              option(value="HEALTH") Health Care
                              option(value="IT") Information Technology
                              option(value="MANUFACTURING") Manufacturing
                              option(value="MEDIA") Media
                              option(value="RETAIL") Retail
                              option(value="OTHER") Other
                        .col-lg-6
                          .form-group
                            label Years of experience:
                            select.selectpicker.form-control(name='seniority')
                              option(disabled selected value) -- select an option --
                              option(value="0_to_5") 0-5
                              option(value="5_to_10") 5-10
                              option(value="10_to_20") 10-20
                              option(value="20_plus") 20+  
                //- End Form Wizard Step 3 - Experience

                //- Form Actions
                form.kt-form.pt-0
                  //- form element here is only used as a dummy component because style definition of actions
                  //- needs to be inside a form
                  .kt-form__actions
                    a.skip-btn.btn.btn-outline-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper(href="/dashboard" style="display: none;")
                      | Skip
                    .submit-btn.btn.btn-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper(data-ktwizard-type='action-submit')
                      | Submit
                    .btn.btn-brand.btn-md.btn-tall.btn-wide.btn-bold.btn-upper(data-ktwizard-type='action-next')
                      | Next Step
                //- End Form Actions

                //- Privacy Declaration
                .row.mb-3
                  .col-12.text-center.text-muted
                    i.text-center.mt-5.pb-4 We respect your privacy -
                      | !{' '}
                      a(href="https://legal.wealthyhood.dev/privacy-policy.pdf" target="_blank") Our Privacy Policy
                //- End Privacy Declaration

            //- End Wizard Wrapper

block scripts
  script(src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js" type="text/javascript")
  script.
    window.addEventListener("load", function () {
      $('#select2_past_investments').select2({placeholder: "Select past investments"});  
    })

  script(src="https://unpkg.com/axios/dist/axios.min.js" type="text/javascript")
  script.
    window.addEventListener("load", function () {
      var initWizard = function () {
        // Initialize form wizard
        const wizard = new KTWizard('kt_wizard_v1', {
          startStep: 1
        });

        // Validation before going to next page
        wizard.on('beforeNext', function(wizardObj) {
          const step = wizard.getStep();

          const form = document.querySelector("#kt_form_" + step);
          const formData = new FormData(form);
          let postData;
          let postEndpoint;

          if (step === 1) {
            const firstName = formData.get("firstname");
            const lastName = formData.get("lastname");

            if (!firstName || !lastName) {
              // Prevent form from getting submitted if not both values are preset
              // Wizard won't go to the next step
              wizardObj.stop();
              swal({
                "title": "", 
                "text": "First & last name cannot be empty.", 
                "type": "error",
                "confirmButtonClass": "btn btn-secondary m-btn m-btn--wide"
              });

              return;
            }

            postData = {
              firstName,
              lastName
            };
            postEndpoint = "/onboarding/name";
          } else if (step === 2) {
            const pastInvestmentsAsset = $('#select2_past_investments').select2("data").map(entry => entry.id);
            postData = {
              pastInvestmentsSize: formData.get("past_investments_size") || "",
              pastInvestmentsAsset,
              investingStyle: formData.get("investing_style") || ""
            };
            postEndpoint = "/onboarding/past-investments";
          }

          axios
            .post(postEndpoint, postData)
            .catch(function (error) {});
        })

        // Change event
        wizard.on('change', function(wizard) {
          // Display skip button only after second step - first step is mandatory
          if (wizard.getStep() > 1) {
            const skipBtn = document.querySelector(".skip-btn");
            skipBtn.style.display = "block";
          }
          setTimeout(function() {
            KTUtil.scrollTop();    
          }, 100);
        });

        return wizard;
      }

      const initSubmit = function() {
        const submitBtn = document.querySelector(".submit-btn");
        submitBtn.onclick = () => {
          const form = document.querySelector("#kt_form_3");
          form.submit();
        }
      }

      initWizard(); 
      initSubmit();
    })
