const path = require("path");
const Dotenv = require("dotenv-webpack");

const config = (env) => ({
  entry: {
    // Every pages entry point should be mentioned here
    adminEditParty: ["./react-views/entrypoints/adminEditPartyIndex.tsx"],
    adminAssetTransactionBreakdown: ["./react-views/entrypoints/adminOrdersBreakdownIndex.tsx"],
    adminBankAccounts: ["./react-views/entrypoints/adminBankAccountsIndex.tsx"],
    adminDepositTransactions: ["./react-views/entrypoints/adminDepositTransactionsIndex.tsx"],
    adminWithdrawalTransactions: ["./react-views/entrypoints/adminWithdrawalTransactionsIndex.tsx"],
    adminOrderAnalytics: ["./react-views/entrypoints/adminOrderAnalyticsIndex.tsx"],
    adminOrderAnalyticsBreakdown: ["./react-views/entrypoints/adminOrderAnalyticsBreakdownIndex.tsx"],
    adminOrderManagement: ["./react-views/entrypoints/adminOrderManagementIndex.tsx"],
    adminRewardDetails: ["./react-views/entrypoints/adminRewardDetailsIndex.tsx"],
    adminRewards: ["./react-views/entrypoints/adminRewardsIndex.tsx"],
    adminGifts: ["./react-views/entrypoints/adminGiftsIndex.tsx"],
    adminGiftDetails: ["./react-views/entrypoints/adminGiftDetailsIndex.tsx"],
    adminFailedAccounts: ["./react-views/entrypoints/adminFailedAccountsIndex.tsx"],
    adminRewardSubmission: ["./react-views/entrypoints/adminRewardSubmissionIndex.tsx"],
    adminUserList: ["./react-views/entrypoints/adminUserListIndex.tsx"],
    assetDiscovery: ["./react-views/entrypoints/assetDiscoveryIndex.tsx"],
    collectPersonalDetails: ["./react-views/entrypoints/collectPersonalDetailsIndex.tsx"],
    investments: ["./react-views/entrypoints/investmentsIndex.tsx"],
    depositTransactionsAdmin: ["./react-views/entrypoints/depositTransactionsAdminIndex.tsx"],
    emailDisposable: ["./react-views/entrypoints/emailDisposableIndex.tsx"],
    cash: ["./react-views/entrypoints/investorCashIndex.tsx"],
    investorAccountDetails: ["./react-views/entrypoints/investorAccountDetailsIndex.tsx"],
    investmentSuccess: ["./react-views/entrypoints/investmentSuccessIndex.tsx"],
    investmentActivity: ["./react-views/entrypoints/investmentActivityIndex.tsx"],
    planUpdateSuccess: ["./react-views/entrypoints/planUpdateSuccessIndex.tsx"],
    newRecurringTopUpSuccess: ["./react-views/entrypoints/newRecurringTopUpSuccessIndex.tsx"],
    mandateSuccess: ["./react-views/entrypoints/mandateSuccessIndex.tsx"],
    cancellationSuccess: ["./react-views/entrypoints/transactionCancellationSuccessIndex.tsx"],
    rebalancingSuccess: ["./react-views/entrypoints/rebalancingSuccessIndex.tsx"],
    paymentPending: ["./react-views/entrypoints/paymentPendingIndex.tsx"],
    paymentSuccess: ["./react-views/entrypoints/paymentSuccessIndex.tsx"],
    giftSuccess: ["./react-views/entrypoints/giftSuccessIndex.tsx"],
    withdrawalSuccess: ["./react-views/entrypoints/withdrawalSuccessIndex.tsx"],
    sendGift: ["./react-views/entrypoints/sendGiftIndex.tsx"],
    earnFreeShares: ["./react-views/entrypoints/referFriendIndex.tsx"],
    inviteFriend: ["./react-views/entrypoints/inviteFriendIndex.tsx"],
    selectPlan: ["./react-views/entrypoints/selectPlanOnboardingIndex.tsx"],
    setReferralCode: ["./react-views/entrypoints/setReferralCodeIndex.tsx"],
    joinWealthybites: ["./react-views/entrypoints/joinWealthybitesIndex.tsx"],
    verificationInitiated: ["./react-views/entrypoints/verificationInitiatedIndex.tsx"],
    verificationPending: ["./react-views/entrypoints/verificatioPendingIndex.tsx"],
    verificationSuccess: ["./react-views/entrypoints/verificationSuccessIndex.tsx"],
    portfolioCreationSuccess: ["./react-views/entrypoints/portfolioCreationSuccessIndex.tsx"],
    portfolioTarget: ["./react-views/entrypoints/portfolioTargetIndex.tsx"],
    portfolioPersonalisation: ["./react-views/entrypoints/portfolioPersonalisationIndex.tsx"],
    portfolioCreation: ["./react-views/entrypoints/portfolioCreationIndex.tsx"],
    portfolioCreationRoboAdvisor: ["./react-views/entrypoints/portfolioCreationRoboAdvisorIndex.tsx"],
    portfolioCreationTemplateInfo: ["./react-views/entrypoints/portfolioCreationTemplateInfoIndex.tsx"],
    portfolioSetup: ["./react-views/entrypoints/portfolioSetupIndex.tsx"],
    portfolioSetupAsStep: ["./react-views/entrypoints/portfolioSetupAsStepIndex.tsx"],
    adminOrdersBreakdown: ["./react-views/entrypoints/adminOrdersBreakdownIndex.tsx"],
    adminDepositTransactionBreakdown: ["./react-views/entrypoints/adminDepositTransactionBreakdownIndex.tsx"],
    adminWithdrawalTransactionBreakdown: [
      "./react-views/entrypoints/adminWithdrawalTransactionBreakdownIndex.tsx"
    ],
    assetTransactionsAdmin: ["./react-views/entrypoints/assetTransactionsAdminIndex.tsx"],
    createMyPortfolio: ["./react-views/entrypoints/createMyPortfolioIndex.tsx"],
    closingAccount: ["./react-views/entrypoints/closingAccountIndex.tsx"],
    residencyCountry: ["./react-views/entrypoints/residencyCountryIndex.tsx"],
    emailVerified: ["./react-views/entrypoints/emailVerifiedIndex.tsx"],
    billing: ["./react-views/entrypoints/billingIndex.tsx"],
    statements: ["./react-views/entrypoints/statementsIndex.tsx"],
    autopilot: ["./react-views/entrypoints/autopilotIndex.tsx"],
    home: ["./react-views/entrypoints/summaryIndex.tsx"],
    learningHub: ["./react-views/entrypoints/learningHubIndex.tsx"],
    guide: ["./react-views/entrypoints/guideIndex.tsx"],
    analystInsights: ["./react-views/entrypoints/articleIndex.tsx"],
    news: ["./react-views/entrypoints/articleIndex.tsx"],
    pendingTransactions: ["./react-views/entrypoints/pendingTransactionsIndex.tsx"],
    changePlan: ["./react-views/entrypoints/changePlanIndex.tsx"],
    openAccount: ["./react-views/entrypoints/openAccountIndex.tsx"],
    idVerification: ["./react-views/entrypoints/idVerificationIndex.tsx"],
    idVerificationPolling: ["./react-views/entrypoints/idVerificationPollingIndex.tsx"],
    idVerificationResume: ["./react-views/entrypoints/idVerificationResumeIndex.tsx"],
    savingsTopupSuccess: ["./react-views/entrypoints/savingsTopupSuccessIndex.tsx"],
    savingsWithdrawalSuccess: ["./react-views/entrypoints/savingsWithdrawalSuccessIndex.tsx"],
    notificationSettings: ["./react-views/entrypoints/notificationSettingsIndex.tsx"]
  },
  target: "web",
  devtool: env && env === "development" ? "eval" : "nosources-source-map",
  mode: "development",
  output: {
    path: path.resolve(__dirname, "public", "dist"),
    filename: "[name].bundle.js"
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        use: [{ loader: "ts-loader", options: { transpileOnly: true } }]
      }
    ]
  },
  resolve: {
    fallback: { fs: false },
    extensions: [".js", ".jsx", ".ts", ".tsx"]
  },
  plugins: [new Dotenv({ path: "./client.env" })]
});
module.exports = config;
